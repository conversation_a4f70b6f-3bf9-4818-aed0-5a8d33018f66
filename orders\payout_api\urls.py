from django.urls import path
from .views import (
    AccountBalance,
    AccountPayoutDetails,
    UpdatePayoutAndPayoutBalanceEveryday,
    GetTransactionsOfStore,
    GetTransactionsOfOrder,
    PayoutWithdrawal,
    GeneratePayoutOtp,
    VerifyPayoutOtp,
    AddBankDetailsView,
    GetBankDetailsView,
    SetPrimaryBankAccountView,
    DeleteBankDetailsView,
)

urlpatterns = [
    # payouts
    path(
        "get_account_balance/<str:store_reference>/",
        AccountBalance.as_view(),
        name="get-account-balance",
    ),
    path(
        "get_account_payout_details/<str:store_reference>/",
        AccountPayoutDetails.as_view(),
        name="get-account-payout",
    ),
    path(
        "update_payout_and_payout_balance/",
        UpdatePayoutAndPayoutBalanceEveryday.as_view(),
        name="update-payout-and-payout-balance",
    ),
    path(
        "all_transactions_store/<str:store_reference>/",
        GetTransactionsOfStore.as_view(),
        name="all-transactions-store",
    ),
    path(
        "all_transactions_order/<str:order_number>/",
        GetTransactionsOfOrder.as_view(),
        name="all-transactions-order",
    ),
    path(
        "payout_withdrawal/",
        PayoutWithdrawal.as_view(),
        name="payout-withdrawal",
    ),
    path(
            "send_payout_otp/",
            GeneratePayoutOtp.as_view(),
            name="send-payout-otp",
    ),
    path(
            "verify_payout_otp/",
            VerifyPayoutOtp.as_view(),
            name="verify-payout-otp",
    ),
    path('add-bank-details/', AddBankDetailsView.as_view(), name='add_bank_details'),
    path('set-primary-account/', SetPrimaryBankAccountView.as_view(), name='set_primary_account'),
    path('get-bank-details/', GetBankDetailsView.as_view(), name='retrieve_bank_details'),
    path('delete-bank-details/', DeleteBankDetailsView.as_view(), name='delete_bank_details'),
]
