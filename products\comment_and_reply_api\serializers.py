from rest_framework import serializers
from .models import Comment, Reply, ReviewImages
import datetime
from django.utils.timezone import utc
from users.user_api.models import User
from stores.store_api.models import Store

class AddCommentSerializer(serializers.ModelSerializer):
    REVIEW = "review"
    COMMENT = "comment"
    QUESTION = "question"

    COMMENT_TYPE_CHOICES = [
        (REVIEW, "review"),
        (COMMENT, "comment"),
        (QUESTION, "question"),
    ]
    comment_type = serializers.ChoiceField(choices=COMMENT_TYPE_CHOICES)

    class Meta:
        model = Comment
        fields = [
            "commentid",
            "product_reference",
            "reference",
            "comments",
            "comment_type",
        ]


class GetReplySerializer(serializers.ModelSerializer):
    replied_at = serializers.SerializerMethodField("get_replied_at")
    username = serializers.SerializerMethodField('get_username')
    user_clapped_or_not = serializers.BooleanField(default=False)

    class Meta:
        model = Reply
        fields = [
            "replyid",
            "commentid",
            "created_date",
            "modified_date",
            "reply",
            "username",
            "reference",
            "claps",
            "clapped_users",
            "user_clapped_or_not",
            "replied_at",
            "product_version",
        ]

    def get_username(self, obj):
        reference = obj.reference
        if reference:
            if reference.startswith("U"):
                user_instance = User.objects.get(user_reference=reference)
                return user_instance.user_name
            elif reference.startswith("S"):
                store_instance = Store.objects.get(store_reference=reference)
                return store_instance.storehandle
        else:
            return None

    def get_replied_at(self, obj):
        now = datetime.datetime.utcnow().replace(tzinfo=utc)
        timediff = now - obj.modified_date
        second = int(timediff.total_seconds())
        time = int(second // 3600)
        if time >= 24:
            return str(time // 24) + "day"
        else:
            if time >= 1:
                return str(time) + "h"
            elif time < 1:
                if second // 60 >= 1:
                    return str(second // 60) + "m"
                elif second // 60 < 1:
                    return str(second) + "s"


class ReviewImageSerializer(serializers.ModelSerializer):
    image = serializers.ImageField(source="review_image")

    class Meta:
        model = ReviewImages
        fields = [
            "review_image_id",
            "commentid",
            "created_by",
            "image",
            "created_date",
            "modified_date",
        ]


class GetCommentSerializer(serializers.ModelSerializer):
    commented_at = serializers.IntegerField(default=0)
    user_clapped_or_not = serializers.BooleanField(default=False)
    username = serializers.SerializerMethodField('get_username')
    comments = serializers.SerializerMethodField('get_comments')
    replies = GetReplySerializer(many=True)
    comment_review_image = ReviewImageSerializer(many=True)

    class Meta:
        model = Comment
        fields = [
            "commentid",
            "productid",
            "product_version",
            "commented_at",
            "created_date",
            "modified_date",
            "username",
            "reference",
            "comments",
            "claps",
            "clapped_users",
            "user_clapped_or_not",
            "comment_type",
            "review",
            "replies",
            "comment_review_image",
        ]

    def get_comments(self, obj):
        if obj.comments is None:
            return ""
        else:
            return obj.comments

    def get_username(self, obj):
        reference = obj.reference
        if reference:
            if reference.startswith("U"):
                user_instance = User.objects.get(user_reference=reference)
                return user_instance.user_name
            elif reference.startswith("S"):
                store_instance = Store.objects.get(store_reference=reference)
                return store_instance.storehandle
        else:
            return None

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if representation.get("comment_review_image"):
            list_of_review_images = representation.get("comment_review_image")
            lst = []
            for elem in list_of_review_images:
                context = {
                    "review_image_id": elem["review_image_id"],
                    "review_image": elem["image"],
                    "created_date": elem["created_date"],
                }
                lst.append(context)
            representation.update(comment_review_image=lst)
            return representation
        else:
            return representation


class AddReplySerializer(serializers.ModelSerializer):
    class Meta:
        model = Reply
        fields = ["replyid", "commentid", "reply", "reference"]


class AddReviewCommentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Comment
        fields = [
            "commentid",
            "product_reference",
            "reference",
            "comments",
            "comment_type",
            "order_reference",
        ]
