from rest_framework import serializers
from .models import (
    TrustCenter,
    Documents,
    DeliverySettings,
    DeliveryLocations,
    RefundAndWarranty,
    Address,
)
from stores.store_api.models import StoreConfig, Store


class TrustCenterSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrustCenter
        fields = [
            "trustcenterid",
            "store_reference",
            "trustscore",
            "phonenumber",
            "emailid",
            "primarycontacttype",
            "sellerlevel",
            "isphysicalstore",
            "address",
            "city",
            "pincode",
            "state",
            "longitude",
            "latitude",
            "swadeshi_owned",
            "trust_center_note"
        ]


class TrustCenterContactSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrustCenter
        fields = [
            "trustcenterid",
            "store_reference",
            "phonenumber",
            "emailid",
            "primarycontacttype",
        ]


class TrustCenterLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrustCenter
        fields = [
            "trustscore",
            "storeid",
            "store_reference",
            "sellerlevel",
            "isphysicalstore",
            "address",
            "city",
            "pincode",
            "state",
            "longitude",
            "latitude",
        ]


class TrustCenterSwadeshiLabelSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrustCenter
        fields = [
            "swadeshi_owned"
        ]


class DocumentsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Documents
        fields = "__all__"


class AddDeliverySettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliverySettings
        fields = '__all__'


class DeliverySettingsSerializer(serializers.ModelSerializer):
    is_default_settings = serializers.SerializerMethodField("get_is_default_settings")
    is_pan_exists = serializers.SerializerMethodField("get_is_pan_exists")
    is_gst_exists = serializers.SerializerMethodField("get_is_gst_exists")
    trustcenter_location_state = serializers.SerializerMethodField("get_trustcenter_location_state")
    class Meta:
        model = DeliverySettings
        fields = [
            "deliverysettingid",
            "is_default_settings",
            "store_reference",
            "storeid",
            "productid",
            "product_reference",
            "deliverymethod_swadesic",
            "deliverymethod_logistics",
            "deliverymethod_self",
            "delivery_locations",
            "selected_location_count",
            "deliveryfeetype_all_free",
            "deliveryfeetype_standard",
            "deliveryfeetype_distance",
            "deliveryfee_value",
            "deliveryfee_valuetype",
            "distance_based_max_deliveryfee",
            "no_deliveryfee_maxvalue",
            "no_deliveryfee_maxvalue_enabled",
            "no_deliveryfee_products",
            "no_deliveryfee_products_enabled",
            "no_deliveryfee_samepincode_enabled",
            "no_deliveryfee_samecity_enabled",
            "delivery_personal_name",
            "default_logistic_partner",
            "delivery_personal_phone",
            "created_by",
            "modified_by",
            "time_to_prepare",
            "time_to_deliver",
            "delivery_settings_version",
            "is_pan_exists",
            "is_gst_exists",
            "trustcenter_location_state",
            "fulfillment_options",
            "pickup_location_ids"

        ]

    def get_trustcenter_location_state(self, obj):
        trust_center_instance = TrustCenter.objects.filter(store_reference=obj.store_reference).first()
        if trust_center_instance:
            return trust_center_instance.state
        else:
            return None

    def get_is_gst_exists(self, obj):
        store_instance = Store.objects.filter(store_reference=obj.store_reference.store_reference).first()
        if store_instance and store_instance.gst_number:
            return True
        else:
            return False

    def get_is_pan_exists(self, obj):
        store_instance = Store.objects.filter(store_reference=obj.store_reference.store_reference).first()
        if store_instance and store_instance.pan_number:
            return True
        else:
            return False

    def get_is_default_settings(self, obj):
        if obj.product_reference:
            is_default = False
        else:
            is_default = True
        return is_default

    # @staticmethod
    # def convert_delivery_locations_to_pincode(delivery_locations):
    #     queryset = DeliveryLocations.objects.all()
    #     all_states = list(queryset.values_list("state", flat=True).order_by ('state').distinct ())
    #     all_cities = list(queryset.values_list("city", flat=True).order_by ('city').distinct ())
    #     lst = []
    #     if delivery_locations:
    #         list_of_locations = delivery_locations.split("|")
    #         lst = []
    #         for elem in list_of_locations:
    #             if elem in all_states:
    #                 pincode = list(queryset.filter(state=elem).values_list("pincode", flat=True))
    #                 lst.extend(pincode)
    #             elif elem in all_cities:
    #                 pincode = queryset.filter(city=elem).values_list("pincode", flat=True)
    #                 lst.extend(pincode)
    #             else:
    #                 lst.append(elem)
    #     return lst
    #
    # def get_delivery_locations(self, obj):
    #     delivery_locations = obj.delivery_locations
    #     list_of_pincode = self.convert_delivery_locations_to_pincode(delivery_locations)
    #     return list_of_pincode


class DeliveryLocationsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryLocations
        fields = "__all__"


class RefundAndWarrantySerializer(serializers.ModelSerializer):
    is_default_settings = serializers.SerializerMethodField("get_is_default_settings")
    class Meta:
        model = RefundAndWarranty
        fields = [
            "refundandwarrantyid",
            "is_default_settings",
            "store_reference",
            "storeid",
            "productid",
            "product_reference",
            "has_warranty",
            "warranty_period_no",
            "warranty_period",
            "warranty_card_available",
            "return_type",
            "replacement_type",
            "return_period",
            "return_conditions",
            "return_pickup",
            "return_cost_on_customer",
            "return_cost_on_seller",
            "delivery_fee_reevaluation_on_seller",
            "delivery_fee_reevaluation_on_customer",
            "return_delivery_address_id",
            "refund_warranty_version",
        ]
    def get_is_default_settings(self, obj):
        if obj.product_reference:
            is_default = False
        else:
            is_default = True
        return is_default


class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = [
            "addressid",
            "store_reference",
            "storeid",
            "address",
            "city",
            "pincode",
            "state",
            "name",
            "phone_number",
            "default_address",
            "is_pickup_location",
            "latitude",
            "longitude",
            "location_link",
            "pickup_timings",
            "store_address_type",
            "pickup_location_in_shiprocket"
        ]


class PincodeSerializer(serializers.Serializer):
    pincode = serializers.CharField()
    pincode_marked = serializers.IntegerField(default=0)


class CitySerializer(serializers.Serializer):
    city = serializers.CharField()
    city_marked = serializers.IntegerField(default=0)
    pincodes = PincodeSerializer(many=True)


class StateSerializer(serializers.Serializer):
    state = serializers.CharField()
    state_marked = serializers.IntegerField(default=0)
    cities = CitySerializer(many=True)


class StoreConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = StoreConfig
        fields = "__all__"
