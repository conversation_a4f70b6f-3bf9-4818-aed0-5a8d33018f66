# Generated by Django 4.2.7 on 2024-08-06 08:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0118_orderconfiguration_grace_period"),
    ]

    operations = [
        migrations.AddField(
            model_name="order",
            name="razorpay_payment_id",
            field=models.Char<PERSON>ield(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="razorpay_payment_id",
            field=models.Char<PERSON>ield(max_length=50, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="order",
            name="bank_transaction_id",
            field=models.Char<PERSON>ield(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="order",
            name="payment_mode",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="order",
            name="transaction_date",
            field=models.Char<PERSON><PERSON>(max_length=50, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="order",
            name="transaction_id",
            field=models.Char<PERSON>ield(max_length=100, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="order",
            name="txn_token",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="orderlifecycle",
            name="bank_transaction_id",
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="orderlifecycle",
            name="payment_mode",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="orderlifecycle",
            name="transaction_date",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="orderlifecycle",
            name="transaction_id",
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="orderlifecycle",
            name="txn_token",
            field=models.CharField(max_length=50, null=True),
        ),
    ]
