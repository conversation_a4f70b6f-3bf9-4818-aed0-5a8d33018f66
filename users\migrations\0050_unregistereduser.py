# Generated by Django 4.2.7 on 2023-12-21 13:46

from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0045_merge_20231005_1006'),
        ('users', '0049_userdevicedetails'),
    ]

    operations = [
        migrations.CreateModel(
            name='UnregisteredUser',
            fields=[
                ('unregistered_user_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('phonenumber', phonenumber_field.modelfields.PhoneNumberField(max_length=128, region=None)),
                ('is_following', models.BooleanField(default=False)),
                ('store_follower', models.ForeignKey(blank=True, db_column='store_follower', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='unregisteredstorefollower', to='stores.store', to_field='store_reference')),
                ('user_follower', models.ForeignKey(blank=True, db_column='user_follower', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='unregistereduserfollower', to='users.user', to_field='user_reference')),
            ],
            options={
                'verbose_name_plural': 'unregistered users',
                'db_table': '"user"."unregistered_users"',
            },
        ),
    ]
