# Generated by Django 4.2.7 on 2025-02-20 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AppFeatureRequests",
            fields=[
                (
                    "app_feature_request_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("reference", models.CharField(max_length=30, unique=True)),
                ("requested_store_ai", models.BooleanField(default=False)),
                ("requested_product_affiliation", models.BooleanField(default=False)),
            ],
            options={
                "verbose_name_plural": "app_feature_requests",
                "db_table": '"app_common"."app_feature_requests"',
            },
        ),
    ]
