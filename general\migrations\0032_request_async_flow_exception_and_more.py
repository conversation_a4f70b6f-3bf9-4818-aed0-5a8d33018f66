# Generated by Django 4.2.7 on 2024-10-26 08:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("general", "0031_alter_rewardtransferconfig_transfer_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="request",
            name="async_flow_exception",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="request",
            name="async_flow_exception_details",
            field=models.TextField(blank=True, null=True),
        ),
    ]
