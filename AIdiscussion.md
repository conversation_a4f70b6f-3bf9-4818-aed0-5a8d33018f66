# Task: Implementation of swadesic fee (flat fee per order) deduction during payout on the first order of the order.

# Refer to model classes and understand how these tables are related: Order, Suborder, SuborderPaymentDetail, and OrderPayout tables and get the attributes and relationships in this table that helps the process.
# Task: Implementation of swadesic fee (flat fee per order) deduction during payout on the first order of the order.

# Refer to model classes and understand how these tables are related: Order, Suborder, SuborderPaymentDetail, and OrderPayout tables and get the attributes and relationships in this table that helps the process.

# Implementation steps:
# 1. Identify the first suborder of an order
# 2. Calculate the swadesic fee for the order
# 3. Deduct the swadesic fee from the payout amount of the first suborder
# 4. Update the SuborderPaymentDetail and OrderPayout tables accordingly

# Pseudo-code:

def process_swadesic_fee(order):
    # Get the first suborder of the order
    first_suborder = order.suborders.order_by('created_at').first()
    
    # Calculate the swadesic fee (assuming it's a flat fee per order)
    swadesic_fee = calculate_swadesic_fee(order)
    
    # Get the SuborderPaymentDetail for the first suborder
    payment_detail = SuborderPaymentDetail.objects.get(suborder=first_suborder)
    
    # Deduct the swadesic fee from the payout amount
    original_payout_amount = payment_detail.payout_amount
    new_payout_amount = original_payout_amount - swadesic_fee
    
    # Update the SuborderPaymentDetail
    payment_detail.payout_amount = new_payout_amount
    payment_detail.swadesic_fee = swadesic_fee
    payment_detail.save()
    
    # Update the OrderPayout
    order_payout = OrderPayout.objects.get(order=order)
    order_payout.total_payout_amount -= swadesic_fee
    order_payout.swadesic_fee = swadesic_fee
    order_payout.save()

def calculate_swadesic_fee(order):
    # Implement the logic to calculate the swadesic fee based on the order
    # This could be a flat fee or based on some order attributes
    return FLAT_FEE_AMOUNT

# Call this function for each order that needs to be processed
for order in Order.objects.filter(status='completed'):
    process_swadesic_fee(order)
