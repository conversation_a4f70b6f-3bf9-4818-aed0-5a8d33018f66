import datetime
import json
import logging
import random
import string
import paytmchecksum
import requests
import pytz
import pandas as pd
from decouple import config

from django.db.models import Q, QuerySet
from .order_api.models import Order, SubOrder, RefundedAmount, OrderConfiguration
from .order_api.serializers import Refunddetailsserializer
from .payment_api.models import SubOrderPaymentDetails
from .cart_api.models import CartItem
from .payout_api.models import OrderPayout
from requests.auth import HTTPBasicAuth
from django.db import transaction



from users.user_api.models import User
from stores.store_settings_api.models import (
    DeliverySettings,
    RefundAndWarranty,
    TrustCenter,
)

from users.notification_api.models import Notifications
from common.util.notification_handler import NotificationHandler
from .payment_api.utils import calculate_sha256_string, base64_encode, get_dummy_txn_token
from decimal import Decimal
from typing import List, Dict, Any, Tuple
from django.db.models import Prefetch

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

M_ID = config("TEST_MERCHANT_ID")
M_KEY = config("TEST_MERCHANT_KEY")
SKIP_PAYTM_API = config("SKIP_PAYTM_API", cast=bool)

SKIP_RAZORPAY_API = config("SKIP_RAZORPAY_API", cast=bool)
RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")


############################################### OrderPayout Calculations #####################################################

class OrderPayoutCalculations():
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
            pass

    def update_orderpayout_entry(self, payout_status = None):
        
        refund_amount = 0 if self.refunded_amount_instance is None else self.refunded_amount_instance.refunded_amount
            
        new_tds_calculated = round((self.order_payout_instance.order_amount - refund_amount) * 0.01, 2)

        new_payout_amount = (self.order_payout_instance.payout_amount - refund_amount) + (self.order_payout_instance.tds_calculated - new_tds_calculated)
    
        self.order_payout_instance.tds_calculated = new_tds_calculated
        self.order_payout_instance.payout_amount = round(0 if new_payout_amount < 0 and new_payout_amount > -1 else new_payout_amount ,2)
        self.order_payout_instance.payout_release_date = self.order_status_self.refund_requested_date
        if payout_status:
            self.order_payout_instance.payout_status = payout_status
        self.order_payout_instance.save()

        # return round(self.order_payout_instance.expected_payout_amount - self.refunded_amount_instance.refunded_amount, 2)

####################################################################################################


############################################### Refund Calculations #####################################################

class RefundCalculations():

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def calculate_refund_amount(self):
        # TODO: when a Positive delivery order is present store df should be sent to seller when all other are cancelled

        # self.order_payout_instance #= self.order_status_self.suborder.order_payout_items.first() # OrderPayout.objects.filter(suborder_number=self.order_status_self.suborder).first()
        delivery_group = self._get_delivery_status_group()
        self.refund_cost_on = self._get_refund_cost_on()

        refund_amount = self.order_payout_instance.order_amount
        delivery_deduction = self._calculate_delivery_deduction(delivery_group)
        transaction_fee_deduction_perc = self._calculate_transaction_fee_deduction()
        transaction_fee_and_tds_deduction_perc = transaction_fee_deduction_perc*(1 + 0.01) # tds is 1% of transaction fee
        refund_amount = (refund_amount - delivery_deduction) * (100 - transaction_fee_and_tds_deduction_perc) / 100
        return round(refund_amount, 2)

    def _calculate_delivery_deduction(self, delivery_group):
        if delivery_group == "AFTER_SHIPPING":
            return self.order_payout_instance.product_delivery_fee + self.order_payout_instance.store_delivery_fee
        return 0

    def _calculate_transaction_fee_deduction(self):
        if self.refund_cost_on in ['USER', 'STORE_TO_USER']:
            return self.order_payout_instance.transaction_fee_percentage
        return 0    
        
    def _get_refund_cost_on(self) -> str:

        if self.store_df_record:
            return "USER" if self._get_delivery_status_group() == "BEFORE_SHIPPING" else 'STORE'
        else:
            refund_warranty = RefundAndWarranty.objects.filter(
                refundandwarrantyid=self.order_payout_instance.suborder_number.refund_warranty_id
            ).first()

            if self.order_status_self.new_return_cost_on:
                return self.order_status_self.new_return_cost_on
            if refund_warranty.return_cost_on_seller:
                return "STORE"
            elif refund_warranty.return_cost_on_customer:
                return "STORE" if self.order_status_self.cancelled_by in ["seller", "auto_cancelled"] else "USER" #TODO: need to check this 
            return "USER"

    def _get_delivery_status_group(self):
        before_shipping = [
            "ORDER_INITIATED",
            "PAYMENT_INITIATED",
            "PAYMENT_PENDING",
            "PAYMENT_SUCCESS",
            "PAYMENT_FAILED",
            "WAITING_FOR_CONFIRMATION",
            "ORDER_CONFIRMED",
            "ORDER_CANCELLED",
            "ORDER_CANCELLED_BY_BUYER",
            "ORDER_CANCELLED_BY_SELLER",
            "ORDER_AUTO_CANCELLED"
        ]

        after_shipping = [
            "DELIVERY_IN_PROGRESS",
            "ORDER_DELIVERED",
            "DELIVERY_FAILED",
            "RETURN_REQUESTED",
            "RETURN_CONFIRMED",
            "RETURN_IN_PROGRESS",
            "RETURNED_TO_SELLER",
            "RETURN_FAILED",
            "REFUND_HOLD",
            "REFUNDED"
        ]

        if self.order_status_self.current_status in before_shipping:
            return "BEFORE_SHIPPING"
        elif self.order_status_self.current_status in after_shipping:
            return "AFTER_SHIPPING"
        else:
            return "UNKNOWN"

        # Usage example:
        # Assuming 'self.current_status' contains the current status
        # group = self.get_status_group(self.current_status)



    # def update_refund_amount(self):
    #     logger.info(f"Updating refund amount for order: {self.order_self}")
    #     active_suborders = self._get_active_suborders()
    #     suborder_settings_list = self._get_suborder_settings(active_suborders)

        
    # def _get_active_suborders(self) -> QuerySet:
    #     suborder_status_list = [
    #         SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
    #         SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
    #         SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED,
    #     ]
    #     return SubOrder.objects.filter(Q(order_number=self.order_status_self.suborder.order_number) & ~Q(suborder_status__in=suborder_status_list))
    
    # def _get_suborder_settings(self, active_suborders: QuerySet) -> List[Dict[str, Any]]:
    #     suborder_settings_list = []

    #     delivery_settings_ids = list(active_suborders.values_list('delivery_settings_id', flat=True))
    #     refund_warranty_ids = list(active_suborders.values_list('refund_warranty_id', flat=True))

    #     delivery_settings_dict = {ds.deliverysettingid: ds for ds in
    #                             DeliverySettings.objects.filter(deliverysettingid__in=delivery_settings_ids)}
    #     refund_warranty_dict = {rw.refundandwarrantyid: rw for rw in
    #                             RefundAndWarranty.objects.filter(refundandwarrantyid__in=refund_warranty_ids)}

    #     suborder_settings_list = []

    #     for suborder in active_suborders:
    #         settings = self._extract_suborder_settings(delivery_settings_dict,refund_warranty_dict)
    #         suborder_settings_list.append({suborder.suborder_number: settings})
    #     return suborder_settings_list
            
    # def _extract_suborder_settings(self, delivery_settings_dict:dict,refund_warranty_dict:dict) -> Dict[str, Any]:
    #     delivery_settings = delivery_settings_dict[self.order_status_self.suborder.delivery_settings_id]
    #     refund_warranty = refund_warranty_dict[self.order_status_self.suborder.refund_warranty_id]

    #     return {
    #         "df_settings": "product_settings" if delivery_settings.product_reference else "store_settings",
    #         "df_type": delivery_settings.deliveryfee_valuetype,
    #         "df_value": delivery_settings.deliveryfee_value,
    #         "suborder_price": self.order_status_self.suborder.selling_price,
    #         "suborder_quantity": self.order_status_self.suborder.product_quantity,
    #         "suborder_status": self.order_status_self.suborder.suborder_status,
    #         "refund_settings_version": refund_warranty.refund_warranty_version,
    #         "package_number": self.order_status_self.suborder.package_number,
    #         "order_shipped": bool(self.order_status_self.suborder.package_number),
    #         "store_reference": self.order_status_self.suborder.store_reference,
    #         "return_cost_on": get_return_cost_on(refund_warranty, self.order_status_self.cancelled_by),
    #         "product_reference": self.order_status_self.suborder.product_reference,
    #     }
    
    # def _get_return_cost_on(self, refund_warranty: RefundAndWarranty, cancelled_by: str) -> str:
    #     if refund_warranty.return_cost_on_seller:
    #         return "seller"
    #     elif refund_warranty.return_cost_on_customer:
    #         return "seller" if cancelled_by in ["seller", "auto_cancelled"] else "customer"
    #     return "customer"

# ########################################calculate refund amount start#################################################

def update_refund_amount_old(suborder_number_list: List[str], order_number: str = None, cancelled_by: str = None,
                         return_breakup: bool = False, new_return_cost_on: str = None) -> Any:
    logger.info("Entered refund amount calculation function")

    order_number = suborder_number_list[0].split("-")[0]
    active_suborders = get_active_suborders(order_number)
    suborder_settings_list = get_suborder_settings(active_suborders, cancelled_by)

    sorted_suborder_list = sort_suborders(suborder_number_list)

    refund_details = calculate_refund_details(sorted_suborder_list, suborder_settings_list, order_number, new_return_cost_on)

    if return_breakup:
        return unpack_refund_details(refund_details)
    else:
        return convert_refund_amount(refund_details['refund_grant_total'])


def initiate_refund(suborder_number_list, cancelled_by=None):
    def process_refund(suborder_number, razorpay_payment_id):
        order_number = suborder_number.split("-")[0]
        order = Order.new_objects.get(order_number=order_number)

        refund_instance = RefundedAmount.objects.get(
            suborder_reference=suborder_number,
            refund_status=RefundedAmount.Status.UNPROCESSED,
        )
        refund_amount = refund_instance.refunded_amount  # todo
        refund_id = create_refund_id(suborder_number)

        if SKIP_RAZORPAY_API:
            response = get_mock_response(order, refund_amount, refund_id)
        else:
            response = call_razorpay_api(razorpay_payment_id, refund_amount, refund_id, order)

        return handle_refund_response(response, suborder_number, order_number, refund_instance)

    def get_mock_response(order, refund_amount, refund_id):
        return {
            "body": {
                "txnTimestamp": get_date(),
                "orderId": order.order_request_number,
                "mid": RAZORPAY_ID,
                "refId": "123456789",
                "resultInfo": {
                    "resultStatus": "PENDING",
                    "resultCode": "601",
                    "resultMsg": "Refund request was raised for this transaction. But it is pending state",
                },
                "refundId": refund_id,
                "txnId": "arn_12345678",
                "refundAmount": str(refund_amount),
                "razorpayRefundId": "dummy_rfnd_qwert123",
                "acquirerData": {"utr": "123456789"}
            },
        }

    def get_refund_txn_id_from_acquirer_data(acquirer_data):
        if acquirer_data.get("rrn"):
            return acquirer_data["rrn"]
        elif acquirer_data.get("arn"):
            return acquirer_data["arn"]
        elif acquirer_data.get("utr"):
            return acquirer_data["utr"]
        else:
            return ""

    def call_razorpay_api(razorpay_payment_id, refund_amount, refund_id, order):
        url = f'https://api.razorpay.com/v1/payments/{razorpay_payment_id}/refund'
        payload = {
            'amount': refund_amount * 100,
            'receipt': refund_id,
            'notes': {
                'order_request_number': order.order_request_number,
                'order_number': order.order_number,
                'transaction_id': order.transaction_id,
            }
        }
        # logger.info(f"razorpay_url:{url}")
        # logger.info(f"razorpay_payload:{payload}")
        # logger.info(f"razorpay_auth:{HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY)}")

        response = requests.post(
            url,
            json=payload,
            auth=HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY),
            headers={'Content-Type': 'application/json'}
        )
        logger.info(f"razorpay_response:{response.content}")
        api_response = response.json()

        if api_response.get("error") is not None:
            logger.info(f"Razorpay API error: {api_response['error']['description']}")
            return None

        return {
            "body": {
                "txnTimestamp": api_response["created_at"],
                "orderId": api_response["notes"]["order_request_number"],
                "mid": RAZORPAY_ID,
                "refId": "123456789",
                "resultInfo": {
                    "resultStatus": api_response["status"],
                    "resultCode": "601",
                    "resultMsg": api_response["status"]
                },
                "refundId": api_response["receipt"],
                "txnId": get_refund_txn_id_from_acquirer_data(acquirer_data=api_response["acquirer_data"]),
                "refundAmount": str(api_response["amount"] / 100),
                "razorpayRefundId": api_response["id"],
                "acquirerData": api_response["acquirer_data"]
            },
        }

    def handle_refund_response(response, suborder_number, order_number, refund_instance):
        if response is not None:
            body = response["body"]
            initiate_response_status = body["resultInfo"]["resultStatus"]
            initiate_response_msg = body["resultInfo"]["resultMsg"]
            razorpay_refund_id = body["razorpayRefundId"]
            refund_transaction_id = body["txnId"]
            refund_id = body["refundId"]
            refund_amount = float(body["refundAmount"])
            refunded_date_unix = body["txnTimestamp"]
            refunded_date = datetime.datetime.fromtimestamp(refunded_date_unix)

            order_payment_details = SubOrderPaymentDetails.objects.get(
                suborder_number=SubOrder.objects.get(suborder_number=suborder_number)
            )

            status_mapping = {
                "processed": (RefundedAmount.Status.REFUNDED, SubOrderPaymentDetails.Payment_Status.REFUND_SUCCESS),
                "pending": (RefundedAmount.Status.PENDING, SubOrderPaymentDetails.Payment_Status.REFUND_PENDING),
                "failed": (RefundedAmount.Status.REFUND_FAILED, SubOrderPaymentDetails.Payment_Status.REFUND_FAILED),
            }

            refund_status, payment_status = status_mapping.get(
                initiate_response_status.lower(),
                (RefundedAmount.Status.REFUND_FAILED, SubOrderPaymentDetails.Payment_Status.REFUND_FAILED)
            )

            # Update RefundedAmount instance
            refund_instance.refund_status = refund_status
            refund_instance.refund_id = refund_id
            refund_instance.initiate_refund_status = initiate_response_status
            refund_instance.initiate_refund_message = initiate_response_msg
            refund_instance.refund_transaction_id = refund_transaction_id
            refund_instance.razorpay_refund_id = razorpay_refund_id
            refund_instance.refunded_date = refunded_date
            refund_instance.save(update_fields=[
                "refund_status", "refund_id", "initiate_refund_status",
                "initiate_refund_message", "refund_transaction_id", "razorpay_refund_id", "refunded_date"
            ])

            # Update OrderPaymentDetails instance
            order_payment_details.payment_status = payment_status
            order_payment_details.refund_id = refund_id
            order_payment_details.refund_amount = refund_amount
            order_payment_details.save(update_fields=["payment_status", "refund_id", "refund_amount"])

            if initiate_response_status.lower() == "processed":
                create_refund_notification(order_number, suborder_number)

            return "success" if initiate_response_status.lower() == "processed" else "error"
        else:
            return "error"

    def create_refund_notification(order_number, suborder_number):
        order = Order.objects.get(order_number=order_number)
        suborder = SubOrder.objects.get(suborder_number=suborder_number)

        notification_handler = NotificationHandler(
            notified_user=order.user_reference.user_reference,
            notification_type=Notifications.Notifications_Type.REFUND_INITIATED,
            notification_about=order_number,
            image=suborder.product_image,
        )
        notification_handler.create_notification(notification_handler)

    for suborder_number in suborder_number_list:
        results = []
        order_reference = suborder_number_list[0].split("-")[0]
        razorpay_payment_id = Order.objects.filter(order_number=order_reference).values_list('razorpay_payment_id', flat=True)[0]

        try:
            with transaction.atomic():
                result = process_refund(suborder_number, razorpay_payment_id)
            results.append(result)
        except Exception as e:
            logger.error(f"Error processing refund for suborder {suborder_number}: {str(e)}")
            results.append({"suborder_number": suborder_number, "status": "error", "message": str(e)})

    logger.info({"refund_results": results})


def get_active_suborders(order_number: str) -> QuerySet:
    suborder_status_list = [
        SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
        SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
        SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED,
    ]
    return SubOrder.objects.filter(Q(order_number=order_number) & ~Q(suborder_status__in=suborder_status_list))


def get_suborder_settings(active_suborders: QuerySet, cancelled_by: str) -> List[Dict[str, Any]]:
    suborder_settings_list = []

    delivery_settings_ids = list(active_suborders.values_list('delivery_settings_id', flat=True))
    refund_warranty_ids = list(active_suborders.values_list('refund_warranty_id', flat=True))

    delivery_settings_dict = {ds.deliverysettingid: ds for ds in
                              DeliverySettings.objects.filter(deliverysettingid__in=delivery_settings_ids)}
    refund_warranty_dict = {rw.refundandwarrantyid: rw for rw in
                            RefundAndWarranty.objects.filter(refundandwarrantyid__in=refund_warranty_ids)}

    suborder_settings_list = []

    for suborder in active_suborders:
        settings = extract_suborder_settings(suborder, cancelled_by,delivery_settings_dict,refund_warranty_dict)
        suborder_settings_list.append({suborder.suborder_number: settings})
    return suborder_settings_list


def extract_suborder_settings(suborder: SubOrder, cancelled_by: str,delivery_settings_dict:dict,refund_warranty_dict:dict) -> Dict[str, Any]:
    delivery_settings = delivery_settings_dict[suborder.delivery_settings_id]
    refund_warranty = refund_warranty_dict[suborder.refund_warranty_id]

    return {
        "df_settings": "product_settings" if delivery_settings.product_reference else "store_settings",
        "df_type": delivery_settings.deliveryfee_valuetype,
        "df_value": delivery_settings.deliveryfee_value,
        "suborder_price": suborder.selling_price,
        "suborder_quantity": suborder.product_quantity,
        "suborder_status": suborder.suborder_status,
        "refund_settings_version": refund_warranty.refund_warranty_version,
        "package_number": suborder.package_number,
        "order_shipped": bool(suborder.package_number),
        "store_reference": suborder.store_reference,
        "return_cost_on": get_return_cost_on(refund_warranty, cancelled_by),
        "product_reference": suborder.product_reference,
    }


def get_return_cost_on(refund_warranty: RefundAndWarranty, cancelled_by: str) -> str:
    if refund_warranty.return_cost_on_seller:
        return "seller"
    elif refund_warranty.return_cost_on_customer:
        return "seller" if cancelled_by in ["seller", "auto_cancelled"] else "customer"
    return "customer"


def sort_suborders(suborder_number_list: List[str]) -> List[str]:
    """
    Sorts a list of suborder numbers based on their order status. Suborders with status 'ORDER_CONFIRMED' or 'PAYMENT_SUCCESS' are sorted first, followed by the rest.

    Args:
        suborder_number_list (List[str]): A list of suborder numbers to be sorted.

    Returns:
        List[str]: The sorted list of suborder numbers.
    """

    suborder_status_lists = SubOrder.objects.filter(suborder_number__in=suborder_number_list).values('suborder_number',
                                                                                                     'package_number',
                                                                                                     'suborder_status')
    return sorted(suborder_status_lists,
                  key=lambda x: x['suborder_status'] not in ['ORDER_CONFIRMED', 'PAYMENT_SUCCESS'])


def calculate_refund_details(sorted_suborder_list: List[str], suborder_settings_list: List[Dict[str, Any]],
                             order_number: str, new_return_cost_on: str = None) -> Dict[str, Any]:
    refund_details = initialize_refund_details()
    suborder_with_store_settings = get_suborders_with_store_settings(suborder_settings_list)

    for suborder_number in set(i['suborder_number'] for i in sorted_suborder_list):
        suborder_settings = [v for elem in suborder_settings_list for k, v in elem.items() if k == suborder_number][0]
        refund_info = calculate_single_refund(suborder_number, suborder_settings, suborder_with_store_settings,
                                              order_number, new_return_cost_on)
        update_refund_details(refund_details, refund_info)
        # will not create an entry for the suborder when the refund amounts table entry is not created
        update_refunded_amount_entry(suborder_number, refund_info, new_return_cost_on)

    return refund_details


def initialize_refund_details() -> Dict[str, Any]:
    return {
        'refund_grant_total': Decimal('0'),
        'total_cancelled_suborder_amount': [],
        'total_cancelled_suborder_df': [],
        'total_reverse_df': [],
        'transaction_fees_list': [],
        'suborder_details': []
    }


def get_suborders_with_store_settings(suborder_settings_list: List[Dict[str, Any]]) -> List[str]:
    return [k for elem in suborder_settings_list for k, v in elem.items() if v["df_settings"] == "store_settings"]


def calculate_single_refund(suborder_number: str, suborder_settings: Dict[str, Any],
                            suborder_with_store_settings: List[str], order_number: str, new_return_cost_on: str = None):
    cancelled_suborder_amount = suborder_settings['suborder_price'] * suborder_settings['suborder_quantity']
    cancelled_suborder_df = calculate_suborder_df(suborder_settings)
    product_df, df_addition = calculate_df_refund(suborder_settings, cancelled_suborder_df, suborder_with_store_settings)
    refund_amount = cancelled_suborder_amount + product_df + df_addition
    return_cost_on = new_return_cost_on if new_return_cost_on else suborder_settings['return_cost_on']
    new_refund_amount, transaction_fee = calculate_transaction_fee_for_refund(refund_amount, return_cost_on, order_number )

    refund_info = {
        "suborder_number": suborder_number,
        "suborder_status": suborder_settings['suborder_status'],
        "return_cost_on": return_cost_on,
        "is_shipped": suborder_settings['order_shipped'],
        "df_settings": suborder_settings['df_settings'],
        "df_type": suborder_settings['df_type'],
        "product_price": cancelled_suborder_amount,
        "product_df": product_df,
        "df_reversal": df_addition,
        "refund_amt": new_refund_amount,
        "transaction_fee": transaction_fee,
    }
    return refund_info


def calculate_suborder_df(suborder_settings: Dict[str, Any]) -> Decimal:
    if not suborder_settings['df_value']:
        return Decimal('0')
    return (
        suborder_settings['df_value']
        if suborder_settings['df_type'] == "per order"
        else suborder_settings['df_value'] * suborder_settings['suborder_quantity']
    )


def calculate_df_refund(suborder_settings: Dict[str, Any], cancelled_suborder_df: Decimal,
                        suborder_with_store_settings: List[str]) -> Decimal:
    """ Calculates the delivery fee (DF) addition for a suborder based on various conditions.

      This function determines the DF addition using the following logic:

      If the suborder has store settings with 'per order' DF type and there are other suborders with store settings, no DF is added (returns 0).
      For suborders with product settings or store settings with 'per product' DF type, or if it's the last suborder with store settings and 'per order' DF type: a. If the order is not shipped or the return cost is on the seller, the full cancelled suborder DF is added. b. Otherwise, no DF is added (returns 0).
      In all other cases, no DF is added (returns 0).
      Args: suborder_settings (Dict[str, Any]): A dictionary containing the suborder's settings. cancelled_suborder_df (Decimal): The calculated DF for the cancelled suborder. suborder_with_store_settings (List[str]): A list of suborders with store settings.

      Returns: Decimal: The calculated DF addition for the suborder.

      Note: This function is crucial for determining the correct DF refund amount based on the suborder's settings and the overall order context.
      """

    # check if suborder is with product settings or store settings with 'per product' DF type
    # - store DF or product DF should refund
    condition1 = (suborder_settings['df_settings'] == "product_settings") or (
            suborder_settings['df_settings'] == "store_settings" and suborder_settings['df_type'] == "per product")
    # check if the current suborder is the last suborder with store settings and 'per order' DF type
    # - store DF should refund on conditions
    condition2 = suborder_settings['df_settings'] == "store_settings" and suborder_settings['df_type'] == "per order"

    # checking if the current suborder is the last suborder with store settings  this cancel or return
    # - store DF should refund
    condition3 = condition2 and (
                len(suborder_with_store_settings) == 1)  # Caution: if store settings has 2 types of store setting, then the len() condition will not support

    # Condition where the current order is store setting and there are few active suborders with store settings left
    # - so no DF to refund
    if condition2 and len(suborder_with_store_settings) > 1:
        return Decimal('0'), Decimal('0')

    # Condition where (suborder with Store DF with per product or Product DF exists) or
    # (last suborder with store settings per order) exists - so DF to refund
    # Seller is bearing full refund even if the order is shipped
    if condition1 or condition3:
        if not suborder_settings['order_shipped'] or suborder_settings['return_cost_on'] == "seller":
            if condition3:
                return Decimal('0'), cancelled_suborder_df
            return cancelled_suborder_df, Decimal('0')

    return Decimal('0'), Decimal('0')


def calculate_transaction_fee_for_refund(refund_amount: Decimal, return_cost_on: str, order_number: str) -> Decimal:
    order_instance = Order.objects.filter(order_number=order_number).first()

    transaction_fee_percentage = order_instance.pg_transctionfee_with_tax_perc
    transaction_fee = round(refund_amount * (Decimal(str(transaction_fee_percentage)) / Decimal('100')), 2)
    if return_cost_on == "seller":
        logger.info("adding the liability to seller account")  # Transaction is added from the scheduler job
        return refund_amount, Decimal('0')
    elif return_cost_on == "seller to buyer" or return_cost_on == "buyer":
        return (refund_amount - transaction_fee), transaction_fee
    else:
        return (refund_amount - transaction_fee), transaction_fee


def update_refund_details(refund_details: Dict[str, Any], refund_info: Dict[str, Any]) -> None:
    refund_details['refund_grant_total'] += refund_info['refund_amt']
    refund_details['total_cancelled_suborder_amount'].append(refund_info['product_price'])
    refund_details['total_cancelled_suborder_df'].append(refund_info['product_df'])
    refund_details['total_reverse_df'].append(refund_info['df_reversal'])
    refund_details['transaction_fees_list'].append(refund_info['transaction_fee'])
    refund_details['suborder_details'].append(refund_info)


def update_refunded_amount_entry(suborder_number: str, refund_info: Dict[str, Any], new_return_cost_on: str = None, df_addition : Decimal = None) -> None:
    if RefundedAmount.objects.filter(suborder_reference=suborder_number, refund_status="UNPROCESSED").exists():
        refund_instance = RefundedAmount.objects.get(suborder_reference=suborder_number)
        new_dict_copy = refund_info.copy()
        new_dict_copy["refunded_amount"] = new_dict_copy.pop("refund_amt")
        if new_return_cost_on:
            new_dict_copy['return_cost_on'] = new_return_cost_on
        serializer = Refunddetailsserializer(refund_instance, data=new_dict_copy)
        if serializer.is_valid():
            serializer.save()
        else:
            logger.info("error", serializer.errors)


def unpack_refund_details(refund_details: Dict[str, Any]) -> Tuple[
    Decimal, List[Decimal], List[Decimal], List[Decimal], List[Decimal], List[Dict[str, Any]]]:
    return (
        refund_details['refund_grant_total'],
        refund_details['total_cancelled_suborder_amount'],
        refund_details['total_cancelled_suborder_df'],
        refund_details['total_reverse_df'],
        refund_details['transaction_fees_list'],
        refund_details['suborder_details'],
    )


def convert_refund_amount(refund_amount: Decimal) -> Any:
    return getattr(refund_amount, "tolist", lambda: refund_amount)()
# ########################################calculate refund amount end#################################################


def input_formatter_order_to_fee_calculation_wrapper(
    order, user_pincode, user_city, suborder_number=None
):
    final_input = []
    for sub_order in order:
        store_reference = sub_order.store_reference
        storeid = sub_order.storeid
        productid = sub_order.productid
        product_reference = sub_order.product_reference
        product_price = sub_order.selling_price
        product_quantity = sub_order.product_quantity
        total_product_price = product_price * product_quantity

        if DeliverySettings.objects.filter(
            product_reference=product_reference,
            store_reference=store_reference,
            is_deleted=False,
        ).exists():
            delivery_settings_instance = DeliverySettings.objects.filter(
                product_reference=product_reference,
                store_reference=store_reference,
                is_deleted=False,
            ).last()
            df_settings = "product_settings"
        else:
            delivery_settings_instance = DeliverySettings.objects.filter(
                store_reference=store_reference,
                product_reference__isnull=True,
                is_deleted=False,
            ).last()
            df_settings = "store_settings"

        data_input = {
            "storeid": storeid,
            "store_reference": store_reference,
            "productid": productid,
            "product_reference": product_reference,
            "product_quantity": product_quantity,
            "user_pincode": user_pincode,
            "user_city": user_city,
            "delivery_settings_id": delivery_settings_instance.deliverysettingid,
            "df_settings": df_settings,
            "product_price": product_price,
            "total_product_price": total_product_price,
        }
        final_input.append(data_input)
    return final_input


def create_refund_id(suborder_number):
    random_four_length_character = "".join(random.choices(string.ascii_uppercase, k=4))
    id_from_suborder = suborder_number.replace("O", "RE")
    final_code = (id_from_suborder, random_four_length_character)
    refund_id = "".join(final_code)
    if SubOrderPaymentDetails.objects.filter(refund_id=refund_id).exists():
        refund_id = create_refund_id(suborder_number)
        return refund_id
    return refund_id


def get_date():
    date = datetime.datetime.now()
    local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
    return local_time.strftime("%Y-%m-%d %H:%M:%S")




def get_order_request_number():
    now = datetime.datetime.now()
    code = now.strftime("%y%m%d%H%M%S")
    my_code = ("OR", code)
    return "".join(my_code)


def input_formatter_cart_to_fee_calculation_wrapper(user_id, cart_items_list):
    user_instance = User.objects.get(userid=user_id, deleted=False)
    user_pincode = user_instance.pincode
    user_city = user_instance.user_location

    final_input = (
        []
    )  # For all the items in cart_items_list create list of details which used to calculate order fee
    for items in cart_items_list:
        data_input = {}
        cart_item_instance = CartItem.new_objects.get(cartitemid=items)
        storeid = cart_item_instance.storeid.storeid
        store_reference = cart_item_instance.storeid.store_reference
        productid = cart_item_instance.productid
        product_price = cart_item_instance.product_reference.selling_price
        product_reference = cart_item_instance.product_reference.product_reference

        if DeliverySettings.objects.filter(
            product_reference=product_reference,
            store_reference=store_reference,
            is_deleted=False,
        ).exists():
            delivery_settings_instance = DeliverySettings.objects.filter(
                product_reference=product_reference,
                store_reference=store_reference,
                is_deleted=False,
            ).last()
            df_settings = "product_settings"
        else:
            delivery_settings_instance = DeliverySettings.objects.filter(
                store_reference=store_reference,
                product_reference__isnull=True,
                is_deleted=False,
            ).last()
            df_settings = "store_settings"

        data_input["storeid"] = storeid
        data_input["store_reference"] = store_reference
        data_input["productid"] = productid
        data_input["product_reference"] = product_reference
        data_input["product_quantity"] = cart_item_instance.product_quantity
        data_input["user_pincode"] = user_pincode
        data_input["user_city"] = user_city
        data_input["df_settings"] = df_settings
        data_input['delivery_fee_valuetype'] = delivery_settings_instance.deliveryfee_valuetype 
        data_input["delivery_fee_value"] = delivery_settings_instance.deliveryfee_value
        data_input["delivery_settings_id"] = delivery_settings_instance.deliverysettingid
        data_input["product_price"] = product_price
        data_input["total_product_price"] = (product_price * cart_item_instance.product_quantity)
        final_input.append(data_input)
    # logger.info(final_input)
    return final_input

"""
Calculates the order fee and delivery fee for a given set of cart items.

This function takes a list of cart item details, including the store, product, and quantity information, and calculates the total order amount, delivery fee, and other relevant metrics. It handles the application of any applicable promotions or discounts, and returns the final order details.

Args:
    data (list): A list of dictionaries, where each dictionary represents the details of a cart item, including the store, product, and quantity information.

Returns:
    tuple: A tuple containing the following elements:
        - grant_total (float): The total order amount, including the delivery fee.
        - order_amount (float): The total amount of the products in the order.
        - order_delivery_fee (float): The total delivery fee for the order.
        - store_list (list): A list of dictionaries, where each dictionary represents the details of a store in the order, including the total store amount, delivery fee, and product details.
        - order_delivery_fee_after_promotion (float): The total delivery fee for the order after any applicable promotions or discounts.
        - grant_total_after_promotion (float): The total order amount, including the delivery fee, after any applicable promotions or discounts.
        - single_delivery_fee (float): The delivery fee for a single product, if applicable.
        - order_amount_list (list): A list of lists, where each inner list represents the order amount for a single store.
        - order_df_list (list): A list of lists, where each inner list represents the delivery fee for each product in a store.
"""

def fee_calculation_wrapper(data):
    logger.info("Entered fee_calculation_wrapper function ")
    df = pd.DataFrame(data)
    # logger.info(df)
    store_list = []
    order_amount = 0
    order_delivery_fee = 0
    order_delivery_fee_after_promotion = 0
    user_pincode = df["user_pincode"].unique()
    user_city = df["user_city"].unique()

    order_amount_list = []
    order_df_list = []

    for s_reference in df[
        "store_reference"
    ].unique():  # iterate through unique store_reference on the df.
        first_store_order_df = []
        order_amount_store_list = []
        order_df_store_list = []
        store_dict = {}
        store_rows = df[
            df.store_reference == s_reference
        ]  # filtering all the rows with same store_reference as in the iterating reference.
        store_rows["total_price"] = (
            store_rows["product_price"] * store_rows["product_quantity"]
        )
        # store_rows['product_price'] = store_rows['product_price']
        # store_rows['total_product_price'] = store_rows["product_price"] * store_rows["product_quantity"]
        store_total_product_amount = sum(store_rows["total_price"])

        store_total_delivery_fee = 0
        total_store_promotions = 0
        total_store_amount = 0
        product_list = []
        single_delivery_fee = 0

        # Extract the store delivery setting
        store_level_delivery_settings = DeliverySettings.objects.filter(
            store_reference=s_reference,
            product_reference__isnull=True,
            is_deleted=False,
        ).last()

        # (
        #     promotion_not_applied_products,
        #     promotion_applied_settings,
        # ) = check_promotion_applied_or_not(
        #     df, store_level_delivery_settings, store_rows, user_pincode, user_city
        # )

        for id in store_rows["delivery_settings_id"].unique():  # iterate through unique delivery settings id on the store_rows.
            rows = df[df.delivery_settings_id == id]  # filtering all the rows with same delivery_settings_id as in the iterating ids.
            product_id = rows["productid"].tolist()  # List product ids to create product detail list.
            delivery_settings = DeliverySettings.objects.get(deliverysettingid=id)
            product_quantity = rows["product_quantity"].sum()  # sum of product_quantity under a delivery_settings id
            delivery_fee = delivery_fee_calculation(delivery_settings, product_quantity)

            store_total_delivery_fee += delivery_fee # both product & store delivery fees are added in this step.
            
            if (len(product_id) > 1):  
                # to check if this product has its own delivery fee or not.
                # len(product_id)>1 means that all the product id in product_id list has a single delivery fee,
                # and it will assign here. Otherwise, it has product level delivery fee, so it will skip the next step.

                single_delivery_fee = delivery_fee

            for item in product_id:
                product_reference = df["product_reference"].iloc[
                    df.index[df.productid == item].tolist()[0]
                ]
                product_quantity = df["product_quantity"].iloc[
                    df.index[df.productid == item].tolist()[0]
                ]
                unit_product_price = df["product_price"].iloc[
                    df.index[df.productid == item].tolist()[0]
                ]
                product_unit_price = unit_product_price * product_quantity
                order_amount_store_list.append(product_unit_price)
                product_dict = {
                    "productid": item,
                    "delivery_settings_id": id,
                    "product_reference": product_reference,
                    "product_quantity": product_quantity,
                    "product_price": product_unit_price,
                    "total_product_price": product_unit_price*product_quantity,
                }

                if (
                    len(product_id) == 1
                ):  # if product has own delivery fee it will assign here.
                    # product settings assigned here. but should store delivery fee with quantity also be assigned here?
                    product_dict["product_delivery_fee"] = delivery_fee
                    order_df_store_list.append(delivery_fee)
                else:
                    if not first_store_order_df:
                        order_df_store_list.append(delivery_fee)
                        first_store_order_df.append(item)
                    else:
                        order_df_store_list.append(0)
                    product_dict[
                        "product_delivery_fee"
                    ] = 0  # product doesn't have own delivery fee it will take store level delivery_fee

                product_list.append(product_dict)

        order_amount_list.append(order_amount_store_list)
        order_df_list.append(order_df_store_list)
        total_store_amount = store_total_product_amount + store_total_delivery_fee
        order_amount += store_total_product_amount
        order_delivery_fee += store_total_delivery_fee
        grant_total = order_amount + order_delivery_fee
        delivery_fee_after_promotions = (
            store_total_delivery_fee - total_store_promotions
        )
        order_delivery_fee_after_promotion += delivery_fee_after_promotions
        grant_total_after_promotion = order_amount + order_delivery_fee_after_promotion

        store_dict["storeid"] = store_rows["storeid"].iloc[0]
        store_dict["store_reference"] = s_reference
        store_dict["total_store_amount"] = total_store_amount
        store_dict["store_total"] = store_total_product_amount
        store_dict["total_store_delivery_fee"] = store_total_delivery_fee
        store_dict["single_delivery_fee"] = single_delivery_fee
        store_dict["delivery_fee_after_promotions"] = delivery_fee_after_promotions
        store_dict["product_split"] = product_list
        store_list.append(store_dict)

    # logger.info("list %s", order_amount_list)
    # logger.info("df list %s", order_df_list)
    logger.info("Exited fee_calculation_wrapper function")
    
    return (
        grant_total,
        order_amount,
        order_delivery_fee,
        store_list,
        order_delivery_fee_after_promotion,
        grant_total_after_promotion,
        single_delivery_fee,
        order_amount_list,
        order_df_list,
    )


def fee_calculation_wrapper_v2(data):
    logger.info("Entered fee_calculation_wrapper function ")
    df = pd.DataFrame(data)
    # logger.info(df)
    store_list = []
    order_amount = 0
    order_delivery_fee = 0
    order_delivery_fee_after_promotion = 0
    user_pincode = df["user_pincode"].unique()
    user_city = df["user_city"].unique()

    order_amount_list = []
    order_df_list = []

    for s_reference in df[
        "store_reference"
    ].unique():  # iterate through unique store_reference on the df.
        first_store_order_df = []
        order_amount_store_list = []
        order_df_store_list = []
        store_dict = {}
        store_rows = df[
            df.store_reference == s_reference
        ]  # filtering all the rows with same store_reference as in the iterating reference.
        store_rows["total_price"] = (
            store_rows["product_price"] * store_rows["product_quantity"]
        )
        # store_rows['product_price'] = store_rows['product_price']
        # store_rows['total_product_price'] = store_rows["product_price"] * store_rows["product_quantity"]
        store_total_product_amount = sum(store_rows["total_price"])

        store_total_delivery_fee = 0
        total_store_promotions = 0
        total_store_amount = 0
        product_list = []
        single_delivery_fee = 0

        # Extract the store delivery setting
        store_level_delivery_settings = DeliverySettings.objects.filter(
            store_reference=s_reference,
            product_reference__isnull=True,
            is_deleted=False,
        ).last()

        # (
        #     promotion_not_applied_products,
        #     promotion_applied_settings,
        # ) = check_promotion_applied_or_not(
        #     df, store_level_delivery_settings, store_rows, user_pincode, user_city
        # )

        for id in store_rows["delivery_settings_id"].unique():  # iterate through unique delivery settings id on the store_rows.
            rows = df[df.delivery_settings_id == id]  # filtering all the rows with same delivery_settings_id as in the iterating ids.
            product_id = rows["productid"].tolist()  # List product ids to create product detail list.
            delivery_settings = DeliverySettings.objects.get(deliverysettingid=id)
            product_quantity = rows["product_quantity"].sum()  # sum of product_quantity under a delivery_settings id
            delivery_fee = delivery_fee_calculation(delivery_settings, product_quantity)

            store_total_delivery_fee += delivery_fee # both product & store delivery fees are added in this step.
            
            if (len(product_id) > 1):  
                # to check if this product has its own delivery fee or not.
                # len(product_id)>1 means that all the product id in product_id list has a single delivery fee,
                # and it will assign here. Otherwise, it has product level delivery fee, so it will skip the next step.

                single_delivery_fee = delivery_fee

            for item in product_id:
                product_reference = df["product_reference"].iloc[
                    df.index[df.productid == item].tolist()[0]
                ]
                product_quantity = df["product_quantity"].iloc[
                    df.index[df.productid == item].tolist()[0]
                ]
                unit_product_price = df["product_price"].iloc[
                    df.index[df.productid == item].tolist()[0]
                ]
                product_unit_price = unit_product_price * product_quantity
                order_amount_store_list.append(product_unit_price)
                product_dict = {
                    "productid": item,
                    "delivery_settings_id": id,
                    "product_reference": product_reference,
                    "product_quantity": product_quantity,
                    "product_price": product_unit_price,
                    "total_product_price": product_unit_price*product_quantity,
                }

                if (
                    len(product_id) == 1
                ):  # if product has own delivery fee it will assign here.
                    # product settings assigned here. but should store delivery fee with quantity also be assigned here?
                    product_dict["product_delivery_fee"] = delivery_fee
                    order_df_store_list.append(delivery_fee)
                else:
                    if not first_store_order_df:
                        order_df_store_list.append(delivery_fee)
                        first_store_order_df.append(item)
                    else:
                        order_df_store_list.append(0)
                    product_dict[
                        "product_delivery_fee"
                    ] = 0  # product doesn't have own delivery fee it will take store level delivery_fee

                product_list.append(product_dict)

        order_amount_list.append(order_amount_store_list)
        order_df_list.append(order_df_store_list)
        total_store_amount = store_total_product_amount + store_total_delivery_fee
        order_amount += store_total_product_amount
        order_delivery_fee += store_total_delivery_fee
        grant_total = order_amount + order_delivery_fee
        delivery_fee_after_promotions = (
            store_total_delivery_fee - total_store_promotions
        )
        order_delivery_fee_after_promotion += delivery_fee_after_promotions
        grant_total_after_promotion = order_amount + order_delivery_fee_after_promotion

        store_dict["storeid"] = store_rows["storeid"].iloc[0]
        store_dict["store_reference"] = s_reference
        store_dict["total_store_amount"] = total_store_amount
        store_dict["store_total"] = store_total_product_amount
        store_dict["total_store_delivery_fee"] = store_total_delivery_fee
        store_dict["single_delivery_fee"] = single_delivery_fee
        store_dict["delivery_fee_after_promotions"] = delivery_fee_after_promotions
        store_dict["product_split"] = product_list
        store_list.append(store_dict)

    # logger.info("list %s", order_amount_list)
    # logger.info("df list %s", order_df_list)
    logger.info("Exited fee_calculation_wrapper function")
    
    return (
        grant_total,
        order_amount,
        order_delivery_fee,
        store_list,
        order_delivery_fee_after_promotion,
        grant_total_after_promotion,
        single_delivery_fee,
        order_amount_list,
        order_df_list,
    )



def check_promotion_applied_or_not(
    df, store_level_delivery_settings, store_rows, user_pincode, user_city):
    s_reference = store_level_delivery_settings.store_reference
    store_level_delivery_settings_id = store_level_delivery_settings.deliverysettingid
    trust_center_instance = TrustCenter.objects.get(store_reference=s_reference)

    promotion_applied_products = []
    promotion_not_applied_products = []
    promotion_applied_settings = []
    for id in store_rows[
        "delivery_settings_id"
    ].unique():  # iterate through unique delivery settings id on the store_rows.
        rows = df[
            df.delivery_settings_id == id
        ]  # filtering all the rows with same delivery_settings_id as in the iterating ids.

        store_rule_price = store_level_delivery_settings.no_deliveryfee_maxvalue
        store_rule_quantity = store_level_delivery_settings.no_deliveryfee_products
        store_rule_pincode = trust_center_instance.pincode
        store_rule_city = trust_center_instance.city
        # for product_id in rows:

        if id == store_level_delivery_settings_id:
            # product_id = rows.loc[rows["product_cancel"] == False, "productid"].tolist()
            product_id = rows["productid"].tolist()
            # total_product_price = rows.loc[rows["product_cancel"] == False, "total_product_price"].sum()
            total_product_price = rows["total_product_price"].sum()
            product_quantity = rows["product_quantity"].sum()
            # product_quantity = rows.loc[rows["product_cancel"] == False, "product_quantity"].sum()

            if (
                store_level_delivery_settings.no_deliveryfee_maxvalue_enabled
                and store_rule_price < total_product_price
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "price"})
            elif (
                store_level_delivery_settings.no_deliveryfee_products_enabled
                and store_rule_quantity < product_quantity
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "quantity"})
            elif (
                store_level_delivery_settings.no_deliveryfee_samepincode_enabled
                and store_rule_pincode == user_pincode
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "pincode"})
            elif (
                store_level_delivery_settings.no_deliveryfee_samecity_enablded
                and store_rule_city == user_city
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "city"})
            else:
                # for productid in store_rows["delivery_settings_id"].unique():
                #     if df[df.productid == productid]["product_cancel"] is True:
                #         promotion_not_applied_products.append({"product_id": product_id, "cancel_or_not": True})
                #     else:
                #         promotion_not_applied_products.append({"product_id": product_id, "cancel_or_not": False})
                promotion_not_applied_products = (
                    promotion_not_applied_products + product_id
                )
        else:
            prod_delivery_setting_id = rows["delivery_settings_id"].iloc[0].item()
            product_id = rows["productid"].tolist()
            total_product_price = rows["total_product_price"].iloc[0].item()
            product_quantity = rows["product_quantity"].iloc[0].item()

            prod_delivery_setting = DeliverySettings.objects.get(
                deliverysettingid=prod_delivery_setting_id
            )

            if (
                store_level_delivery_settings.no_deliveryfee_maxvalue_enabled
                and prod_delivery_setting.no_deliveryfee_maxvalue_enabled
                and store_rule_price < total_product_price
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "price"})
            elif (
                store_level_delivery_settings.no_deliveryfee_products_enabled
                and prod_delivery_setting.no_deliveryfee_products_enabled
                and store_rule_quantity < product_quantity
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "quantity"})
            elif (
                prod_delivery_setting.no_deliveryfee_samepincode_enabled
                and store_rule_pincode == user_pincode
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "pincode"})
            elif (
                prod_delivery_setting.no_deliveryfee_samecity_enablded
                and store_rule_city == user_city
            ):
                promotion_applied_products = promotion_applied_products + product_id
                promotion_applied_settings.append({"id": id, "condition": "city"})
            else:
                # for productid in store_rows["delivery_settings_id"].unique():
                #     if df[df.productid == productid]["product_cancel"] is True:
                #         promotion_not_applied_products.append({"product_id": product_id, "cancel_or_not": True})
                #     else:
                #         promotion_not_applied_products.append({"product_id": product_id, "cancel_or_not": False})
                promotion_not_applied_products = (
                    promotion_not_applied_products + product_id
                )
    # logger.info ("promotion_applied_products %s", promotion_applied_products)
    # logger.info ("promotion_not_applied_products %s", promotion_not_applied_products)
    return promotion_not_applied_products, promotion_applied_settings


def delivery_fee_calculation(delivery_settings, product_quantity):
    logger.info("entered delivery_fee_calculation function")
    delivery_fee_standard = delivery_settings.deliveryfeetype_standard
    delivery_fee_distance = delivery_settings.deliveryfeetype_distance
    delivery_fee_value_type = delivery_settings.deliveryfee_valuetype
    delivery_fee_value = delivery_settings.deliveryfee_value
    distance_based_max_delivery_fee = delivery_settings.distance_based_max_deliveryfee
    delivery_fee_free_for_all = delivery_settings.deliveryfeetype_all_free

    if delivery_fee_free_for_all:
        delivery_fee = 0

    elif delivery_fee_standard:
        if delivery_fee_value_type == "per product":
            delivery_fee = delivery_fee_value * product_quantity

        elif delivery_fee_value_type == "per order":
            delivery_fee = delivery_fee_value

    elif delivery_fee_distance:
        total_distance = 10
        if delivery_fee_value_type == "per product":
            delivery_fee = delivery_fee_value * product_quantity * total_distance

        elif delivery_fee_value_type == "per order":
            delivery_fee = delivery_fee_value * total_distance

        if delivery_fee > distance_based_max_delivery_fee:
            delivery_fee = distance_based_max_delivery_fee

    logger.info("calculated delivery fee value is %s", delivery_fee)
    logger.info("Exited delivery_fee_calculation function")
    return delivery_fee





################ Payout helpers ###################33

class PayoutCalculation:
    def __init__(self):
        pass

    def calculate_expected_payout_values(self, suborder_payment_detail, pg_transctionfee_with_tax_perc, store_df_record=False):
        self.suborder_payment_detail = suborder_payment_detail
        self.pg_transctionfee_with_tax_perc = pg_transctionfee_with_tax_perc
        self.store_df_record = store_df_record
        self._calculate_order_amount()

        self._get_refunded_amount()
        self._calculate_tds()
        self._calculate_transaction_fee()
        self.commission_calculated, self.commission_fee, self.commission_fee_discount = 0,0,0 #self._calculate_commission(order_configuration)
        self._calculate_payout_amount()
        # all values are now present in self
        return self

    def _calculate_order_amount(self):
        # find the order_amount of that suborder ie; product_total + df.
        if self.store_df_record:
            self.order_amount = self.suborder_payment_detail.product_delivery_fee
        else:
            self.order_amount = self.suborder_payment_detail.product_amount + self.suborder_payment_detail.product_delivery_fee # product amount = price * quantity

        # # suborder has store order df.
        # else:
        #     suborder_number = self.suborder_number.suborder_number
        #     suffix = "-01"
        #     # for suborders with store order df, df is credited to seller for first suborder in that order
        #     # below condition checks that.
        #     if suborder_number.endswith(suffix):
        #         order_amount = product_total + store_order_delivery_fee
        #     else:
        #         order_amount = product_total

    def _get_refunded_amount(self):
        if self.store_df_record:
            suborders = SubOrder.objects.filter(order_number=self.suborder_payment_detail.order_number)
            if suborders.exists() and all(
                    s.suborder_status == SubOrder.Suborder_Status.ORDER_CANCELLED for s in suborders):
                # TODO: Add all cancel statusses here, check if this is really required here
                refunded_amount = self.order_amount
        else:
            try:
                suborder = SubOrder.objects.get(suborder_number=self.suborder_payment_detail.suborder_number)
                if suborder.suborder_status == SubOrder.Suborder_Status.ORDER_CANCELLED:
                    return RefundedAmount.objects.get(suborder_number=self.suborder_payment_detail.suborder_number).refunded_amount
            except (SubOrder.DoesNotExist, RefundedAmount.DoesNotExist):
                pass
        refunded_amount = 0
        self.refunded_amount = refunded_amount

    def _calculate_tds(self):
        self.tds_calculated = round((self.order_amount - self.refunded_amount) * 0.01, 2)

    def _calculate_transaction_fee(self):
        if self.pg_transctionfee_with_tax_perc is None:
            raise ValueError("Transaction fee percentage is required")
        transaction_fee = round(self.order_amount * (self.pg_transctionfee_with_tax_perc / 100),2)
        # return round(transaction_fee, 2), transaction_fee_percentage
        self.transaction_fee_calculated, self.transaction_fee_percentage = round(transaction_fee, 2), self.pg_transctionfee_with_tax_perc

    def _calculate_commission(self):
        order_configuration = OrderConfiguration.objects.first()
        commission_fee = order_configuration.commission_fee
        commission_fee_discount = order_configuration.commission_fee_discount
        if self.suborder_payment_detail.suborder_number.suborder_number.endswith("-01"):
            return -abs(commission_fee) + commission_fee_discount, commission_fee, commission_fee_discount
        return 0, commission_fee, commission_fee_discount

    def _calculate_payout_amount(self):
        self.expected_payout_amount = round(
            self.order_amount
            - self.refunded_amount
            - self.tds_calculated
            - self.transaction_fee_calculated
            + self.commission_calculated, 2)
