# Generated by Django 4.2.7 on 2024-09-27 10:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0139_orderlifecycle_return_request_cancelled_date_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="orderpayout",
            name="product_amount",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="suborderpaymentdetails",
            name="product_amount",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="suborderpaymentdetails",
            name="product_delivery_fee",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="suborderpaymentdetails",
            name="product_quantity",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="suborderpaymentdetails",
            name="product_unit_price",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="suborderpaymentdetails",
            name="store_delivery_fee",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
    ]
