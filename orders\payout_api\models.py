from django.db import models
from django.utils.translation import gettext_lazy as _
import random
import string
from django.conf import settings
from cryptography.fernet import Fernet
import base64
from decouple import config

class PayoutBalance(models.Model):
    order_payout_balance_id = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        related_name="account_payout_balance",
        db_column="store_reference",
    )
    life_time_balance = models.FloatField(default=0)
    current_balance = models.FloatField(default=0)
    missed_revenue = models.FloatField(default=0)

    class Meta:
        verbose_name_plural = "order payout balance"
        db_table = '"order"."order_payout_balance"'


class OrderPayout(models.Model):
    class Payout_Status(models.TextChoices):
        WAITING_FOR_CONFIRMATION = "WAITING_FOR_CONFIRMATION", _("Waiting for Confirmation")
        IN_PROCESS = "IN_PROCESS", _("In process")
        WAITING_FOR_RELEASE = "WAITING_FOR_RELEASE", _("Waiting for release")
        AMOUNT_RELEASED = "AMOUNT_RELEASED", _("Amount released")
        NOTHING_TO_BE_ADDED = "NOTHING_TO_BE_ADDED", _("Nothing to be added")
        AMOUNT_RELEASED_CORRECTED = "AMOUNT_RELEASED_CORRECTED", _("Amount released & corrected")
        PAYOUT_HOLD = "PAYOUT_HOLD", _("Payout hold")

    class OrderType(models.TextChoices):
        PRODUCT = 'PRODUCT', 'Product'
        COMMISSION = 'COMMISSION', 'Commission'
        STORE_DF = 'STORE_DF', 'Store_DF'

    order_payout_id = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        db_column="store_reference",
        related_name="order_payout_items",
    )
    suborder_number = models.ForeignKey(
        "orders.SubOrder",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="order_payout_items",
        to_field="suborder_number",
        db_column="suborder_number",
    )

    order_number = models.ForeignKey(
        "orders.Order",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="order_number",
        db_column="order_number",
        related_name="order_payout_items",

    )
    user_reference = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_column="user_reference",
        to_field="user_reference",
        related_name="order_payout_items",

    )
    product_amount = models.PositiveIntegerField(null=True, blank=True, default=0)
    product_delivery_fee = models.IntegerField(default=0)
    store_delivery_fee = models.IntegerField(default=0)
    order_amount = models.IntegerField(default=0)
    transaction_fee_calculated = models.FloatField(default=0)
    swadesic_fee = models.IntegerField(default=0)
    flash_points = models.IntegerField(default=0)
    infinity_points = models.IntegerField(default=0)
    payout_amount = models.FloatField(default=0)
    expected_payout_amount = models.FloatField(default=0)
    order_date = models.DateField(null=True, blank=True)
    payout_status = models.CharField(
        max_length=50, choices=Payout_Status.choices, null=True, blank=True
    )
    payout_release_date = models.DateField(null=True, blank=True)
    transaction_fee_percentage = models.FloatField(default=0)
    expected_swadesic_fee = models.IntegerField(default=0)
    promotional_value = models.FloatField(default=0)
    tds_calculated = models.FloatField(default=0)
    
    order_type = models.CharField(
            max_length=20,
            choices=OrderType.choices,
            null=True,
            blank=True
        )
    remarks = models.CharField(max_length=255, null=True, blank=True)
    

    class Meta:
        verbose_name_plural = "order payout"
        db_table = '"order"."order_payout"'

    def save(self, *args, **kwargs):
        super(OrderPayout, self).save(*args, **kwargs)


class PayoutTransactions(models.Model):
    class Transaction_Type(models.TextChoices):
        CREDITED = "CREDITED", _("Credited")
        DEBITED = "DEBITED", _("Debited")
        WITHDRAWAL = "WITHDRAWAL", _("Withdrawal")

    class Transaction_Status(models.TextChoices):
        SUCCESS = "SUCCESS", _("Success")
        FAILURE = "FAILURE", _("Failure")
        PENDING = "PENDING", _("Pending")

    payout_transaction_id = models.AutoField(primary_key=True)
    transaction_reference = models.CharField(unique=True, max_length=20, null=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        db_column="store_reference",
    )
    suborder_number = models.ForeignKey(
        "orders.SubOrder",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="suborder_number",
        db_column="suborder_number",
    )
    order_number = models.ForeignKey(
        "orders.Order",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="order_number",
        db_column="order_number",
    )
    payout_amount = models.FloatField(default=0)
    order_date = models.CharField(max_length=100, null=True, blank=True)
    payout_release_date = models.DateField(null=True, blank=True)
    transaction_status = models.CharField(
        max_length=10, choices=Transaction_Status.choices, null=True, blank=True
    )
    transaction_type = models.CharField(
        max_length=10, choices=Transaction_Type.choices, null=True, blank=True
    )
    bank_account_number = models.CharField(max_length=100, null=True, blank=True)
    bank_account_name = models.CharField(max_length=100, null=True, blank=True)
    bank_ifsc_code = models.CharField(max_length=50, null=True, blank=True)
    bank_name = models.CharField(max_length=50, null=True, blank=True)
    bank_branch = models.CharField(max_length=50, null=True, blank=True)
    bank_reference_number = models.CharField(max_length=100, null=True, blank=True)
    transaction_time = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)

    def create_transaction_reference(self):
        random_digits = "".join(random.choices(string.digits, k=7))
        final_code = ("REF", random_digits)
        transaction_reference = "".join(final_code)
        if PayoutTransactions.objects.filter(transaction_reference=transaction_reference).exists ():
            transaction_reference = self.create_transaction_reference()
            return transaction_reference
        return transaction_reference

    def save(self, *args, **kwargs):
        if self.payout_transaction_id is None:
            self.transaction_reference = self.create_transaction_reference()
        super(PayoutTransactions, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "payout transactions"
        db_table = '"order"."payout_transactions"'

def encrypt_string(string_to_encrypt):
    f = Fernet(config('ENCRYPTION_KEY'))
    return f.encrypt(string_to_encrypt.encode()).decode()

def decrypt_string(encrypted_string):
    f = Fernet(config('ENCRYPTION_KEY'))
    return f.decrypt(encrypted_string.encode()).decode()

class BankDetails(models.Model):
    bank_details_id = models.AutoField(primary_key=True)
    bank_detail_reference = models.CharField(max_length=100, unique=True, null=True, blank=True)
    account_name = models.CharField(max_length=100, null=True, blank=True)
    account_holder_name = models.CharField(max_length=100, null=True, blank=True)
    ifsc_code = models.TextField(blank=True, null=True)  # Increased length to accommodate encrypted data
    account_number = models.TextField(blank=True, null=True)  # Increased length to accommodate encrypted data
    bank_branch = models.CharField(max_length=50, null=True, blank=True)
    bank_name = models.CharField(max_length=50, null=True, blank=True)
    is_primary = models.BooleanField(default=False)
    entity_reference = models.CharField(max_length=50, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_date = models.DateTimeField(auto_now=True, null=True, blank=True)

    def save(self, *args, **kwargs):
        # Encrypt sensitive data before saving
        self.bank_detail_reference = self.generate_unique_key()
        self.account_number = encrypt_string(self.account_number)
        self.ifsc_code = encrypt_string(self.ifsc_code)
        super(BankDetails, self).save(*args, **kwargs)

    def generate_unique_key(self):
        random_digits = "".join(random.choices(string.digits + string.ascii_letters, k=10))
        return f"BD{random_digits}"

    class Meta:
        verbose_name_plural = "bank details"
        db_table = '"order"."bank_details"'

