# Generated by Django 4.2.7 on 2024-07-11 15:35

from django.db import migrations, models
import random
import string


def generate_invite_code(apps, schema_editor):
    User = apps.get_model('users', 'User')
    for user in User.objects.all():
        if user.first_name:
            username_part = (user.first_name[:4]).upper().ljust(4, 'S')
            random_part = ''.join(random.choices(string.digits, k=4))
            referral_code = f"SW{username_part}{random_part}"
            user.user_invite_code = referral_code
            user.save()


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0066_rename_is_verified_user_is_phonenumber_verified_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="user",
            old_name="invite_code",
            new_name="invited_by_code",
        ),
        migrations.AddField(
            model_name="user",
            name="infinity_points",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="user",
            name="user_invite_code",
            field=models.CharField(blank=True, max_length=50, null=True, unique=True),
        ),
        migrations.RunPython(generate_invite_code),
    ]
