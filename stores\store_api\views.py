from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework import generics, mixins
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
from django.db.models import Case, When
from django.db import transaction
from decouple import config
from django.core.cache import cache

from rest_framework_simplejwt.authentication import JWTAuthentication
# from common.util.validators import validate_api_caller


from users.user_api.models import User, UserStore, UserProduct
from products.models import Product
from products.api.serializers import GetProductSerializer, GetProductListSerializer
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .models import Store, Category, Storelink, Cities, BusinessTypes
from stores.store_reward_api.models import StoreRewardsHistory,StoreRewards
from users.invite_api.models import RewardsHistory, UserRewards
from ..store_settings_api.models import DeliverySettings, DeliveryLocations, TrustCenter, TrustCenterChecker
from common.util.notification_handler import Notification<PERSON>andler
from common.util.support_helper import compress, get_unread_conversations_count, delete_user_from_messaging_server
from users.notification_api.models import Notifications
from common.util.support_helper import GenerateNotifications
from general.models import ReservedHandles
from GraphDB.models import Neo4jStore, Neo4jUser, Neo4jEntity
from GraphDB.queries import dbqueries
from neomodel import db
from django.core.files.storage import default_storage
from django.utils import timezone
from .serializers import (
    GetStoreSerializer,
    AddStoreSerializer,
    StoreActivationSettingsSerializer,
    CategorySerializer,
    StoreLinkSerializer,
    GetStoreFollowersSerializer,
    StoreDashboardSerializer,
    CitiesSerializer,
    BusinessTypesSerializer,
    StoreVerificationSerializer
)
import datetime
import logging
from ..store_settings_api.models import StoreByState, StoresByCategory
from ..store_api.models import StoreConfig
from users.user_api.utils import generate_referral_code, update_messaging_server_user_data
from general.views import get_app_config
from .utils import create_store_messaging_user, create_store_ai_messaging_user, create_store_admin_group_chat
from orders.payout_api.views import encrypt_string
from users.user_api.utils import generate_otp, send_otp_via_sms, validate_otp


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def get_config():
    return get_app_config()

class GetRecentlyVisitedStores(APIView):
    @swagger_auto_schema(operation_summary="list of recently visited stores")
    def get(self, request, user_reference, limit=None, offset=None):

        """
        This method is for getting all the recently visited stores for a given user_id.

        first, get all the stores visited by the given user from UserStore table
        and order by modified date to get the recent stores that the user visited.

        second, filter stores where storeid is same as storeid in recently visited
        store list that created above

        """

        limit = int(request.query_params.get("limit"))
        offset = int(request.query_params.get("offset"))

        # recently_visited_stores = (
        #     UserStore.objects.filter(user_supporter=user_reference, is_visited=True)
        #     .values_list("store_reference", flat=True)
        #     .order_by("-modified_date")[:30]
        # )
        # list_of_recent_visit = list(recently_visited_stores)
        list_of_recent_visit = dbqueries.get_recently_visited_store_references(asking_reference=user_reference,
                                                                               limit=limit,
                                                                               offset=offset
                                                                               )

        # Build a conditional expression for preserving the order
        ordering = Case(*[When(store_reference=id_val, then=pos) for pos, id_val in enumerate(list_of_recent_visit)])

        store_list = Store.objects.filter(
            deleted=False, store_reference__in=list_of_recent_visit, is_active=True
        ).order_by(ordering)  # list of query sets
        qs = GetStoreSerializer.setup_eager_loading(store_list)
        serializer = GetStoreSerializer(qs, many=True)

        return Response(
            {"message": "success", "data": serializer.data},
            status=status.HTTP_200_OK,
        )


class GetFollowingStores(APIView):
    @swagger_auto_schema(operation_summary="list of user following stores")
    def get(self, request, user_reference, limit=None, offset=None):
        """
        This method is for getting all the stores followed by a given user_id.

        first, Getting list of storeid from UserStore where user_id corresponding
        to the given userid and is_following as true

        second, filter all the stores with the above list.

        """
        # try:
        # user_instance = User.objects.get(userid=user_id,deleted=False)
        # following_stores = UserStore.objects.filter(
        #     user_supporter=user_instance.user_reference, is_following=True
        # ).values_list("store_reference", flat=True)
        # # filter (is_following=True).count()
        list_of_following_store_references = dbqueries.get_following_store_references(
            asking_reference=user_reference,limit=limit,offset=offset)
        storeList = Store.objects.filter(
            deleted=False, store_reference__in=list(list_of_following_store_references),
        )
        qs = GetStoreSerializer.setup_eager_loading(storeList)
        serializer = GetStoreSerializer(qs, many=True)
        return Response(
            {"message": "success", "data": serializer.data},
            status=status.HTTP_200_OK,
        )


############# store onboarding seller  ##########################

class GetAllStoresOfOneUser(mixins.ListModelMixin, generics.GenericAPIView):
    @swagger_auto_schema(operation_summary="get list of stores created by a user")
    def get(self, request, *args, **kwargs):
        """To Getting all stores created by a user by passing userid"""
        userid = kwargs.get("userid")
        try:
            stores = Store.objects.filter(created_by=userid, deleted=False).order_by(
                "created_date"
            )
        except Store.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        qs = GetStoreSerializer.setup_eager_loading(stores)
        serializer = GetStoreSerializer(qs, many=True)

        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class StoreListAV(APIView):
    @swagger_auto_schema(operation_summary="get list of all stores")
    def get(self, request):
        """get all stores ordered by created date (used in search page to get all the storelist)"""
        stores = Store.objects.filter(deleted=False).order_by("-created_date")
        qs = GetStoreSerializer.setup_eager_loading(stores)  # Set up eager loading to avoid N+1 selects
        serializer = GetStoreSerializer(qs, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Add a store", request_body=AddStoreSerializer
    )
    def post(self, request):

        """create a new store"""
        created_user = User.objects.get(userid=request.data.get('created_by'))
        is_test_store = request.data.get('is_test_store', False)

        # Check the current store count
        user_real_store_count = Store.objects.filter(created_by=created_user, deleted=False, is_test_store=False).count()
        user_test_store_count = Store.objects.filter(created_by=created_user, deleted=False, is_test_store=True).count()

        if is_test_store:
            if user_test_store_count >= get_config().test_store_creation_limit:
                created_user.create_test_store = 0
                created_user.save(update_fields=['create_test_store'])
                return Response(
                    {"message": "Test store limit reached. Only one test store is allowed per user.", "is_custom": True},
                    status=status.HTTP_403_FORBIDDEN
                )
            if created_user.create_test_store == 0:
                return Response(
                    {"message":  "Test store creation is currently unavailable for your account.", "is_custom": True},
                    status=status.HTTP_403_FORBIDDEN
                )
        else:
            if user_real_store_count >= get_config().real_store_creation_limit:
                created_user.create_store = 0
                created_user.save(update_fields=['create_store'])
                return Response(
                    {"message": "Store limit reached. Maximum of 5 stores allowed per user.", "is_custom": True},
                    status=status.HTTP_403_FORBIDDEN
                )
            if created_user.create_store == 0:
                return Response(
                    {"message": "Store creation is currently unavailable for your account.", "is_custom": True},
                    status=status.HTTP_403_FORBIDDEN
                )

        serializer = AddStoreSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            data = serializer.data
            store = Store.objects.get(storeid=data["storeid"])
            store_reference = store.store_reference
            data["store_reference"] = store_reference
            store.invite_code = generate_referral_code(name=store.storehandle, invite_type='STORE')

            # Create messaging server account for store
            username, password, token2, new_messaging_user_id = create_store_messaging_user(store)
            if username:
                store.xmpp_jid = username
                store.xmpp_password = encrypt_string(password)  # Encrypt the password
                store.new_messaging_token = token2
                store.new_messaging_user_id = new_messaging_user_id

            store.save()

            # Create admin group chat for the store with static user
            if store.new_messaging_user_id:
                try:
                    chat_id = create_store_admin_group_chat(store)
                    if chat_id:
                        logger.info(f"Successfully created admin group chat {chat_id} for store {store.store_reference}")
                    else:
                        logger.warning(f"Failed to create admin group chat for store {store.store_reference}")
                except Exception as e:
                    logger.error(f"Error creating admin group chat for store {store.store_reference}: {str(e)}")
            try:
                neo4j_user = Neo4jUser.nodes.get(reference=created_user.user_reference)

                # Start a Neo4j transaction
                with db.transaction:
                    neo4j_store = Neo4jStore(
                        reference=store_reference,
                        name=store.store_name,
                        handle=store.storehandle,
                        category_name=store.category_name,
                        created_date=store.created_date
                        # Add other properties as needed
                    )
                    neo4j_store.save()
                    neo4j_store.created_by.connect(neo4j_user)
                    update_messaging_server_user_data(instance=store, handle=store.storehandle)

                logger.info(f"Neo4jStore node created successfully: {neo4j_store}")
            except Neo4jUser.DoesNotExist:
                logger.warning("Neo4jUser with specified user_reference does not exist.")
            except Exception as e:
                logger.error(f"Failed to create Neo4jStore node: {e}")
            return Response(
                {"message": "success", "data": data}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class StoreDetailsAV(APIView):
    @swagger_auto_schema(operation_summary="Get details of a single store")
    def get(self, request, store_reference):
        """get a store details by store_reference"""
        try:
            store = Store.objects.get(store_reference=store_reference)
        except Store.DoesNotExist:
            return Response(
                {"message": "error"}, status=status.HTTP_400_BAD_REQUEST
            )
        # This eager loading is not using here because, prefetch related it will only work on query set,
        # and here the value in "store" is a django object and not a query set. so using this will give error.
        # even if it's not used api is in optimal way, no unwanted db queries are happening.

        # qs = GetStoreSerializer.setup_eager_loading(store)
        serializer = GetStoreSerializer(store)
        serializer_data = serializer.data
        # TODO need to update it to work for new messaging server
        # unread_conversations_count = get_unread_conversations_count(instance=store)
        serializer_data['unread_conversations_count'] = 0
        return Response(
            {"message": "success", "data": serializer_data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Edit store details", request_body=AddStoreSerializer
    )
    def put(self, request, store_reference):
        """Edit a store by store_reference"""
        try:
            store = Store.objects.get(store_reference=store_reference, deleted=False)
            current_store_handle = store.storehandle
            trustcenter_instance = store.store_details.first()
            old_category_name = store.category_name
            serializer = AddStoreSerializer(store, data=request.data, partial=True)

            if serializer.is_valid():
                with transaction.atomic():
                    # Check if GST or PAN are being updated
                    new_gst = request.data.get('gst_number')
                    new_pan = request.data.get('pan_number')

                    if new_gst or new_pan:
                        self.handle_id_verification(store, new_gst, new_pan)

                    serializer.save()

                    # Existing trust center logic
                    if new_gst or new_pan:
                        is_trust_completed = TrustCenterChecker(trustcenter_instance)
                        if is_trust_completed.check_trust_completed(self):
                            store.trustcenter_detail = True
                        else:
                            store.trustcenter_detail = False
                        store.save(update_fields=["trustcenter_detail"])

                    # Existing Neo4j logic
                    store_handle = request.data.get('storehandle')
                    if store_handle is not None:
                        try:
                            neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
                            neo4j_store.handle = request.data["storehandle"]
                            neo4j_store.name = request.data["store_name"]
                            neo4j_store.category_name = request.data["category_name"]
                            neo4j_store.save()
                        except Neo4jStore.DoesNotExist:
                            logger.info("StoreNode does not exist")
                        if store_handle != current_store_handle:
                            try:
                                update_messaging_server_user_data(instance=store, handle=store_handle, name=
                                request.data["store_name"])
                            except Exception as e:
                                logger.error(f"Error updating messaging server user data: {e}")


                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "error", "data": serializer.errors},
                    status=status.HTTP_404_NOT_FOUND,
                )
        except Store.DoesNotExist:
            return Response(
                {"message": "Store not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def handle_id_verification(self, store, new_gst, new_pan):
        if new_gst:
            if 'GST' not in store.ID_verification_requested:
                store.ID_verification_requested.append('GST')
                if not store.verification_requested_time:
                    store.verification_requested_time = timezone.now()
            if store.gst_number and store.gst_number != new_gst:
                store.is_gst_verified = False
                store.gst_verified_time = None

        if new_pan:
            if 'PAN' not in store.ID_verification_requested:
                store.ID_verification_requested.append('PAN')
                if not store.verification_requested_time:
                    store.verification_requested_time = timezone.now()
            if store.pan_number and store.pan_number != new_pan:
                store.is_pan_verified = False
                store.pan_verified_time = None

        store.save()

    @swagger_auto_schema(operation_summary="Delete a store")
    def delete(self, request, store_reference):
        """Delete store by its id (not used yet)"""
        store = Store.objects.get(store_reference=store_reference, deleted=False)
        store.deleted = True
        store.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class StoreLogo(APIView):
    def post(self, request, store_reference):

        """Add store logo while creating a store or updating a store"""
        store = Store.objects.get(store_reference=store_reference, deleted=False)
        image = request.data.get("icon")
        if image:
            image = compress(image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
            file_path = 'store_icons/' + image.name
            file_path = default_storage.save(file_path, image)
            store.icon = file_path
            store.save()
        try:
            neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
            neo4j_store.icon = default_storage.url(store.icon)
            neo4j_store.save()
            update_messaging_server_user_data(instance=store, icon=default_storage.url(store.icon))
            logger.info("Store Node icon updated")
        except Neo4jStore.DoesNotExist:
            logger.info("Store Node does not exist")

        return Response({"message": "success"}, status=status.HTTP_200_OK)


class StoreCoverImage(APIView):
    def post(self, request, store_reference):

        """Add store logo while creating a store or updating a store"""
        store = Store.objects.get(store_reference=store_reference, deleted=False)
        image = request.data.get("cover_image")
        if image:
            image = compress(image, min_image_length=2000, desired_min_size_in_KB=120, desired_max_size_in_KB=140)
            file_path = 'store_cover_images/' + image.name
            file_path = default_storage.save(file_path, image)
            store.cover_image = file_path
            store.save()
        try:
            neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
            neo4j_store.cover_image = default_storage.url(store.cover_image)
            neo4j_store.save()
            logger.info("Store Node icon updated")
        except Neo4jStore.DoesNotExist:
            logger.info("Store Node does not exist")

        return Response({"message": "success"}, status=status.HTTP_200_OK)


class StoreSignature(APIView):
    def post(self, request, store_reference):
        return self._handle_signature_upload(request, store_reference)

    def patch(self, request, store_reference):
        return self._handle_signature_upload(request, store_reference)

    def _handle_signature_upload(self, request, store_reference):
        try:
            store = Store.objects.get(store_reference=store_reference, deleted=False)
            signature = request.FILES.get("signature")
            if signature:
                # Delete old signature file if it exists
                if store.store_signature:
                    store.is_pan_verified = False

                # Save new signature file
                file_path = f'stores/{store.store_reference}/store_signatures/{signature.name}' #TODO to use this format for every image upload
                file_path = default_storage.save(file_path, signature)
                store.store_signature = file_path
                store.save()

            return Response({"message": "Store signature updated successfully"}, status=status.HTTP_200_OK)
        except Store.DoesNotExist:
            return Response({"error": "Store not found"}, status=status.HTTP_404_NOT_FOUND)


class OpenOrCloseStore(APIView):
    @swagger_auto_schema(operation_summary="Open or close a store")
    def get(self, request, store_reference):
        """Make a store available for orders. If the store closed then this api will make it open for orders
        if it is already open then this api will close the store."""
        if Store.objects.filter(
            store_reference=store_reference, open_for_order=False
        ).exists():
            Store.objects.filter(store_reference=store_reference).update(
                open_for_order=True
            )
            open_for_order_value = True
            data = "store opened"
        else:
            Store.objects.filter(store_reference=store_reference).update(
                open_for_order=False
            )
            open_for_order_value = False
            data = "store closed"
        # update neo4j store
        try:
            neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
            neo4j_store.is_open = open_for_order_value
            neo4j_store.save()
            logger.info(f"Store Node open status updated as {open_for_order_value}")
        except Neo4jStore.DoesNotExist:
            logger.info("Store Node does not exist")
        return Response({"message": "success", "data": data})


class StoreDashboard(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="Get store dashboard")
    def get(self, request, *args, **kwargs):
        store_reference = kwargs["store_reference"]
        store_instance = Store.objects.get(
            store_reference=store_reference, deleted=False
        )
        serializer = StoreDashboardSerializer(store_instance)
        return Response(
            {"messasge": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class CategoryListAV(APIView):
    @swagger_auto_schema(operation_summary="get list of store categories")
    def get(self, request):

        """get list of categories (in store onboard page)"""
        categories = Category.objects.all()
        serializer = CategorySerializer(categories, many=True)
        logger.info("success")
        return Response({"message": "success", "data": serializer.data})

    @swagger_auto_schema(
        operation_summary="Add store category", request_body=CategorySerializer
    )
    def post(self, request):

        """Add new category"""
        serializer = CategorySerializer(data=request.data)
        logger.info("inside post category api")
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_404_NOT_FOUND,
            )


# These class is not used yet.
class CategoryDetailsAV(APIView):
    @swagger_auto_schema(
        auto_schema=None,
        operation_summary="Get category(unused)",
        operation_description="Get category by category_id(not using in project till now)",
    )
    def get(self, request, category_id):
        try:
            category = Category.objects.get(pk=category_id)
        except Category.DoesNotExist:
            return Response(
                {"Error": "category not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = CategorySerializer(category)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        auto_schema=None,
        operation_summary="Edit category(unused)",
        operation_description="Edit category by category_id(not using in project till now)",
        request_body=CategorySerializer,
    )
    def put(self, request, category_id):
        category = Category.objects.get(pk=category_id)
        serializer = CategorySerializer(category, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(
        auto_schema=None,
        operation_summary="Delete category(unused)",
        operation_description="Delete category by category_id(not using in project till now)",
    )
    def delete(self, request, category_id):
        category = Category.objects.get(pk=category_id)
        category.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class StoreLinkList(APIView):
    @swagger_auto_schema(operation_summary="get list of all store links(unused)")
    def get(self, request):
        store_links = Storelink.objects.all().select_related('store_reference')
        serializer = StoreLinkSerializer(store_links, many=True)
        return Response({"message": "success", "data": serializer.data})

    @swagger_auto_schema(
        operation_summary="Add store link",
        operation_description="Add store links to store",
        request_body=StoreLinkSerializer,
    )
    def post(self, request):
        store = Store.objects.get(store_reference=request.data["store_reference"])
        store_id = store.storeid
        request.data["storeid"] = store_id
        serializer = StoreLinkSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class StoreLinkDetail(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = StoreLinkSerializer
    @swagger_auto_schema(
        operation_summary="get store link",
        operation_description="get store link by store_link_id",
    )
    def get(self, request, store_link_id):
        try:
            store_link = Storelink.objects.get(pk=store_link_id)
        except Storelink.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        serializer = StoreLinkSerializer(store_link)
        return Response(
            {"message": "success", "data": serializer.data},
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Edit store link",
        operation_description="Edit store link by store_link_id",
        request_body=StoreLinkSerializer,
    )
    def put(self, request, store_link_id):
        store_link = Storelink.objects.get(pk=store_link_id)
        serializer = StoreLinkSerializer(store_link, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_summary="Delete store link",
        operation_description="Delete store link by store_link_id",
    )
    def delete(self, request, store_link_id):
        store_link = Storelink.objects.get(pk=store_link_id)
        store_link.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GettingReferenceFromHandle(APIView):

    def post(self, request):
        """getting the store_reference from a given store_handle"""
        handle = request.data["handle"]

        if Store.objects.filter(storehandle=handle, deleted=False).exists():
            store_instance = Store.objects.get(storehandle=handle, deleted=False)
            reference = store_instance.store_reference

        elif User.objects.filter(user_name=handle, deleted=False).exists():
            user_instance = User.objects.get(user_name=handle, deleted=False)
            reference = user_instance.user_reference

        else:
            reference = None
        return Response(
            {"message": "success", "reference": reference}, status=status.HTTP_200_OK
        )


class StoreHandleAvailabilityAV(APIView):
    @swagger_auto_schema(
        operation_summary="To check uniqueness of store handle",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["input"],
            properties={"input": openapi.Schema(type=openapi.TYPE_STRING)},
        ),
    )
    def post(self, request):
        """To check if a given store_handle already exists or not"""
        input_data = request.data["input"]
        input_without_space = input_data.replace(
            " ", ""
        )  # from the input data remove the space
        is_store_handle_exists = Store.objects.filter(
            Q(storehandle__iexact=input_data) | Q(storehandle__iexact=input_without_space),
            deleted=False,
        ).exists()
        is_user_name_exists = User.objects.filter(Q(user_name__iexact=input_data) | Q(user_name__iexact=input_without_space),
            deleted=False,).exists()

        is_reserved_handle_exists = ReservedHandles.objects.filter(
            Q(handle_name__iexact=input_data) | Q(handle_name__iexact=input_without_space)
        ).exists()

        if is_store_handle_exists or is_user_name_exists or is_reserved_handle_exists:
            return Response(
                {"message": "success", "available": "true"}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "success", "available": "false"}, status=status.HTTP_200_OK
            )


class GetStoreProductsSellerSide(APIView):
    @swagger_auto_schema(operation_summary="Get all store products")
    def get(self, request, store_reference):

        default_limit = 10
        default_offset = 0

        # Retrieve limit and offset from query parameters
        limit = request.query_params.get('limit', default_limit)
        offset = request.query_params.get('offset', default_offset)
        visitor_reference = request.query_params.get('visitor_reference')

        # Convert limit and offset to integers
        limit = int(limit)
        offset = int(offset)
        """Get all the products in a store orderby created date"""
        try:
            products = Product.objects.filter(
                store_reference=store_reference, hide=False, deleted=False
            ).order_by("-created_date")[offset: offset + limit]
        except Product.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_200_OK)
        pincode=None
        if visitor_reference.startswith('U'):
            user_instance = User.objects.filter(user_reference=visitor_reference).first()
            if user_instance:
                pincode = user_instance.pincode

        qs = GetProductListSerializer.setup_eager_loading(products)
        serializer_copy = GetProductListSerializer(qs, many=True, context={
            "visitor_reference": visitor_reference,
            "user_pincode": pincode
        }).data

        logger.info("GetStoreProductsSellerSide success")
        return Response(
            {"message": "success", "data": serializer_copy}, status=status.HTTP_200_OK
        )


# ################store page buyer side##################

class VisitStoreAV(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="visit store",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["store_reference", "user_visitor", "store_visitor"],
            properties={
                "store_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "user_visitor": openapi.Schema(type=openapi.TYPE_STRING),
                "store_visitor": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        store_reference = request.data.get('store_reference')
        user_visitor = request.data.get('user_visitor')
        store_visitor = request.data.get('store_visitor')

        """
           To check if a particular user visited the store and keep track of all the user
           who visited the store, this info will use to create recently visited stores.

           Whenever a user visit a store, if it's for first time it will create a new entry with is_visited field as True
           in UserStore table, otherwise it will get the particular object from UserStore table.
        """

        instance = Store.objects.get(store_reference=store_reference)
        visited_entity = Neo4jEntity.nodes.get(reference=store_reference)
        if user_visitor:
            store_instance = None
            user_instance = User.objects.get(user_reference=user_visitor)
            visitor_entity = Neo4jEntity.nodes.get(reference=user_visitor)
        elif store_visitor:
            user_instance = None
            store_instance = Store.objects.get(store_reference=store_visitor)
            visitor_entity = Neo4jEntity.nodes.get(reference=store_visitor)

        try:
            user_store_instance = UserStore.objects.get(
                store_reference=instance,
                user_supporter=user_instance,
                store_supporter=store_instance,
                is_visited=True
            )
            user_store_instance.modified_date = datetime.datetime.now()
            user_store_instance.save(update_fields=["modified_date"])
        except UserStore.DoesNotExist:
            UserStore.objects.create(
                store_reference=instance,
                user_supporter=user_instance,
                store_supporter=store_instance,
                is_visited=True
            )

        try:
            if visitor_entity.visited.is_connected(visited_entity):
                # Relationship exists
                relation = visitor_entity.visited.relationship(visited_entity)
                relation.datetimestamp = datetime.datetime.now()
                relation.save()
            else:
                # Relationship does not exist, create a new one
                relation = visitor_entity.visited.connect(visited_entity)
                relation.datetimestamp = datetime.datetime.now()
                relation.save()
        except Exception as e:
            logger.error(f"Error in creating node relationship: {e}")

        return Response({"message": "visited"}, status=status.HTTP_200_OK)


# class VisitStoreAV(APIView):
#     @swagger_auto_schema(
#         operation_summary="visit store",
#         request_body=openapi.Schema(
#             type=openapi.TYPE_OBJECT,
#             required=["userid", "store_reference"],
#             properties={
#                 "userid": openapi.Schema(type=openapi.TYPE_INTEGER),
#                 "store_reference": openapi.Schema(type=openapi.TYPE_STRING),
#             },
#         ),
#     )
#     def post(self, request):

#         user_id = request.data["userid"]
#         store_reference = request.data["store_reference"]
#         try:
#             user_instance = User.objects.get(userid=user_id, deleted=False)
#             store_instance = Store.objects.get(
#                 store_reference=store_reference, deleted=False
#             )
#
#             if UserStore.objects.filter(
#                 userid=user_instance, storeid=store_instance, is_visited=True
#             ).exists():
#                 user_store_instance = UserStore.objects.get(
#                     userid=user_instance, storeid=store_instance, is_visited=True
#                 )
#                 user_store_instance.modified_date = datetime.datetime.now()
#                 user_store_instance.save(update_fields=["modified_date"])
#                 return Response({"message": "visited"}, status=status.HTTP_200_OK)
#             else:
#                 UserStore.objects.create(
#                     userid=user_instance, storeid=store_instance, is_visited=True
#                 )
#                 return Response({"message": "visited"}, status=status.HTTP_200_OK)
#         except:
#             return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

class GetStoreProductBuyerSide(APIView):
    @swagger_auto_schema(operation_summary="Get all products of a store")
    def get(self, request, *args, **kwargs):
        """User can see all the products on a particular store, if the user marked any product as saved then this
        will give an extra field which mentions saved products. All other details are the same as
        get_store_product_seller_side API"""

        store_reference = kwargs.get("store_reference")
        user_id = request.query_params.get("user_id")

        try:
            user_pincode = None

            if user_id:
                try:
                    user_instance = User.objects.get(userid=user_id, deleted=False)
                    user_pincode = user_instance.pincode
                except User.DoesNotExist:
                    # If the user doesn't exist, continue without raising an exception
                    pass

            products = Product.objects.filter(
                store_reference=store_reference, hide=False, deleted=False
            ).order_by("-created_date")

            # if this path converter included in the URL, this will use a serializer which will keep all the fields
            # that are query expensive to null. This can be used for loading the API faster
            if kwargs.get("v1"):
                qs = GetProductListSerializer.setup_eager_loading(products)
                serializer = GetProductListSerializer(qs, many=True)
            # Otherwise, it will keep all the fields as such, so it will be query expensive and take time as data increase.
            else:
                serializer = GetProductSerializer(products, many=True, context={"user_pincode": user_pincode})

            serialized_data = serializer.data

            if user_id and UserProduct.objects.filter(userid=user_id, is_saved=True).exists():
                saved_products = UserProduct.objects.filter(
                    userid=user_id, is_saved=True
                )
                for data in serialized_data:
                    for product in saved_products:
                        if product.productid_id == data["productid"]:
                            data["saved_or_not"] = True
            return Response(
                {"message": "success", "data": serialized_data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response({"message": f"An error occurred: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# class GetStoreProductBuyerSide(APIView):
#     @swagger_auto_schema(operation_summary="Get all products of a store")
#     def get(self, request, *args, **kwargs):
#
#         """User can see all the products on a particular store , if the user marked any product as saved then this
#         will give an extra field which mention saved products. All other details are same as
#         get_store_product_seller_side api"""
#
#         store_reference = kwargs.get("store_reference")
#
#         user_id = kwargs.get("user_id")
#         user_instance = User.objects.get(userid=user_id, deleted=False)
#         user_pincode = user_instance.pincode
#
#         try:
#             products = Product.objects.filter(
#                 store_reference=store_reference, hide=False, deleted=False
#             ).order_by("-created_date")
#
#             # if this path converter included in url, this will use a serializer which will keep all the fields
#             # that is query expensive to null. this can be used for loading the api faster
#             if kwargs.get("v1"):
#                 qs = GetProductListSerializer.setup_eager_loading(products)
#                 serializer = GetProductListSerializer(qs, many=True)
#
#             # Otherwise it will keep all the fields as such, so it will be query expensive and take time as data increase.
#             else:
#                 serializer = GetProductSerializer(products, many=True, context={"user_pincode": user_pincode})
#
#             serialized_data = serializer.data
#             if UserProduct.objects.filter(userid=user_id, is_saved=True).exists():
#                 saved_products = UserProduct.objects.filter(
#                     userid=user_id, is_saved=True
#                 )
#                 for data in serialized_data:
#                     for product in saved_products:
#                         if product.productid_id == data["productid"]:
#                             data["saved_or_not"] = True
#             return Response(
#                 {"message": "success", "data": serialized_data},
#                 status=status.HTTP_200_OK,
#             )
#         except:
#             return Response({"message": "error"}, status=status.HTTP_200_OK)


#
# class CheckStoreFollowedOrNot(APIView):
#     @swagger_auto_schema(operation_summary="user following or not")
#     def get(self, request, *args, **kwargs):
#         """when loading store page for first time it should know whether the page already followed by the user or not.
#         This api used to get the flag."""
#         try:
#             user_reference = User.objects.get(userid=kwargs.get("user_id"), deleted=False)
#             store = Store.objects.get(
#                 store_reference=kwargs.get("store_reference"), deleted=False
#             )
#             store_reference = store.store_reference
#             user_store_instance = UserStore.objects.get(
#                 store_reference=store_reference, user_supporter=user_reference
#             )
#
#             if user_store_instance.is_following:
#                 return Response(
#                     {
#                         "message": "success",
#                         "following": user_store_instance.is_following,
#                     },
#                     status=status.HTTP_200_OK,
#                 )
#             else:
#                 return Response(
#                     {
#                         "message": "success",
#                         "following": user_store_instance.is_following,
#                     },
#                     status=status.HTTP_200_OK,
#                 )
#         except:
#             return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
class CheckStoreFollowedOrNot(APIView):
    @swagger_auto_schema(operation_summary="user/store following store or not")
    def get(self, request, *args, **kwargs):
        """Check if the store is followed by the user or another store."""
        try:
            store_reference = kwargs.get("store_reference")
            supporter_reference = kwargs.get("supporter_reference")

            user_instance = None
            store_instance = None  # Initialize both variables

            if supporter_reference.startswith("U"):
                user_supporter = supporter_reference
                user_instance = User.objects.get(user_reference=user_supporter, deleted=False)
            else:
                store_supporter = supporter_reference
                store_instance = Store.objects.get(store_reference=store_supporter, deleted=False)

            store = Store.objects.get(store_reference=store_reference, deleted=False)
            store_reference = store.store_reference

            user_store_instance = UserStore.objects.get(
                store_reference=store_reference,
                user_supporter=user_instance,
                store_supporter=store_instance
            )

            return Response(
                {
                    "message": "success",
                    "following": user_store_instance.is_following,


                },
                status=status.HTTP_200_OK,
            )
        except (User.DoesNotExist, Store.DoesNotExist, UserStore.DoesNotExist):
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class FollowUnfollowStore(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Follow or unfollow a store",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["store_reference", "user_supporter", "store_supporter"],
            properties={
                "store_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "user_supporter": openapi.Schema(type=openapi.TYPE_STRING),
                "store_supporter": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        store_reference = request.data.get('store_reference')
        user_supporter = request.data.get('user_supporter')
        store_supporter = request.data.get('store_supporter')

        if not store_reference:
            return Response({"messages": "error", "detail": "Missing store_reference"}, status=status.HTTP_400_BAD_REQUEST)

        if not (user_supporter or store_supporter):
            return Response({"messages": "error", "detail": "Missing user_supporter or store_supporter"}, status=status.HTTP_400_BAD_REQUEST)

        image = None

        if user_supporter:
            store_supporter = None
            user_instance = User.objects.get(user_reference=user_supporter)
            if not user_instance:
                return Response({"messages": "error", "detail": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
            if user_instance.icon:
                image = user_instance.icon
            else:
                image=None

        if store_supporter:
            user_supporter = None
            store_instance = Store.objects.get(store_reference=store_supporter)
            if not store_instance:
                return Response({"messages": "error", "detail": "Store not found"}, status=status.HTTP_400_BAD_REQUEST)
            if store_instance.icon:
                image = store_instance.icon
            else:
                image = None

        # Neo4j node follow relationship
        try:
            neo4j_entity = Neo4jStore.nodes.get(reference=store_reference)

            if user_supporter:
                try:
                    neo4j_user = Neo4jUser.nodes.get(reference=user_supporter)
                    if neo4j_user.is_following(neo4j_entity):
                        neo4j_user.unfollow(neo4j_entity)
                    else:
                        neo4j_user.follow(neo4j_entity)
                except Neo4jUser.DoesNotExist:
                    logger.info("The user supporter node does not exist")

            elif store_supporter:
                try:
                    neo4j_store = Neo4jStore.nodes.get(reference=store_supporter)
                    if neo4j_store.is_following(neo4j_entity):
                        neo4j_store.unfollow(neo4j_entity)
                    else:
                        neo4j_store.follow(neo4j_entity)
                except Neo4jStore.DoesNotExist:
                    logger.info("The store supporter node does not exist")

        except Neo4jStore.DoesNotExist:
            logger.info("The store node does not exist")

        # if the supporter visted the store there will already be an entry. Try to find that existing Userfollow entry
        try:
            instance = UserStore.objects.get(store_reference=store_reference,
                                            user_supporter=user_supporter,
                                            store_supporter=store_supporter)

        except UserStore.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        if instance.is_following:
            instance.is_following = False
        else:
            instance.is_following = True
            # Create a notification
            notification_handler = NotificationHandler(
                    notified_user=store_reference,
                    notification_type=Notifications.Notifications_Type.SOMEONE_FOLLOWED_STORE,
                    notification_about=user_supporter if user_supporter else store_supporter,
                    image=image,
                )
            notification_handler.create_notification(notification_handler)

        instance.save(update_fields=["is_following"])
        return Response({"messages": "success", "following": instance.is_following}, status=status.HTTP_200_OK)


# class FollowUnfollowStore(APIView):
#     @swagger_auto_schema(
#         operation_summary="follow or unfollow a store",
#         request_body=openapi.Schema(
#             type=openapi.TYPE_OBJECT,
#             required=["userid", "store_reference"],
#             properties={
#                 "userid": openapi.Schema(type=openapi.TYPE_INTEGER),
#                 "store_reference": openapi.Schema(type=openapi.TYPE_STRING),
#             },
#         ),
#     )
#     def post(self, request):
#         """To follow or unfollow a store. each time when this api hits it will toggle the values."""
#
#         # Get store reference from request data and get the store instance
#         store_reference = request.data["store_reference"]
#         store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
#
#         # Get userid from request data and get the user instance
#         user_id = request.data["userid"]
#         user_instance = User.objects.get(userid=user_id)
#
#         try:
#             # if user visited this store, then an entry will be available in the UserStore table
#             user_store = UserStore.objects.get(userid=user_instance, storeid=store_instance)
#         except UserStore.DoesNotExist:
#             return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
#
#         # if user already following the store, this api will make the user unfollow the store
#         if user_store.is_following:
#             user_store.is_following = False
#         else:
#             # if user not following the store, this api will make the user follow the store
#             user_store.is_following = True
#
#             # every time someone follows a store notification will send to the store
#             # get data required to send this notification
#             user_reference = user_instance.user_reference
#
#             if user_instance.icon:
#                 # Access the icon field
#                 image = user_instance.icon
#             else:
#                 # Handle the case where there is no icon associated with the user
#                 image = None  # Or provide a default image path or URL
#
#             # creating an instance of notification handler to create notification
#             notification_handler = NotificationHandler(
#                 notified_user=store_reference,
#                 notification_type=Notifications.Notifications_Type.SOMEONE_FOLLOWED_STORE,
#                 notification_about=user_reference,
#                 image=image,
#             )
#             notification_handler.create_notification(notification_handler)
#
#         user_store.save(update_fields=["is_following"])
#         return Response(
#             {"message": "success", "following": user_store.is_following},
#             status=status.HTTP_200_OK,
#         )


class GetStoreFollowers(APIView):
    @swagger_auto_schema(operation_summary="all followers of a store(unused)")
    def get(self, request, store_reference):
        """Get all the followers of a particular store."""
        try:
            store = Store.objects.get(store_reference=store_reference, deleted=False)
            store_id = store.storeid
            users = UserStore.objects.filter(storeid=store_id, is_following=True)
        except UserStore.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        serializer = GetStoreFollowersSerializer(users, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


def convert_delivery_locations_to_pincode(delivery_locations):
    queryset = DeliveryLocations.objects.all()
    all_states = list(queryset.values_list("state", flat=True).order_by('state').distinct ())
    all_cities = list(queryset.values_list("city", flat=True).order_by('city').distinct ())
    list_of_locations = delivery_locations.split("|")
    lst = []
    for elem in list_of_locations:
        if elem in all_states:
            pincode = list(queryset.filter(state=elem).values_list("pincode", flat=True))
            lst.extend(pincode)
        elif elem in all_cities:
            pincode = queryset.filter(city=elem).values_list("pincode", flat=True)
            lst.extend(pincode)
        else:
            lst.append(elem)
    return lst


class CheckProductDeliverability(APIView):
    """check if a product is deliverable to a particular pincode or not"""

    def post(self, request):
        store_reference = request.data["store_reference"]
        user_pincode = request.data["user_pincode"]
        product_reference = request.data["product_reference"]
        if product_reference == "0":
            delivery_settings_instance = DeliverySettings.objects.filter(
                store_reference=store_reference, product_reference__isnull=True, is_deleted=False
            ).last()
        else:
            if DeliverySettings.objects.filter(
                    product_reference=product_reference, store_reference=store_reference, is_deleted=False
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter (
                    product_reference=product_reference, store_reference=store_reference, is_deleted=False
                ).last ()
            else:
                delivery_settings_instance = DeliverySettings.objects.filter (
                    store_reference=store_reference, product_reference__isnull=True, is_deleted=False
                ).last()

        delivery_locations = delivery_settings_instance.delivery_locations
        list_of_pincode = convert_delivery_locations_to_pincode(delivery_locations)
        if user_pincode in list_of_pincode:
            return Response(
                {"message": "success", "product_deliverability": True},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "success", "product_deliverability": False},
            status=status.HTTP_200_OK,
        )
#
# class ActivateOrDeactivateStore(generics.UpdateAPIView):
#     queryset = Store.objects.filter(deleted=False)
#     serializer_class = StoreActivationSettingsSerializer
#     lookup_field = 'store_reference'
#     def patch(self, request, *args, **kwargs):
#         """update store activation and deactivation by store_reference."""
#         is_active_value = request.data["is_active"]
#         if is_active_value:
#             new_data = request.data
#         else:
#             request.data["open_for_order"] = False
#             new_data = request.data
#         instance = self.get_object()
#         serializer = self.get_serializer(instance, data=new_data, partial=True)
#         if serializer.is_valid():
#             self.perform_update(serializer)
#
#
#             return Response(
#                 {"message": "success", "data": "updated"},
#                 status=status.HTTP_200_OK,
#             )
#         else:
#             return Response(
#                 {"message": "error", "data": "something went wrong"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )
#
def is_any_other_store_verified(store_instance):
    return Store.objects.filter(created_by=store_instance.created_by, first_verified_date__isnull=False).exclude(store_reference=store_instance.store_reference).exists()

class StoreReferralProcessing:
    # steps:
    # Look for every activation of the store that is created within 10 days of user onboarding
    # If the store owner is referred by another user then infinity points should be given to both the store and the referrer
    # if the any of the store under the same owner is already activated then it will have activated date, then we need not to give infinity points to anyone as the incentive is already given.

    # Rules & context
    # 1. Every time the store is reactivated, the activated_date is being updated as current time overwriting the previous activated_date.
    # 2. if the store is reaactivated, the infinity points should not be given again.
    # 3. Referred user should create a store within first 10 days of their onboarding for this incentive to be given.

    @staticmethod
    def process_referral(store_instance):
        store_owner = store_instance.created_by

        if not (store_owner.invited_by_code or StoreReferralProcessing._has_unactivated_store(store_owner)):
            print("Store owner not referred or already has activated stores")
            return

        if StoreReferralProcessing._is_activation_within_time_limit(store_owner) and not is_any_other_store_verified(store_instance):
            if store_owner.invited_by_code:
                referrer = User.objects.get(invite_code=store_owner.invited_by_code)
                StoreReferralProcessing._award_infinity_points(referrer, store_instance)
        else:
            print("The store activation date has passed 10 days past user referred & onboarded date")

    @staticmethod
    def _has_unactivated_store(store_owner):
        return Store.objects.filter(created_by=store_owner, activated_date__isnull=True).exists()

    @staticmethod
    def _is_activation_within_time_limit(store_owner):
        return (timezone.now() - store_owner.created_date).days <= 10

    @staticmethod
    def _award_infinity_points(referrer, store_instance):

        reward_data = {
            'event_reference': store_instance.store_reference,
            'sent_value': get_config().invite_reward_store_verification,
            'received_value': get_config().invite_reward_store_verification,
            'reward_type': RewardsHistory.RewardTypeChoices.STORE_VERIFICATION,
            'transaction_type': RewardsHistory.TransactionTypeChoices.CREDIT,
            'sender_reference': 'SWADESIC',
            'reward_ratio': '1:1',
            'reward_status': RewardsHistory.RewardStatusChoices.PENDING,
            'invite_code': referrer.invite_code
        }

        RewardsHistory.objects.create(
            receiver_reference=referrer.user_reference,
            **reward_data
        )

        RewardsHistory.objects.create(
            receiver_reference=store_instance.store_reference,
            **reward_data
        )

# create an API to change verification status of PAN & GST of store - one at a time or both
# this API should also support rejecting each with a note
# Provide the rejection note to store during his visit to Trust center


class ActivateOrDeactivateStore(generics.UpdateAPIView):
    queryset = Store.objects.filter(deleted=False)
    serializer_class = StoreActivationSettingsSerializer
    lookup_field = 'store_reference'

    def patch(self, request, *args, **kwargs):
        # Get the store instance
        instance = self.get_object()
        store_reference = kwargs.get('store_reference')

        # Attempt to get the trust center instance associated with the store
        try:
            trustcenter_instance = TrustCenter.objects.get(store_reference=instance)
            state = trustcenter_instance.state
        except TrustCenter.DoesNotExist:
            trustcenter_instance = None

        is_active_value = request.data["is_active"]
        if is_active_value:
            new_data = request.data
            StoreReferralProcessing.process_referral(store_instance=instance)
            if not instance.activated_date:
                instance.activated_date = timezone.now()
                instance.save()
            # code for creating store ai messaging user.
            try:
                if not instance.store_ai_reference:
                    user_id, user_name, token = create_store_ai_messaging_user(instance)
                    if user_id and user_name and token:
                        instance.store_ai_messaging_user_id = user_id
                        instance.store_ai_reference = user_name
                        instance.store_ai_messaging_token = token
                        instance.save()
                        logger.info(f"Store AI user created for store {store_reference}")
            except Exception as e:
                logger.error(f"Error creating store AI messaging user: {e}")

        else:
            request.data["open_for_order"] = False
            new_data = request.data

        serializer = self.get_serializer(instance, data=new_data, partial=True)
        if serializer.is_valid():
            self.perform_update(serializer)
            try:
                neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
                neo4j_store.is_active = is_active_value
                neo4j_store.save()
                logger.info(f"Store Node active status updated as {is_active_value}")
            except Neo4jStore.DoesNotExist:
                logger.info("Store Node does not exist")

            # Check if a trust center instance exists and if it has a pincode
            if trustcenter_instance and trustcenter_instance.pincode:
                # whenever a store is created it will create a notification

                users_with_same_pincode = User.objects.filter(
                    pincode=trustcenter_instance.pincode
                ).values_list("user_reference", flat=True)

                for user in users_with_same_pincode:
                    if is_active_value:
                        notification_handler = NotificationHandler(
                            notified_user=user,
                            notification_type=Notifications.Notifications_Type.STORE_CREATED,
                            notification_about=instance.store_reference,
                            store_pincode=trustcenter_instance.pincode,
                            image=instance.icon,
                        )
                        notification_handler.create_notification(notification_handler)

            return Response(
                {"message": "success", "data": "updated"},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": "something went wrong"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class DeleteStore(generics.UpdateAPIView):
    queryset = Store.objects.filter(deleted=False)
    serializer_class = AddStoreSerializer
    lookup_field = 'store_reference'

    def patch(self, request, *args, **kwargs):
        """update store activation and deactivation by store_reference."""
        instance = self.get_object()
        store_reference = kwargs.get('store_reference')

        # Soft delete the products associated with the store
        with transaction.atomic():
            # Set 'deleted' flag for the store
            serializer = self.get_serializer(instance, data={"deleted": True}, partial=True)
            if serializer.is_valid():
                self.perform_update(serializer)
                try:
                    neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
                    neo4j_store.is_deleted = True
                    neo4j_store.save()
                    logger.info(f"Store Node delete successful")
                except Neo4jStore.DoesNotExist:
                    logger.info("Store Node does not exist")

                try:
                    delete_user_from_messaging_server(instance=instance)
                    logger.info("Messaging Server User deleted")
                except Exception as e:
                    logger.error(f"Error while deleting user from messaging server: {e}")
            else:
                return Response(
                    {"message": "error"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Soft delete the products associated with the store
            products_to_soft_delete = Product.objects.filter(storeid=instance.storeid)
            products_to_soft_delete.update(deleted=True)

        return Response(
            {"message": "success"},
            status=status.HTTP_200_OK,
        )


class SendStoreCreationOtp(APIView):

    def check_phonenumber_already_registered(self, phone_number, user_reference):
        if User.objects.filter(phonenumber=phone_number, is_phonenumber_verified=True).exclude(user_reference=user_reference).exists():
            return True
        return False

    def post(self, request):
        user_reference = request.data.get('user_reference')
        phone_number = request.data.get('phone_number')
        phone = phone_number[3:]

        if config("SEND_OTP").lower() == 'false':
            if self.check_phonenumber_already_registered(phone_number, user_reference):
                return Response({"message": "Phone number already registered", "is_custom": True}, status=400)
            return Response({"message": "OTP sent successfully"}, status=200)

        try:
            user = User.objects.get(user_reference=user_reference, deleted=False)

            if self.check_phonenumber_already_registered(phone_number, user_reference):
                return Response({"message": "Phone number already registered", "is_custom": True}, status=400)

            if not user.phonenumber:
                otp = generate_otp(identifier=phone)
                send_otp_via_sms(phone_number=phone_number, otp=otp)
                return Response({"message": "OTP sent successfully"}, status=200)

            else:
                if user.is_phonenumber_verified:
                    return Response({"message": "Phone number already verified", "is_custom": True}, status=200)
                else:
                    otp = generate_otp(identifier=phone)
                    send_otp_via_sms(phone_number=phone_number, otp=otp)
                    return Response({"message": "OTP sent successfully"}, status=200)
                
        except User.DoesNotExist:
            return Response({"message": "User not found", "is_custom": True}, status=404)
        except Exception as e:
            # Ideally, log the exception
            return Response({"error": str(e)}, status=400)


class VerifyStoreCreationOtp(APIView):

    def check_phonenumber_already_registered(self, phone_number, user_reference):
        if User.objects.filter(phonenumber=phone_number, is_phonenumber_verified=True).exclude(user_reference=user_reference).exists():
            return True
        return False

    def post(self, request):
        user_reference = request.data.get('user_reference')
        phone_number = request.data.get('phone_number')
        otp = request.data.get('otp')
        phone = phone_number[3:]

        if config("SEND_OTP").lower() == 'false' and otp == '222222':
            if self.check_phonenumber_already_registered(phone_number, user_reference):
                return Response({"message": "Phone number already registered", "is_custom": True}, status=400)

            user = User.objects.get(user_reference=user_reference, deleted=False)
            user.is_phonenumber_verified = True
            user.phonenumber = phone_number
            user.save()
            return Response({"message": "OTP verified successfully"}, status=200)

        try:
            user = User.objects.get(user_reference=user_reference, deleted=False)

            if self.check_phonenumber_already_registered(phone_number, user_reference):
                return Response({"message": "Phone number already registered", "is_custom": True}, status=400)

            if not user.phonenumber:
                otp_validation = validate_otp(identifier=phone, otp=otp)
                if otp_validation:
                    user.is_phonenumber_verified = True
                    user.phonenumber = phone_number  # Redundant if already same, but kept if update is intended
                    user.save()
                    return Response({"message": "OTP verified successfully"}, status=200)
                else:
                    return Response({"message": "OTP validation unsuccessful"}, status=400)
            else:
                if user.is_phonenumber_verified:
                    return Response({"message": "Phone number already verified", "is_custom": True}, status=200)
                else:
                    otp_validation = validate_otp(identifier=phone, otp=otp)
                    if otp_validation:
                        user.is_phonenumber_verified = True
                        user.phonenumber = phone_number  # Redundant if already same, but kept if update is intended
                        user.save()
                        return Response({"message": "OTP verified successfully"}, status=200)
                    else:
                        return Response({"message": "OTP validation unsuccessful"}, status=400)

        except User.DoesNotExist:
            return Response({"message": "User not found", "is_custom": True}, status=404)
        except Exception as e:
            # Ideally, log the exception
            return Response({"error": str(e)}, status=400)


class GetStoreMilestones(APIView):
    def get(self, request, store_reference):
        try:
            # store = Store.objects.get(store_reference=store_reference, deleted=False)
            # store_id = store.storeid
            # store_sales_count = Order.new_objects.filter(storeid=store_id, order_status='DELIVERED').aggregate(Sum('total_amount'))['total_amount__sum'] or 0
            # store_sales_count = int(store_sales_count)

            return Response(
                {
                    "message": "success", 
                    "data": []
                    }, status=status.HTTP_200_OK)
        except Store.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


# class DeleteStore(generics.UpdateAPIView):
#     queryset = Store.objects.filter(deleted=False)
#     serializer_class = AddStoreSerializer
#     lookup_field = 'store_reference'
#     def patch(self, request, *args, **kwargs):
#         """update store activation and deactivation by store_reference."""
#         instance = self.get_object()
#         serializer = self.get_serializer(instance, data={"deleted":True}, partial=True)
#         if serializer.is_valid():
#             self.perform_update(serializer)
#             return Response(
#                 {"message": "success"},
#                 status=status.HTTP_200_OK,
#             )
#         else:
#             return Response(
#                 {"message": "error"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )


class CitiesAV(generics.ListCreateAPIView):
    serializer_class = CitiesSerializer

    def get(self, request):
        """get list of categories (in store onboard page)"""
        cities = Cities.objects.all()
        serializer = self.get_serializer(cities, many=True)
        return Response({"message": "success", "data": serializer.data})

    # here create will be a bulk create. instead of an object,
    # input will be a list of objects.
    def get_serializer(self, *args, **kwargs):
        if isinstance(kwargs.get("data", {}), list):
            kwargs["many"] = True
        return super(CitiesAV, self).get_serializer(*args, **kwargs)


class CitiesDetailsAV(generics.RetrieveUpdateDestroyAPIView):
    queryset = Cities.objects.all()
    serializer_class = CitiesSerializer


class BusinessTypesAV(generics.ListCreateAPIView):
    serializer_class = BusinessTypesSerializer

    def get(self, request):
        """get list of categories (in store onboard page)"""
        cities = BusinessTypes.objects.all ()
        serializer = self.get_serializer (cities, many=True)
        return Response({"message": "success", "data": serializer.data})

    # here create will be a bulk create. instead of an object,
    # input will be a list of objects.
    def get_serializer(self, *args, **kwargs):
        if isinstance(kwargs.get("data", {}), list):
            kwargs["many"] = True
        return super(BusinessTypesAV, self).get_serializer (*args, **kwargs)


class BusinessTypesDetailsAV(generics.RetrieveUpdateDestroyAPIView):
    queryset = BusinessTypes.objects.all()
    serializer_class = BusinessTypesSerializer


class BulkCreateCategory(generics.CreateAPIView):
    serializer_class = CategorySerializer

    # here create will be a bulk create. instead of an object,
    # input will be a list of objects.
    def get_serializer(self, *args, **kwargs):
        if isinstance(kwargs.get("data", {}), list):
            kwargs["many"] = True
        return super(BulkCreateCategory, self).get_serializer(*args, **kwargs)



