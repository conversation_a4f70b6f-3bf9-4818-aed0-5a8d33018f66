{% extends 'seo/base.html' %}

{% block schema %}
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "Article",
    "headline": "{{ story.title }}",
    "author": {
        "@type": "Organization",
        "name": "{{ store.store_name }}"
    },
    {% if story.sections.0.type == 'image' and story.sections.0.content %}
    "image": "{{ story.sections.0.content }}",
    {% endif %}
    "datePublished": "{{ story.created_at|date:'c' }}",
    "publisher": {
        "@type": "Organization",
        "name": "{{ store.store_name }}",
        "logo": {
            "@type": "ImageObject",
            "url": "{{ store.icon.url }}"
        }
    }
}
</script>
{% endblock %}

{% block content %}
<div class="story-detail">
    <div class="story-header">
        <h1>{{ story.title }}</h1>
        <p class="store-info">
            {% if store.icon %}
            <img src="{{ store.icon.url }}" alt="{{ store.store_name }} logo" class="store-icon">
            {% endif %}
            <span>By {{ store.store_name }}</span>
            {% if store.storehandle %}
            <span class="store-handle">@{{ store.storehandle }}</span>
            {% endif %}
        </p>
        <p class="timestamp">{{ story.created_at|timesince }} ago</p>
    </div>

    <div class="story-content">
        {% for section in story.sections %}
            {% if section.type == 'text' %}
            <div class="story-text">
                <p>{{ section.content }}</p>
            </div>
            {% elif section.type == 'image' %}
            <div class="story-image">
                <img src="{{ section.content }}" alt="Story image">
                {% if section.caption %}
                <p class="caption">{{ section.caption }}</p>
                {% endif %}
            </div>
            {% endif %}
        {% endfor %}
    </div>

    {% if store_location %}
    <div class="store-location">
        <h2>Store Location</h2>
        {% if store_location.isphysicalstore %}
        <p class="physical-store">Physical Store Available</p>
        {% endif %}
        <address>
            {{ store_location.address }}<br>
            {{ store_location.city }}, {{ store_location.state }}<br>
            PIN: {{ store_location.pincode }}
        </address>
    </div>
    {% endif %}

    {% if store.storelinks.exists %}
    <div class="store-links">
        <h2>Store Links</h2>
        {% for link in store.storelinks.all %}
        <a href="{{ link.link }}" class="store-link" target="_blank">{{ link.link_name }}</a>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endblock %}
