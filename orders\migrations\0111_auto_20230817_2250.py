# Generated by Django 3.2.13 on 2023-08-17 17:20

from django.db import migrations
import datetime

def fill_refund_requested_date(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # this will fill refund requested date field in the RefundedAmount table.

    RefundedAmount = apps.get_model("orders", "RefundedAmount")

    for item in RefundedAmount.objects.all():
        if item.refund_request_initiated_date and item.refund_status == 'UNPROCESSED':
            if item.suborder_reference.suborder_status == 'ORDER_CANCELLED_BY_SELLER' or item.suborder_reference.suborder_status == 'ORDER_AUTO_CANCELLED':
                item.refund_requested_date = item.refund_request_initiated_date
            elif item.suborder_reference.suborder_status == 'ORDER_CANCELLED_BY_BUYER' or item.suborder_reference.suborder_status == 'RETURNED_TO_SELLER':
                item.refund_requested_date = item.refund_request_initiated_date + datetime.timedelta(hours=24)
            else:
                item.refund_requested_date = None
            item.save(update_fields=["refund_requested_date"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    RefundedAmount = apps.get_model("orders", "RefundedAmount")
    for item in RefundedAmount.objects.all():
        item.refund_requested_date = None
        item.save(update_fields=["refund_requested_date"])

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0110_auto_20230814_1630'),
    ]

    operations = [migrations.RunPython(fill_refund_requested_date, reverse_func)
    ]
