# Generated by Django 4.2.7 on 2024-06-01 09:12

from django.db import migrations, models
import django.db.models.deletion


def populate_store_config(apps, schema_editor):
    Store = apps.get_model('stores', 'Store')
    StoreConfig = apps.get_model('stores', 'StoreConfig')

    stores = Store.objects.all()
    store_configs = [StoreConfig(store_reference=store, enable_orders=False) for store in stores]
    StoreConfig.objects.bulk_create(store_configs)


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0055_alter_store_store_name_alter_store_storehandle"),
    ]

    operations = [
        migrations.CreateModel(
            name="StoreConfig",
            fields=[
                (
                    "store_config_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("enable_orders", models.BooleanField(default=False)),
                (
                    "store_reference",
                    models.ForeignKey(
                        db_column="store_reference",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="store_configurations",
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "configurations",
                "db_table": '"store"."store_config"',
            },
        ),
        migrations.RunPython(populate_store_config),
    ]
