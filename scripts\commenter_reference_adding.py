import django
import os
import sys
from django.utils.text import slugify
import random
import string

# Add the parent directory (one level above `swadesic`) to sys.path
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(BASE_DIR)

# Set the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "swadesic.settings")

# Initialize Django
django.setup()

from content.models import Comments
from GraphDB.models import Neo4jComment
from django.db.models import Q
from neomodel.exceptions import DoesNotExist

# Step 1: Get all comment references that need update

# Get comment references where commenter_reference is missing
comment_ids_missing_commenter = Comments.objects.filter(
    Q(commenter_reference__isnull=True) | Q(commenter_reference='')
)

# Get comment references where comment_type is missing
comment_ids_missing_type = Comments.objects.filter(
    Q(comment_type__isnull=True) | Q(comment_type='')
)

comment_refs_to_update = set(
    comment_ids_missing_commenter.values_list('comment_reference', flat=True)
).union(
    comment_ids_missing_type.values_list('comment_reference', flat=True)
)

print(f"Total comments to update: {len(comment_refs_to_update)}")

# Step 2: Iterate through references and update
for comment_ref in comment_refs_to_update:
    try:
        # Fetch Neo4jComment
        neo4j_comment = Neo4jComment.nodes.get(reference=comment_ref)

        # Fetch corresponding RDB comment
        rdb_comment = Comments.objects.get(comment_reference=comment_ref)

        # Fill commenter_reference if missing
        if not rdb_comment.commenter_reference:
            creator = neo4j_comment.get_comment_creator()
            if creator:
                rdb_comment.commenter_reference = creator.reference
            else:
                print(f"No commenter found for {comment_ref}")

        # Fill comment_type if missing
        if not rdb_comment.comment_type and neo4j_comment.comment_type:
            rdb_comment.comment_type = neo4j_comment.comment_type

        rdb_comment.save()
        print(f"Updated comment: {comment_ref}")

    except DoesNotExist:
        print(f"Neo4jComment not found for reference: {comment_ref}")
    except Comments.DoesNotExist:
        print(f"RDB comment not found for reference: {comment_ref}")
    except Exception as e:
        print(f"Error processing {comment_ref}: {str(e)}")
