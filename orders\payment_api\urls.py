from django.urls import path
# from .phonepe_api_views import (PhonepeUpiCollectInitiate, PhonepeUpiIntentInitiate, ValidateVPA,
#                                 PhonepeCheckStatus, PhonepeRefundInitiate, PhonepeUpiPaymentInitiate)
from .payment_api_views import *
from .views import (
    PaymentCreation,
    PaymentStatusCheck,
    PaymentStatusVarify,
    ProcessTransaction,
    send_checkout_otp,
    validate_checkout_otp,
    net_banking_payment_channels,
    fetch_card_details,
    validate_vpa,
    refund_initiate_by_seller,
    CheckRefundStatus,
)

urlpatterns = [
    # PAYMENT
    path("payment_creation/", PaymentCreation.as_view(), name="payment-creation"),

    path("payment_status_check/", PaymentStatusCheck, name="payment-status-check"),
    path(
        "payment_status_varify/", PaymentStatusVarify.as_view(), name="payment-status-varify",
    ),
    path(
        "process_transaction/", ProcessTransaction.as_view(), name="process-transaction"
    ),
    path("send_checkout_otp/", send_checkout_otp, name="send-otp"),
    path("validate_checkout_otp/", validate_checkout_otp, name="validate-otp"),
    path(
        "get_netbanking_payment_channels/",
        net_banking_payment_channels,
        name="get-netbanking_payment_channels",
    ),
    path("fetch_card_details/", fetch_card_details, name="fetch-card-details"),
    path("validate_vpa/", validate_vpa, name="validate-vpa"),
    path(
        "refund_initiate_by_seller/",
        refund_initiate_by_seller,
        name="refund-initiate-by-seller",
    ),
    path(
        "check_refund_status/", CheckRefundStatus.as_view(), name="check-refund-status"
    ),
    # # Phonepe Apis
    # path("phonepe_intent_initiate/", PhonepeUpiIntentInitiate.as_view(), name="phonepe-intent-initiate"),
    # path("phonepe_collect_initiate/", PhonepeUpiCollectInitiate.as_view(), name="phonepe-collect-initiate"),
    # path("phonepe_validate_vpa/", ValidateVPA.as_view(), name="phonepe-validate-vpa"),
    # path("phonepe_payment_status/", PhonepeCheckStatus.as_view(), name="phonepe-payment-status"),
    # path("phonepe_refund_initiate/", PhonepeRefundInitiate.as_view(), name="phone-refund-initiate"),
    # path("phonepe_payment_initiate/", PhonepeUpiPaymentInitiate.as_view(), name="phone-payment-initiate"),

    # new payment apis
    path("initiate_razorpay_payment/", PaymentInitiate.as_view(), name="initiate-payment"),
    path("check_razorpay_payment/", CheckPaymentStatus.as_view(), name="check-payment"),
    path("initiate_razorpay_refund/", RefundInitiate.as_view(), name="initiate-razorpay-refund"),
]
