# Generated by Django 3.2.13 on 2023-08-10 11:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0106_auto_20230804_1537'),
    ]

    operations = [
        migrations.AddField(
            model_name='refundedamount',
            name='paytm_initiate_refund_message',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='paytm_initiate_refund_status',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='refund_id',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='refundedamount',
            name='refund_status',
            field=models.CharField(blank=True, choices=[('UNPROCESSED', 'Unprocessed'), ('CANCELLED', 'Cancelled'), ('PROCESSED', 'Processed'), ('PENDING', 'Pending'), ('HOLD', 'Hold'), ('REFUND_INITIATE_FAILED', 'Initiate failed'), ('REFUND_FAILED', 'Refund failed')], max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='suborder',
            name='suborder_status',
            field=models.CharField(choices=[('ORDER_INITIATED', 'Order initiated'), ('PAYMENT_INITIATED', 'Payment initiated'), ('PAYMENT_SUCCESS', 'Payment success'), ('WAITING_FOR_CONFIRMATION', 'waiting for confirmation'), ('PAYMENT_PENDING', 'Payment pending'), ('PAYMENT_FAILED', 'Payment failed'), ('ORDER_CONFIRMED', 'Order confirmed'), ('DELIVERY_IN_PROGRESS', 'Delivery in progress'), ('ORDER_DELIVERED', 'Order delivered'), ('ORDER_CANCELLED', 'Order cancelled'), ('ORDER_CANCELLED_BY_BUYER', 'Order cancelled by buyer'), ('ORDER_CANCELLED_BY_SELLER', 'Order cancelled by seller'), ('DELIVERY_FAILED', 'Delivery failed'), ('RETURN_REQUESTED', 'Return requested'), ('RETURN_CONFIRMED', 'Return confirmed'), ('RETURN_IN_PROGRESS', 'Return in progress'), ('RETURNED_TO_SELLER', 'Return to seller'), ('RETURN_FAILED', 'Return failed'), ('REFUND_HOLD', 'Refund hold'), ('REFUNDED', 'Refunded'), ('ORDER_AUTO_CANCELLED', 'Order auto cancelled')], default='ORDER_INITIATED', max_length=50),
        ),
    ]
