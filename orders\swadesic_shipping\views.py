from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal

import pytz
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from django.db import transaction
from django.shortcuts import get_object_or_404
from users.user_api.models import User<PERSON>ddress, User
from orders.order_api.views import create_package_number
from orders.order_api.models import Order, SubOrder, OrderLifeCycle, ShippingHistory
from rest_framework.permissions import IsAuthenticated
from .models import (
    StoreShippingAccountBalance,
    SwadesicShippingPackageDetails,
    StoreShippingBalanceHistory
)
from .shiprocket_api.shiprocket_api import ShiprocketAPI
from stores.store_api.models import Store
from stores.store_settings_api.models import Address, TrustCenter
from decouple import config
from .utils import get_courier_details, convert_date_format, map_shiprocket_status_to_swadesic
import logging
from shared_utils.order_utils import UpdateOrderStatus_single
from django.template import Template, Context
from requests.auth import HTTP<PERSON>asic<PERSON>uth
import requests
import time
from rest_framework import generics
from rest_framework.exceptions import ValidationError
from ..payment_api.utils import get_dummy_txn_token, get_payment_details, verify_signature, get_payment_channel, get_bank_txn_id_from_acquirer_data, get_refund_txn_id_from_acquirer_data
from ..payout_api.models import PayoutBalance, PayoutTransactions


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

SKIP_RAZORPAY_API = config("SKIP_RAZORPAY_API", cast=bool)
RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")
TEST_RAZORPAY_ID = config("TEST_RAZORPAY_ID")
TEST_RAZORPAY_KEY = config("TEST_RAZORPAY_KEY")

dummy_webhook_data = {
    "awb": "**************",
    "courier_name": "Delhivery Surface",
    "current_status": "IN TRANSIT",
    "current_status_id": 20,
    "shipment_status": "IN TRANSIT",
    "shipment_status_id": 18,
    "current_timestamp": "23 05 2023 11:43:52",
    "order_id": "1373900_150876814",
    "sr_order_id": *********,
    "awb_assigned_date": "2023-05-19 11:59:16",
    "pickup_scheduled_date": "2023-05-19 11:59:17",
    "etd": "2023-05-23 15:40:19",
    "scans": [
        {
            "date": "2023-05-19 11:59:16",
            "status": "X-UCI",
            "activity": "Manifested - Manifest uploaded",
            "location": "Chomu_SamodRd_D (Rajasthan)",
            "sr-status": "5",
            "sr-status-label": "MANIFEST GENERATED"
        },
        {
            "date": "2023-05-19 15:32:17",
            "status": "X-PPOM",
            "activity": "In Transit - Shipment picked up",
            "location": "Chomu_SamodRd_D (Rajasthan)",
            "sr-status": "42",
            "sr-status-label": "PICKED UP"
        }
    ],
    "is_return": 0,
    "channel_id": 3422553,
    "pod_status": "OTP Based Delivery",
    "pod": "Not Available"
}


class ShiprocketWebhookView(APIView):
    """Handle Shiprocket webhook notifications for tracking updates."""
    authentication_classes = []  # Disable authentication
    permission_classes = []      # Disable permissions
    
    def post(self, request):
        # Verify security token
        logger.info(
            f"Webhook request received. "
            f"Request body: {request.body.decode('utf-8')}, "  # Decode request body
            f"Request headers: {request.headers}, "
            f"Request method: {request.method}, "
            f"Request path: {request.path}"
        )
        api_key = request.headers.get('X-Api-Key')
        if not api_key:
            return Response({'error': 'Security token missing'}, status=status.HTTP_401_UNAUTHORIZED)
        
        if str(api_key) != config('SHIPROCKET_WEBHOOK_SECRET'):
            return Response({'error': 'Invalid security token'}, status=status.HTTP_401_UNAUTHORIZED)

        # Get webhook data first
        webhook_data = request.data
        
        # Check for test webhook data
        if webhook_data.get('awb') == "123456":
            return Response({'status': 'success'}, status=status.HTTP_200_OK)
            
        # Process actual webhook data
        awb = webhook_data.get('awb')
        current_status = webhook_data.get('current_status')
        scans = webhook_data.get('scans', [])
        etd = webhook_data.get('etd')
        
        try:
            # Find package by AWB number
            package = SwadesicShippingPackageDetails.objects.get(awb_number=awb)

            # Get order and suborder instance
            order_instance = get_object_or_404(Order, order_number=package.order_reference)
            suborder_instances = SubOrder.objects.filter(package_number=package.package_number)
            
            # Update current status
            package.current_status = current_status
            
            # Update tracking milestones
            tracking_milestones = package.tracking_milestones or {}
            timeline = tracking_milestones.get('timeline', [])
            
            # Add new scans to timeline
            for scan in scans:
                scan_entry = {
                    'status': scan.get('status'),
                    'activity': scan.get('activity'),
                    'location': scan.get('location'),
                    'timestamp': scan.get('date'),
                    'sr_status': scan.get('sr-status'),
                    'sr_status_label': scan.get('sr-status-label')
                }
                if scan_entry not in timeline:
                    timeline.append(scan_entry)
                    ShippingHistory.objects.create(
                        title=f"Tracking Update: {scan.get('sr-status-label')}",
                        description=f"{scan.get('activity')} at {scan.get('location')}",
                        shipping_reference=package.package_number,
                        order_number=order_instance,
                    )
                    package.save()

            # Get latest status for updating suborder status
            mapped_status = map_shiprocket_status_to_swadesic(current_status)
            for suborder in suborder_instances:
                suborder_input = {
                    "suborder_number": suborder.suborder_number,
                    "status": mapped_status,
                }
                kwargs = {}  # Initialize with empty dict
                if mapped_status == "DELIVERY_IN_PROGRESS":
                    kwargs.update({
                        "package_number": package.package_number,
                    })
                elif mapped_status == "ORDER_DELIVERED":
                    kwargs.update({
                        "delivered_date": datetime.now(),
                    })
                elif mapped_status == "RETURN_REQUESTED":
                    kwargs.update({
                        "return_reason": "Returned beacause of customer request/ cancellation",
                    })
                elif mapped_status == "RETURN_CONFIRMED":
                    kwargs.update({
                        "return_reason": "Returned beacause of customer request/ cancellation",
                        "return_estimated_delivery_date": etd
                    })
                elif mapped_status == "RETURN_IN_PROGRESS":
                    return_package_number = create_package_number(packaging_type="RETURN")
                    kwargs.update({
                        "return_package_number": return_package_number,
                    })
                elif mapped_status == "RETURNED":
                    pass
                if not suborder.suborder_status == 'CANCELLED_IN_TRANSIT':
                    update_order_status_single = UpdateOrderStatus_single(**suborder_input, **kwargs)
                    update_order_status_single.update_order_status(optional_status=mapped_status)
                else:
                    update_order_status_single = UpdateOrderStatus_single(**suborder_input, **kwargs)
                    update_order_status_single.update_secondary_order_status(optional_status=mapped_status)
            
            # Sort timeline by date
            timeline.sort(key=lambda x: datetime.strptime(x['timestamp'], '%Y-%m-%d %H:%M:%S'))
            
            # Update package tracking milestones
            package.tracking_milestones = {'timeline': timeline}
            package.save()
            
            return Response({'status': 'success'}, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {'error': f'error processing webhook for AWB {awb}:{e}'},
                status=status.HTTP_400_BAD_REQUEST
            )


class ShiprocketMixin:
    """Mixin to provide Shiprocket API instance."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.shiprocket = ShiprocketAPI(config('SHIPROCKET_TOKEN'))


class CheckServiceabilityView(ShiprocketMixin, APIView):
    """Check courier serviceability between pickup and delivery locations."""
    permission_classes = [IsAuthenticated]

    def _filter_courier_data(self, courier, shipping_balance):
        """Filter and return only required fields from courier data."""
        rate = courier.get('rate', 0) + config("SHIPROCKET_SERVICE_CHARGE", cast=float)
        return {
            'courier_name': courier.get('courier_name'),
            'courier_company_id': courier.get('courier_company_id'),
            'pickup_time': courier.get('seconds_left_for_pickup'),
            'rate': rate,
            'estimated_delivery_days': courier.get('estimated_delivery_days'),
            'etd': courier.get('etd'),
            'cutoff_time': courier.get('cutoff_time'),
            'pickup_availability': courier.get('pickup_availability'),
            'realtime_tracking': courier.get('realtime_tracking'),
            'performance': {
                'pickup': courier.get('pickup_performance'),
                'rto': courier.get('rto_performance'),
                'tracking': courier.get('tracking_performance')
            },
            'delivery_boy_contact': courier.get('delivery_boy_contact'),
            'rto_charges': courier.get('rto_charges'),
            'call_before_delivery': courier.get('call_before_delivery'),
            'is_selectable': shipping_balance >= rate if rate else False
        }

    def post(self, request):
        try:
            pickup_postcode = request.data.get('pickup_postcode')
            delivery_postcode = request.data.get('delivery_postcode')
            cod = request.data.get('cod', 0)
            weight = float(request.data.get('weight', 0.5))
            store_reference = request.data.get('store_reference')

            store_instance = get_object_or_404(Store, store_reference=store_reference)

            if not all([pickup_postcode, delivery_postcode, store_reference]):
                return Response(
                    {'error': 'Pickup postcode, delivery postcode, and store reference are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get store's shipping balance
            try:
                shipping_balance_obj, created = StoreShippingAccountBalance.objects.get_or_create(
                    store_reference=store_instance,
                    defaults={'shipping_balance': 0}  # Set a default balance if created
                )
                shipping_balance = shipping_balance_obj.shipping_balance
            except Exception as e:
                return Response(
                    {'error': f'Error retrieving or creating store shipping balance: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            response = self.shiprocket.check_serviceability(
                pickup_postcode=pickup_postcode,
                delivery_postcode=delivery_postcode,
                cod=cod,
                weight=weight
            )

            # Extract and format the required fields
            filtered_response = {
                'currency': response.get('currency'),
                'shiprocket_recommended_courier_id': response.get('data', {}).get('shiprocket_recommended_courier_id'),
                'available_couriers': [
                    self._filter_courier_data(courier, float(shipping_balance))
                    for courier in response.get('data', {}).get('available_courier_companies', [])
                ]
            }

            return Response(filtered_response)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CreateShiprocketOrderView(ShiprocketMixin, APIView):
    """Create a new shipping order with Shiprocket and generate AWB number."""
    permission_classes = [IsAuthenticated]

    def _validate_suborders(self, suborder_list):
        """Validate that all suborders belong to the same order."""
        if not suborder_list:
            return False, "No suborders provided"
            
        # Get all suborders in one query
        suborders = SubOrder.objects.filter(suborder_number__in=suborder_list)
        if not suborders:
            return False, "No valid suborders found"
            
        # Get distinct order numbers count
        distinct_orders = suborders.values('order_number').distinct().count()
        
        # If more than one distinct order number, suborders are from different orders
        if distinct_orders > 1:
            return False, "All suborders must belong to the same order"
                
        return True, suborders

    def _prepare_order_items(self, suborders):
        """Prepare order items list for Shiprocket API."""
        items = []
        total_amount = 0
        
        for suborder in suborders:
            item = {
                "name": suborder.product_name,
                "sku": suborder.product_reference,
                "units": suborder.product_quantity,
                "selling_price": str(suborder.selling_price),
                "discount": "",
                "tax": "",
                "hsn": ""
            }
            items.append(item)
            total_amount += (suborder.selling_price * suborder.product_quantity)
            
        return items, total_amount

    @transaction.atomic
    def post(self, request):
        try:
            # 1. Validate input data
            suborder_list = request.data.get('suborder_list')
            display_package_number = request.data.get('display_package_number')
            courier_company_id = request.data.get('courier_company_id')
            pickup_address_id = request.data.get('pickup_address_id')
            length = request.data.get('length')
            breadth = request.data.get('breadth')
            height = request.data.get('height')
            dead_weight = request.data.get('dead_weight')

            if not all([suborder_list, courier_company_id, pickup_address_id, 
                       length, breadth, height, dead_weight]):
                return Response({
                    "status": "error",
                    "message": "Missing required fields"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 2. Validate suborders
            is_valid, result = self._validate_suborders(suborder_list)
            if not is_valid:
                return Response({
                    "status": "error",
                    "message": result
                }, status=status.HTTP_400_BAD_REQUEST)
            
            suborders = result
            first_suborder = suborders[0]
            
            # 3. Get Shipping Balance Instance
            store = get_object_or_404(Store, store_reference=first_suborder.store_reference)
            shipping_balance = get_object_or_404(StoreShippingAccountBalance, store_reference=store)
            current_shipping_balance = shipping_balance.shipping_balance

            # 4. Get pickup address and update if needed
            pickup_address = get_object_or_404(Address, addressid=pickup_address_id)

            # Get store trustcenter details
            store_trustcenter = store.store_details.first()
            if not store_trustcenter:
                return Response(
                    {'error': 'Store details not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            if not pickup_address.pickup_location_in_shiprocket:
                pickup_location = self.shiprocket.add_pickup_location(**{
                    "pickup_location": pickup_address.store_reference.store_reference + '/' + str(pickup_address.name),
                    "name": pickup_address.name,
                    "email": store_trustcenter.emailid if store_trustcenter.emailid else store.created_by.email,
                    "phone": pickup_address.phone_number if pickup_address.phone_number else str(store.created_by.phonenumber),
                    "address": pickup_address.address,
                    "city": pickup_address.city,
                    "state": pickup_address.state,
                    "country": "India",
                    "pin_code": pickup_address.pincode
                })
                logger.info(f"Pickup location created: {pickup_location}")
                if pickup_location.get('address'):
                    pickup_address.pickup_location_in_shiprocket = pickup_location['address'].get('pickup_code')
                    pickup_address.save()
                else:
                    logger.info(f"Failed to create pickup location: {pickup_location}")
            
            pickup_location = pickup_address.pickup_location_in_shiprocket
            logger.info(f"Pickup location: {pickup_location}")
            
            # 4. Get delivery address
            delivery_address = get_object_or_404(UserAddress, useraddressid=first_suborder.orderid.billing_address_id.useraddressid)
            user = get_object_or_404(User, user_reference=first_suborder.user_reference)

            # 5. Call courier details with pickup, delivery postcode and weight, courier_id for validation
            courier_details = get_courier_details(
                self.shiprocket,
                store.store_reference,
                pickup_address.pincode,
                delivery_address.pincode,
                courier_company_id,
                dead_weight
            )
            freight_charges = courier_details.get('selected_courier', [{}])[0].get('rate', 0)

            #6. Check if current shipping balance is enough to cover the freight charges
            if current_shipping_balance < freight_charges:
                return Response(
                    {'error': 'Insufficient shipping balance'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 6. Create package number
            package_number = create_package_number(packaging_type="DELIVERY")
            
            # 7. Prepare order items and calculate total
            order_items, sub_total = self._prepare_order_items(suborders)
            
            # 7. Create Shiprocket order request
            order_request = {
                'order_id': f"{first_suborder.order_number}--{package_number}",
                'order_date': str(first_suborder.created_date),
                'pickup_location': str(pickup_location),
                'billing_customer_name': delivery_address.name,
                'billing_last_name': "",
                'billing_address': delivery_address.address,
                'billing_city': delivery_address.city,
                'billing_pincode': delivery_address.pincode,
                'billing_state': delivery_address.state,
                'billing_country': 'India',
                'billing_email': user.email,
                'billing_phone': delivery_address.phone_number,
                'shipping_is_billing': True,
                'shipping_customer_name': delivery_address.name,
                'shipping_last_name': "",
                'shipping_address': delivery_address.address,
                'shipping_address_2': "",
                'shipping_city': delivery_address.city,
                'shipping_pincode': delivery_address.pincode,
                'shipping_country': 'India',
                'shipping_state': delivery_address.state,
                'shipping_email': user.email,
                'shipping_phone': delivery_address.phone_number,
                'order_items': order_items,
                'payment_method': 'Prepaid',
                'length': length,
                'breadth': breadth,
                'height': height,
                'weight': dead_weight,
                'sub_total': sub_total
            }
            
            # 8. Create Shiprocket order
            if not config("SKIP_SHIPROCKET_API"):
                order_request['order_id'] = f"{first_suborder.order_number}--{package_number}"
                order_response = self.shiprocket.create_order(order_request)
                if not order_response.get('order_id'):
                    raise ValueError("Failed to create Shiprocket order")
            else:
                order_response = {
                    'order_id': int(time.time()),
                    'shipment_id': str(int(time.time()))
                }
                
            shiprocket_order_id = order_response['order_id']
            shipment_id = order_response['shipment_id']
            
            # 9. Generate AWB
            if not config("SKIP_SHIPROCKET_API"):
                awb_response = self.shiprocket.assign_awb(
                    shipment_id=shipment_id,
                    courier_id=courier_company_id
                )
            
                if awb_response.get('awb_assign_status') != 1:
                    raise ValueError(f"Failed to generate AWB: {awb_response}")
                    
                awb_number = awb_response['response']['data']['awb_code']
                freight_charges = float(awb_response['response']['data']['freight_charges'])
                estimated_pickup_date = awb_response['response']['data'].get('pickup_scheduled_date')
                courier_name = awb_response['response']['data'].get('courier_name')

                logger.info(f"AWB generated: {awb_number}, Freight charges: {freight_charges}, Pickup date: {estimated_pickup_date}, Courier: {courier_name}")
            else:
                awb_number = "AWB" + str(int(time.time()))
                freight_charges = freight_charges
                estimated_pickup_date = datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)
                courier_name = "Shiprocket Test Courier name"

            # Schedule pickup if not scheduled
            if not estimated_pickup_date:
                if not config("SKIP_SHIPROCKET_API"):
                    logger.info(f"parameters for schedule pickup: shipment_ids={[shipment_id]}, pickup_dates={[(datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)).strftime('%Y-%m-%d')]}")
                    schedule_pickup_response = self.shiprocket.schedule_pickup(
                        shipment_ids=[shipment_id],
                        pickup_dates=[(datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)).strftime('%Y-%m-%d')]
                    )
                    estimated_pickup_date = schedule_pickup_response.get('response', {}).get('pickup_scheduled_date')
                    logger.info(f"estimated_pickup_date from response:{estimated_pickup_date}")
                else:
                    estimated_pickup_date = datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)

            # 10. Update suborders
            etd = courier_details.get('selected_courier', [{}])[0].get('etd')
            for suborder in suborders:
                suborder.package_number = package_number
                suborder.display_package_number = display_package_number
                suborder.suborder_status = SubOrder.Suborder_Status.SCHEDULED_FOR_PICKUP
                suborder.estimated_delivery_date = convert_date_format(etd) if etd else None
                suborder.estimated_pickup_date = estimated_pickup_date.strftime('%Y-%m-%d') if estimated_pickup_date else None
                suborder.tracking_number = awb_number
                suborder.tracking_link = f'https://shiprocket.co/tracking/{awb_number}'
                suborder.logistics_partner = courier_name
                suborder.delivery_by_swadesic = True
                suborder.save()

                OrderLifeCycle.objects.create(
                    orderid = suborder.orderid,
                    suborderid = suborder,
                    suborder_status = SubOrder.Suborder_Status.SCHEDULED_FOR_PICKUP,
                    delivery_by_swadesic = True,
                    order_number = suborder.order_number,
                    suborder_number = suborder.suborder_number,
                    estimated_delivery_date = suborder.estimated_delivery_date,
                    tracking_number = suborder.tracking_number,
                    logistic_partner = suborder.logistic_partner,
                    package_number = suborder.package_number,
                    display_package_number = suborder.display_package_number,
                    created_by=suborder.orderid.userid,
                    tracking_link = suborder.tracking_link
                )
            
            # 11. Create shipping package details
            swadesic_shipping_package_detail = SwadesicShippingPackageDetails.objects.create(
                package_number=package_number,
                order_reference=first_suborder.order_number,
                store_reference=first_suborder.store_reference,
                user_reference=first_suborder.user_reference,
                shiprocket_order_id=shiprocket_order_id,
                shipment_id=shipment_id,
                awb_number=awb_number,
                courier_name=courier_name,
                pickup_address={
                    'name': pickup_address.name,
                    'phone_number': pickup_address.phone_number,
                    'address': pickup_address.address,
                    'city': pickup_address.city,
                    'state': pickup_address.state,
                    'country': 'India',
                    'pincode': pickup_address.pincode,
                    'email': store_trustcenter.emailid
                },
                delivery_address={
                    'name': delivery_address.name,
                    'phone_number': delivery_address.phone_number,
                    'address': delivery_address.address,
                    'city': delivery_address.city,
                    'state': delivery_address.state,
                    'country': 'India',
                    'pincode': delivery_address.pincode,
                    'email': user.email
                },
                volumetric_weight=float(length * breadth * height) / 5000,
                dead_weight=dead_weight,
                length=length,
                breadth=breadth,
                height=height,
                package_contents={'products': order_items},
                shipping_cost=freight_charges,
                current_status='Pickup Scheduled',
                selected_courier_details=courier_details,
                tracking_url=f'https://shiprocket.co/tracking/{awb_number}'
            )
            
            # 12. Deduct shipping balance and update shipping package details and balance                 
            shipping_balance.shipping_balance -= Decimal(str(freight_charges))
            shipping_balance.save()
            swadesic_shipping_package_detail.is_shipping_balance_deducted = True
            swadesic_shipping_package_detail.save()
            
            # 13. Create shipping balance history
            StoreShippingBalanceHistory.objects.create(
                store_reference=store,
                package_number=package_number,
                transaction_type='DEBIT',
                payment_amount=freight_charges,
                transaction_date=datetime.now(),
                order_reference=first_suborder.order_number,
                suborder_references=suborder_list,
            )

            # Create shipping history record
            ShippingHistory.objects.create(
                title=f"Swadesic Shipping/{courier_name} Selected",
                description=f"Scheduled to pickup from Store: {estimated_pickup_date}",
                shipping_reference=package_number,
                order_number=first_suborder.orderid,
            )

            return Response({
                "status": "success",
                "message": "Shipping order created successfully",
                "data": {
                    "package_number": package_number,
                    "display_package_number": display_package_number,
                    "awb_number": awb_number,
                    "shiprocket_order_id": shiprocket_order_id,
                    "shipment_id": shipment_id,
                    "freight_charges": freight_charges,
                    "estimated_pickup_date": estimated_pickup_date
                }
            }, status=status.HTTP_201_CREATED)
            
        except ValueError as e:
            return Response({
                "status": "error",
                "message": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"An unexpected error occurred:{e}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CreateShiprocketReturnOrderView(ShiprocketMixin, APIView):
    """Create a return shipping order with Shiprocket and generate AWB number."""
    permission_classes = [IsAuthenticated]

    def _validate_suborders(self, suborder_list):
        """Validate that all suborders belong to the same order and are eligible for return."""
        if not suborder_list:
            return False, "No suborders provided"
            
        # Get all suborders in one query
        suborders = SubOrder.objects.filter(suborder_number__in=suborder_list)
        if not suborders:
            return False, "No valid suborders found"
            
        # Get distinct order numbers count
        distinct_orders = suborders.values('order_number').distinct().count()
        
        # If more than one distinct order number, suborders are from different orders
        if distinct_orders > 1:
            return False, "All suborders must belong to the same order"
        
        # Check if all suborders are delivered
        for suborder in suborders:
            if suborder.suborder_status != SubOrder.Suborder_Status.ORDER_DELIVERED:
                return False, f"Suborder {suborder.suborder_number} is not delivered yet and cannot be returned"
                
        return True, suborders

    def _prepare_return_items(self, suborders):
        """Prepare order items list for Shiprocket API."""
        items = []
        total_amount = 0
        
        for suborder in suborders:
            item = {
                "name": suborder.product_name,
                "sku": suborder.product_reference,
                "units": suborder.product_quantity,
                "selling_price": str(suborder.selling_price)
            }
            items.append(item)
            total_amount += (suborder.selling_price * suborder.product_quantity)
            
        return items, total_amount

    @transaction.atomic
    def post(self, request):
        try:
            # 1. Validate input data
            suborder_list = request.data.get('suborder_list')
            courier_company_id = request.data.get('courier_company_id')
            length = request.data.get('length')
            breadth = request.data.get('breadth')
            height = request.data.get('height')
            dead_weight = request.data.get('dead_weight')
            use_same_courier = request.data.get('use_same_courier', True)
            

            if not all([suborder_list, courier_company_id, length, breadth, height, dead_weight]):
                return Response({
                    "status": "error",
                    "message": "Missing required fields"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 2. Validate suborders
            is_valid, result = self._validate_suborders(suborder_list)
            if not is_valid:
                return Response({
                    "status": "error",
                    "message": result
                }, status=status.HTTP_400_BAD_REQUEST)
            
            suborders = result
            first_suborder = suborders[0]
            
            # 3. Get store and validate shipping balance
            store = get_object_or_404(Store, store_reference=first_suborder.store_reference)
            shipping_balance = get_object_or_404(StoreShippingAccountBalance, store_reference=store)
            store_trustcenter = store.store_details.first()

            if not store_trustcenter:
                return Response(
                    {'error': 'Store details not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            delivery_package_detail = get_object_or_404(SwadesicShippingPackageDetails, package_number=first_suborder.package_number)
            delivery_courier_details = delivery_package_detail.selected_courier_details
            # delivery_package_order_id = delivery_package_detail.shiprocket_order_id

            if use_same_courier:
                courier_company_id = delivery_courier_details.get('courier_company_id')
                length = delivery_package_detail.length
                breadth = delivery_package_detail.breadth
                height = delivery_package_detail.height
                dead_weight = delivery_package_detail.volumetric_weight

            # 4. Get addresses
            delivery_address = get_object_or_404(Address, addressid=request.data.get('delivery_address_id'))
            pickup_address = get_object_or_404(UserAddress, useraddressid=first_suborder.orderid.billing_address_id.useraddressid)
            user = get_object_or_404(User, user_reference=first_suborder.user_reference)

            # 5. Check courier serviceability and get rates
            courier_details = get_courier_details(
                self.shiprocket,
                store.store_reference,
                pickup_address.pincode,
                delivery_address.pincode,
                courier_company_id,
                dead_weight
            )
            freight_charges = courier_details.get('selected_courier', [{}])[0].get('rate', 0)

            # 6. Check shipping balance
            if shipping_balance.shipping_balance < freight_charges:
                return Response(
                    {'error': 'Insufficient shipping balance'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 7. Create return package number
            return_package_number = create_package_number(packaging_type="RETURN")
            
            # 8. Prepare order items and calculate total
            order_items, sub_total = self._prepare_return_items(suborders)
            
            # 9. Create return order request
            return_order_request = {
                'order_id': f"{first_suborder.order_number}--{return_package_number}",
                'order_date': str(first_suborder.created_date),
                'channel_id': '',
                'pickup_customer_name': pickup_address.name,
                'pickup_address': pickup_address.address,
                'pickup_city': pickup_address.city,
                'pickup_state': pickup_address.state,
                'pickup_country': 'India',
                'pickup_pincode': pickup_address.pincode,
                'pickup_email': user.email,
                'pickup_phone': pickup_address.phone_number,
                'shipping_customer_name': delivery_address.name,
                'shipping_address': delivery_address.address,
                'shipping_city': delivery_address.city,
                'shipping_state': delivery_address.state,
                'shipping_country': 'India',
                'shipping_pincode': delivery_address.pincode,
                'shipping_email': store_trustcenter.emailid,
                'shipping_phone': delivery_address.phone_number,
                'order_items': order_items,
                'payment_method': 'Prepaid',
                'length': length,
                'breadth': breadth,
                'height': height,
                'weight': dead_weight,
                'sub_total': sub_total
            }
            
            # 10. Create Shiprocket return order
            if not config("SKIP_SHIPROCKET_API"):
                order_response = self.shiprocket.create_return_order(return_order_request)
                if not order_response.get('order_id'):
                    raise ValueError("Failed to create Shiprocket return order")
            else:
                order_response = {
                    'order_id': int(time.time()),
                    'shipment_id': str(int(time.time()))
                }
                
            shiprocket_order_id = order_response['order_id']
            shipment_id = order_response['shipment_id']

            # 11. Generate AWB
            if not config("SKIP_SHIPROCKET_API"):
                awb_response = self.shiprocket.assign_awb(
                    shipment_id=shipment_id,
                    courier_id=courier_company_id,
                    is_return=1
                )

                if awb_response.get('awb_assign_status') != 1:
                    raise ValueError(f"Failed to generate AWB: {awb_response}")

                awb_number = awb_response['response']['data']['awb_code']
                freight_charges = float(awb_response['response']['data']['freight_charges'])
                estimated_pickup_date = awb_response['response']['data'].get('pickup_scheduled_date')
                courier_name = awb_response['response']['data'].get('courier_name')

                logger.info(
                    f"AWB generated: {awb_number}, Freight charges: {freight_charges}, Pickup date: {estimated_pickup_date}, Courier: {courier_name}")
            else:
                awb_number = "AWB" + str(int(time.time()))
                freight_charges = 50
                estimated_pickup_date = datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)
                courier_name = "Shiprocket Test Courier name"

            # Schedule pickup if not scheduled
            if not estimated_pickup_date:
                if not config("SKIP_SHIPROCKET_API"):
                    logger.info(f"parameters for schedule pickup: shipment_ids={[shipment_id]}, pickup_dates={[(datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)).strftime('%Y-%m-%d')]}")
                    schedule_pickup_response = self.shiprocket.schedule_pickup(
                        shipment_ids=[shipment_id],
                        pickup_dates=[
                            (datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)).strftime('%Y-%m-%d')]
                    )
                    estimated_pickup_date = schedule_pickup_response.get('response', {}).get('pickup_scheduled_date')
                    logger.info(f"estimated_pickup_date from response:{estimated_pickup_date}")
                else:
                    estimated_pickup_date = datetime.now(pytz.timezone('Asia/Kolkata')) + timedelta(days=1)

            # 12. Update suborders
            etd = courier_details.get('selected_courier', [{}])[0].get('etd')
            for suborder in suborders:
                suborder.return_package_number = return_package_number
                suborder.suborder_status = SubOrder.Suborder_Status.RETURN_SCHEDULED
                suborder.estimated_return_delivery_date = convert_date_format(etd) if etd else None
                suborder.estimated_return_pickup_date = estimated_pickup_date.strftime('%Y-%m-%d') if estimated_pickup_date else None
                suborder.return_tracking_number = awb_number
                suborder.return_tracking_link = f'https://shiprocket.co/tracking/{awb_number}'
                suborder.return_logistics_partner = courier_name
                suborder.save()

                OrderLifeCycle.objects.create(
                    orderid=suborder.orderid,
                    suborderid=suborder,
                    suborder_status=SubOrder.Suborder_Status.RETURN_SCHEDULED,
                    delivery_by_swadesic=True,
                    order_number=suborder.order_number,
                    suborder_number=suborder.suborder_number,
                    estimated_return_delivery_date=suborder.estimated_return_delivery_date,
                    return_tracking_number=suborder.return_tracking_number,
                    return_pickup_logistic_partner=suborder.return_logistics_partner,
                    return_package_number=return_package_number,
                    created_by=suborder.orderid.userid,
                    return_tracking_link=suborder.return_tracking_link
                )
            
            # 13. Create shipping package details
            swadesic_shipping_package_detail = SwadesicShippingPackageDetails.objects.create(
                package_number=return_package_number,
                order_reference=first_suborder.order_number,
                store_reference=first_suborder.store_reference,
                user_reference=first_suborder.user_reference,
                shiprocket_order_id=shiprocket_order_id,
                shipment_id=shipment_id,
                awb_number=awb_number,
                courier_name=courier_name,
                pickup_address={
                    'name': pickup_address.name,
                    'phone_number': pickup_address.phone_number,
                    'address': pickup_address.address,
                    'city': pickup_address.city,
                    'state': pickup_address.state,
                    'country': 'India',
                    'pincode': pickup_address.pincode,
                    'email': user.email
                },
                delivery_address={
                    'name': delivery_address.name,
                    'phone_number': delivery_address.phone_number,
                    'address': delivery_address.address,
                    'city': delivery_address.city,
                    'state': delivery_address.state,
                    'country': 'India',
                    'pincode': delivery_address.pincode,
                    'email': store_trustcenter.emailid
                },
                volumetric_weight=float(length * breadth * height) / 5000,
                dead_weight=dead_weight,
                length=length,
                breadth=breadth,
                height=height,
                package_contents={'products': order_items},
                shipping_cost=freight_charges,
                current_status='Return Pickup Scheduled',
                selected_courier_details=courier_details,
                tracking_url=f'https://shiprocket.co/tracking/{awb_number}',
                is_return=True
            )
            
            # 14. Deduct shipping balance and update shipping package details
            shipping_balance.shipping_balance -= Decimal(str(freight_charges))
            shipping_balance.save()
            swadesic_shipping_package_detail.is_shipping_balance_deducted = True
            swadesic_shipping_package_detail.save()
            
            # 15. Create shipping balance history
            StoreShippingBalanceHistory.objects.create(
                store_reference=store,
                package_number=return_package_number,
                transaction_type='DEBIT',
                payment_amount=freight_charges,
                transaction_date=datetime.now(),
                order_reference=first_suborder.order_number,
                suborder_references=suborder_list,
                notes='Return Shipping Cost'
            )

            # Create shipping history record
            ShippingHistory.objects.create(
                title=f"Return Shipment/{courier_name} Scheduled",
                description=f"Return pickup scheduled from customer on: {estimated_pickup_date}",
                shipping_reference=return_package_number,
                order_number=first_suborder.orderid,
            )

            return Response({
                "status": "success",
                "message": "Return shipping order created successfully",
                "data": {
                    "return_package_number": return_package_number,
                    "awb_number": awb_number,
                    "shiprocket_order_id": shiprocket_order_id,
                    "shipment_id": shipment_id,
                    "freight_charges": freight_charges,
                    "estimated_pickup_date": estimated_pickup_date
                }
            }, status=status.HTTP_201_CREATED)
            
        except ValueError as e:
            return Response({
                "status": "error",
                "message": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating return order: {str(e)}", exc_info=True)
            return Response({
                "status": "error",
                "message": f"An unexpected error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CreateShippingOrderView(ShiprocketMixin, APIView):
    """Create a new shipping order with Shiprocket."""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            store = get_object_or_404(Store, store_reference=request.data.get('store_reference'))
            user = get_object_or_404(User, user_reference=request.data.get('user_reference'))
            order_reference = request.data.get('order_reference')
            length = request.data.get('length')
            breadth = request.data.get('breadth')
            height = request.data.get('height')
            weight = request.data.get('weight')
            sub_total = request.data.get('sub_total')
            
            pickup_address = get_object_or_404(
                Address,
                addressid=request.data.get('pickup_address_id')
            )
            delivery_address = get_object_or_404(
                UserAddress,
                useraddressid=request.data.get('delivery_address_id')
            )
            package_contents = request.data.get('package_contents')
            shipping_cost = float(request.data.get('shipping_cost', 0))

            # Check shipping balance
            shipping_balance = StoreShippingAccountBalance.objects.get(
                store_reference=store
            )
            if shipping_balance.shipping_balance < Decimal(str(shipping_cost)):
                return Response(
                    {'error': 'Insufficient shipping balance'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Get store trustcenter details
            store_detail = store.store_details.first()
            if not store_detail:
                return Response(
                    {'error': 'Store details not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            if pickup_address.pickup_location_in_shiprocket is not None:
                pickup_location = pickup_address.pickup_location_in_shiprocket
            else:
                pickup_location_response = self.shiprocket.add_pickup_location(
                    pickup_location='SHPRKT_PKL' + store.store_reference,
                    name=pickup_address.name,
                    email=store_detail.emailid,
                    phone=pickup_address.phone_number,
                    address=pickup_address.address,
                    city=pickup_address.city,
                    state=pickup_address.state,
                    country='India',
                    pin_code=pickup_address.pincode
                )
                pickup_address.pickup_location_in_shiprocket = pickup_location_response.get('pickup_code')
                pickup_address.store_address_type.append('SWADESIC_SHIPPING_PICKUP') if 'SWADESIC_SHIPPING_PICKUP' not in pickup_address.store_address_type else None
                pickup_address.save()
                
                pickup_location = pickup_location_response.get('pickup_code')

            # create a package number
            package_number = create_package_number(packaging_type="DELIVERY")

            # Prepare order details for Shiprocket
            order_details = {
                'order_id': order_reference +'--'+ package_number,
                'order_date': request.data.get('order_date'),
                'pickup_location': pickup_location,
                'billing_customer_name': delivery_address.name,
                'billing_last_name': "",
                'billing_address': delivery_address.address,
                'billing_city': delivery_address.city,
                'billing_pincode': delivery_address.pincode,
                'billing_state': delivery_address.state,
                'billing_country': 'India',
                'billing_email': user.email,
                'billing_phone': delivery_address.phone_number,
                'shipping_is_billing': True,
                'order_items': package_contents.get('products', []),
                'payment_method': 'Prepaid',
                'shipping_charges': shipping_cost,
                'length': length,
                'breadth': breadth,
                'height': height,
                'weight': weight,
                'sub_total': sub_total
            }

            # Create order in Shiprocket
            shiprocket_response = self.shiprocket.create_order(order_details)

            if not shiprocket_response.get('order_id'):
                return Response(
                    {'error': 'Failed to create order in Shiprocket'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            # Create package details record
            with transaction.atomic():
                package = SwadesicShippingPackageDetails.objects.create(
                    order_reference=order_reference,
                    store_reference=store.store_reference,
                    user_reference=user.user_reference,
                    package_number=package_number,
                    shiprocket_order_id=shiprocket_response.get('order_id'),
                    shipment_id=shiprocket_response.get('shipment_id'),
                    pickup_address={
                        'name': pickup_address.name,
                        'phone_number': pickup_address.phone_number,
                        'address': pickup_address.address,
                        'city': pickup_address.city,
                        'state': pickup_address.state,
                        'country': 'India',
                        'pincode': pickup_address.pincode,
                        'addressid': pickup_address.addressid
                    },
                    delivery_address={
                        'name': delivery_address.name,
                        'phone_number': delivery_address.phone_number,
                        'address': delivery_address.address,
                        'city': delivery_address.city,
                        'state': delivery_address.state,
                        'country': 'India',
                        'pincode': delivery_address.pincode,
                        'useraddressid': delivery_address.useraddressid
                    },
                    package_contents=package_contents,
                    shipping_cost=shipping_cost
                )

                # Deduct shipping balance
                shipping_balance.shipping_balance -= Decimal(str(shipping_cost))
                shipping_balance.save()

                # Create balance history record
                StoreShippingBalanceHistory.objects.create(
                    store_reference=store,
                    package_number=package.package_number,
                    order_reference=order_reference,
                    transaction_type='DEBIT',
                    payment_status='SUCCESS',
                    shipping_balance_payment_reference=f"SHIP-{order_reference}"
                )

            return Response({
                'message': 'Shipping order created successfully',
                'package_details': {
                    'package_number': package.package_number,
                    'shiprocket_order_id': package.shiprocket_order_id
                }
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TrackShipmentView(ShiprocketMixin, APIView):
    """Track a shipment using its package number."""
    permission_classes = [IsAuthenticated]

    def get(self, request, package_number):
        try:
            package = get_object_or_404(
                SwadesicShippingPackageDetails,
                package_number=package_number
            )
            
            if not package.shipment_id:
                return Response(
                    {'error': 'Shipment not yet created'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            tracking_data = self.shiprocket.track_shipment(package.shipment_id)
            
            # Update tracking milestones
            package.tracking_milestones = tracking_data
            package.current_status = tracking_data.get('current_status', 'In Transit')
            package.save()

            return Response(tracking_data)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetPackageDetailsView(APIView):
    """Get package details by package number."""
    permission_classes = [IsAuthenticated]

    def get(self, request, package_number):
        try:
            package = get_object_or_404(
                SwadesicShippingPackageDetails,
                package_number=package_number
            )
            
            package_data = {
                'package_number': package.package_number,
                'order_reference': package.order_reference,
                'store_reference': package.store_reference,
                'user_reference': package.user_reference,
                'shiprocket_order_id': package.shiprocket_order_id,
                'shipment_id': package.shipment_id,
                'awb_number': package.awb_number,
                'courier_name': package.courier_name,
                'tracking_url': package.tracking_url,
                'selected_courier_details': package.selected_courier_details,
                'pickup_address': package.pickup_address,
                'delivery_address': package.delivery_address,
                'volumetric_weight': package.volumetric_weight,
                'dead_weight': package.dead_weight,
                'length': package.length,
                'breadth': package.breadth,
                'height': package.height,
                'package_contents': package.package_contents,
                'shipping_cost': package.shipping_cost,
                'is_shipping_balance_deducted': package.is_shipping_balance_deducted,
                'current_status': package.current_status,
                'tracking_milestones': package.tracking_milestones,
                'created_at': package.created_at,
                'updated_at': package.updated_at
            }
            
            return Response(package_data)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetShippingBalanceView(APIView):
    """Get store's shipping account balance."""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            store_reference = request.query_params.get('store_reference')
            if not store_reference:
                return Response(
                    {'error': 'Store reference is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            balance = get_object_or_404(
                StoreShippingAccountBalance,
                store_reference=store_reference
            )

            return Response({
                'store_reference': store_reference,
                'shipping_balance': float(balance.shipping_balance)
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ShippingBalanceHistoryView(APIView):
    """Get store's shipping transaction history."""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            store_reference = request.query_params.get('store_reference')
            if not store_reference:
                return Response(
                    {'error': 'Store reference is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            history = StoreShippingBalanceHistory.objects.filter(
                store_reference=store_reference
            ).order_by('-created_at')

            return Response([{
                'transaction_id': item.shipping_balance_payment_reference,
                'package_number': item.package_number,
                'order_reference': item.order_reference,
                'transaction_type': item.transaction_type,
                'payment_status': item.payment_status,
                'transaction_date': item.transaction_date
            } for item in history])

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GenerateShippingLabelView(ShiprocketMixin, APIView):
    """Generate shipping label for a package."""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            package_number = request.data.get('package_number')
            package = get_object_or_404(
                SwadesicShippingPackageDetails,
                package_number=package_number
            )
            package_shipment_id = package.shipment_id

            if not package_shipment_id:
                return Response(
                    {'error': 'Shipment ID not found for this package'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            if not package.label_url:
                try:
                    shiprocket_generate_label_response=self.shiprocket.generate_label(shipment_ids=[package_shipment_id])
                    if shiprocket_generate_label_response.get('label_created') == 1:
                        package.label_url = shiprocket_generate_label_response.get('label_url')
                        package.save()
                        return Response(
                            {'message': 'success',
                             'label_url': package.label_url},
                            status=200
                        )
                    else:
                        return Response(
                            {'error': f'Failed to generate label{shiprocket_generate_label_response}'},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR
                        )
                except Exception as e:
                    return Response(
                        {'error': str(e)},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )
            else:
                return Response(
                    {'message': 'success',
                     'label_url': package.label_url},
                    status=200
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AddShippingBalanceAndInitiatePayment(generics.GenericAPIView):
    are_all_test_store_products = False

    """
    Handles the creation of a new order, including validating input data, checking deliverability, calculating fees, 
    and initiating the payment process.

    This method is part of the `OrderAPIView` class, which is a Django REST Framework view that handles 
    order-related API requests.

    Args:
        request (django.http.request.HttpRequest): The HTTP request object containing the order data.
        *args: Additional positional arguments.
        **kwargs: Additional keyword arguments.

    Returns:
        django.http.response.Response: A JSON response containing the order details, including any errors or issues 
        encountered during the order creation process.
    """

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        ## Wrapper method start
        # Extract and validate input data
        store_reference, amount, is_webpage = self.extract_input_data(request.data)

        # Validate input data
        self.validate_input_data(store_reference, amount, is_webpage)

        # Get store, trustcenter, store owner instances
        store_instance = Store.objects.get(store_reference=store_reference)
        trust_center_instance = TrustCenter.objects.get(store_reference=store_instance)
        store_owner_instance = User.objects.get(userid=store_instance.created_by.userid)

        # create an entry in store shipping balance history with pending status and input amount
        store_shipping_balance_history = self.create_or_get_store_shipping_balance_history(store_instance, amount)

        # Generate payment response data
        response_data = self.generate_payment_response_data(amount, store_shipping_balance_history.shipping_balance_payment_reference, store_instance, trust_center_instance, store_owner_instance)

        if is_webpage:
            html_short_url = self.create_html_short_url(response_data)
        else:
            html_short_url = None

        return Response(
            {
                "message": "success",
                "payment_details": response_data,
                "html_short_url": html_short_url
            },
            status=status.HTTP_200_OK,
        )

    def extract_input_data(self, data):
        return (
            data.get("store_reference"),
            data.get("amount"),
            data.get("is_webpage"),
        )

    def validate_input_data(self, store_reference, amount, is_webpage):
        if not all([store_reference, amount]):
            raise ValidationError("Missing required fields")

    # def get_transaction_fee(self):
    #     order_configuration = OrderConfiguration.objects.all().first()
    #     return order_configuration.transaction_fee

    def create_or_get_store_shipping_balance_history(self, store_instance, amount):
        store_shipping_balance_pending_instance = StoreShippingBalanceHistory.objects.filter(
            store_reference=store_instance,
            payment_status=StoreShippingBalanceHistory.PaymentStatus.PENDING,
            transaction_type=StoreShippingBalanceHistory.TransactionTypeChoices.CREDIT,
            payment_amount=amount
        ).first()
        
        if not store_shipping_balance_pending_instance:
            # Create store shipping balance history
            shipping_balance_payment_reference = self.generate_payment_reference()
            store_shipping_balance_history = StoreShippingBalanceHistory.objects.create(
                store_reference=store_instance,
                shipping_balance_payment_reference=shipping_balance_payment_reference,
                transaction_type=StoreShippingBalanceHistory.TransactionTypeChoices.CREDIT,
                payment_status=StoreShippingBalanceHistory.PaymentStatus.PENDING,
                payment_amount=amount,
            )
            return store_shipping_balance_history
        else:
            return store_shipping_balance_pending_instance

    def generate_payment_reference(self):   
        return f"SP-{int(time.time() * 1000)}"

    def generate_payment_response_data(self, amount, shipping_balance_payment_reference, store_instance, trust_center_instance, store_owner_instance):
        dummy_token = get_dummy_txn_token()

        if SKIP_RAZORPAY_API:
            return self.generate_dummy_payment_data(amount, shipping_balance_payment_reference, dummy_token, store_instance, trust_center_instance, store_owner_instance)
        else:
            return self.generate_razorpay_payment_data(amount, shipping_balance_payment_reference, dummy_token, store_instance, trust_center_instance, store_owner_instance)

    def generate_dummy_payment_data(self, amount, shipping_balance_payment_reference, dummy_token, store_instance, trust_center_instance, store_owner_instance):
        return {
            "key": RAZORPAY_ID,
            "amount": amount,
            "currency": "INR",
            "name": "Swadesic by Socially X",
            "description": "dummy_order_Oet4sfJJESHafo",
            "image": "https://raw.githubusercontent.com/Socially-X/public_assets/MASTER/Swadesic%20logo%20(1).png",
            "order_id": "dummy_order_Oet4sfJJESHafo",
            "prefill": {
                "name": store_instance.store_name if store_instance.store_name else store_instance.storehandle,
                "email": trust_center_instance.emailid if trust_center_instance.emailid else store_owner_instance.email,
                "contact": trust_center_instance.phonenumber if trust_center_instance.phonenumber else store_owner_instance.phonenumber,
            },
            "notes": {
                "shipping_balance_payment_reference": shipping_balance_payment_reference,
                "txn_token": dummy_token
            },
            "theme": {
                "color": "#10c057"
            }
        }

    def generate_razorpay_payment_data(self, grand_total, shipping_balance_payment_reference, dummy_token, store_instance, trust_center_instance, store_owner_instance):
        def prepare_order_data():
            return {
                "amount": int(grand_total) * 100,
                "currency": "INR",
                "receipt": dummy_token,
                "notes": {
                    "user_name": store_instance.store_name if store_instance.store_name else store_instance.storehandle,
                    "user_email": trust_center_instance.emailid if trust_center_instance.emailid else store_owner_instance.email,
                    "user_contact": trust_center_instance.phonenumber if trust_center_instance.phonenumber else store_owner_instance.phonenumber,
                    "shipping_balance_payment_reference": shipping_balance_payment_reference,
                    "transaction_id": dummy_token
                }
            }

        def make_razorpay_request(data):
            headers = {"Content-Type": "application/json"}
            auth = HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY)
            logger.info(f"Using LIVE Razorpay keys for order request number: {shipping_balance_payment_reference}")

            return requests.post(
                'https://api.razorpay.com/v1/orders',
                headers=headers,
                auth=auth,
                json=data
            )

        def prepare_response_data(api_response):
            return {
                "key": RAZORPAY_ID,
                "amount": api_response["amount"] / 100,
                "currency": api_response["currency"],
                "name": "Swadesic by Socially X",
                "description": api_response["id"],
                "image": "https://raw.githubusercontent.com/Socially-X/public_assets/MASTER/Swadesic%20logo%20(1).png",
                "order_id": api_response["id"],
                "prefill": {
                    "name": api_response["notes"]["user_name"],
                    "email": api_response["notes"]["user_email"],
                    "contact": api_response["notes"]["user_contact"],
                },
                "notes": {
                    "shipping_balance_payment_reference": api_response["notes"]["shipping_balance_payment_reference"],
                    "txn_token": api_response["receipt"]
                },
                "theme": {
                    "color": "#10c057"
                }
            }

        try:
            order_data = prepare_order_data()
            response = make_razorpay_request(order_data)
            api_response = response.json()

            if response.status_code == 200:
                try:
                    response_data = prepare_response_data(api_response)
                    txn_token = response_data["notes"]["txn_token"]
                    # Updating Order transaction token in shipping balance history table
                    store_shipping_balance_history = StoreShippingBalanceHistory.objects.get(
                        store_reference=store_instance,
                        shipping_balance_payment_reference=api_response["notes"]["shipping_balance_payment_reference"]
                    )
                    store_shipping_balance_history.txn_token = txn_token
                    store_shipping_balance_history.save()

                    return response_data
                except Exception as e:
                    return Response({"message": "error", "data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(
                    {"message": "error", "data": f"Razorpay Order could not be created: {response.content}"},
                    status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"message": "error", "data": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def create_html_short_url(self, response_data):
        import os
        import json
        from django.conf import settings
        import uuid

        filename = f"payment_{response_data['notes']['shipping_balance_payment_reference']}{uuid.uuid4().hex}.html"
        payments_dir = os.path.join(settings.MEDIA_ROOT, 'payment_html_pages')
        os.makedirs(payments_dir, exist_ok=True)
        file_path = os.path.join(payments_dir, filename)

        # Prepare context for template
        context = {
            'key': response_data['key'],
            'amount': str(int(float(response_data['amount']) * 100)),
            'currency': response_data['currency'],
            'name': response_data['name'],
            'description': response_data['description'],
            'image': response_data['image'],
            'order_id': response_data['order_id'],
            'callback_url': config('RAZORPAY_CALLBACK_URL'),
            'prefill': response_data['prefill'],
            'notes': json.dumps(response_data["notes"]),
            'theme': response_data['theme']
        }

        # Render the template with context using the correct template path
        template_path = os.path.join(settings.BASE_DIR, 'orders', 'payment_api', 'pay.html')
        with open(template_path, 'r', encoding='utf-8') as template_file:
            template_content = template_file.read()

        template = Template(template_content)
        html_content = template.render(Context(context))

        # Write the rendered HTML to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return f"{settings.MEDIA_URL}payment_html_pages/{filename}"


class ValidateShippingBalancePayment(APIView):
    def get_date(self):
        date = datetime.now()
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%Y-%m-%d %H:%M:%S")

    def post(self, request):
        payment_data = self.extract_payment_data(request.data)
        shipping_balance_history = StoreShippingBalanceHistory.objects.get(
            shipping_balance_payment_reference=payment_data['order_id'],
            payment_status=StoreShippingBalanceHistory.PaymentStatus.PENDING
        )
        
        if not self.is_valid_payment_data(payment_data, shipping_balance_history):
            return self.handle_failed_payment(payment_data)

        payment_details = get_payment_details(payment_data['razorpay_pay_id'])
        response_data = (
            self.create_test_success_response(payment_data)
            if SKIP_RAZORPAY_API
            else self.process_payment(payment_data, payment_details)
        )

        if response_data["body"]["resultInfo"]["resultStatus"] == "PAYMENT_SUCCESS":
            return self.handle_successful_payment(response_data, payment_details)
        else:
            return self.handle_failed_payment(response_data)

    def extract_payment_data(self, data):
        return {
            'transaction_id': data.get('transaction_id'),
            'amount': data.get('amount'),
            'order_id': data.get('shipping_balance_payment_reference'),
            'razorpay_order_id': data['razorpay_order_id'],
            'razorpay_pay_id': data['razorpay_pay_id'],
            'razorpay_signature': data['razorpay_signature']
        }

    def is_valid_payment_data(self, payment_data, shipping_balance_history):
        if float(payment_data['amount']) != shipping_balance_history.payment_amount:
            return False
        if not all([payment_data['razorpay_order_id'], payment_data['razorpay_pay_id'], payment_data['razorpay_signature'], shipping_balance_history]):
            return False
        if payment_data['transaction_id'] != shipping_balance_history.txn_token:
            return False
        return True

    def handle_failed_payment(self, payment_data):
        response_data = self.create_failed_payment_response(payment_data)
        return Response({
            "message": "Payment failed",
            "data": response_data,
        }, status=status.HTTP_200_OK)

    def create_failed_payment_response(self, payment_data):
        return {
            "head": {
                "responseTimestamp": self.get_date(),
                "version": "v1",
                "clientId": "C11",
                "signature": ""
            },
            "body": {
                "resultInfo": {
                    "resultStatus": "PAYMENT_FAILED",
                    "resultCode": "01",
                    "resultMsg": "Transaction Failed"
                },
                "txnId": payment_data['transaction_id'],
                "bankTxnId": "",
                "orderId": payment_data['order_id'],
                "txnAmount": str(payment_data['amount']),
                "txnType": "SALE",
                "gatewayName": "RAZORPAY",
                "bankName": "",
                "mid": RAZORPAY_ID,
                "paymentMode": "",
                "refundAmt": "",
                "txnDate": self.get_date()
            },
        }

    def create_test_success_response(self, payment_data):
        return {
            "head": {
                "responseTimestamp": self.get_date(),
                "version": "v1",
                "clientId": "C11",
                "signature": ""
            },
            "body": {
                "resultInfo": {
                    "resultStatus": "PAYMENT_SUCCESS",
                    "resultCode": "01",
                    "resultMsg": "Transaction Success"
                },
                "txnId": payment_data['transaction_id'],
                "bankTxnId": "",
                "orderId": payment_data['order_id'],
                "txnAmount": str(payment_data['amount']),
                "txnType": "SALE",
                "gatewayName": "RAZORPAY",
                "bankName": "",
                "mid": RAZORPAY_ID,
                "paymentMode": "",
                "refundAmt": "0.00",
                "txnDate": self.get_date(),
                "razorpayPayId": payment_data['razorpay_pay_id']
            },
        }

    def process_payment(self, payment_data, payment_details):
        result_message = verify_signature(
            razorpay_order_id=payment_data['razorpay_order_id'],
            razorpay_payment_id=payment_data['razorpay_pay_id'],
            razorpay_signature=payment_data['razorpay_signature'],
            secret=RAZORPAY_KEY
        )
        is_payment_success = result_message == "PAYMENT_SUCCESS"

        if not is_payment_success:
            return self.create_failed_payment_response(payment_data)

        return self.create_success_payment_response(payment_details)

    def create_success_payment_response(self, payment_details):
        return {
            "head": {
                "responseTimestamp": self.get_date(),
                "version": "v1",
                "clientId": "C11",
                "signature": ""
            },
            "body": {
                "resultInfo": {
                    "resultStatus": "PAYMENT_SUCCESS",
                    "resultCode": "01",
                    "resultMsg": payment_details["status"],
                },
                "txnId": get_refund_txn_id_from_acquirer_data(payment_details["acquirer_data"]),
                "bankTxnId": get_bank_txn_id_from_acquirer_data(payment_details["acquirer_data"], payment_details["method"]),
                "orderId": payment_details["notes"]["shipping_balance_payment_reference"],
                "txnAmount": "{:.2f}".format(payment_details["amount"] / 100),
                "txnType": "SALE",
                "gatewayName": "RAZORPAY",
                "bankName": payment_details.get("bank", ""),
                "mid": RAZORPAY_ID,
                "paymentMode": payment_details.get("method", ""),
                "refundAmt": "0.00",
                "txnDate": self.get_date(),
                "razorpayPayId": payment_details["id"]
            }
        }

    @transaction.atomic
    def handle_successful_payment(self, response_data, payment_details):
        # Update shipping balance history status
        balance_history = StoreShippingBalanceHistory.objects.get(
            shipping_balance_payment_reference=payment_details["notes"]["shipping_balance_payment_reference"],
            payment_status=StoreShippingBalanceHistory.PaymentStatus.PENDING
        )
        
        balance_history.payment_status = StoreShippingBalanceHistory.PaymentStatus.SUCCESS
        balance_history.razorpay_payment_id = payment_details["id"]
        balance_history.transaction_id = response_data["body"]["txnId"]
        balance_history.bank_transaction_id = response_data["body"]["bankTxnId"]
        balance_history.transaction_date = response_data["body"]["txnDate"]
        balance_history.payment_mode = response_data["body"]["paymentMode"]
        balance_history.payment_channel = get_payment_channel(payment_details)
        balance_history.save()

        # Update store shipping balance
        shipping_balance = StoreShippingAccountBalance.objects.get(store_reference=balance_history.store_reference)
        shipping_balance.shipping_balance += Decimal(str(balance_history.payment_amount))
        shipping_balance.save()

        return Response({
            "message": "Payment successful",
            "data": response_data,
        }, status=status.HTTP_200_OK)


class AddShippingBalanceFromAccountBalance(APIView):
    def post(self, request):
        store_reference = request.data.get('store_reference')
        amount = request.data.get('amount')
        
        if not store_reference or not amount:
            return Response({
                "message": "Store reference and amount are required",
                "data": None,
            }, status=status.HTTP_400_BAD_REQUEST)

        store_instance = Store.objects.get(store_reference=store_reference)
        store_payout_balance_instance = PayoutBalance.objects.get(store_reference=store_instance)

        amount_check = self.validate_amount(amount, store_payout_balance_instance)
        if not amount_check:
            return Response({
                "message": "Insufficient account balance",
                "data": None,
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Deduct amount from account balance and add to storeshipping balance
            store_payout_balance_instance.current_balance -= float(amount)
            store_payout_balance_instance.save()

            store_shipping_balance_instance = StoreShippingAccountBalance.objects.get(store_reference=store_instance)
            store_shipping_balance_instance.shipping_balance += Decimal(amount)
            store_shipping_balance_instance.save()

            # Create store shipping balance history and Payout balance History
            payout_transaction = PayoutTransactions.objects.create(
                store_reference=store_instance,
                transaction_type=PayoutTransactions.Transaction_Type.DEBITED,
                transaction_status=PayoutTransactions.Transaction_Status.SUCCESS,
                payout_amount=float(amount),
                transaction_time=datetime.now(),
                payout_release_date=datetime.now(),
                notes="Added to store shipping balance"
            )
            payout_transaction.save()

            store_shipping_balance_history = StoreShippingBalanceHistory.objects.create(
                store_reference=store_instance,
                shipping_balance_payment_reference=payout_transaction.transaction_reference,
                transaction_type=StoreShippingBalanceHistory.TransactionTypeChoices.CREDIT,
                payment_status=StoreShippingBalanceHistory.PaymentStatus.SUCCESS,
                payment_amount=float(amount),
                transaction_date=datetime.now(),
                payment_channel="Internal",
                payment_mode="Internal Transfer",
                notes="Added to store shipping balance from payout balance"
            )
            store_shipping_balance_history.save()
        except Exception as e:
            return Response({
                "message": f"Failed to create txn history: {str(e)}",
                "data": None,
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            "message": "success",
            "data": {
                "shipping_balance_payment_reference": store_shipping_balance_history.shipping_balance_payment_reference,
                "store_reference": store_reference,
                "amount": amount,
                "shipping_balance": store_shipping_balance_instance.shipping_balance,
                "account_balance": store_payout_balance_instance.current_balance,
            },
        }, status=status.HTTP_200_OK)



    def validate_amount(self, amount, store_payout_balance_instance):
        try:
            if float(amount) > store_payout_balance_instance.current_balance:
                return False
            return True
        except Exception as e:
            return Response({
                "message": str(e),
                "data": None,
            }, status=status.HTTP_400_BAD_REQUEST)
