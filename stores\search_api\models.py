from django.db import models
from django.utils.translation import gettext_lazy as _

class SearchHistory(models.Model):
    search_history_id = models.AutoField(primary_key=True)
    user_reference = models.Char<PERSON>ield(max_length=20)
    search_input_text = models.Char<PERSON><PERSON>(max_length=20)
    created_date = models.DateTimeField(auto_now_add=True)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "search history"
        db_table = '"store"."search_history"'

class SearchedItems(models.Model):
    class SearchItemType(models.TextChoices):
        USER = "USER", _("User")
        PRODUCT = "PRODUCT", _("Product")
        STORE = "STORE", _("Store")
    searched_item_id = models.AutoField(primary_key=True)
    user_reference = models.Char<PERSON>ield(max_length=20)
    search_input_text = models.Char<PERSON><PERSON>(max_length=20)
    search_item = models.<PERSON><PERSON><PERSON><PERSON>(max_length=25)
    search_item_type = models.<PERSON><PERSON><PERSON><PERSON>(max_length=20,
        choices=SearchItemType.choices,
        null=True,
        blank=True,)
    created_date = models.DateTimeField(auto_now_add=True)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "searched items"
        db_table = '"store"."searched_items"'

