# Generated by Django 4.2.7 on 2024-09-29 14:21

from django.db import migrations


def create_default_app_configuration(apps, schema_editor):
    AppConfigurations = apps.get_model('general', 'AppConfigurations')
    AppConfigurations.objects.create(
        swadesic_default_invite_code="SWADESHIFIRST",
        real_store_creation_limit=5,
        test_store_creation_limit=1,
        invite_reward_onboarding=51,
        invite_reward_store_verification=51,
        flash_promotion_period=12,
        monthly_flash_point_credit=500,
        is_promotion_active=True,
        # Note: promotion_start_time and promotion_end_time are left as NULL
    )


class Migration(migrations.Migration):
    dependencies = [
        ('general', '0023_appconfigurations'),
    ]

    operations = [
        migrations.RunPython(create_default_app_configuration),
    ]
