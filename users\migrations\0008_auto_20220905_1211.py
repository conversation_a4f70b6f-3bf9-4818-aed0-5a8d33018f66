# Generated by Django 3.2.13 on 2022-09-05 06:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0007_inviteuser_is_deleted"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="invite_code",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="invite_type",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="member_access",
            field=models.Bo<PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="seller_access",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="inviteuser",
            name="invite_code",
            field=models.Char<PERSON>ield(max_length=100, unique=True),
        ),
    ]
