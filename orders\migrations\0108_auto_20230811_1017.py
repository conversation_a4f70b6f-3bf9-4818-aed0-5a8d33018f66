# Generated by Django 3.2.13 on 2023-08-11 04:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0107_auto_20230810_1710'),
    ]

    operations = [
        migrations.AddField(
            model_name='refundedamount',
            name='refund_hold_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='orderpaymentdetails',
            name='payment_status',
            field=models.CharField(blank=True, choices=[('PAYMENT_INITIATED', 'Payment initiated'), ('PAYMENT_SUCCESS', 'Payment success'), ('REFUND_INITIATED', 'Refund initiated'), ('REFUND_INITIATE_FAILED', 'Refund initiate failed'), ('REFUND_PENDING', 'Refund pending'), ('REFUND_SUCCESS', 'Refund success'), ('REFUND_FAILED', 'Refund failed')], max_length=50, null=True),
        ),
    ]
