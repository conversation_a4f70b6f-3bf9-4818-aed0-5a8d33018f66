from rest_framework import serializers
from .models import Posts, PostImages, PostLikes, Story


class PostsCreateSerializer(serializers.ModelSerializer):
    post_reference = serializers.CharField(read_only=True)

    class Meta:
        model = Posts
        fields = ['post_text', 'user_reference', 'store_reference', 'post_reference']

    def create(self, validated_data):
        # Manually set the post_reference before saving
        instance = super(PostsCreateSerializer, self).create(validated_data)
        instance.post_reference = Posts.get_post_reference(instance)
        instance.save()
        return instance

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # No need to convert post_reference to string, as it's already a string in the model
        return representation


class PostImagesSerializer(serializers.ModelSerializer):
    class Meta:
        model = PostImages
        fields = '__all__'


class PostsSerializer(serializers.ModelSerializer):
    like_count = serializers.SerializerMethodField("get_like_count")
    post_images = PostImagesSerializer(many=True)
    is_liked = serializers.SerializerMethod<PERSON>ield("get_liked")
    icon = serializers.SerializerMethodField("get_icon")
    handle = serializers.SerializerMethodField("get_handle")

    class Meta:
        model = Posts
        fields = [
            "post_id",
            "post_reference",
            "store_reference",
            "user_reference",
            "icon",
            "handle",
            "post_text",
            "created_date",
            "is_deleted",
            "like_count",
            "is_liked",
            "post_images",
            "analytics_view_count"
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        non_deleted_images = [image for image in representation['post_images'] if not image['is_deleted']]
        representation['post_images'] = non_deleted_images
        return representation

    def get_like_count(self, instance):
        like_count = PostLikes.objects.filter(post_reference=instance.post_reference, is_liked=True).count()
        return like_count

    def get_liked(self, instance):
        visitor_reference = self.context.get('visitor_reference')
        if visitor_reference.startswith('U'):
            # Check if the post is liked by the visitor
            is_liked = PostLikes.objects.filter(post_reference=instance.post_reference,
                                                liked_by_user=visitor_reference, is_liked=True).exists()
            return is_liked

        else:
            is_liked = PostLikes.objects.filter(post_reference=instance.post_reference,
                                                liked_by_store=visitor_reference, is_liked=True).exists()
            return is_liked

    def get_icon(self, instance):
        icon = None  # Initialize icon as None

        if instance.user_reference:
            user = instance.user_reference  # Use "user_reference" to get the related user instance
            if user and user.icon:
                icon = user.icon.url  # Check if user has an icon and get its URL

        if instance.store_reference:
            store = instance.store_reference  # Use "store_reference" to get the related store instance
            if store and store.icon:
                icon = store.icon.url  # Check if store has an icon and get its URL

        return icon

    def get_handle(self,instance):
        handle = None
        if instance.user_reference:
            user = instance.user_reference  # Use "user_reference" to get the related user instance
            if user:
                handle = user.user_name

        if instance.store_reference:
            store = instance.store_reference  # Use "store_reference" to get the related store instance
            if store:
                handle = store.storehandle
        return handle


class SinglePostSerializer(serializers.ModelSerializer):
    class Meta:
        model = Posts
        fields = '__all__'


class PostLikesSerializer(serializers.ModelSerializer):
    class Meta:
        model = PostLikes
        fields = '__all__'


class StorySerializer(serializers.ModelSerializer):
    entity_reference = serializers.CharField(write_only=True, required=True)
    product_reference = serializers.CharField(write_only=True, required=False)
    story_reference = serializers.CharField(read_only=True)

    class Meta:
        model = Story
        fields = [
            'story_reference',
            'title',
            'sections',
            'created_at',
            'updated_at',
            'entity_reference',
            'product_reference',
            'tagged_content',
            'like_count',
            'comment_count',
            'repost_count',
            'repost_plus_count',
            'save_count',
            'share_count'
        ]
        read_only_fields = ['story_reference', 'created_at', 'updated_at', 'like_count', 'comment_count', 'repost_count', 'repost_plus_count', 'save_count', 'share_count']

    def validate_sections(self, value):
        """
        Validate the sections data structure
        """
        if not isinstance(value, list):
            raise serializers.ValidationError("Sections must be a list")

        valid_types = ['H1', 'H2', 'body', 'image', 'divider']
        order_numbers = set()

        for section in value:
            if not isinstance(section, dict):
                raise serializers.ValidationError("Each section must be a dictionary")

            if 'type' not in section:
                raise serializers.ValidationError("Each section must have a type")

            if 'order' not in section:
                raise serializers.ValidationError("Each section must have an order")

            if section['order'] in order_numbers:
                raise serializers.ValidationError("Order numbers must be unique")

            order_numbers.add(section['order'])

            if section['type'] not in valid_types:
                raise serializers.ValidationError(f"Invalid section type. Must be one of {valid_types}")

            if section['type'] == 'image':
                if 'images' not in section:
                    raise serializers.ValidationError("Image section must have images array")

                image_orders = set()
                for image in section['images']:
                    if 'image_url' not in image or 'caption' not in image or 'order' not in image:
                        raise serializers.ValidationError("Each image must have image_url, caption, and order")

                    if image['order'] in image_orders:
                        raise serializers.ValidationError("Image order numbers must be unique within a section")

                    image_orders.add(image['order'])
            else:
                if 'content' not in section:
                    raise serializers.ValidationError(f"Section type {section['type']} must have content")

        return value


    def update(self, instance, validated_data):
        """
        Update and return an existing Story instance, given the validated data.
        """
        instance.title = validated_data.get('title', instance.title)
        instance.sections = validated_data.get('sections', instance.sections)
        instance.save()
        return instance

    def create(self, validated_data):
        # Remove the extra fields before creating Story
        entity_reference = validated_data.pop('entity_reference', None)
        product_reference = validated_data.pop('product_reference', None)

        # Create the Story instance
        story = Story.objects.create(**validated_data)
        story.save()

        return story


class StoryViewSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField("get_image")
    
    class Meta:
        model = Story
        fields = ['story_reference', 'title', 'image', 'created_at', 'updated_at']
    
    def get_image(self, instance):
        """Get first image URL from first image section"""
        # Find first image section
        image_section = next(
            (section for section in instance.sections 
             if section.get('type') == 'image' and section.get('images')), 
            None
        )
        
        # Return first image URL if found
        return image_section['images'][0]['image_url'] if image_section else None

