from rest_framework import serializers
from users.user_api.models import User
from rest_framework.validators import UniqueValidator
from rest_framework.exceptions import ValidationError


class EmailUserCheckSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

    class Meta:
        fields = ["email"]


class EmailSignInSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    access_token = serializers.CharField(required=False, allow_null=True)

    class Meta:
        fields = ["email", "access_token"]


class EmailVerifySerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    email_otp = serializers.CharField(max_length=6, required=False, allow_null=True)
    access_token = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs):
        email = attrs.get('email')
        email_otp = attrs.get('email_otp')
        access_token = attrs.get('access_token')

        # Either OTP or access token must be provided
        if not email_otp and not access_token:
            raise ValidationError({"message": "Either email OTP or Google access token is required"})

        return attrs


class EmailResendOtpSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

    class Meta:
        fields = ["email"]
