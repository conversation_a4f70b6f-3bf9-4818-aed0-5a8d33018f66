from django import template
from django.conf import settings
from ..utils import get_seo_url

register = template.Library()


@register.simple_tag
def seo_url(content_type, reference):
    """
    Template tag to generate SEO URLs
    Usage: {% seo_url 'product' product.product_reference %}
    """
    return get_seo_url(content_type, reference)


@register.simple_tag
def canonical_url(content_type, reference):
    """
    Template tag to generate canonical URLs
    Usage: {% canonical_url 'product' product.product_reference %}
    """
    return get_seo_url(content_type, reference)


@register.inclusion_tag('seo/meta_tags.html')
def render_meta_tags(title, description, image_url=None, canonical_url=None):
    """
    Render meta tags for SEO
    Usage: {% render_meta_tags title description image_url canonical_url %}
    """
    return {
        'title': title,
        'description': description[:160] if description else '',
        'image_url': image_url,
        'canonical_url': canonical_url,
        'site_domain': settings.SITE_DOMAIN,
    }


@register.filter
def truncate_description(value, length=160):
    """
    Truncate description for meta tags
    """
    if not value:
        return ''
    return value[:length] + '...' if len(value) > length else value
