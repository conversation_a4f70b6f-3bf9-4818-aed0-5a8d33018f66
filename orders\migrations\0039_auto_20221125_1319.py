# Generated by Django 3.2.13 on 2022-11-25 07:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0038_orderpaymentdetails_is_suborder_deleted"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderlifecycle",
            name="additional_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="delivery_by_logistic_partner",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="logistic_partner",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="self_delivery_by_store",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="tracking_number",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="suborder",
            name="additional_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="suborder",
            name="delivery_by_logistic_partner",
            field=models.Boolean<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name="suborder",
            name="logistic_partner",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="suborder",
            name="self_delivery_by_store",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="suborder",
            name="tracking_number",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
