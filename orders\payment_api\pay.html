<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Page</title>
</head>
<body>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <button id="pay-button">Pay Now</button>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            document.getElementById('pay-button').addEventListener('click', function () {
                var options = {
                    "key": "{{ key }}",
                    "amount": "{{ amount }}",
                    "currency": "{{ currency }}",
                    "name": "{{ name }}",
                    "description": "{{ description }}",
                    "image": "{{ image }}",
                    "order_id": "{{ order_id }}",
                    "handler": function (response) {
                        var query = `payment_id=${response.razorpay_payment_id}&order_id=${response.razorpay_order_id}&signature=${response.razorpay_signature}`;
                        window.location.href = `{{ callback_url }}?${query}`;
                    },
                    "prefill": {
                        "name": "{{ prefill.name }}",
                        "email": "{{ prefill.email }}",
                        "contact": "{{ prefill.contact }}"
                    },
                    "notes": JSON.parse('{{ notes|safe }}'), // ✅ Fix applied
                    "theme": {
                        "color": "{{ theme.color }}"
                    }
                };

                var rzp1 = new Razorpay(options);
                rzp1.open();

                rzp1.on('payment.failed', function (response) {
                    console.error('Payment Failed:', response);
                });
            });
        });
    </script>
</body>
</html>
