# Generated by Django 3.2.13 on 2023-06-01 11:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0087_orderpayout_tds_calculated'),
    ]

    operations = [
        migrations.RenameField(
            model_name='refundedamount',
            old_name='status',
            new_name='refund_status',
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='df_reversal',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='df_settings',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='df_type',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='is_shipped',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='product_df',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='product_price',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='return_cost_on',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='suborder_status',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='transaction_fee',
            field=models.FloatField(default=0),
        ),
        migrations.AlterField(
            model_name='refundedamount',
            name='refunded_amount',
            field=models.FloatField(default=0),
        ),
    ]
