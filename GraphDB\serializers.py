from rest_framework import serializers
from .models import Neo4jPost, Neo4jPostImage




class Neo4jPostImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Neo4jPostImage
        fields = ['post_image_id', 'post_image', 'is_deleted']


class Neo4jPostSerializer(serializers.ModelSerializer):
    # Serializer fields for Neo4jPost
    post_reference = serializers.CharField(source='post_reference')
    post_text = serializers.CharField(source='post_text')
    created_date = serializers.DateTimeField(source='created_date')
    is_deleted = serializers.BooleanField(source='is_deleted')
    like_count = serializers.IntegerField(source='get_like_count')
    user_like_status = serializers.BooleanField(source='get_user_like_status')
    post_images = Neo4jPostImageSerializer(many=True, source='get_post_images')

    class Meta:
        model = Neo4jPost
        fields = '__all__'
