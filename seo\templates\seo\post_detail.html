{% extends 'seo/base.html' %}

{% block schema %}
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "SocialMediaPosting",
    "headline": "{{ post.post_title }}",
    "author": {
        "@type": "{% if entity.storehandle %}Organization{% else %}Person{% endif %}",
        "name": "{{ entity.store_name|default:entity.user_name }}",
        "url": "{{ request.build_absolute_uri }}"
    },
    "datePublished": "{{ post.created_at|date:'c' }}",
    "articleBody": "{{ post.post_text }}",
    {% if post.post_images.exists %}
    "image": "{{ post.post_images.first.post_image.url }}"
    {% endif %}
}
</script>
{% endblock %}

{% block content %}
<div class="entity-header">
    {% if entity.icon %}
    <img src="{{ entity.icon.url }}" alt="{{ entity.store_name|default:entity.user_name }} icon" class="entity-icon">
    {% endif %}
    <div class="entity-info">
        {% if entity.storehandle %}
            <h1>{{ entity.store_name }}</h1>
            {% if entity.store_desc %}
            <p>{{ entity.store_desc }}</p>
            {% endif %}
            <p>Store Handle: @{{ entity.storehandle }}</p>
        {% else %}
            <h1>{{ entity.user_name }}</h1>
            <p>User Handle: @{{ entity.user_name }}</p>
        {% endif %}
    </div>
</div>

<article class="post-detail">
    {% if post.post_title %}
    <header>
        <h2>{{ post.post_title }}</h2>
        <p class="post-meta">Posted {{ post.created_at|timesince }} ago</p>
    </header>
    {% endif %}

    {% if post.post_images.exists %}
    <div class="post-images">
        {% for image in post.post_images.all %}
        <img src="{{ image.post_image.url }}" alt="Post image {{ forloop.counter }}" class="post-image">
        {% endfor %}
    </div>
    {% endif %}

    {% if post.post_text %}
    <div class="post-content">
        {{ post.post_text|linebreaks }}
    </div>
    {% endif %}

    {% if post.hashtags %}
    <div class="hashtags">
        <h2>Tags</h2>
        {% for tag in hashtags %}
        <span class="tag">#{{ tag }}</span>
        {% endfor %}
    </div>
    {% endif %}

    {% if post_comments %}
    <section class="comments">
        <h3>Comments</h3>
        {% for comment in post_comments %}
        <div class="comment">
            <p>{{ comment.comment_text }}</p>
            <p class="comment-meta">Posted {{ comment.created_date|timesince }} ago</p>
        </div>
        {% endfor %}
    </section>
    {% endif %}
</article>
{% endblock %}
