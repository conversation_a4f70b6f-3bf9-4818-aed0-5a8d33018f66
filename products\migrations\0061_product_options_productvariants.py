# Generated by Django 4.2.23 on 2025-06-23 10:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0060_auto_20250612_1646'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='options',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.CreateModel(
            name='ProductVariants',
            fields=[
                ('product_variantid', models.AutoField(primary_key=True, serialize=False)),
                ('variant_reference', models.CharField(max_length=25, unique=True)),
                ('combinations', models.JSONField(blank=True, default=dict, null=True)),
                ('mrp_price', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('selling_price', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('modified_date', models.DateField(auto_now=True)),
                ('product_reference', models.ForeignKey(db_column='product_reference', on_delete=django.db.models.deletion.CASCADE, related_name='product_variants', to='products.product', to_field='product_reference')),
            ],
            options={
                'verbose_name_plural': 'product variants',
                'db_table': '"product"."product_variants"',
            },
        ),
    ]
