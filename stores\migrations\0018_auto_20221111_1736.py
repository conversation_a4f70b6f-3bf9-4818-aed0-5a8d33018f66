# Generated by Django 3.2.13 on 2022-11-11 12:06

from django.db import migrations


def generate_product_reference_from_product_ids_in_delivery_settings(
    apps, schema_editor
):
    DeliverySettings = apps.get_model("stores", "DeliverySettings")
    Product = apps.get_model("products", "Product")

    for item in DeliverySettings.objects.all():
        if Product.objects.filter(productid=item.productid).exists():
            item.product_reference = Product.objects.values_list(
                "product_reference", flat=True
            ).get(productid=item.productid)
        else:
            item.product_reference = None
        item.save(update_fields=["product_reference"])


def generate_product_reference_from_product_ids_in_refund_and_warranty(
    apps, schema_editor
):
    RefundAndWarranty = apps.get_model("stores", "RefundAndWarranty")
    Product = apps.get_model("products", "Product")

    for item in RefundAndWarranty.objects.all():
        if Product.objects.filter(productid=item.productid).exists():
            item.product_reference = Product.objects.values_list(
                "product_reference", flat=True
            ).get(productid=item.productid)
        else:
            item.product_reference = None
        item.save(update_fields=["product_reference"])


def reverse_func_delivery_settings(apps, schema_editor):
    # Reverse the change that has done in above func.
    DeliverySettings = apps.get_model("stores", "DeliverySettings")
    for item in DeliverySettings.objects.all():
        item.product_reference = None
        item.save(update_fields=["product_reference"])


def reverse_func_refund_and_warranty(apps, schema_editor):
    # Reverse the change that has done in above func.
    RefundAndWarranty = apps.get_model("stores", "RefundAndWarranty")

    for item in RefundAndWarranty.objects.all():
        item.product_reference = None
        item.save(update_fields=["product_reference"])


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0017_auto_20221108_1348"),
    ]

    operations = [
        migrations.RunPython(
            generate_product_reference_from_product_ids_in_delivery_settings,
            reverse_func_delivery_settings,
        ),
        migrations.RunPython(
            generate_product_reference_from_product_ids_in_refund_and_warranty,
            reverse_func_refund_and_warranty,
        ),
    ]
