import logging
import time
import base64
import json
import re
from django.http import JsonResponse
from django.urls import resolve
from graphql import GraphQLError
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken
from general.models import Request
from django.utils.deprecation import MiddlewareMixin
from common.util.support_helper import extract_input_arguments_with_values

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class SwadesicMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.log_prefixes = [
            '/general', '/store', '/order', '/product', '/common', '/user',
            '/graphDBql', '/content', '/lean'
        ]
        self.jwt_excluded_paths = [
            '/user/userlogin/', '/media/', '/data/', '/api/token/refresh',
            '/user/newuserlogin/', '/user/googlelogin/', '/user/signin/',
            '/user/check_user_info/', '/user/verify_signin/', '/user/resend_otp/',
            '/common/app_meta_data/', '/common/app_configurations/', '/ws/',
            '/order/update-swadesic-shipping-details-wh/', '/user/email/check_user_info/',
            '/user/email/signin/', '/user/email/verify_signin/', '/user/email/resend_otp/',
            '/seo/',
            '/seo/story/*/',
            '/seo/product/*/',
            '/seo/post/*/',
            '/seo/store/*/',
            '/favicon.ico'
        ]
        self.static_user_allowed_paths = {
            '/graphdb/check_access/': self.handle_check_access_request,
            '/store/getting_reference_from_handle/': self.handle_getting_reference_request,
            '/product/get_product_reference_from_slug/': self.handle_get_product_reference_request
        }

    def __call__(self, request):
        start_perf = time.perf_counter()

        if request.path.startswith('/ws/'):
            return self.get_response(request)

        is_encryption = request.headers.get('Cache-Control-Speed')
        response = None

        if is_encryption is None:
            response = self.handle_no_encryption(request)
        elif is_encryption == 'medium':
            if not self.process_request_body(request):
                return self.error_response('Invalid JSON data in request body.', 400, is_encryption)
            if self.is_excluded_path(request):
                response = self.handle_excluded_path(request, is_encryption)
            elif not self.authenticate_request(request):
                return self.error_response('Authentication credentials were not provided.', 401, is_encryption)
            elif self.is_static_user(request):
                response = self.handle_static_user(request, is_encryption)
            else:
                request._jwt_middleware_processed = True
                response = self.encode_response(self.get_response(request)) if is_encryption else self.get_response(request)

        if not response:
            response = self.get_response(request)

        self.log_request(request, response, start_perf, time.perf_counter())
        print(f"Swadesic Middleware Total time: {time.perf_counter() - start_perf:.4f} seconds")
        return response

    def handle_no_encryption(self, request):
        start_perf = time.perf_counter()
        if self.is_excluded_path(request):
            return self.get_response(request)
        if not self.authenticate_request(request):
            return self.error_response('Authentication credentials were not provided.', 401)
        if self.is_static_user(request):
            return self.handle_static_user(request)
        print(f"handle_no_encryption time: {time.perf_counter() - start_perf:.4f} seconds")
        return self.get_response(request)

    def is_excluded_path(self, request):
        return any(request.path.startswith(path) for path in self.jwt_excluded_paths)

    def authenticate_request(self, request):
        start_perf = time.perf_counter()
        try:
            user, token = JWTAuthentication().authenticate(request)
            if not user:
                print(f"authenticate_request time: {time.perf_counter() - start_perf:.4f} seconds")
                return False
            request.user = user
            return True
        except InvalidToken:
            return False

    def is_static_user(self, request):
        return getattr(request.user, 'user_reference', None) == 'U9999999999999'

    def handle_static_user(self, request, is_encryption=None):
        start_perf = time.perf_counter()
        if request.path in self.static_user_allowed_paths:
            print(f"handle_static_user time: {time.perf_counter() - start_perf:.4f} seconds")
            return self.static_user_allowed_paths[request.path](request, is_encryption)
        elif request.path.startswith('/graphDBql/'):
            print(f"handle_static_user time: {time.perf_counter() - start_perf:.4f} seconds")
            return self.handle_graphql_request(request, is_encryption)
        elif request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            print(f"handle_static_user time: {time.perf_counter() - start_perf:.4f} seconds")
            return self.error_response('POST, PUT, PATCH, and DELETE methods are not allowed for unregistered users.', 401, is_encryption)
        return self.encode_response(self.get_response(request)) if is_encryption else self.get_response(request)

    def handle_graphql_request(self, request, is_encryption=None):
        start_perf = time.perf_counter()
        try:
            data = json.loads(request.body.decode('utf-8'))
            if 'query IntrospectionQuery' not in data.get("query", "") and 'query' not in data:
                if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
                    print(f"handle_graphql_request time: {time.perf_counter() - start_perf:.4f} seconds")
                    return self.error_response('POST, PUT, PATCH, and DELETE methods are not allowed for unregistered users.', 401, is_encryption)
        except json.JSONDecodeError:
            return self.error_response('Invalid JSON data in request body.', 400, is_encryption)
        request._jwt_middleware_processed = True
        print(f"handle_graphql_request time: {time.perf_counter() - start_perf:.4f} seconds")
        return self.encode_response(self.get_response(request)) if is_encryption else self.get_response(request)

    def handle_check_access_request(self, request, is_encryption=None):
        check_access_start_perf = time.perf_counter()
        response = self.encode_response(self.get_response(request)) if is_encryption else self.get_response(request)
        print(f"handle_check_access_request time: {time.perf_counter() - check_access_start_perf:.4f} seconds")
        return response

    def handle_getting_reference_request(self, request, is_encryption=None):
        getting_reference_start_perf = time.perf_counter()
        response = self.encode_response(self.get_response(request)) if is_encryption else self.get_response(request)
        print(f"handle_getting_reference_request time: {time.perf_counter() - getting_reference_start_perf:.4f} seconds")
        return response
    
    def handle_get_product_reference_request(self, request, is_encryption=None):
        getting_product_reference_start_perf = time.perf_counter()
        response = self.encode_response(self.get_response(request)) if is_encryption else self.get_response(request)
        print(f"handle_get_product_reference_request time: {time.perf_counter() - getting_product_reference_start_perf:.4f} seconds")
        return response

    def handle_excluded_path(self, request, is_encryption=None):
        excluded_path_start_perf = time.perf_counter()
        if request.path.startswith('/media/'):
            print(f"handle_excluded_path time: {time.perf_counter() - excluded_path_start_perf:.4f} seconds")
            return self.get_response(request)
        print(f"handle_excluded_path time: {time.perf_counter() - excluded_path_start_perf:.4f} seconds")
        return self.encode_response(self.get_response(request)) if is_encryption else self.get_response(request)

    def encode_response(self, response):
        encode_response_start_perf = time.perf_counter()
        response =JsonResponse({'buds': base64.b32encode(response.content).decode('utf-8')}, status=response.status_code)
        print(f"encode_response time: {time.perf_counter() - encode_response_start_perf:.4f} seconds")
        return response

    def error_response(self, message, status_code, is_encryption=None):
        error_response_start_perf = time.perf_counter()
        resp = JsonResponse({'error': message}, status=status_code)
        response = self.encode_response(resp) if is_encryption else resp
        print(f"error_response time: {time.perf_counter() - error_response_start_perf:.4f} seconds")
        return response

    def process_request_body(self, request):
        start_perf = time.perf_counter()
        try:
            content_type = request.headers.get('Content-Type', '')
            if 'form-data' not in content_type and request.body:
                data = json.loads(request.body.decode('utf-8'))
                if not data.get('query') and 'basket' in data:
                    decoded = base64.b32decode(data['basket']).decode('utf-8')
                    request._body = decoded.encode('utf-8')
            print(f"process_request_body time: {time.perf_counter() - start_perf:.4f} seconds")
            return True
        except Exception:
            return False

    def log_request(self, request, response, start_time, end_time):
        start_perf = time.perf_counter()
        if not any(request.get_full_path().startswith(p) for p in self.log_prefixes):
            return

        try:
            request_data = getattr(request, '_body', request.body)
            request_json = json.loads(request_data) if request_data else ""
        except:
            request_json = ""

        try:
            user_reference = getattr(request.user, 'user_reference', None)
            exceptions = getattr(response, '_exceptions', [])
            async_msg = ''.join(e.get("message", "") for e in exceptions) if exceptions else None
            async_details = ''.join(e.get("data", "") for e in exceptions) if exceptions else None

            Request.objects.create(
                endpoint=request.get_full_path(),
                response_status_code=response.status_code,
                request_method=request.method,
                remote_address=self.get_client_ip(request),
                start_time=start_time,
                end_time=end_time,
                exec_time=int((end_time - start_time) * 1000),
                request_body=request_json,
                http_user_agent=request.META.get('HTTP_USER_AGENT'),
                query_string=request.META.get('QUERY_STRING'),
                user_reference=user_reference,
                async_flow_exception=async_msg,
                async_flow_exception_details=async_details
            )
            print(f"log_request time: {time.perf_counter() - start_perf:.4f} seconds")
        except Exception as e:
            logger.warning(f"Failed to log request: {e}")

    def get_client_ip(self, request):
        return request.META.get('HTTP_X_FORWARDED_FOR', '').split(',')[0] or request.META.get('REMOTE_ADDR')



class InputValidationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response


    def validate_user_reference(self, user_reference):
        """
        Validates a user reference in the format 'U<13 digits>'.
        Example: U1234567890123
        """
        return bool(re.match(r'^U\d{13}$', user_reference)) or bool(re.match(r'^U\d{9}$', user_reference))


    def validate_key_values(self,key_value_pair_list):
        """
        Validates a user reference in the format 'U<13 digits>'.
        Example: U1234567890123
        """
        final_list = []
        for key, value in key_value_pair_list:
            validated_value = True
            if key in ['user_reference', 'user_follower', ]:
                validated_value = self.validate_user_reference(value)

            final_list.append(validated_value)

            # Logging inputs which didn't qualify validation
        invalid_key_values = 'Invalid Inputs received for these fields:' + str(
            [key_value_pair_list[i[0]] for i in enumerate(final_list) if not i[1]])

        return all(final_list), invalid_key_values

    def validate_inputs(self, request):
        try:
            # Check if the request is for the user endpoint
            url_parameters = {}
            post_args = {}
            if request.path.startswith('/user/'):
                # Check URL parameters
                url_parameters = resolve(request.path_info).kwargs

                # POST request body arguments
                # TODO add layer to handle images during validation
                post_args = json.loads(request.body) if request.body else {}

            if request.path.startswith('/graphDBql/'):
                url_parameters = resolve(request.path_info).kwargs

                # POST request body arguments
                gql_query = json.loads(request.body) if request.body else {}

                query = gql_query['query']
                post_args = extract_input_arguments_with_values(query)

            # Create a list of key-value pairs as tuples combining x and y
            combined_list = [(key, value) for key, value in url_parameters.items()] + [(key, value) for key, value in
                                                                                       post_args.items()]

            validation_result, invalid_key_values = self.validate_key_values(
                key_value_pair_list=combined_list) if combined_list else (True, '')
            if validation_result:
                return None
            else:
                return JsonResponse({'error': 'Invalid user reference', 'data': invalid_key_values}, status=400)
        except:
            return None


    def __call__(self, request):
        # Pre-process request
        response = self.validate_inputs(request)
        if response:
            return response

        # Call the next middleware or the view
        response = self.get_response(request)

        # Post-process response
        return response




