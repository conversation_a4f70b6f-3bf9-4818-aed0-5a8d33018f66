from django.urls import path
from .views import *

urlpatterns = [
    path('store/<str:store_reference>/subscription-plans/',
         GetStoreSubscriptionPlanView.as_view(),
         name='get-store-subscription-plans'),
    path('user/<str:user_reference>/subscription-plans/',
         GetUserSubscriptionPlanView.as_view(),
         name='get-user-subscription-plans'),
    path('create-subscription/', CreateSubscriptionView.as_view(), name='create-subscription'),
    path('check-subscription-status/<str:subscription_id>/', CheckSubscriptionStatusView.as_view(),
         name='check-subscription-status'),
    path('subscription/invoices/',
         GetSubscriptionInvoicesView.as_view(),
         name='get-subscription-invoices'),
         
    path('process-payments/', 
         ProcessSubscriptionPaymentsView.as_view(),
         name='process-subscription-payments'),
    path('cancel-subscription/',
         CancelSubscriptionView.as_view(),
         name='cancel-subscription'),
]
