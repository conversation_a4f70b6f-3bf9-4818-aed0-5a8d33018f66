from .models import (
    StoreShippingAccountBalance,
)
from django.core.exceptions import ObjectDoesNotExist
from datetime import datetime


def get_courier_details(shiprocket, store_reference, pickup_postcode, delivery_postcode, courier_id, weight=0.5, cod=0):
    """
    Get courier details between pickup and delivery locations.
    """
    try:
        # Validate required parameters
        if not all([pickup_postcode, delivery_postcode, store_reference]):
            return {'error': 'Pickup postcode, delivery postcode, and store reference are required'}

        # Call Shiprocket API to check serviceability
        response = shiprocket.check_serviceability(
            pickup_postcode=pickup_postcode,
            delivery_postcode=delivery_postcode,
            cod=cod,
            weight=weight
        )

        def filter_courier_data(courier):
            """Filter and return only required fields from courier data."""
            rate = courier.get('rate', 0)
            return {
                'courier_name': courier.get('courier_name'),
                'courier_company_id': courier.get('courier_company_id'),
                'pickup_time': courier.get('seconds_left_for_pickup'),
                'rate': rate,
                'estimated_delivery_days': courier.get('estimated_delivery_days'),
                'etd': courier.get('etd'),
                'cutoff_time': courier.get('cutoff_time'),
                'pickup_availability': courier.get('pickup_availability'),
                'realtime_tracking': courier.get('realtime_tracking'),
                'performance': {
                    'pickup': courier.get('pickup_performance'),
                    'rto': courier.get('rto_performance'),
                    'tracking': courier.get('tracking_performance')
                },
                'delivery_boy_contact': courier.get('delivery_boy_contact'),
                'rto_charges': courier.get('rto_charges'),
                'call_before_delivery': courier.get('call_before_delivery'),
            }

        available_couriers = response.get('data', {}).get('available_courier_companies', [])
        selected_courier = next((c for c in available_couriers if c["courier_company_id"] == int(courier_id)), None)
        # Extract and format the required fields
        return {
            'currency': response.get('currency'),
            'shiprocket_recommended_courier_id': response.get('data', {}).get('shiprocket_recommended_courier_id'),
            'selected_courier': [
                filter_courier_data(selected_courier)
            ]
        }
    except Exception as e:
        return {'error': str(e)}


def convert_date_format(date_str):
    """Convert date from 'MMM DD, YYYY' to 'DD-MM-YYYY' format."""
    try:
        # Parse the date string
        date_obj = datetime.strptime(date_str, '%b %d, %Y')
        # Convert to desired format
        return date_obj.strftime('%d-%m-%Y')
    except (ValueError, TypeError):
        return None

def map_shiprocket_status_to_swadesic(sr_status_label: str) -> str:
    """
    Maps Shiprocket's sr-status-label to Swadesic order status.

    :param sr_status_label: The status label received from Shiprocket webhook.
    :return: The corresponding Swadesic order status.
    """
    sr_status_label = sr_status_label.upper()
    status_mapping = {
        "SCHEDULED_FOR_PICKUP": ["MANIFEST GENERATED"],
        "DELIVERY_IN_PROGRESS": ["PICKED UP", "SHIPPED", "IN TRANSIT", "OUT FOR DELIVERY"],
        "ORDER_DELIVERED": ["DELIVERED"],
        "RETURN_CONFIRMED": ["RTO INITIATED"],
        "RETURN_IN_PROGRESS": ["RTO_IN_TRANSIT"],
        "RETURNED_TO_SELLER": ["RTO DELIVERED", "RETURNED", "RTO ACKNOWLEDGED"],
        "ORDER_CANCELLED": ["CANCELLED"],
        "DELIVERY_FAILED": ["LOST", "DAMAGED", "FAILED", "EXCEPTION"],
        "WAITING_FOR_CONFIRMATION": ["PENDING", "INFO RECEIVED"],
        "REFUND_HOLD": ["HOLD"]
    }

    # Iterate through the mapping dictionary to find the corresponding Swadesic status
    for swadesic_status, sr_labels in status_mapping.items():
        if sr_status_label in sr_labels:
            return swadesic_status

    return "UNKNOWN_STATUS"
