import django
import os
import sys
from django.utils.text import slugify
import random
import string

# Add the parent directory (one level above `swadesic`) to sys.path
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(BASE_DIR)

# Set the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "swadesic.settings")

# Initialize Django
django.setup()
from products.models import Product

def generate_unique_code(product_name):
    base_code = ''.join(e for e in product_name.upper() if e.isalnum())[:10]
    random_suffix = ''.join(random.choices(string.digits, k=4))
    return f"{base_code[:6]}-{random_suffix}"

def populate_slug_and_code():
    products = Product.objects.filter(product_slug__isnull=True) | Product.objects.filter(product_slug='')
    updated_count = 0

    for product in products:
        updated = False

        # Generate slug
        if not product.product_slug:
            product.product_slug = slugify(product.product_name)
            updated = True

        # Generate code
        if not product.product_code:
            product.product_code = generate_unique_code(product.product_name)
            updated = True

        if updated:
            product.save(update_fields=["product_slug", "product_code"])
            updated_count += 1

    print(f"Updated {updated_count} products with slug and code.")

if __name__ == "__main__":
    populate_slug_and_code()
