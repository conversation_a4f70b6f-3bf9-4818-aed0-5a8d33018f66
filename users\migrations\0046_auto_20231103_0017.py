# Generated by Django 3.2.22 on 2023-11-02 18:47

from django.db import migrations, models
import django.db.models.deletion


def populate_new_columns_with_references(apps, schema_editor):
    UserStore = apps.get_model('users', 'UserStore')
    User = apps.get_model('users', 'User')
    Store = apps.get_model('stores', 'Store')

    for user_store in UserStore.objects.all():
        user_instance = None
        store_instance = None

        if user_store.userid is not None and isinstance(user_store.userid, int):
            try:
                user_instance = User.objects.get(pk=int(user_store.userid))
            except User.DoesNotExist:
                pass


        if user_store.storeid is not None and isinstance(user_store.storeid, int):
            try:
                store_instance = Store.objects.get(pk=int(user_store.storeid))
            except Store.DoesNotExist:
                pass

        user_store.user_supporter = user_instance if user_instance else None
        user_store.store_reference = store_instance if store_instance else None
        user_store.store_reference = None

        user_store.save()


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0045_merge_20231005_1006'),
        ('users', '0045_auto_20231101_2232'),
    ]

    operations = [

        migrations.AddField(
            model_name='userstore',
            name='store_reference',
            field=models.ForeignKey(blank=True, db_column='store_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='supports', to='stores.store'),
        ),
        migrations.AddField(
            model_name='userstore',
            name='store_supporter',
            field=models.ForeignKey(blank=True, db_column='store_supporter', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='storesupporter', to='stores.store'),
        ),
        migrations.AddField(
            model_name='userstore',
            name='user_supporter',
            field=models.ForeignKey(blank=True, db_column='user_supporter', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='usersupporter', to='users.user'),
        ),

        migrations.RunPython(populate_new_columns_with_references),

        migrations.RemoveField(
            model_name='userstore',
            name='storeid',
        ),
        migrations.RemoveField(
            model_name='userstore',
            name='userid',
        ),
    ]
