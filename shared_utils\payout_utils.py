
from decimal import Decimal
from django.db.models import Q
from orders.order_api.models import OrderConfiguration, RefundedAmount, Order, SubOrder
from orders.payout_api.models import PayoutTransactions, PayoutBalance,OrderPayout

import logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)




#########################
def update_payout_after_cancel_or_return(suborder_number: str) -> None:
    """
    Updates the payout details for a given suborder number after a cancellation or return.

    Args:
        suborder_number (str): The suborder number for which the payout details need to be updated.

    Returns:
        None
    """
    logger.info("Updating payout after cancel or return for suborder %s", suborder_number)

    order_payout = get_order_payout(suborder_number)
    order_config = get_order_configuration()
    refunded_amount = get_refunded_amount(suborder_number)

    update_payout_calculations(order_payout, order_config, refunded_amount)
    save_order_payout(order_payout)

def get_order_payout(suborder_number: str) -> OrderPayout:
    """Retrieves the OrderPayout instance for the given suborder number."""
    return OrderPayout.objects.get(suborder_number=suborder_number)

def get_order_configuration() -> OrderConfiguration:
    """Retrieves the first OrderConfiguration instance."""
    return OrderConfiguration.objects.first()

def get_refunded_amount(suborder_number: str) -> Decimal:
    """Retrieves the refunded amount for the given suborder number."""
    refund = RefundedAmount.objects.filter(suborder_reference=suborder_number).first()
    return refund.refunded_amount if refund else Decimal('0')

def update_payout_calculations(order_payout: OrderPayout, order_config: OrderConfiguration, refunded_amount: Decimal) -> None:
    """Updates the payout calculations for the given OrderPayout instance."""
    order_amount = order_payout.order_amount
    order_number = order_payout.order_number

    tds_calculated = calculate_tds(order_amount, refunded_amount)
    transaction_fee_calculated, transaction_fee_percentage = calculate_transaction_fee_for_suborder(order_number, order_amount)
    commission_calculated = calculate_commission(order_number, order_config, refunded_amount)
    payout_amount = calculate_payout_amount(order_amount, refunded_amount, tds_calculated, transaction_fee_calculated, commission_calculated)

    update_order_payout_fields(order_payout, tds_calculated, transaction_fee_calculated, transaction_fee_percentage,
                            commission_calculated, order_config, payout_amount, order_amount)

def calculate_tds(order_amount: Decimal, refunded_amount: Decimal) -> Decimal:
    """Calculates the TDS (Tax Deducted at Source) based on the order amount and refunded amount."""
    tds = (Decimal(order_amount) - Decimal(refunded_amount)) * Decimal('0.01')
    return round(tds, 2)

def calculate_transaction_fee_for_suborder(order_number: str, order_amount: Decimal) -> tuple[Decimal, Decimal]:
    """Calculates the transaction fee based on the payment mode and order amount."""
    order = Order.objects.filter(order_number=order_number).first()
    transaction_fee_percentage = order.pg_transctionfee_with_tax_perc
    transaction_fee = order_amount * (transaction_fee_percentage / 100)
    return round(transaction_fee, 2), transaction_fee_percentage

def calculate_commission(order_number: str, order_config: OrderConfiguration, refunded_amount: Decimal) -> Decimal:
    """Calculates the commission fee for the order.

    This function calculates the commission fee for an order based on the order configuration and the refunded amount. If there are no successful payouts for the order and the refunded amount is 0, the function returns the commission fee minus the commission fee discount. Otherwise, it returns 0.

    Args:
        order_number (str): The order number.
        order_config (OrderConfiguration): The order configuration.
        refunded_amount (Decimal): The refunded amount for the order.

    Returns:
        Decimal: The calculated commission fee.
    """

    commission_fee = order_config.commission_fee
    commission_fee_discount = order_config.commission_fee_discount
    cancelled_statuses = [
        SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
        SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
        SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED,
        SubOrder.Suborder_Status.RETURN_CONFIRMED,
        SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
        SubOrder.Suborder_Status.RETURNED_TO_SELLER,
    ]
# TODO: Should we not use the below one?
#   cancelled_statuses = [
#       SubOrder.Suborder_Status.ORDER_CANCELLED,
#       SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
#       SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
#       SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED,
#       SubOrder.Suborder_Status.RETURN_REQUESTED,
#       SubOrder.Suborder_Status.RETURN_CONFIRMED,
#       SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
#       SubOrder.Suborder_Status.RETURNED_TO_SELLER,
#       SubOrder.Suborder_Status.RETURN_FAILED,
#       SubOrder.Suborder_Status.REFUND_HOLD,
#       SubOrder.Suborder_Status.REFUNDED,
#   ]
    

    if OrderPayout.objects.filter(
        Q(order_number=order_number) &
        Q(payout_status="AMOUNT_RELEASED") &
        ~Q(suborder_number__suborder_status__in=cancelled_statuses)
    ).count() == 0 and refunded_amount == 0:
        return -abs(commission_fee) + commission_fee_discount
    return Decimal('0')

def calculate_payout_amount(order_amount: Decimal, refunded_amount: Decimal, tds_calculated: Decimal,
                            transaction_fee_calculated: Decimal, commission_calculated: Decimal) -> Decimal:
    """Calculates the final payout amount."""
    payout_amount = (
        order_amount
        - refunded_amount
        - tds_calculated
        - transaction_fee_calculated
        + commission_calculated
    )
    return round(payout_amount, 2)

def update_order_payout_fields(order_payout: OrderPayout, tds_calculated: Decimal, transaction_fee_calculated: Decimal,
                            transaction_fee_percentage: Decimal, commission_calculated: Decimal,
                            order_config: OrderConfiguration, payout_amount: Decimal, order_amount: Decimal) -> None:
    """Updates the fields of the OrderPayout instance with the calculated values."""
    order_payout.tds_calculated = tds_calculated
    order_payout.transaction_fee_calculated = transaction_fee_calculated
    order_payout.swadesic_fee = commission_calculated
    order_payout.payout_amount = payout_amount
    order_payout.transaction_fee_percentage = transaction_fee_percentage
    order_payout.expected_swadesic_fee = order_config.commission_fee
    order_payout.promotional_value = order_config.commission_fee_discount
    order_payout.order_amount = order_amount

def save_order_payout(order_payout: OrderPayout) -> None:
    """Saves the updated OrderPayout instance."""
    order_payout.save(
        update_fields=[
            "transaction_fee_calculated",
            "order_amount",
            "commission_calculated",
            "transaction_fee_percentage",
            "commission_fee",
            "promotional_value",
            "tds_calculated",
            "payout_amount",
        ]
    )

###################################


def create_payout_transaction(instance):
    """
    Creates a new payout transaction record in the database.
    
    Args:
        instance (OrderPayout): The OrderPayout instance containing the payout details.
    
    This function creates a new PayoutTransactions record with the following details:
    - store_reference: The store reference associated with the payout.
    - suborder_number: The suborder number associated with the payout.
    - order_number: The order number associated with the payout.
    - payout_amount: The absolute value of the payout amount.
    - order_date: The date of the order.
    - payout_release_date: The date the payout was released.
    - transaction_type: The type of transaction, either CREDITED or DEBITED.
    - transaction_status: The status of the transaction, set to SUCCESS.
    """

    payout_amount = instance.payout_amount
    if payout_amount > 0:
        transaction_type = PayoutTransactions.Transaction_Type.CREDITED
    elif payout_amount < 0:
        transaction_type = PayoutTransactions.Transaction_Type.DEBITED
    # payout amount can be positive or negative, In transaction table always save positive value.
    payout_amount = abs(payout_amount)
    PayoutTransactions.objects.create(
        store_reference=instance.store_reference,
        suborder_number=instance.suborder_number,
        order_number=instance.order_number,
        payout_amount=payout_amount,
        order_date=instance.order_date,
        payout_release_date=instance.payout_release_date,
        transaction_type=transaction_type,
        transaction_status=PayoutTransactions.Transaction_Status.SUCCESS,
    )

###########################################

def create_store_payout_balance(instance):
    logger.info("inside create_store_payout_balance %s", instance.store_reference)
    """Create a payout balance entry for a store."""
    if instance.expected_payout_amount:
        missed_revenue = instance.expected_payout_amount - instance.payout_amount
        missed_revenue = round(missed_revenue, 2)

        PayoutBalance.objects.create(
            store_reference=instance.store_reference,
            life_time_balance=instance.payout_amount,
            current_balance=instance.payout_amount,
            missed_revenue=missed_revenue,
        )
        logger.info("Payout balance created")


#############################################


class OrderPayoutCalculations():
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
            pass

    def update_orderpayout_entry(self, payout_status=None):

        refund_amount = 0 if self.refunded_amount_instance is None else self.refunded_amount_instance.refunded_amount

        new_tds_calculated = round((self.order_payout_instance.order_amount - refund_amount) * 0.01, 2)

        new_payout_amount = (self.order_payout_instance.payout_amount - refund_amount) + (
                    self.order_payout_instance.tds_calculated - new_tds_calculated)

        self.order_payout_instance.tds_calculated = new_tds_calculated
        self.order_payout_instance.payout_amount = round(
            0 if new_payout_amount < 0 and new_payout_amount > -1 else new_payout_amount, 2)
        self.order_payout_instance.payout_release_date = self.order_status_self.refund_requested_date
        if payout_status:
            self.order_payout_instance.payout_status = payout_status
        self.order_payout_instance.save()

        # return round(self.order_payout_instance.expected_payout_amount - self.refunded_amount_instance.refunded_amount, 2)