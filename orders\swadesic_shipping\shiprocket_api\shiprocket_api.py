import requests
from typing import List, Dict, Union, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class ShiprocketAPI:
    """A wrapper class for Shiprocket API endpoints.
    
    This class provides methods to interact with all major Shiprocket API endpoints
    including order creation, tracking, label generation, and more.
    """
    
    BASE_URL = "https://apiv2.shiprocket.in/v1/external"
    
    def __init__(self, token: str):
        """Initialize the Shiprocket API client.
        
        Args:
            token (str): Your Shiprocket API authentication token. This can be obtained
                        from your Shiprocket dashboard under Settings > API > Generate Token.
        """
        self.token = token
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }

    ####ORDER RELATED####

    def check_serviceability(self, pickup_postcode: str, delivery_postcode: str, cod: int = 0, weight: float = 0.5) -> Dict:
        """Check courier serviceability between pickup and delivery locations.
        
        Args:
            pickup_postcode (str): Pickup location pincode (e.g., "524004")
            delivery_postcode (str): Delivery location pincode (e.g., "500032")
            cod (int, optional): Cash on Delivery flag. Defaults to 0.
                               Set to 1 for COD orders, 0 for prepaid orders.
            weight (float, optional): Weight of the shipment in KG. Defaults to 0.5.
        
        Returns:
            Dict: Response containing available courier services, rates, and ETD.
                 Example:
                 {
                     "data": {
                         "available_courier_companies": [{
                             "courier_name": "...",
                             "rate": 123.45,
                             "etd": "2025-03-21"
                         }]
                     }
                 }
        """
        url = f"{self.BASE_URL}/courier/serviceability/"
        params = {
            'pickup_postcode': pickup_postcode,
            'delivery_postcode': delivery_postcode,
            'cod': cod,
            'weight': weight
        }
        response = requests.get(url, headers=self.headers, params=params)
        logger.info(f"Shiprocket serviceability check response: {response.json()}")
        return response.json()
    
    def create_order(self, order_details: Dict) -> Dict:
        """Create a new order in Shiprocket.
        
        Args:
            order_details (Dict): Order information containing:
                - order_id (str): Your unique order ID (e.g., "224-447")
                - order_date (str): Order date in format "YYYY-MM-DD HH:MM"
                - pickup_location (str): Pickup location name as registered in Shiprocket
                - channel_id (str): Channel ID (can be empty string)
                - comment (str, optional): Additional order comments
                - billing_customer_name (str): Customer's first name
                - billing_last_name (str): Customer's last name (can be empty)
                - billing_address (str): Street address line 1
                - billing_address_2 (str): Street address line 2
                - billing_city (str): City name
                - billing_pincode (str): PIN code
                - billing_state (str): State name
                - billing_country (str): Country name
                - billing_email (str): Customer's email
                - billing_phone (str): Customer's phone
                - shipping_is_billing (bool): True if shipping address same as billing
                - shipping_customer_name (str): Shipping customer's name
                - shipping_last_name (str): Shipping customer's last name
                - shipping_address (str): Shipping street address
                - shipping_address_2 (str): Shipping address line 2
                - shipping_city (str): Shipping city
                - shipping_pincode (str): Shipping PIN code
                - shipping_country (str): Shipping country
                - shipping_state (str): Shipping state
                - shipping_email (str): Shipping email
                - shipping_phone (str): Shipping phone
                - order_items (List[Dict]): List of items with:
                    - name (str): Product name
                    - sku (str): Product SKU
                    - units (int): Quantity
                    - selling_price (str): Price per unit
                    - discount (str, optional): Discount amount
                    - tax (str, optional): Tax amount
                    - hsn (int, optional): HSN code
                - payment_method (str): "Prepaid" or "COD"
                - shipping_charges (float): Shipping charges
                - giftwrap_charges (float): Gift wrap charges
                - transaction_charges (float): Transaction charges
                - total_discount (float): Total discount
                - sub_total (float): Total order value
                - length (float): Package length in cm
                - breadth (float): Package breadth in cm
                - height (float): Package height in cm
                - weight (float): Package weight in kg
        
        Returns:
            Dict: Created order details including:
                - order_id (int): Shiprocket order ID
                - shipment_id (int): Shiprocket shipment ID
                - status (str): Order status (e.g., "NEW")
                - status_code (int): Status code
                - awb_code (str): AWB number (empty if not assigned)
                - courier_company_id (str): Courier company ID
                - courier_name (str): Courier company name
        """
        url = f"{self.BASE_URL}/orders/create/adhoc"
        response = requests.post(url, headers=self.headers, json=order_details)
        logger.info(f"Shiprocket order creation response: {response.json()}")
        return response.json()
    
    def assign_awb(self, shipment_id: str, courier_id: str, is_return: int = 0) -> Dict:
        """Assign an AWB (Air Waybill) number to a shipment.
        
        Args:
            shipment_id (str): Shiprocket shipment ID (e.g., "*********")
            courier_id (str): Courier company ID (e.g., "32" for Amazon Shipping)
            is_return (int, optional): Flag for return shipment. Defaults to 0.)
        
        Returns:
            Dict: AWB assignment details including:
                - awb_assign_status (int): 1 for success, 0 for failure
                - response.data.courier_company_id (int): Courier company ID
                - response.data.awb_code (str): The assigned AWB number
                - response.data.cod (int): COD flag (0 or 1)
                - response.data.order_id (int): Shiprocket order ID
                - response.data.shipment_id (int): Shiprocket shipment ID
                - response.data.applied_weight (float): Applied weight in kg
                - response.data.courier_name (str): Courier company name
                - response.data.pickup_scheduled_date (str): Scheduled pickup date
        """
        url = f"{self.BASE_URL}/courier/assign/awb"
        payload = {
            "shipment_id": shipment_id,
            "courier_id": courier_id,
            "is_return": is_return
        }
        response = requests.post(url, headers=self.headers, json=payload)
        logger.info(f"Shiprocket AWB assignment response: {response.json()}")
        return response.json()

    ####PICKUP RELATED####

    def schedule_pickup(self, shipment_ids: List[str], pickup_dates: List[str]) -> Dict:
        """Schedule pickup for one or more shipments.
        
        Args:
            shipment_ids (List[str]): List of Shiprocket shipment IDs (e.g., ["*********"])
            pickup_dates (List[str]): List of pickup dates in format "YYYY-MM-DD",
                                    must correspond to shipment_ids (e.g., ["2025-03-21"])
        
        Returns:
            Dict: Pickup details including:
                - pickup_status (int): 1 for success, 0 for failure
                - response.pickup_scheduled_date (str): Confirmed pickup date and time
                - response.pickup_token_number (str): Reference number for the pickup
                - response.status (int): Status code
                - response.pickup_generated_date (Dict): 
                    - date (str): Pickup generation timestamp
                    - timezone_type (int): Timezone type (e.g., 3)
                    - timezone (str): Timezone name (e.g., "Asia/Kolkata")
                - response.data (str): Success message with AWB details
        """
        url = f"{self.BASE_URL}/courier/generate/pickup"
        payload = {
            "shipment_id": shipment_ids,
            "pickup_date": pickup_dates
        }
        response = requests.post(url, headers=self.headers, json=payload)
        logger.info(f"Shiprocket pickup scheduling response: {response.json()}")
        return response.json()

    def add_pickup_location(self, pickup_location: str, name: str, email: str, phone: str, address: str,
                            city: str, state: str, country: str, pin_code: str) -> Dict:
        """Add a new pickup location for the company.

        Args:
            pickup_location (str): Code or name for the pickup location.
            name (str): Contact name for the new pickup location.
            email (str): Contact email.
            phone (str): Contact phone.
            address (str): Address line 1.
            city (str): City name.
            state (str): State name.
            country (str): Country name.
            pin_code (str): PIN or postal code.

        Returns:
            Dict: API response containing pickup location details.
                Example:
                {
                    "success": true,
                    "address": { ... },
                    "pickup_id": 1856901,
                    "company_name": "ShiprocketTest",
                    "full_name": "API"
                }
        """
        url = f"{self.BASE_URL}/settings/company/addpickup"
        payload = {
            "pickup_location": pickup_location,
            "name": name,
            "email": email,
            "phone": phone,
            "address": address,
            "city": city,
            "state": state,
            "country": country,
            "pin_code": pin_code
        }
        response = requests.post(url, headers=self.headers, json=payload)
        logger.info(f"Shiprocket add pickup location response: {response.json()}")
        return response.json()

    ####MANIFEST LABEL RELATED####

    def generate_manifest(self, shipment_ids: List[str]) -> Dict:
        """Generate manifest for one or more shipments.
        
        Args:
            shipment_ids (List[str]): List of Shiprocket shipment IDs (e.g., ["*********"])
        
        Returns:
            Dict: Manifest details including:
                - status (int): 1 for success, 0 for failure
                - manifest_url (str): URL to download the manifest PDF
                    (e.g., "https://s3-ap-south-1.amazonaws.com/.../manifest/MANIFEST-0002.pdf")
                - error (List): List of any errors that occurred
        """
        url = f"{self.BASE_URL}/manifests/generate"
        payload = {
            "shipment_id": shipment_ids
        }
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()
    
    def print_manifest(self, order_ids: List[int]) -> Dict:
        """Print manifest for one or more orders.
        
        Args:
            order_ids (List[int]): List of Shiprocket order IDs (e.g., [787829385])
        
        Returns:
            Dict: Manifest details including:
                - manifest_url (str): URL to download the printable manifest PDF
                    (e.g., "https://s3-ap-south-1.amazonaws.com/.../print_manifests/6953563_amazon-shipping-surface-2kg__Vy16n1742477366.pdf")
        """
        url = f"{self.BASE_URL}/manifests/print"
        payload = {
            "order_ids": order_ids
        }
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()
    
    def generate_label(self, shipment_ids: List[str]) -> Dict:
        """Generate shipping labels for one or more shipments.
        
        Args:
            shipment_ids (List[str]): List of Shiprocket shipment IDs (e.g., ["*********"])
        
        Returns:
            Dict: Label details including:
                - label_created (int): 1 for success, 0 for failure
                - label_url (str): URL to download the label PDF
                    (e.g., "https://kr-shipmultichannel-mum.s3.ap-south-1.amazonaws.com/5641520/labels/712410731final_labels_amazon.pdf")
                - response (str): Success/failure message ("Label has been created and uploaded successfully!")
                - not_created (List): List of shipment IDs for which label creation failed
        """
        url = f"{self.BASE_URL}/courier/generate/label"
        payload = {
            "shipment_id": shipment_ids
        }
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()
    
    def generate_invoice(self, order_ids: List[int]) -> Dict:
        """Generate invoices for one or more orders.
        
        Args:
            order_ids (List[int]): List of Shiprocket order IDs (e.g., [787829385])
        
        Returns:
            Dict: Invoice details including:
                - is_invoice_created (bool): True for success, False for failure
                - invoice_url (str): URL to download the invoice PDF
                    (e.g., "https://s3-ap-south-1.amazonaws.com/.../invoices/Retail0000351acda34-f502-485f-9643-c4767ace5714.pdf")
                - not_created (List): List of order IDs for which invoice creation failed
                - irn_no (str): Invoice reference number (if applicable)
        """
        url = f"{self.BASE_URL}/orders/print/invoice"
        payload = {
            "ids": order_ids
        }
        response = requests.post(url, headers=self.headers, json=payload)
        return response.json()

    ####TRACKING RELATED####

    def track_by_awb(self, awb: str) -> Dict:
        """Track a shipment using its AWB number.
        
        Args:
            awb (str): AWB (Air Waybill) number (e.g., "348453552036")
        
        Returns:
            Dict: Tracking details including:
                - tracking_data.track_status (int): 1 for success, 0 for failure
                - tracking_data.shipment_status (int): Current status code
                - tracking_data.shipment_track (List[Dict]): List of shipment details:
                    - awb_code (str): AWB number
                    - courier_name (str): Courier company name
                    - current_status (str): Current shipment status
                    - delivered_date (str): Delivery date if delivered
                    - destination (str): Delivery city
                    - origin (str): Pickup city
                    - etd (str): Estimated delivery date
                    - weight (str): Package weight
                - tracking_data.shipment_track_activities (List[Dict]): Status history:
                    - date (str): Activity date and time
                    - status (str): Status code
                    - activity (str): Status description
                    - location (str): Activity location
                - tracking_data.track_url (str): URL to track shipment online
                - tracking_data.etd (str): Estimated delivery date and time
        """
        url = f"{self.BASE_URL}/courier/track/awb/{awb}"
        response = requests.get(url, headers=self.headers)
        return response.json()
    
    def track_by_order_id(self, order_id: str) -> List[Dict]:
        """Track a shipment using the order ID.
        
        Args:
            order_id (str): Your order ID (not Shiprocket's order ID) (e.g., "224-448")
        
        Returns:
            List[Dict]: List of tracking details for all shipments in the order.
                       Each item has the same structure as track_by_awb response.
        """
        url = f"{self.BASE_URL}/courier/track"
        params = {
            'order_id': order_id
        }
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()
    
    def track_by_shipment_id(self, shipment_id: str) -> Dict:
        """Track a shipment using Shiprocket's shipment ID.
        
        Args:
            shipment_id (str): Shiprocket shipment ID (e.g., "*********")
        
        Returns:
            Dict: Tracking details with same structure as track_by_awb response.
        """
        url = f"{self.BASE_URL}/courier/track/shipment/{shipment_id}"
        response = requests.get(url, headers=self.headers)
        return response.json()

    ####CANCELATION RELATED####

    def cancel_order(self, order_ids: List[str]) -> Dict:
        """Cancel order(s) in Shiprocket using order IDs.
        
        Args:
            order_ids (List[str]): List of Shiprocket order IDs to cancel
        
        Returns:
            Dict: Cancelation response from Shiprocket API
        """
        url = f"{self.BASE_URL}/orders/cancel"
        payload = {"ids": order_ids}
        
        response = requests.post(
            url, 
            headers=self.headers | {"Content-Type": "application/json"}, 
            json=payload
        )
        logger.info(f"Shiprocket cancel order response: {response.json()}")
        return response.json(), response.status_code

    def cancel_shipment(self, awbs:List[str]) -> Dict:
        """Cancel shipment(s) in Shiprocket using AWB number(s).
        
        Args:
            awbs (List[str]): List of AWB numbers to cancel
        
        Returns:
            Dict: Cancelation response from Shiprocket API
        """
        url = f"{self.BASE_URL}/orders/cancel/shipment/awbs"
        payload = {"awbs": awbs}
        
        response = requests.post(
            url, 
            headers=self.headers | {"Content-Type": "application/json"}, 
            json=payload
        )
        logger.info(f"Shiprocket cancel shipment response: {response.json()}")
        return response.json(), response.status_code

    ####RETURN ORDER RELATED####

    def create_return_order(self, return_details: Dict) -> Dict:
        """Create a return order in Shiprocket.
        
        Args:
            return_details (Dict): Return order information containing:
                - order_id (str): Original order ID
                - order_date (str): Original order date (YYYY-MM-DD HH:mm)
                - channel_id (str): Channel ID
                - pickup_customer_name (str): Customer's name for pickup
                - pickup_last_name (str): Customer's last name for pickup
                - pickup_address (str): Pickup address line 1
                - pickup_address_2 (str): Pickup address line 2
                - pickup_city (str): Pickup city name
                - pickup_state (str): Pickup state name
                - pickup_country (str): Pickup country name
                - pickup_pincode (str): Pickup PIN code
                - pickup_email (str): Customer's email for pickup
                - pickup_phone (str): Customer's phone for pickup
                - shipping_customer_name (str): Return delivery name
                - shipping_last_name (str): Return delivery last name
                - shipping_address (str): Return delivery address
                - shipping_address_2 (str): Return delivery address line 2
                - shipping_city (str): Return delivery city
                - shipping_country (str): Return delivery country
                - shipping_state (str): Return delivery state
                - shipping_pincode (str): Return delivery PIN code
                - shipping_email (str): Return delivery email
                - shipping_phone (str): Return delivery phone
                - order_items (List[Dict]): List of items with:
                    - sku (str): Product SKU
                    - name (str): Product name
                    - units (int): Quantity
                    - selling_price (str): Price per unit
                - payment_method (str): "Prepaid" or "COD"
                - sub_total (float): Subtotal amount
                - length (float): Package length in cm
                - breadth (float): Package breadth in cm
                - height (float): Package height in cm
                - weight (float): Package weight in kg
        
        Returns:
            Dict: Created return order details including:
                - order_id (int): Shiprocket return order ID
                - shipment_id (int): Shiprocket return shipment ID
                - status_code (int): Status code
                - status (str): Order status
        """
        url = f"{self.BASE_URL}/orders/create/return"
        response = requests.post(url, headers=self.headers, json=return_details)
        logger.info(f"Shiprocket return order creation response: {response.json()}")
        return response.json()

    def track_return_by_order_id(self, order_id: str) -> List[Dict]:
        """Track a return shipment using the order ID.
        
        Args:
            order_id (str): Your return order ID
        
        Returns:
            List[Dict]: List of tracking details for the return shipment including:
                - current_status (str): Current status of return
                - delivered_date (str): Return delivery date if delivered
                - etd (str): Estimated return delivery date
                - track_url (str): URL to track return shipment
                - shipment_track (List[Dict]): Detailed tracking info
                - shipment_track_activities (List[Dict]): Status history
        """
        url = f"{self.BASE_URL}/courier/track/return"
        params = {
            'order_id': order_id
        }
        response = requests.get(url, headers=self.headers, params=params)
        logger.info(f"Shiprocket return tracking response: {response.json()}")
        return response.json()

    def generate_return_label(self, shipment_id: str) -> Dict:
        """Generate shipping label for a return shipment.
        
        Args:
            shipment_id (str): Shiprocket return shipment ID
        
        Returns:
            Dict: Label details including:
                - label_url (str): URL to download the return label PDF
                - label_created (int): 1 for success, 0 for failure
                - response (str): Success/failure message
        """
        url = f"{self.BASE_URL}/courier/generate/return/label"
        payload = {
            "shipment_id": shipment_id
        }
        response = requests.post(url, headers=self.headers, json=payload)
        logger.info(f"Shiprocket return label generation response: {response.json()}")
        return response.json()

    def cancel_return_order(self, order_ids: List[str]) -> Dict:
        """Cancel return order(s) in Shiprocket.
        
        Args:
            order_ids (List[str]): List of Shiprocket return order IDs to cancel
        
        Returns:
            Dict: Cancelation response from Shiprocket API including:
                - status (int): 1 for success, 0 for failure
                - message (str): Success/failure message
        """
        url = f"{self.BASE_URL}/orders/cancel/return"
        payload = {"ids": order_ids}
        
        response = requests.post(url, headers=self.headers, json=payload)
        logger.info(f"Shiprocket return order cancelation response: {response.json()}")
        return response.json(), response.status_code
