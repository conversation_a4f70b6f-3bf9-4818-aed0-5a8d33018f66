# Generated by Django 3.2.13 on 2022-09-02 08:08

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0005_inviteuser"),
    ]

    operations = [
        migrations.RenameField(
            model_name="inviteuser",
            old_name="coupon_expiry_date",
            new_name="created_by",
        ),
        migrations.RenameField(
            model_name="inviteuser",
            old_name="number_of_coupon",
            new_name="number_of_invites",
        ),
        migrations.RenameField(
            model_name="inviteuser",
            old_name="remaining_coupon",
            new_name="remaining_invites",
        ),
        migrations.AddField(
            model_name="inviteuser",
            name="created_date",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="inviteuser",
            name="event_name",
            field=models.Char<PERSON>ield(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="inviteuser",
            name="event_note",
            field=models.CharField(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="inviteuser",
            name="invite_expiry_date",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="inviteuser",
            name="invite_status",
            field=models.CharField(default="CREATED", max_length=200),
        ),
        migrations.AddField(
            model_name="inviteuser",
            name="modified_date",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="inviteuser",
            name="invited_user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="users.user"
            ),
        ),
    ]
