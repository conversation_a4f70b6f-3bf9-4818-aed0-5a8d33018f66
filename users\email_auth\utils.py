import logging
from decouple import config
from rest_framework.response import Response
from rest_framework import status  # Used in Response status codes
from users.user_api.models import User
from users.user_api.utils import (
    google_get_user_info,
    generate_otp,
    validate_otp,
    send_otp_via_email,
    generate_jwt_token,
    handle_google_profile_picture,
    create_messaging_server_user
)
from GraphDB.models import Neo4jUser
from neomodel import db


# Get an instance of a logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def mask_phonenumber(phone_number):
    """
    Mask a phone number for privacy.

    Args:
        phone_number: Phone number to mask (e.g., '+************' or PhoneNumber object)

    Returns:
        str: Masked phone number (e.g., '+91XXXX43210')
    """
    if not phone_number:
        return None

    try:
        # Convert PhoneNumber object to string if needed
        phone_str = str(phone_number)

        # For Indian numbers in format +91XXXXXXXXXX
        if phone_str.startswith('+91') and len(phone_str) == 13:
            # Keep country code and last 5 digits visible
            return phone_str[:3] + 'XXXX' + phone_str[-5:]
        # For other formats, mask the middle part
        elif len(phone_str) > 6:
            visible_start = min(4, len(phone_str) // 3)
            visible_end = min(4, len(phone_str) // 3)
            masked_length = len(phone_str) - visible_start - visible_end
            return phone_str[:visible_start] + 'X' * masked_length + phone_str[-visible_end:]
        else:
            # For very short numbers, just return as is
            return phone_str
    except Exception as e:
        logger.error(f"Error masking phone number: {str(e)}")
        # Return string representation to ensure JSON serialization
        return str(phone_number)


def handle_email_login(user, is_email_verified, email, google_auth=None):
    """
    Handle successful login via email, making phone number optional.

    Args:
        user: User instance
        is_email_verified: Boolean indicating if email is verified
        email: User's email address
        google_auth: Boolean indicating if authentication is via Google (used for tracking)

    Returns:
        dict: Response data with user information
    """
    # google_auth parameter is used for tracking authentication method in calling functions
    # Build access control dictionary using dictionary comprehension
    access_control = {
        key: getattr(user, key) for key in [
            "search_and_view", "view_stores", "follow_stores", "view_store_contact",
            "report_stores", "view_store_documents", "view_product", "save_product",
            "buy_product", "report_product", "view_comment_question_review", "add_comment",
            "add_question", "add_review", "report_comment_question_review", "clap_comment_question_review",
            "follow_people", "report_people", "message_people", "create_store", "create_test_store", "store_live",
            "add_products", "receive_orders", "receive_payments", "send_member_invites",
            "send_seller_invites", "vote_for_membership", "giving_suggestions", "create_promotion",
            "view_community_forum", "edit_community_forum", "include_in_rating_calculation",
            "include_in_analytics", "included_in_others_trust_score", "include_in_self_trust_score"
        ]
    }

    welcome_message = user.is_email_verified

    # Create Neo4jUser node if not already done
    if not user.is_email_verified:
        with db.transaction:
            try:
                neo4j_user = Neo4jUser.nodes.get(email=user.email)
            except Neo4jUser.DoesNotExist:
                neo4j_user = Neo4jUser(
                    reference=user.user_reference,
                    created_date=user.created_date,
                    email=user.email,
                    pincode=user.pincode
                    # Add other properties as needed
                )
                neo4j_user.save()
                logger.info(f"Neo4jUser node created successfully: {neo4j_user}") if neo4j_user.reference else logger.warning("Failed to create Neo4jUser node")

    # Update verification status
    if not user.is_email_verified:
        user.is_email_verified = is_email_verified
        user.email = email
        user.save()

    # Check if user profile exists - using email instead of phone number
    user_instance = User.objects.filter(email=user.email).only("user_name", "user_roles").first()

    profile_exists_or_not = bool(user_instance.gender) and bool(user_instance.user_location)

    response_data = {
        "message": "welcome" if not welcome_message else "welcome back",
        "userid": user.userid,
        "user_reference": user.user_reference,
        "pin_code": user.pincode,
        "role": user.invite_type,
        "user_roles": user.user_roles,
        "profile_complete": profile_exists_or_not,
        "member_invite_balance": user.member_invite_balance,
        "seller_invite_balance": user.seller_invite_balance,
        "access_control": access_control,
        "is_email_verified": user.is_email_verified,
        "is_phonenumber_verified": user.is_phonenumber_verified
    }

    return response_data


def email_authentication_flow(action, email=None, google_access_token=None, email_otp=None):
    """
    Handle email-based authentication flow

    Args:
        action (str): 'initiate' or 'validate'
        email (str): User's email address
        google_access_token (str, optional): Google OAuth access token
        email_otp (str, optional): OTP for email verification

    Returns:
        Response: Django REST framework Response object
    """
    logger.info(
        f"Email Authentication Flow called with action: {action}, email: {email}, "
        f"google_access_token: {'present' if google_access_token else 'not present'}, "
        f"email_otp: {email_otp}"
    )

    config_send_otp = config("SEND_OTP").lower() == 'true'  # Get the value of SEND_OTP from .env
    logger.info(f"config_send_otp: {config_send_otp}")

    google_auth = False
    google_user_info = None

    # Get user info from Google if access token is provided
    if google_access_token:
        google_auth = True
        google_user_info = google_get_user_info(access_token=google_access_token)
        # If email is not provided, use the one from Google
        if not email:
            email = google_user_info['email']

    # Check if user exists with this email
    user = User.objects.filter(email=email, deleted=False).first()

    if action == 'initiate':
        if user:
            # User exists, generate token
            token_info = generate_jwt_token(user=user)

            # If Google Auth, we can directly authenticate the user if email is verified
            if google_auth:
                user_info = handle_email_login(
                    user,
                    is_email_verified=True,
                    email=email,
                    google_auth=True
                )

                # Update profile picture if needed
                if not user.icon and google_user_info:
                    icon = handle_google_profile_picture(google_user_info, user)
                    if icon:
                        user_info['icon'] = icon

                return Response({"token_info": token_info, "user_info": user_info}, status=200)

            # Otherwise, send OTP if email is not verified
            if config_send_otp and not google_auth:
                email_otp = generate_otp(identifier=email)
                send_otp_via_email(email_id=email, otp=email_otp)

            return Response({
                "user_exists": True,
                "is_email_verified": user.is_email_verified,
                "sent_email_otp": True,
                "phonenumber": mask_phonenumber(user.phonenumber) if user.phonenumber else None,
            }, status=200)
        else:
            # User doesn't exist, create a new user
            new_user = User(email=email)

            # If Google Auth, set additional fields from Google profile
            if google_auth and google_user_info:
                new_user.first_name = google_user_info.get('given_name', '')
                new_user.last_name = google_user_info.get('family_name', '')
                # new_user.is_email_verified = True  # Email is verified through Google

            new_user.save()

            # Create messaging server user
            xmpp_jid, xmpp_password, js_messaging_token, new_messaging_token, new_messaging_user_id = create_messaging_server_user(new_user, email)
            if xmpp_jid and xmpp_password:
                new_user.xmpp_jid = xmpp_jid
                new_user.xmpp_password = xmpp_password
                new_user.js_messaging_token = js_messaging_token
                new_user.new_messaging_token = new_messaging_token
                new_user.new_messaging_user_id = new_messaging_user_id
                new_user.save()

            # Create Neo4j node
            with db.transaction:
                try:
                    neo4j_user = Neo4jUser(
                        reference=new_user.user_reference,
                        created_date=new_user.created_date,
                        email=email
                    )
                    neo4j_user.save()
                    logger.info(f"Neo4jUser node created successfully: {neo4j_user}")
                except Exception as e:
                    logger.error(f"Error creating Neo4jUser node: {str(e)}")

            # Send OTP if not Google Auth
            if not google_auth and config_send_otp:
                email_otp = generate_otp(identifier=email)
                send_otp_via_email(email_id=email, otp=email_otp)

            # give login response if Google auth
             # If Google Auth, we can directly authenticate the user if email is verified
            if google_auth:
                token_info = generate_jwt_token(user=new_user)
                user_info = handle_email_login(
                    new_user,
                    is_email_verified=True,
                    email=email,
                    google_auth=True
                )

                # Update profile picture if needed
                if not new_user.icon and google_user_info:
                    icon = handle_google_profile_picture(google_user_info, user)
                    if icon:
                        user_info['icon'] = icon

                return Response({"token_info": token_info, "user_info": user_info}, status=200)


            return Response({
                "user_exists": False,
                "is_email_verified": new_user.is_email_verified,
                "sent_email_otp": True,
            }, status=200)

    elif action == 'validate':
        if not user:
            return Response({
                "message": "User not found with this email",
                "is_custom": True
            }, status=400)

        # Generate token for the user
        token_info = generate_jwt_token(user=user)

        # If Google Auth, we can directly authenticate the user
        if google_auth:
            # Update email verification status
            if not user.is_email_verified:
                user.is_email_verified = True
                user.save()

            user_info = handle_email_login(
                user,
                is_email_verified=True,
                email=email,
                google_auth=True
            )

            # Update profile picture if needed
            if not user.icon and google_user_info:
                icon = handle_google_profile_picture(google_user_info, user)
                if icon:
                    user_info['icon'] = icon

            return Response({"token_info": token_info, "user_info": user_info}, status=200)

        # Otherwise, validate OTP
        email_otp_verified = False

        if config_send_otp:
            if email_otp:
                email_otp_verified = validate_otp(identifier=email, otp=email_otp)
        else:
            # For testing environments
            if email_otp:
                email_otp_verified = True if email_otp == '222222' else False

        if not email_otp_verified:
            return Response({
                "message": "Please enter valid email OTP",
                "is_custom": True,
            }, status=400)

        # # Update email verification status
        # if not user.is_email_verified:
        #     user.is_email_verified = True
        #     user.save()

        # Handle successful login
        user_info = handle_email_login(
            user,
            is_email_verified=True,
            email=email
        )

        return Response({"token_info": token_info, "user_info": user_info}, status=200)

    return Response({
        "message": "Invalid action",
        "is_custom": True
    }, status=400)
