from django.db import models

# Create your models here.
class AppFeatureRequests(models.Model):
    app_feature_request_id = models.AutoField(primary_key=True)
    reference = models.CharField(max_length=30, unique=True)
    requested_store_ai = models.BooleanField(default=False)
    requested_product_affiliation = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "app_feature_requests"
        db_table = '"app_common"."app_feature_requests"'
