# Generated by Django 4.2.7 on 2024-11-14 22:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("general", "0032_request_async_flow_exception_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="rewardtransferconfig",
            name="transfer_type",
            field=models.CharField(
                choices=[
                    ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                    ("INVITE_CODE_REWARD", "Invite Code Reward"),
                    ("STORE_VERIFICATION", "Store Verification"),
                    ("U2U_TRANSFER", "U2U Transfer"),
                    ("S2U_TRANSFER", "S2U Transfer"),
                    ("AFFILIATE_REWARD", "Affiliate Reward"),
                ],
                max_length=20,
            ),
        ),
    ]
