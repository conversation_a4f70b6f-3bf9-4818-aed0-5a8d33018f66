from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db.models import Q, Value, Case, When, TextField, Prefetch
from django.db.models.functions import Greatest
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank, TrigramSimilarity
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import time
import logging
import json
from stores.store_api.serializers import NewGetstoreSerializer
from users.user_api.serializers import GetUserDetailsSerializer

from general.views import BuyStatusCheckerBatch
from products.models import Product, ProductImages
from stores.store_api.models import Store
from users.user_api.models import User
from content.models import Posts
from GraphDB.models import Neo4jPost
from GraphDB.neo4j_helpers import (
    get_batched_entity_follow_status,
    get_batched_entity_metrics
)
from GraphDB.queries import dbqueries, sqlqueries
from lean_apis.serializers import (
    LeanProductPartialDetailsSerializer,
    LeanSearchUserSerializer,
    LeanSearchStoreSerializer,
    LeanPostPartialDetailsSerializer
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class LeanSearchView(APIView):
    """
    A lean API view for search functionality with optimized database queries and Neo4j operations.
    Supports searching for products, stores, users, and posts with pagination.
    """

    @swagger_auto_schema(
        operation_summary="Search API (lean version)",
        operation_description="Search for products, stores, users, or posts with optimized performance",
        manual_parameters=[
            openapi.Parameter(
                'entity_type',
                openapi.IN_QUERY,
                description="Type of entity to search for (PRODUCT, STORE, USER, POST)",
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'query',
                openapi.IN_QUERY,
                description="Search query string",
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'visitor_reference',
                openapi.IN_QUERY,
                description="Visitor reference (optional)",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'user_pincode',
                openapi.IN_QUERY,
                description="User pincode (optional)",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description="Number of results to return",
                type=openapi.TYPE_INTEGER,
                default=10
            ),
            openapi.Parameter(
                'offset',
                openapi.IN_QUERY,
                description="Number of results to skip",
                type=openapi.TYPE_INTEGER,
                default=0
            ),
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'product': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'store': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'user': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'post': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request):
        """
        Search for products, stores, users, or posts with optimized performance.

        Args:
            request: HTTP request with search parameters

        Returns:
            Response with search results
        """
        self.start = start = time.perf_counter()

        # Get query parameters
        visitor_reference = request.query_params.get("visitor_reference")
        user_pincode = request.query_params.get("user_pincode")
        limit = int(request.query_params.get("limit", 10))
        offset = int(request.query_params.get("offset", 0))
        entity_type = request.query_params.get("entity_type")
        query = request.query_params.get("query", "").strip()

        # Validate required parameters
        if not entity_type or not query:
            return Response({"message": "Missing query or entity_type"}, status=400)

        # Route to appropriate search method based on entity type
        if entity_type == "PRODUCT":
            return self._search_product(query, user_pincode, visitor_reference, offset, limit, start)
        elif entity_type == "STORE":
            return self._search_store(query, user_pincode, offset, limit, start, visitor_reference)
        elif entity_type == "USER":
            return self._search_user(query, offset, limit, start, visitor_reference)
        elif entity_type == "POST":
            return self._search_post(query, visitor_reference, offset, limit, start)

        self.end = time.perf_counter()
        logger.info(f"INVALID ENTITY Search Time: {self.end - self.start:.6f} seconds")
        return Response({"message": "Invalid entity type"}, status=400)

    def _search_product_old(self, query, user_pincode, visitor_reference, test_store_refs, user_test_store_refs, offset, limit, start):
        """
        Search for products with optimized performance.

        Args:
            query: Search query string
            user_pincode: User pincode for deliverability check
            visitor_reference: Visitor reference for interaction status
            test_store_refs: Set of test store references to exclude
            user_test_store_refs: Set of user's test store references to include
            offset: Number of results to skip
            limit: Number of results to return
            start: Start time for performance measurement

        Returns:
            Response with product search results
        """
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('product_name', weight='A') +
            SearchVector('brand_name', weight='A') +
            SearchVector('product_description', weight='C') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B')
        )
        search_query = SearchQuery(query)

        # Apply search ranking and filtering to main queryset
        main_qs = Product.objects.select_related('store_reference').filter(
            deleted=False,
            store_reference__is_active=True
        ).exclude(
            store_reference__store_reference__in=test_store_refs
        ).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('product_name', query),
            brand_similarity=TrigramSimilarity('brand_name', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(product_name__icontains=query) |
            Q(brand_name__icontains=query) |
            Q(name_similarity__gt=0.3) |
            Q(brand_similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(product_name__iexact=query, then=Value(1.0)),
                When(brand_name__iexact=query, then=Value(0.1)),
                default=Greatest('rank', 'name_similarity', 'brand_similarity'),
                output_field=TextField()
            )
        )

        # Include user's test store products if any
        if user_test_store_refs:
            # Apply the same search ranking and filtering to test products
            test_products = Product.objects.filter(
                store_reference__store_reference__in=user_test_store_refs,
                deleted=False
            ).annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query),
                name_similarity=TrigramSimilarity('product_name', query),
                brand_similarity=TrigramSimilarity('brand_name', query)
            ).filter(
                Q(rank__gte=0.1) |
                Q(product_name__icontains=query) |
                Q(brand_name__icontains=query) |
                Q(name_similarity__gt=0.3) |
                Q(brand_similarity__gt=0.3)
            ).annotate(
                combined_score=Case(
                    When(product_name__iexact=query, then=Value(1.0)),
                    When(brand_name__iexact=query, then=Value(0.1)),
                    default=Greatest('rank', 'name_similarity', 'brand_similarity'),
                    output_field=TextField()
                )
            )

            # Combine the querysets
            qs = main_qs.union(test_products)
            # Sort the combined results
            qs = qs.order_by('-combined_score')
        else:
            # If no test products, just use the main queryset
            qs = main_qs.order_by('-combined_score')

        # Apply pagination
        paginated_qs = qs[offset:offset+limit]

        # Optimize with prefetch_related for product images
        paginated_qs = paginated_qs.prefetch_related(
            Prefetch(
                'prod_images',
                queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                to_attr='prefetched_images'
            )
        )

        # Get Neo4j interaction details in batch if visitor_reference is provided
        context = {"user_pincode": user_pincode}
        if visitor_reference:
            try:
                product_refs = [p.product_reference for p in paginated_qs]
                neo4j_interactions = dbqueries.get_batched_interaction_details(visitor_reference, product_refs)
                context["neo4j_interactions"] = neo4j_interactions
            except Exception as e:
                logger.error(f"Error fetching Neo4j interactions: {e}")

        # Use lean serializer for better performance
        serialized_data = LeanProductPartialDetailsSerializer(
            paginated_qs,
            many=True,
            context=context
        ).data

        end = time.perf_counter()
        logger.info(f"PRODUCT Search Time: {end - start:.6f} seconds")
        return Response({"message": "success", "product": serialized_data}, status=200)

    def _search_product(self, query, user_pincode, visitor_reference, offset, limit, start):
        """
        Optimized product search across real stores and user's test stores.
        Uses bulk operations instead of serializers for better performance.
        """
        start = time.perf_counter()

        # Identify user from visitor_reference (can be user ID or store reference)
        owner_user = None
        if visitor_reference:
            store = Store.objects.filter(store_reference=visitor_reference, is_test_store=True).only('created_by').first()
            if store:
                owner_user = store.created_by
            else:
                try:
                    owner_user = User.objects.only('userid').get(user_reference=visitor_reference)
                except User.DoesNotExist:
                    logger.warning(f"Could not resolve visitor_reference '{visitor_reference}")

        # Base store filter: real stores + test stores created by owner
        store_filter = Q(store_reference__is_test_store=False)
        if owner_user:
            store_filter |= Q(store_reference__is_test_store=True, store_reference__created_by=owner_user)

        # Define weighted search vector
        search_vector = (
            SearchVector('product_name', weight='A') +
            SearchVector('brand_name', weight='A') +
            SearchVector('product_description', weight='C') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B')
        )
        search_query = SearchQuery(query)

        # Step 1: First get only the product references that match the search criteria
        # This is more efficient than fetching all product data at this stage
        product_refs_qs = Product.objects.filter(
            deleted=False,
            store_reference__is_active=True,
        ).filter(store_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('product_name', query),
            brand_similarity=TrigramSimilarity('brand_name', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(product_name__icontains=query) |
            Q(brand_name__icontains=query) |
            Q(name_similarity__gt=0.3) |
            Q(brand_similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(product_name__iexact=query, then=Value(1.0)),
                When(brand_name__iexact=query, then=Value(0.1)),
                default=Greatest('rank', 'name_similarity', 'brand_similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score').values_list('product_reference', flat=True)[offset:offset + limit]

        # Convert to list for further processing
        product_refs = list(product_refs_qs)

        # Step 2: Now fetch the complete product data with all needed relations in one efficient query
        products = Product.objects.filter(
            product_reference__in=product_refs
        ).select_related(
            'store_reference'
        ).prefetch_related(
            Prefetch(
                'prod_images',
                queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                to_attr='prefetched_images'
            )
        )

        # Create a dictionary to preserve the original order from the search results
        product_order_map = {ref: idx for idx, ref in enumerate(product_refs)}

        # Sort products according to the original search order
        sorted_products = sorted(products, key=lambda p: product_order_map.get(p.product_reference, 0))

        end = time.perf_counter()
        logger.info(f"PRODUCT Search Time getting query set: {end - start:.6f} seconds")

        # Step 3: Batch fetch all related data
        neo4j_interactions = {}
        buy_status_map = {}

        if visitor_reference:
            # Get Neo4j interaction details
            try:
                start = time.perf_counter()
                neo4j_interactions = dbqueries.get_batched_interaction_details(visitor_reference, product_refs)
                end = time.perf_counter()
                logger.info(f"Neo4j Interaction Time: {end - start:.6f} seconds")
            except Exception as e:
                logger.error(f"Error fetching Neo4j interactions: {e}")

        # Get buy status details if needed
        if user_pincode:
            try:
                start = time.perf_counter()
                checker = BuyStatusCheckerBatch(products=sorted_products, user_pincode=user_pincode)
                buy_status_map = checker.get_buy_status_map()
                end = time.perf_counter()
                logger.info(f"Buy Status Time: {end - start:.6f} seconds")
            except Exception as e:
                logger.error(f"Error fetching buy status: {e}")

        # Step 4: Build the response data directly without using serializers
        start_build = time.perf_counter()
        product_data = []

        for product in sorted_products:
            # Basic product data
            product_dict = {
                "productid": product.productid,
                "product_reference": product.product_reference,
                "store_reference": product.store_reference.store_reference,
                "store_icon": product.store_reference.icon,
                "storehandle": product.store_reference.storehandle,
                "product_name": product.product_name,
                "brand_name": product.brand_name,
                "product_description": product.product_description,
                "mrp_price": product.mrp_price,
                "selling_price": product.selling_price,
                "swadeshi_brand": product.swadeshi_brand,
                "swadeshi_made": product.swadeshi_made,
                "like_count": product.like_count,
                "comment_count": product.comment_count,
                "repost_count": product.repost_count,
                "repost_plus_count": product.repost_plus_count,
                "save_count": product.save_count,
                "share_count": product.share_count,
            }

            # Add product images
            product_dict["product_images"] = [
                {
                    "image_url": img.image_url,
                    "reorder": img.reorder
                } for img in product.prefetched_images
            ]

            # Add Neo4j interaction data if available
            if product.product_reference in neo4j_interactions:
                interaction = neo4j_interactions[product.product_reference]
                product_dict.update({
                    "liked": interaction.get("liked", False),
                    "saved": interaction.get("saved", False),
                    "reposted": interaction.get("reposted", False),
                    "category": interaction.get("category", ""),
                    "headers": interaction.get("headers", []),
                    "header_text": interaction.get("header_text", "")
                })

            # Add buy status data if available
            if product.product_reference in buy_status_map:
                buy_status = buy_status_map[product.product_reference]
                product_dict.update({
                    "is_buy_enabled": buy_status.get("is_buy_enabled", False),
                    "product_status_message": buy_status.get("product_status_message", "")
                })

            product_data.append(product_dict)

        end_build = time.perf_counter()
        logger.info(f"Build Response Time: {end_build - start_build:.6f} seconds")

        self.end = time.perf_counter()
        logger.info(f"PRODUCT Search Time: {self.end - self.start:.6f} seconds")
        return Response({"message": "success", "product": product_data}, status=200)

    def _search_store_old(self, query, user_pincode, user_test_store_qs, offset, limit, start, visitor_reference=None):
        """
        Search for stores with optimized performance.

        Args:
            query: Search query string
            user_pincode: User pincode for deliverability check
            user_test_store_qs: QuerySet of user's test stores to include
            offset: Number of results to skip
            limit: Number of results to return
            start: Start time for performance measurement
            visitor_reference: Optional visitor reference for interaction status

        Returns:
            Response with store search results
        """
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('store_name', weight='A') +
            SearchVector('storehandle', weight='A') +
            SearchVector('business_description', weight='B') +
            SearchVector('store_details__city', weight='C') +
            SearchVector('store_desc', weight='C')
        )
        search_query = SearchQuery(query)

        # Build and apply search ranking to main queryset
        main_qs = Store.objects.filter(
            deleted=False,
            is_active=True
        ).exclude(
            storehandle__startswith="test_"
        ).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('store_name', query),
            handle_similarity=TrigramSimilarity('storehandle', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(store_name__icontains=query) |
            Q(storehandle__icontains=query) |
            Q(business_description__icontains=query) |
            Q(store_details__city__icontains=query) |
            Q(name_similarity__gt=0.3) |
            Q(handle_similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(storehandle__iexact=query, then=Value(1.0)),
                When(store_name__iexact=query, then=Value(0.9)),
                default=Greatest('rank', 'handle_similarity', 'name_similarity'),
                output_field=TextField()
            )
        )

        # Include user's test stores if any
        if user_test_store_qs.exists():
            # Apply the same search ranking to test stores
            test_stores = user_test_store_qs.annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query),
                name_similarity=TrigramSimilarity('store_name', query),
                handle_similarity=TrigramSimilarity('storehandle', query)
            ).filter(
                Q(rank__gte=0.1) |
                Q(store_name__icontains=query) |
                Q(storehandle__icontains=query) |
                Q(business_description__icontains=query) |
                Q(store_details__city__icontains=query) |
                Q(name_similarity__gt=0.3) |
                Q(handle_similarity__gt=0.3)
            ).annotate(
                combined_score=Case(
                    When(storehandle__iexact=query, then=Value(1.0)),
                    When(store_name__iexact=query, then=Value(0.9)),
                    default=Greatest('rank', 'handle_similarity', 'name_similarity'),
                    output_field=TextField()
                )
            )

            # Combine the querysets
            qs = main_qs.union(test_stores)
            # Sort the combined results
            qs = qs.order_by('-combined_score')[offset:offset+limit]
        else:
            # If no test stores, just use the main queryset
            qs = main_qs.order_by('-combined_score')[offset:offset+limit]

        # Get store references for batch operations
        store_refs = [store.store_reference for store in qs]

        # Batch fetch Neo4j metrics and follow status if visitor_reference is provided
        entity_metrics = {}
        follow_status = {}

        try:
            # Get follower and following counts in batch
            entity_metrics = get_batched_entity_metrics(store_refs)

            # Get follow status in batch if visitor_reference is provided
            if visitor_reference:
                follow_status = get_batched_entity_follow_status(visitor_reference, store_refs)
        except Exception as e:
            logger.error(f"Error in batch Neo4j operations for stores: {e}")

        # Create context for serializer
        context = {
            "user_pincode": user_pincode,
            "entity_metrics": entity_metrics,
            "follow_status": follow_status,
            "visitor_reference": visitor_reference
        }

        # Serialize data with our lean serializer
        serialized_data = LeanSearchStoreSerializer(qs, many=True, context=context).data

        end = time.perf_counter()
        logger.info(f"STORE Search Time: {end - start:.6f} seconds")
        return Response({"message": "success", "store": serialized_data}, status=200)

    def _search_store(self, query, user_pincode, offset, limit, start, visitor_reference=None):
        """
        Optimized store search across both real stores and user test stores.
        """
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('store_name', weight='A') +
            SearchVector('storehandle', weight='A') +
            SearchVector('business_description', weight='B') +
            SearchVector('store_details__city', weight='C') +
            SearchVector('store_desc', weight='C')
        )
        search_query = SearchQuery(query)

        # Base store filter: real stores + test stores created by owner (resolved via visitor_reference)
        store_filter = Q(deleted=False, is_active=True) & ~Q(storehandle__startswith="test_")

        # If visitor_reference is provided, resolve user or store reference
        if visitor_reference:
            owner_user = None
            try:
                # Check if visitor is a test store
                store = Store.objects.only('created_by').filter(
                    store_reference=visitor_reference,
                    is_test_store=True
                ).first()
                if store:
                    owner_user = store.created_by
            except Exception as e:
                logger.warning(f"Could not resolve visitor_reference '{visitor_reference}' to owner: {e}")

            # If owner_user exists, include test stores created by this user
            if owner_user:
                store_filter |= Q(is_test_store=True, created_by=owner_user)

        # Build and apply search ranking to main queryset
        main_qs = Store.objects.filter(store_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('store_name', query),
            handle_similarity=TrigramSimilarity('storehandle', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(store_name__icontains=query) |
            Q(storehandle__icontains=query) |
            Q(business_description__icontains=query) |
            Q(store_details__city__icontains=query) |
            Q(name_similarity__gt=0.3) |
            Q(handle_similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(storehandle__iexact=query, then=Value(1.0)),
                When(store_name__iexact=query, then=Value(0.9)),
                default=Greatest('rank', 'handle_similarity', 'name_similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')

        # Pagination
        paginated_qs = main_qs[offset:offset + limit]

        # Get store references for batch operations
        store_refs = [store.store_reference for store in paginated_qs]

        # Batch fetch Neo4j metrics and follow status if visitor_reference is provided
        entity_metrics = {}
        follow_status = {}

        try:
            # Get follower and following counts in batch
            entity_metrics = get_batched_entity_metrics(store_refs)

            # Get follow status in batch if visitor_reference is provided
            if visitor_reference:
                follow_status = get_batched_entity_follow_status(visitor_reference, store_refs)
        except Exception as e:
            logger.error(f"Error in batch Neo4j operations for stores: {e}")

        # Create context for serializer
        context = {
            "user_pincode": user_pincode,
            "entity_metrics": entity_metrics,
            "follow_status": follow_status,
            "visitor_reference": visitor_reference
        }

        # Serialize data with our lean serializer
        serialized_data = LeanSearchStoreSerializer(paginated_qs, many=True, context=context).data

        # Measure and log search time
        end = time.perf_counter()
        logger.info(f"STORE Search Time: {end - start:.6f} seconds")

        return Response({"message": "success", "store": serialized_data}, status=200)

    def _search_user(self, query, offset, limit, start, visitor_reference=None):
        """
        Search for users with optimized performance.

        Args:
            query: Search query string
            offset: Number of results to skip
            limit: Number of results to return
            start: Start time for performance measurement
            visitor_reference: Optional visitor reference for interaction status

        Returns:
            Response with user search results
        """
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('user_name', weight='A') +
            SearchVector('first_name', weight='B') +
            SearchVector('display_name', weight='B') +
            SearchVector('about_user', weight='C')
        )
        search_query = SearchQuery(query)

        # Apply search ranking and filtering
        qs = User.objects.filter(deleted=False).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            similarity=TrigramSimilarity('user_name', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(user_name__icontains=query) |
            Q(similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(user_name__iexact=query, then=Value(1.0)),
                default=Greatest('rank', 'similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')[offset:offset+limit]

        # Get user references for batch operations
        user_refs = [user.user_reference for user in qs]

        # Batch fetch Neo4j metrics and follow status if visitor_reference is provided
        entity_metrics = {}
        follow_status = {}

        try:
            # Get follower and following counts in batch
            entity_metrics = get_batched_entity_metrics(user_refs)

            # Get follow status in batch if visitor_reference is provided
            if visitor_reference:
                follow_status = get_batched_entity_follow_status(visitor_reference, user_refs)
        except Exception as e:
            logger.error(f"Error in batch Neo4j operations for users: {e}")

        # Create context for serializer
        context = {
            "entity_metrics": entity_metrics,
            "follow_status": follow_status,
            "visitor_reference": visitor_reference
        }

        # Serialize data with our lean serializer
        serialized_data = LeanSearchUserSerializer(qs, many=True, context=context).data

        end = time.perf_counter()
        logger.info(f"USER Search Time: {end - start:.6f} seconds")
        return Response({"message": "success", "user": serialized_data}, status=200)

    def _search_post_old(self, query, test_store_refs, user_test_store_refs, visitor_reference, offset, limit, start):
        """
        Search for posts with optimized performance.

        Args:
            query: Search query string
            test_store_refs: Set of test store references to exclude
            user_test_store_refs: Set of user's test store references to include
            visitor_reference: Visitor reference for interaction status
            offset: Number of results to skip
            limit: Number of results to return
            start: Start time for performance measurement

        Returns:
            Response with post search results
        """
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('post_text', weight='A') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B')
        )
        search_query = SearchQuery(query)

        # Determine stores to exclude (test stores minus user's test stores, plus inactive stores)
        inactive_store_refs = set(Store.objects.filter(is_active=False).values_list("store_reference", flat=True))
        exclude_refs = test_store_refs.union(inactive_store_refs)
        include_refs = user_test_store_refs

        # Build base queryset with optimized joins
        qs = Posts.objects.select_related("store_reference").filter(is_deleted=False)
        if exclude_refs:
            qs = qs.exclude(store_reference__in=exclude_refs - include_refs)

        # Apply search ranking and filtering
        qs = qs.annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            text_similarity=TrigramSimilarity('post_text', query)
        ).filter(
            Q(rank__gte=0.01) |
            Q(post_text__icontains=query) |
            Q(store_reference__store_name__icontains=query) |
            Q(store_reference__storehandle__icontains=query) |
            Q(text_similarity__gt=0.1)
        ).annotate(
            combined_score=Case(
                When(post_text__icontains=query, then=Value(0.1)),
                default=Greatest('rank', 'text_similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')[offset:offset+limit]

        # Get post references for Neo4j lookup
        post_refs = list(qs.values_list('post_reference', flat=True))

        # Create a mapping of post metadata from RDB
        post_meta = {
            post.post_reference: {
                'like_count': post.like_count,
                'comment_count': post.comment_count,
                'repost_count': post.repost_count,
                'repost_plus_count': post.repost_plus_count,
                'save_count': post.save_count,
                'share_count': post.share_count
            } for post in qs
        }

        # Batch fetch Neo4j posts
        neo4j_posts = Neo4jPost.nodes.filter(reference__in=post_refs)

        # Batch fetch interaction statuses if visitor_reference is provided
        interaction_details = {}
        if visitor_reference:
            try:
                interaction_details = dbqueries.get_batched_interaction_details(visitor_reference, post_refs)
            except Exception as e:
                logger.error(f"Error fetching Neo4j interactions: {e}")

        # Prepare post data with optimized Neo4j operations
        post_data = []
        for np in neo4j_posts:
            post_info = {
                'postReference': np.reference,
                'postText': np.post_text,
                'createdDate': np.created_date.isoformat() if np.created_date else None,
                'isDeleted': np.is_deleted,
                'postImages': np.serialized_post_images(),
            }

            # Add metadata from RDB
            meta = post_meta.get(np.reference, {})
            post_info.update({
                'likeCount': meta.get('like_count', 0),
                'commentCount': meta.get('comment_count', 0),
                'repostCount': meta.get('repost_count', 0),
                'repostPlusCount': meta.get('repost_plus_count', 0),
                'saveCount': meta.get('save_count', 0),
                'shareCount': meta.get('share_count', 0),
            })

            # Add interaction statuses from batch operation
            if visitor_reference:
                interactions = interaction_details.get(np.reference, {})
                post_info.update({
                    'likeStatus': interactions.get('liked', False),
                    'repostStatus': interactions.get('reposted', False),
                    'saveStatus': interactions.get('saved', False),
                    'contentCategory': interactions.get('category', 'POST'),
                    'contentHeaders': interactions.get('headers', []),
                    'contentHeaderText': interactions.get('header_text', '')
                })
            else:
                post_info.update({
                    'likeStatus': False,
                    'repostStatus': False,
                    'saveStatus': False,
                    'contentCategory': 'POST',
                    'contentHeaders': [],
                    'contentHeaderText': ''
                })

            # Add creator data
            creator = np.get_post_creator()
            if creator:
                post_info['createdBy'] = {
                    'reference': creator.reference,
                    'handle': creator.handle,
                    'icon': creator.icon,
                    'entityType': creator.get_entity_type(),
                    'name': creator.name,
                    'phoneNumber': creator.phonenumber,
                    'pincode': creator.pincode,
                    'city': creator.city,
                    'isDeleted': creator.is_deleted,
                    'createdDate': creator.created_date.strftime("%d:%m:%Y %H:%M:%S") if creator.created_date else None,
                    'categoryName': getattr(creator, 'category_name', None),
                    'followersOrSupportersCount': creator.get_follower_count(),
                    'followingOrSupportingCount': creator.get_following_count(),
                    'followStatus': creator.follow_status(visitor_reference) if visitor_reference else None,
                    'subscriptionType': creator.get_subscription_type(),
                }
            else:
                post_info['createdBy'] = None

            post_data.append(post_info)

        end = time.perf_counter()
        logger.info(f"POST Search Time: {end - start:.6f} seconds")
        return Response({"message": "success", "post": post_data}, status=200)

    def _search_post(self, query, visitor_reference, offset, limit, start):
        """
        Optimized post search across real stores and user's test stores.
        """
        # Identify user from visitor_reference (can be user ID or store reference)
        start = time.perf_counter()
        owner_user = None
        if visitor_reference:

            store = Store.objects.filter(store_reference=visitor_reference, is_test_store=True).only('created_by').first()
            if store:
                owner_user = store.created_by
            else:
                try:
                    owner_user = User.objects.only('userid').get(user_reference=visitor_reference)
                except User.DoesNotExist as e:
                    logger.warning(f"Could not resolve visitor_reference '{visitor_reference}' to owner: {e}")

        # Base store filter: real stores + test stores created by owner
        store_filter = Q(store_reference__is_test_store=False)
        if owner_user:
            store_filter |= Q(store_reference__is_test_store=True, store_reference__created_by=owner_user)

        user_filter = Q(user_reference__isnull=False)


        # Define weighted search vector
        search_vector = (
            SearchVector('post_text', weight='A') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B')
        )
        search_query = SearchQuery(query)

        # Base queryset with filtering, annotation, and scoring
        qs = Posts.objects.select_related('store_reference').filter(
            is_deleted=False,
        ).filter(store_filter | user_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            post_text_similarity=TrigramSimilarity('post_text', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(post_text__icontains=query) |
            Q(post_text_similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(post_text__iexact=query, then=Value(1.0)),
                default=Greatest('rank', 'post_text_similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')

        # Paginate and prefetch related store details
        paginated_qs = qs[offset:offset + limit].prefetch_related(
            Prefetch(
                'store_reference',
                queryset=Store.objects.filter(is_active=True).only('store_name', 'storehandle'),
                to_attr='prefetched_store_details'
            )
        )
        end = time.perf_counter()
        logger.info(f"POST Search Time getting query set: {end - start:.6f} seconds")

        # Get Neo4j interaction details if needed
        context = {"visitor_reference": visitor_reference}
        if visitor_reference:
            try:
                start = time.perf_counter()
                post_refs = [p.post_reference for p in paginated_qs]
                context["neo4j_interactions"] = dbqueries.get_batched_interaction_details(visitor_reference, post_refs)
                end = time.perf_counter()
                logger.info(f"Neo4j Interaction Time: {end - start:.6f} seconds")
            except Exception as e:
                logger.error(f"Error fetching Neo4j interactions: {e}")

        # Serialize with appropriate serializer
        start_serialize = time.perf_counter()
        serialized_data = LeanPostPartialDetailsSerializer(
            paginated_qs,
            many=True,
            context=context
        ).data
        end_serialize = time.perf_counter()
        logger.info(f"Serialization Time: {end_serialize - start_serialize:.6f} seconds")

        self.end = time.perf_counter()
        logger.info(f"POST Search Time: {self.end - self.start:.6f} seconds")
        return Response({"message": "success", "posts": serialized_data}, status=200)



class LeanSearchViewNew(APIView):
    """
    A lean API view for search functionality with optimized database queries and Neo4j operations.
    Supports searching for products, stores, users, and posts with pagination.
    """

    @swagger_auto_schema(
        operation_summary="Search API (lean version)",
        operation_description="Search for products, stores, users, or posts with optimized performance",
        manual_parameters=[
            openapi.Parameter(
                'entity_type',
                openapi.IN_QUERY,
                description="Type of entity to search for (PRODUCT, STORE, USER, POST)",
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'query',
                openapi.IN_QUERY,
                description="Search query string",
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'visitor_reference',
                openapi.IN_QUERY,
                description="Visitor reference (optional)",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'user_pincode',
                openapi.IN_QUERY,
                description="User pincode (optional)",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description="Number of results to return",
                type=openapi.TYPE_INTEGER,
                default=10
            ),
            openapi.Parameter(
                'offset',
                openapi.IN_QUERY,
                description="Number of results to skip",
                type=openapi.TYPE_INTEGER,
                default=0
            ),
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'product': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'store': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'user': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'post': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request):
        """
        Search for products, stores, users, or posts with optimized performance.

        Args:
            request: HTTP request with search parameters

        Returns:
            Response with search results
        """
        self.start = start = time.perf_counter()

        # Get query parameters
        visitor_reference = request.query_params.get("visitor_reference")
        user_pincode = request.query_params.get("user_pincode")
        limit = int(request.query_params.get("limit", 10))
        offset = int(request.query_params.get("offset", 0))
        entity_type = request.query_params.get("entity_type")
        query = request.query_params.get("query", "").strip()

        # Validate required parameters
        if not entity_type or not query:
            return Response({"message": "Missing query or entity_type"}, status=400)

        # Route to appropriate search method based on entity type
        if entity_type == "PRODUCT":
            return self._search_product(query, user_pincode, visitor_reference, offset, limit, start)
        elif entity_type == "STORE":
            return self._search_store(query, user_pincode, offset, limit, start, visitor_reference)
        elif entity_type == "USER":
            return self._search_user(query, offset, limit, start, visitor_reference)
        elif entity_type == "POST":
            return self._search_post(query, visitor_reference, offset, limit, start)

        self.end = time.perf_counter()
        logger.info(f"INVALID ENTITY Search Time: {self.end - self.start:.6f} seconds")
        return Response({"message": "Invalid entity type"}, status=400)

    def _search_product(self, query, user_pincode, visitor_reference, offset, limit, start):
        """
        Optimized product search across real stores and user's test stores.
        Uses bulk operations instead of serializers for better performance.
        """
        # Identify user from visitor_reference (can be user ID or store reference)
        owner_user = None
        if visitor_reference:
            store = Store.objects.filter(store_reference=visitor_reference, is_test_store=True).only('created_by').first()
            if store:
                owner_user = store.created_by
            else:
                try:
                    owner_user = User.objects.only('userid').get(user_reference=visitor_reference)
                except User.DoesNotExist:
                    logger.warning(f"Could not resolve visitor_reference '{visitor_reference}")

        # Base store filter: real stores + test stores created by owner
        store_filter = Q(store_reference__is_test_store=False)
        if owner_user:
            store_filter |= Q(store_reference__is_test_store=True, store_reference__created_by=owner_user)

        # Define weighted search vector
        search_vector = (
            SearchVector('product_name', weight='A') +
            SearchVector('brand_name', weight='A') +
            SearchVector('product_code', weight='A') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B') +
            SearchVector('product_category', weight='B') +
            SearchVector('product_description', weight='C') +
            SearchVector('product_slug', weight='D')
        )
        search_query = SearchQuery(query)

        # Step 1: First get only the product references that match the search criteria
        # This is more efficient than fetching all product data at this stage
        qs_fetch_start = time.perf_counter()

        product_qs = Product.objects.filter(
            deleted=False,
            store_reference__is_active=True,
        ).filter(store_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('product_name', query),
            brand_similarity=TrigramSimilarity('brand_name', query),
            store_name_similarity=TrigramSimilarity('store_reference__store_name', query),
            store_handle_similarity=TrigramSimilarity('store_reference__storehandle', query),
            category_similarity=TrigramSimilarity('product_category', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(product_name__icontains=query) |
            Q(brand_name__icontains=query) |
            Q(name_similarity__gt=0.15) |
            Q(brand_similarity__gt=0.3) |
            Q(store_name_similarity__gt=0.3) |
            Q(store_handle_similarity__gt=0.3) |
            Q(category_similarity__gt=0.25)
        ).annotate(
            combined_score=Case(
                When(product_name__iexact=query, then=Value(1.0)),
                When(brand_name__iexact=query, then=Value(0.1)),
                default=Greatest('rank', 'name_similarity', 'brand_similarity', 'store_name_similarity', 'store_handle_similarity', 'category_similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')[offset:offset + limit]

        product_refs = list(product_qs.values_list('product_reference', flat=True))

        qs_fetch_end = time.perf_counter()
        logger.info(f"Total time for qs fetch products: {qs_fetch_end-qs_fetch_start:.4f} seconds")

        # get interaction details
        start_db_query = time.perf_counter()
        content_references_dict = dbqueries.get_content_interaction_details(
            content_references=product_refs, 
            visitor_reference=visitor_reference
            )
        interaction_and_creator_details = content_references_dict[0]
        product_references = content_references_dict[1]

        # print(f"interaction_and_creator_detail_references: {[detail['reference'] for detail in interaction_and_creator_details]}")
        # print(f"product_references: {product_references}")
                    
        neo4j_end = time.perf_counter()
        logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


        #sql fetch
        #sql data fetch
        sql_fetch_start = time.perf_counter()
        sql_instance = sqlqueries()
        product_data = sql_instance.get_products_hybrid(product_refs=product_references, store_ref=None, limit=limit, offset=0 )

        sql_fetch_end = time.perf_counter()
        logger.info(f"Total time for SQL: {sql_fetch_end-sql_fetch_start:.4f} seconds")

        product_data_json =  json.loads(product_data)

        # print(f"post_data_json: {post_data_json}")
        # print(f"product_data_json: {product_data_json}")

        #buy statuc batch fetch
        buy_status_start = time.perf_counter()
        checker = BuyStatusCheckerBatch(products=product_qs, user_pincode=user_pincode)
        buy_status_map = checker.get_buy_status_map()
        buy_status_end = time.perf_counter()
        logger.info(f"Total time for buy status: {buy_status_end-buy_status_start:.4f} seconds")

        # # method 1
        
        # Build a single lookup for all references
        reference_lookup = {}

        for product in product_data_json:
            reference_lookup[product["product_reference"]] = product

        # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
        # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

        # Build consolidated output in one pass
        consolidated_output = [
            {
                **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                **item["interaction"],
                **item["creator"],
                **buy_status_map.get(item["reference"], {})
            }
            for item in interaction_and_creator_details
        ]

        # print(f"consolidated_output: {consolidated_output}")

        total_time = time.perf_counter() - self.start
        logger.info(f"Total api time for products: {total_time:.4f} seconds")

        return Response({"message": "success", "product": consolidated_output}, status=200)

    def _search_store(self, query, user_pincode, offset, limit, start, visitor_reference=None):
        """
        Optimized store search across both real stores and user test stores.
        """
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('store_name', weight='A') +
            SearchVector('storehandle', weight='A') +
            SearchVector('business_description', weight='B') +
            SearchVector('store_details__city', weight='C') +
            SearchVector('store_desc', weight='C')
        )
        search_query = SearchQuery(query)

        # Base store filter: real stores + test stores created by owner (resolved via visitor_reference)
        store_filter = Q(deleted=False, is_active=True) & ~Q(storehandle__startswith="test_")

        # If visitor_reference is provided, resolve user or store reference
        if visitor_reference:
            owner_user = None
            try:
                # Check if visitor is a test store
                store = Store.objects.only('created_by').filter(
                    store_reference=visitor_reference,
                    is_test_store=True
                ).first()
                if store:
                    owner_user = store.created_by
            except Exception as e:
                logger.warning(f"Could not resolve visitor_reference '{visitor_reference}' to owner: {e}")

            # If owner_user exists, include test stores created by this user
            if owner_user:
                store_filter |= Q(is_test_store=True, created_by=owner_user)

        # Build and apply search ranking to main queryset
        main_qs = Store.objects.filter(store_filter, deleted=False, is_active=True).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('store_name', query),
            handle_similarity=TrigramSimilarity('storehandle', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(store_name__icontains=query) |
            Q(storehandle__icontains=query) |
            Q(business_description__icontains=query) |
            Q(store_details__city__icontains=query) |
            Q(name_similarity__gt=0.3) |
            Q(handle_similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(storehandle__iexact=query, then=Value(1.0)),
                When(store_name__iexact=query, then=Value(0.9)),
                default=Greatest('rank', 'handle_similarity', 'name_similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')[offset:offset + limit]

        combined_results = NewGetstoreSerializer.setup_eager_loading(main_qs)

        return Response(
            {
                "message": "success",
                "store": NewGetstoreSerializer(combined_results, many=True, context={"user_pincode": user_pincode}).data,
            },
            status=status.HTTP_200_OK,
        )

    def _search_user(self, query, offset, limit, start, visitor_reference=None):
        """
        Search for users with optimized performance.

        Args:
            query: Search query string
            offset: Number of results to skip
            limit: Number of results to return
            start: Start time for performance measurement
            visitor_reference: Optional visitor reference for interaction status

        Returns:
            Response with user search results
        """
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('user_name', weight='A') +
            SearchVector('first_name', weight='B') +
            SearchVector('display_name', weight='B') +
            SearchVector('about_user', weight='C')
        )
        search_query = SearchQuery(query)

        # Apply search ranking and filtering
        qs = User.objects.filter(deleted=False).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            similarity=TrigramSimilarity('user_name', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(user_name__icontains=query) |
            Q(similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(user_name__iexact=query, then=Value(1.0)),
                default=Greatest('rank', 'similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')[offset:offset+limit]

        return Response(
                {
                    "message": "success",
                    "user": GetUserDetailsSerializer(qs, many=True).data,
                },
                status=status.HTTP_200_OK,
            )


    def _search_post(self, query, visitor_reference, offset, limit, start):
        """
        Optimized post search across real stores and user's test stores.
        """
        # Identify user from visitor_reference (can be user ID or store reference)
        start = time.perf_counter()
        owner_user = None
        if visitor_reference:

            store = Store.objects.filter(store_reference=visitor_reference, is_test_store=True).only('created_by').first()
            if store:
                owner_user = store.created_by
            else:
                try:
                    owner_user = User.objects.only('userid').get(user_reference=visitor_reference)
                except User.DoesNotExist as e:
                    logger.warning(f"Could not resolve visitor_reference '{visitor_reference}' to owner: {e}")

        # Base store filter: real stores + test stores created by owner
        store_filter = Q(store_reference__is_test_store=False)
        if owner_user:
            store_filter |= Q(store_reference__is_test_store=True, store_reference__created_by=owner_user)

        user_filter = Q(user_reference__isnull=False)


        # Define weighted search vector
        search_vector = (
            SearchVector('post_text', weight='A') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B')
        )
        search_query = SearchQuery(query)

        qs_start = time.perf_counter()

        # Base queryset with filtering, annotation, and scoring
        qs = Posts.objects.select_related('store_reference').filter(
            is_deleted=False,
        ).filter(store_filter | user_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            post_text_similarity=TrigramSimilarity('post_text', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(post_text__icontains=query) |
            Q(post_text_similarity__gt=0.3)
        ).annotate(
            combined_score=Case(
                When(post_text__iexact=query, then=Value(1.0)),
                default=Greatest('rank', 'post_text_similarity'),
                output_field=TextField()
            )
        ).order_by('-combined_score')[offset:offset+limit].values_list('post_reference', flat=True)

        qs_end = time.perf_counter()
        logger.info(f"Total time for qs fetch: {qs_end - qs_start:.4f} seconds")

        # neo4j fetch
        start_db_query = time.perf_counter()

        content_references_dict = dbqueries.get_content_interaction_details(visitor_reference=visitor_reference, content_references=list(qs))
        interaction_and_creator_details = content_references_dict[0]
        post_references = content_references_dict[2]

        # print(f"interaction_and_creator_detail_references: {[detail['reference'] for detail in interaction_and_creator_details]}")
        # print(f"post_references: {post_references}")

        neo4j_end = time.perf_counter()
        logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


        #sql fetch
        #sql data fetch
        sql_fetch_start = time.perf_counter()

        sql_instance = sqlqueries()
        post_data = sql_instance.get_posts_full(post_refs=post_references)
        post_data_json = json.loads(post_data)

        sql_fetch_end = time.perf_counter()
        logger.info(f"Total time for SQL: {sql_fetch_end-sql_fetch_start:.4f} seconds")

        # # method 1
        # Build a single lookup for all references
        reference_lookup = {}

        for post in post_data_json:
            reference_lookup[post["post_reference"]] = post


        # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
        # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

        # Build consolidated output in one pass
        consolidated_output = [
            {
                **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                **item["interaction"],
                **item["creator"],
            }
            for item in interaction_and_creator_details
        ]

        # print(f"consolidated_output: {consolidated_output}")

        return Response({"message": "success", "posts": consolidated_output}, status=200)
