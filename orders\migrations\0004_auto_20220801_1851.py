# Generated by Django 3.2.13 on 2022-08-01 13:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0007_delete_category"),
        ("stores", "0006_auto_20220727_1458"),
        ("users", "0002_alter_userproduct_is_saved"),
        ("orders", "0003_cartitem_order_orderlifecycle_suborder"),
    ]

    operations = [
        migrations.AlterField(
            model_name="cartitem",
            name="productid",
            field=models.ForeignKey(
                blank=True,
                db_column="productid",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.product",
            ),
        ),
        migrations.AlterField(
            model_name="cartitem",
            name="storeid",
            field=models.ForeignKey(
                blank=True,
                db_column="storeid",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.store",
            ),
        ),
        migrations.AlterField(
            model_name="cartitem",
            name="userid",
            field=models.ForeignKey(
                blank=True,
                db_column="userid",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="users.user",
            ),
        ),
    ]
