from django.db import models
from users.invite_api.models import RewardsHistory
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.utils.translation import gettext_lazy as _

# Create your models here.


class Banner(models.Model):
    banner_id = models.AutoField(primary_key=True)
    image_url = models.ImageField(upload_to="banner_images")
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now=True)
    note = models.CharField(max_length=300, null=True, blank=True)
    page_reference = models.CharField(max_length=30, null=True, blank=True)
    page_url = models.CharField(max_length=2048, null=True, blank=True)
    banner_location = models.CharField(max_length=10, null=True, blank=True)
    current_user_type = models.CharField(max_length=25, blank=True, null=True)
    minimum_targeted_version = models.CharField(max_length=10, blank=True, null=True)
    opening_page = models.Char<PERSON><PERSON>(max_length=20, blank=True, null=True)

    class Meta:
        verbose_name_plural = "banners"
        db_table = '"public"."banner"'


class Request(models.Model):
    endpoint = models.CharField(max_length=200, null=True) # The url the user requested
    response_status_code = models.PositiveSmallIntegerField(null=True, blank=True) # Response status code
    request_method = models.CharField(max_length=10, null=True)  # Request method
    remote_address = models.CharField(max_length=50, null=True) # IP address of user
    exec_time = models.IntegerField(null=True, default=0) # Time taken to create the response
    date = models.DateTimeField(auto_now=True) # Date and time of request
    request_body = models.TextField(null=True)
    http_user_agent = models.TextField(null=True)
    query_string = models.TextField(null=True)
    start_time = models.CharField(max_length=50, null=True)
    end_time = models.CharField (max_length=50, null=True)
    user_reference = models.CharField(max_length=20, blank=True, null=True)
    async_flow_exception = models.TextField(null=True, blank=True)
    async_flow_exception_details = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name_plural = "requests"
        db_table = '"public"."requests"'


class ReservedHandles(models.Model):
    handle_name = models.CharField(max_length=30, default="")
    class Meta:
        verbose_name_plural = "reserved handles"
        db_table = '"public"."reserved_handles"'


class RewardTransferConfig(models.Model):
    config_id = models.AutoField(primary_key=True)
    transfer_type = models.CharField(max_length=20, choices=RewardsHistory.RewardTypeChoices.choices)
    sender_part = models.CharField(max_length=10, null=True, blank=True)
    receiver_part = models.CharField(max_length=10, null=True, blank=True)
    ratio = models.CharField(max_length=10, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Reward transfer configs"
        db_table = '"public"."reward_transfer_config"'


class AppConfigurations(models.Model):
    swadesic_default_invite_code = models.CharField(max_length=20, default="SWADESHIFIRST")
    real_store_creation_limit = models.IntegerField(default=5)
    test_store_creation_limit = models.IntegerField(default=1)
    invite_reward_onboarding = models.IntegerField(default=51)
    invite_reward_store_verification = models.IntegerField(default=51)
    flash_promotion_period = models.IntegerField(default=12)
    monthly_flash_point_credit = models.IntegerField(default=500)
    is_promotion_active = models.BooleanField(default=True)
    promotion_start_time = models.DateTimeField(null=True, blank=True)
    promotion_end_time = models.DateTimeField(null=True, blank=True)
    post_image_limit = models.IntegerField(default=5, blank=True, null=True)
    product_image_limit = models.IntegerField(default=5, blank=True, null=True)
    normal_store_verification_flash_point_reward = models.IntegerField(default=500)
    free_promotional_flash_point_reward = models.IntegerField(default=1000000)
    enable_app_orders = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "App configurations"
        db_table = '"public"."app_configurations"'

    # def save(self, *args, **kwargs):
    #     super().save(*args, **kwargs)
    #     from .views import AppConfigSingleton
    #     AppConfigSingleton.reset_instance()
    #
    # @classmethod
    # def from_db(cls, db, field_names, values):
    #     instance = super().from_db(db, field_names, values)
    #     from .views import AppConfigSingleton
    #     AppConfigSingleton.reset_instance()
    #     return instance



