from .invite_api.models import InviteUser, InviteConfiguration
from django.db.models import Q
import logging
import datetime
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def save_invite_credentials(
    invite_code, invite_type, current_invite_type, phonenumber, Mobile
):
    logger.info("inside save_invite_credentials in user table")
    if InviteUser.objects.filter(
        invite_code=invite_code,
        phone_number=phonenumber,
        invite_status=InviteUser.Invite_Status.CREATED,
        is_deleted=False,
    ).exists():
        invite_instance = InviteUser.objects.get(
            invite_code=invite_code, phone_number=phonenumber, is_deleted=False
        )
        if invite_type == InviteUser.Invite_Type_Choices.MEMBER:
            invite_balance = InviteConfiguration.objects.values_list(
                "max_invite_limit", flat=True
            ).get(invite_type=InviteUser.Invite_Type_Choices.MEMBER)
            Mobile.member_invite_balance = invite_balance
            invite_instance.invite_status = InviteUser.Invite_Status.USED
            Mobile.invite_code = invite_code
            Mobile.invite_type = invite_type
            Mobile.member_access = True
            Mobile.seller_access = False
            Mobile.view_store_contact = "1"
            Mobile.report_stores = "1"
            Mobile.view_store_documents = "1"
            Mobile.buy_product = "1"
            Mobile.add_comment = "1"
            Mobile.add_question = "1"
            Mobile.report_comment_question_review = "1"
            Mobile.report_people = "1"
            Mobile.send_member_invites = "1"
            Mobile.vote_for_membership = "1"
            Mobile.edit_community_forum = "1"
            Mobile.include_in_rating_calculation = "1"
            Mobile.included_in_others_trust_score = "1"
        elif invite_type == InviteUser.Invite_Type_Choices.SELLER:
            if (
                current_invite_type == InviteUser.Invite_Type_Choices.MEMBER
                or current_invite_type == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER
            ):
                invite_balance = InviteConfiguration.objects.values_list(
                    "max_invite_limit", flat=True
                ).get(invite_type=InviteUser.Invite_Type_Choices.MEMBER_TO_SELLER)
                Mobile.seller_invite_balance = invite_balance
            else:
                invite_balance = InviteConfiguration.objects.values_list(
                    "max_invite_limit", flat=True
                ).get(invite_type=InviteUser.Invite_Type_Choices.SELLER)
                Mobile.seller_invite_balance = invite_balance
            invite_instance.invite_status = InviteUser.Invite_Status.USED
            Mobile.invite_code = invite_code
            Mobile.invite_type = invite_type
            Mobile.member_access = True
            Mobile.seller_access = True
            Mobile.view_store_contact = "1"
            Mobile.report_stores = "1"
            Mobile.view_store_documents = "1"
            Mobile.buy_product = "1"
            Mobile.add_comment = "1"
            Mobile.add_question = "1"
            Mobile.report_comment_question_review = "1"
            Mobile.report_people = "1"
            Mobile.create_store = "1"
            Mobile.store_live = "1"
            Mobile.add_products = "1"
            Mobile.receive_orders = "1"
            Mobile.receive_payments = "1"
            Mobile.send_member_invites = "1"
            Mobile.send_seller_invites = "1"
            Mobile.vote_for_membership = "1"
            Mobile.create_promotion = "1"
            Mobile.edit_community_forum = "1"
            Mobile.include_in_rating_calculation = "1"
            Mobile.included_in_others_trust_score = "1"
        invite_instance.save()
        Mobile.save()
    # elif InviteUser.objects.filter(Q(invite_code=invite_code) & Q(phone_number__isnull=True) & Q(invite_status='InviteUser.Invite_Status.CREATED') & (~Q(remaining_invites=0)) & Q(is_deleted=False)).exists():
    elif InviteUser.objects.filter(
        invite_code=invite_code,
        phone_number__isnull=True,
        invite_status=InviteUser.Invite_Status.CREATED,
        is_deleted=False,
    ).exists():
        invite_instance = InviteUser.objects.get(
            invite_code=invite_code, is_deleted=False
        )
        Mobile.invite_code = invite_code
        Mobile.invite_type = invite_type
        invite_instance.remaining_invites -= 1
        if invite_type == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER:
            invite_balance = InviteConfiguration.objects.values_list(
                "max_invite_limit", flat=True
            ).get(invite_type=InviteUser.Invite_Type_Choices.MEMBER)
            Mobile.member_invite_balance = invite_balance
            Mobile.member_access = True
            Mobile.seller_access = False
            Mobile.view_store_contact = "1"
            Mobile.report_stores = "1"
            Mobile.view_store_documents = "1"
            Mobile.buy_product = "1"
            Mobile.add_comment = "1"
            Mobile.add_question = "1"
            Mobile.report_comment_question_review = "1"
            Mobile.report_people = "1"
            Mobile.send_member_invites = "1"
            Mobile.vote_for_membership = "1"
            Mobile.edit_community_forum = "1"
            Mobile.include_in_rating_calculation = "1"
            Mobile.included_in_others_trust_score = "1"
        elif invite_type == InviteUser.Invite_Type_Choices.CORPORATE_SELLER:
            if (
                current_invite_type == InviteUser.Invite_Type_Choices.MEMBER
                or current_invite_type == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER
            ):
                invite_balance = InviteConfiguration.objects.values_list(
                    "max_invite_limit", flat=True
                ).get(invite_type=InviteUser.Invite_Type_Choices.MEMBER_TO_SELLER)
                Mobile.seller_invite_balance = invite_balance
            else:
                invite_balance = InviteConfiguration.objects.values_list(
                    "max_invite_limit", flat=True
                ).get(invite_type=InviteUser.Invite_Type_Choices.SELLER)
                Mobile.seller_invite_balance = invite_balance
            Mobile.member_access = True
            Mobile.seller_access = True
            Mobile.view_store_contact = "1"
            Mobile.report_stores = "1"
            Mobile.view_store_documents = "1"
            Mobile.buy_product = "1"
            Mobile.add_comment = "1"
            Mobile.add_question = "1"
            Mobile.report_comment_question_review = "1"
            Mobile.report_people = "1"
            Mobile.create_store = "1"
            Mobile.store_live = "1"
            Mobile.add_products = "1"
            Mobile.receive_orders = "1"
            Mobile.receive_payments = "1"
            Mobile.send_member_invites = "1"
            Mobile.send_seller_invites = "1"
            Mobile.vote_for_membership = "1"
            Mobile.create_promotion = "1"
            Mobile.edit_community_forum = "1"
            Mobile.include_in_rating_calculation = "1"
            Mobile.included_in_others_trust_score = "1"
        invite_instance.save()
        Mobile.save()
        if invite_instance.remaining_invites == 0:
            invite_instance.invite_status = InviteUser.Invite_Status.CLOSED
            invite_instance.save()
    else:
        Mobile.invite_code = None
        Mobile.invite_type = InviteUser.Invite_Type_Choices.NON_MEMBER
    logger.info("exit save_invite_credentials functon")


def find_applicable_invite_type(input_invite_code, phonenumber):
    logger.info("inside find_applicable_invite_type function")

    invite_code = input_invite_code
    if InviteUser.objects.filter(
        invite_code=invite_code,
        phone_number=phonenumber,
        invite_status=InviteUser.Invite_Status.CREATED,
        is_deleted=False,
    ).exists():
        invite_instance = InviteUser.objects.get(
            invite_code=invite_code, phone_number=phonenumber, is_deleted=False
        )
        invite_type = invite_instance.invite_type
        invite_code = invite_instance.invite_code
    elif InviteUser.objects.filter(
        Q(invite_code=invite_code)
        & Q(phone_number__isnull=True)
        & Q(invite_status=InviteUser.Invite_Status.CREATED)
        & (~Q(remaining_invites=0))
        & Q(is_deleted=False)
    ).exists():
        invite_instance = InviteUser.objects.get(
            invite_code=invite_code, is_deleted=False
        )

        today = datetime.datetime.today().date()
        expiry_date_obj = invite_instance.invite_expiry_date
        expiry_date = datetime.datetime.strptime(expiry_date_obj, "%Y-%m-%d").date()
        if today <= expiry_date:
            invite_type = invite_instance.invite_type
            invite_code = invite_instance.invite_code
        else:
            invite_instance.invite_status = InviteUser.Invite_Status.EXPIRED
            invite_instance.save()
            invite_code = ""
            invite_type = InviteUser.Invite_Type_Choices.NON_MEMBER
    else:
        invite_code = ""
        invite_type = InviteUser.Invite_Type_Choices.NON_MEMBER
    logger.info("exit find_applicable_invite_type function")
    return invite_code, invite_type
