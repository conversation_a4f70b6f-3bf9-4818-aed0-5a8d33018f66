from django.urls import path
from .views import *
urlpatterns = [
    path('create_post/', Neo4jPostsCreateView.as_view(), name='graphdb-create-post'),
    path('update_post/', Neo4jPostsUpdateView.as_view(), name='graphdb-update-post'),
    path('get_post_tagged_object_details/', GetPostTaggedObjectDetails.as_view(), name='graphdb-get-post-tagged-object-details'),
    path('manage_post_tags/', PostTagManagementView.as_view(), name='graphdb-manage-post-tags'),
    path('add_comment/', AddCommentView.as_view(), name='graphdb-add-comment'),
    path('update_comment/', UpdateCommentView.as_view(), name='graphdb-update-comment'),
    path('delete_comment/', DeleteCommentView.as_view(), name='graphdb-delete-comment'),
    path('check_access/', ReviewAccessAPI.as_view(), name='graphdb-check-access'),

    #scheduler test urls
    path('create_feed_post/', CreateFeedPostsView.as_view(), name='graphdb_create_feedposts'),
    path('auto_follow/', AutoFollowView.as_view(), name='graphdb_autofollow'),
    path('delete_unr_users/',DeleteUnrUser.as_view(), name='delete-unr-user')
]
