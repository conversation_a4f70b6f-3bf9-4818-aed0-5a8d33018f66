# Generated by Django 3.2.13 on 2022-10-29 04:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0020_productversion_created_date"),
    ]

    operations = [
        migrations.AddField(
            model_name="productversion",
            name="in_stock",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="productversion",
            name="mrp_price",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="productversion",
            name="promotion_link",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="productversion",
            name="store_reference",
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
        migrations.AddField(
            model_name="productversion",
            name="storeid",
            field=models.IntegerField(null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="productversion",
            name="brand_name",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
    ]
