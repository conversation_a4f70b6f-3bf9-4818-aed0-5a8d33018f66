from django.db import models
from products.models import Product
from common.util.support_helper import DeleteManager


class CartItem(models.Model):
    IN_CART = "IN_CART"
    CART_DONE = "CART_DONE"

    CART_STATUS_TYPE = [
        (IN_CART, "incart"),
        (CART_DONE, "cart done"),
    ]
    cartitemid = models.AutoField(primary_key=True)
    userid = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_column="userid",
    )
    storeid = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_column="storeid",
    )
    productid = models.IntegerField(null=True, blank=True)
    product_reference = models.ForeignKey(
        "products.Product",
        blank=True,
        null=True,
        to_field="product_reference",
        on_delete=models.CASCADE,
        db_column="product_reference",
    )
    product_quantity = models.IntegerField(default=0)
    cart_product_status = models.Char<PERSON><PERSON>(max_length=500, choices=CART_STATUS_TYPE)
    created_by = models.CharField(max_length=500, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_by = models.CharField(max_length=500, null=True, blank=True)
    modified_date = models.DateTimeField(auto_now=True)
    is_deleted = models.BooleanField(default=False)
    order_request_number = models.CharField(max_length=50, null=True, blank=True)
    product_version = models.CharField(max_length=50, null=True, blank=True)

    objects = models.Manager()  # The default manager.
    new_objects = DeleteManager()  # delete manager.

    def save(self, *args, **kwargs):
        product = Product.objects.get(
            product_reference=self.product_reference.product_reference
        )
        product_version = product.product_version
        product_id = product.productid
        self.product_version = product_version
        self.productid = product_id
        super(CartItem, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "cart items"
        db_table = '"order"."cart_item"'
