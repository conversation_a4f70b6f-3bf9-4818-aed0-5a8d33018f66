from django.urls import path
from .views import (
    AddToOrRemoveFromCart,
    UpdateProductQuantity,
    GetCartItems,
    GetCartDetails,
    GetUserDetails,
    FeeCalculation,
    DeleteCarItems
)


urlpatterns = [
    #  CART
    path(
        "add_to_or_remove_from_cart/<int:pk>/",
        AddToOrRemoveFromCart.as_view(),
        name="add-or-remove-cart",
    ),
    path(
        "remove_cart_items/",
        DeleteCarItems.as_view(),
        name="remove-cart-items",
    ),
    path(
        "update_product_quantity/<int:pk>/",
        UpdateProductQuantity.as_view(),
        name="update-quantity",
    ),
    path("get_cart_items/<int:userid>/", GetCartItems.as_view(), name="get-cart-items"),
    path("get_cart_details/", GetCartDetails.as_view(), name="get-cart-details"),
    path(
        "get_user_details/<int:pk>/", GetUserDetails.as_view(), name="get-user-details"
    ),
    path(
        "delivery_fees_calculation/",
        FeeCalculation.as_view(),
        name="delivery-fee-calculation",
    ),
]
