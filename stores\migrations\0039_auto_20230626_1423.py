# Generated by Django 3.2.13 on 2023-06-26 08:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0038_auto_20230622_1445'),
    ]

    operations = [
        migrations.RenameField(
            model_name='store',
            old_name='is_verified',
            new_name='is_gst_verified',
        ),
        migrations.AddField(
            model_name='store',
            name='is_pan_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='store',
            name='pan_name',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='store',
            name='pan_number',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='store',
            name='verification_type',
            field=models.CharField(blank=True, choices=[('BUSINESS', 'Business'), ('INDIVIDUAL', 'Individual')], max_length=30, null=True),
        ),
    ]
