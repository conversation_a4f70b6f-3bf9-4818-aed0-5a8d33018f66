# Generated by Django 3.2.13 on 2022-11-28 06:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0020_inviteuser_invitee_name"),
        ("stores", "0018_auto_20221111_1736"),
        ("orders", "0039_auto_20221125_1319"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderPayout",
            fields=[
                (
                    "order_payout_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "order_value",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("order_date", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "payout_status",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "order_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="order_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.order",
                        to_field="order_number",
                    ),
                ),
                (
                    "store_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="store_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
                (
                    "suborder_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="suborder_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.suborder",
                        to_field="suborder_number",
                    ),
                ),
                (
                    "user_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="user_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "order payout",
                "db_table": '"order"."order_payout"',
            },
        ),
    ]
