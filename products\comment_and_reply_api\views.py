from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework import generics, mixins
import datetime
from django.utils.timezone import utc
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from drf_yasg import openapi
from .serializers import (
    AddCommentSerializer,
    GetCommentSerializer,
    AddReplySerializer,
    GetReplySerializer,
    ReviewImageSerializer,
    AddReviewCommentSerializer,
)
from django.db.models import Q
from .models import Comment, Reply, ReviewImages
from ..models import Product, ProductImages
from orders.order_api.models import SubOrder, Order
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from users.user_api.models import User
from drf_yasg.utils import swagger_auto_schema
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class CheckReviewAccess(APIView):
    def post(self, request):
        user_reference = request.data["reference"]
        product_reference = request.data["product_reference"]

        # list has suborder status which has review access
        lst = [
            SubOrder.Suborder_Status.ORDER_DELIVERED,
            SubOrder.Suborder_Status.DELIVERY_FAILED,
            SubOrder.Suborder_Status.RETURN_REQUESTED,
            SubOrder.Suborder_Status.RETURN_CONFIRMED,
            SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
            SubOrder.Suborder_Status.RETURNED_TO_SELLER,
            SubOrder.Suborder_Status.RETURN_FAILED,
        ]
        # checking if this particular user has purchased this product or not, so that he can give review access.
        if SubOrder.objects.filter(Q(user_reference=user_reference)
                                   & Q(product_reference=product_reference)
                                   & Q(suborder_status__in=lst)).exists():
            return Response({"message": "success", "review_access": True}, status=status.HTTP_200_OK)
        else:
            return Response({"message": "success", "review_access": False}, status=status.HTTP_200_OK)

class AddCommentAV(APIView):
    @swagger_auto_schema(
        operation_summary="Add comment",
        operation_description="Add comment to a product.",
        request_body=AddCommentSerializer,
    )
    def post(self, request):
        # Here this reference can be of user_reference or store_reference. ie, user or store can comment on a product.
        reference = request.data["reference"]
        comment_type = request.data["comment_type"]
        product_reference = request.data["product_reference"]

        # list has suborder status which has review access
        lst = [
            SubOrder.Suborder_Status.ORDER_DELIVERED,
            SubOrder.Suborder_Status.DELIVERY_FAILED,
            SubOrder.Suborder_Status.RETURN_REQUESTED,
            SubOrder.Suborder_Status.RETURN_CONFIRMED,
            SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
            SubOrder.Suborder_Status.RETURNED_TO_SELLER,
            SubOrder.Suborder_Status.RETURN_FAILED,
        ]
        # checking if this particular user has purchased this product or not, so that he can give review access.
        if comment_type == Comment.Comment_Type.REVIEW:
            if SubOrder.objects.filter(
                Q(user_reference=reference)
                & Q(product_reference=product_reference)
                & Q(suborder_status__in=lst)
            ).exists():

                # if user already purchased this product, take the latest purchased suborder.
                suborder_instance = (
                    SubOrder.objects.filter(
                        Q(user_reference=reference)
                        & Q(product_reference=product_reference)
                    )
                    .order_by("-created_date")
                    .first()
                )

                # Add the suborder number to the request body to save it on comment table.
                request.data["order_reference"] = suborder_instance.suborder_number

                # In comment table, if this user has already reviewed this product, then edit that review
                if Comment.objects.filter(
                    comment_type=Comment.Comment_Type.REVIEW,
                    reference=reference, product_reference=product_reference
                ).exists():
                    instance = Comment.objects.get(
                        comment_type=Comment.Comment_Type.REVIEW,
                        reference=reference, product_reference=product_reference
                    )
                    serializer = AddReviewCommentSerializer(instance, data=request.data)
                    if serializer.is_valid():
                        serializer.save()
                        return Response(
                            {
                                "message": "success",
                                "details": "review updated",
                                "data": serializer.data,
                            },
                            status=status.HTTP_200_OK,
                        )
                    return Response(
                        {"message": "error", "data": serializer.errors},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                # If this user reviewing this product for first time create a new review.
                else:
                    serializer = AddReviewCommentSerializer(data=request.data)
                    if serializer.is_valid():
                        serializer.save()
                        comments = serializer.data["comments"]
                        self.comment_notification(product_reference, reference, comment_type, comments)
                        return Response(
                            {
                                "message": "success",
                                "details": "review added",
                                "data": serializer.data,
                            },
                            status=status.HTTP_200_OK,
                        )
                    return Response(
                        {"message": "error", "data": serializer.errors},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # if the user doesn't purchase this product and trying to review a product, they have no access
            else:
                return Response(
                    {"message": "success", "data": "no review access"},
                    status=status.HTTP_200_OK,
                )
        else:
            serializer = AddCommentSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                comments = serializer.data["comments"]
                self.comment_notification(product_reference, reference, comment_type, comments)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @staticmethod
    def action_get_store_handle(comment_handler_data):

        product_instance = comment_handler_data["product_instance"]
        store_handle = product_instance.store_reference.storehandle
        return store_handle

    @staticmethod
    def action_get_user_handle(comment_handler_data):

        reference = comment_handler_data["reference"]
        user_instance = User.objects.get(user_reference=reference)
        user_handle = user_instance.user_name
        return user_handle

    def comment_notification(self, product_reference, reference, comment_type, comments):

        product_instance = Product.objects.get(product_reference=product_reference)
        product_image_instance = ProductImages.objects.filter(product_reference=product_reference).first()

        reference_type = {
            'S': self.action_get_store_handle,
            'U': self.action_get_user_handle
        }

        action_method = reference_type.get(reference[0])
        if action_method is not None:
            comment_handler_data = {
                "product_instance": product_instance,
                "reference": reference
            }
            commented_by = action_method(comment_handler_data)

        notified_to = product_instance.store_reference.store_reference
        image = product_image_instance.product_image

        notification_handler = NotificationHandler(
            notified_user=notified_to,
            notification_type=Notifications.Notifications_Type.COMMENTED,
            notification_about=product_reference,
            image=image,
            commented_by=commented_by,
            comment_type=comment_type,
            comments=comments
        )
        notification_handler.create_notification(notification_handler)

class GetCommentAV(APIView):
    @swagger_auto_schema(operation_summary="Get"
                                           " all comments of a product")
    def get(self, request, product_reference, reference):
        """
        Get all the comment of a product particular to a user\n
        This will include  if that user clapped any comment or not.
        """
        is_pdt_exists = Product.objects.filter(product_reference=product_reference).exists()
        if not is_pdt_exists:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        else:
            try:

                comments = Comment.objects.filter(
                    product_reference=product_reference
                )  # get all the comment of a product
                now = datetime.datetime.utcnow().replace(tzinfo=utc)

                serializer = GetCommentSerializer(comments, many=True)
                serializer_data = serializer.data

                for data in serializer_data:

                    for reply in data["replies"]:

                        reply_clapped_users = reply.get(
                            "clapped_users"
                        )
                        # for replies of each comment get the
                        # clapped_users, It will be in the piped format  (eg : '1|2|3')

                        if reply_clapped_users:
                            int_reply_clapped_users = reply_clapped_users.split("|")
                            # from the piped data make a list of user ids
                            if (
                                reference in int_reply_clapped_users
                            ):  # if the user_id inside the above list, we can make user clapped as true
                                reply["user_clapped_or_not"] = True

                    comment_clapped_users = data[
                        "clapped_users"
                    ]  # same logic above applied here also to get this user clapped any comment or not.
                    if comment_clapped_users:
                        int_comment_clapped_users = comment_clapped_users.split("|")
                        if reference in int_comment_clapped_users:
                            data["user_clapped_or_not"] = True
                    # to find the commented time
                    date_time_obj = datetime.datetime.strptime(
                        data["modified_date"], "%Y-%m-%dT%H:%M:%S.%f%z"
                    )
                    timediff = now - date_time_obj
                    second = int(timediff.total_seconds())
                    time = int(second // 3600)
                    if time >= 24:
                        data["commented_at"] = str(time // 24) + "day"
                    else:
                        if time >= 1:
                            data["commented_at"] = str(time) + "h"
                        elif time < 1:
                            if second // 60 >= 1:
                                data["commented_at"] = str(second // 60) + "m"
                            elif second // 60 < 1:
                                data["commented_at"] = str(second) + "s"
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            except Comment.DoesNotExist:
                return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class AddReplyAV(APIView):
    @swagger_auto_schema(operation_summary="Add reply", request_body=AddReplySerializer)
    def post(self, request):
        """Add reply to a comment"""
        serializer = AddReplySerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class UpdateCommentsAndReply(APIView):
    def patch(self, request, pk):

        """updating comment and reply uses this same api, difference can be noticed by 'type' field in the request data.
        If type is 'parent' that means the call is for updating a comment, if the value of type is 'child', means it is
        for updating a reply."""

        if request.data["type"] == "parent":
            comment_instance = Comment.objects.get(pk=pk)  # pk is comment_id
            serializer = GetCommentSerializer(
                comment_instance, data=request.data, partial=True
            )  # set partial=True to update a data partially
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        elif request.data["type"] == "child":
            reply_instance = Reply.objects.get(pk=pk)  # pk is reply_id
            serializer = GetReplySerializer(
                reply_instance, data=request.data, partial=True
            )  # set partial=True to update a data partially
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AddAndRemoveClaps(APIView):
    @swagger_auto_schema(
        operation_summary="Add or remove claps for a comment or reply",
        operation_description="Adding claps for comment and reply uses this same api they differentiated by looking the type field and"
        "weather replyid or commentid passing in request body.\n "
        "example:\n"
        "if 'type': 'child', that means claps are adding to replies, "
        "should provide 'replyid in request body'\n"
        "if 'type': 'parent', that means claps are adding to comment,  "
        "should provide 'commentid' in request body\n"
        "all other fields are common.\n"
        "calculations are done on front end so here just save the number of claps and clapped users in the piped format.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["type", "replyid", "claps", "clapped_users"],
            properties={
                "type": openapi.Schema(type=openapi.TYPE_STRING),
                "replyid or commentid": openapi.Schema(type=openapi.TYPE_INTEGER),
                "claps": openapi.Schema(type=openapi.TYPE_INTEGER),
                "clapped_users": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request):

        try:
            if request.data["type"] == "parent":
                comment_id = request.data["commentid"]
                comment_instance = Comment.objects.get(pk=comment_id)
                comment_instance.claps = request.data["claps"]
                comment_instance.clapped_users = request.data["clapped_users"]
                comment_instance.save(update_fields=["claps", "clapped_users"])
                return Response({"message": "success"}, status=status.HTTP_200_OK)

            elif request.data["type"] == "child":
                reply_id = request.data["replyid"]
                reply_instance = Reply.objects.get(pk=reply_id)
                reply_instance.claps = request.data["claps"]
                reply_instance.clapped_users = request.data["clapped_users"]
                reply_instance.save(update_fields=["claps", "clapped_users"])
                return Response({"message": "success"}, status=status.HTTP_200_OK)
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class DeleteComment(APIView):
    @swagger_auto_schema(operation_summary="Delete comment")
    def delete(self, request, comment_id):
        """Delete a comment using comment_id, when a comment deleted associated replies also deletes."""
        try:
            Comment.objects.get(commentid=comment_id).delete()
            Reply.objects.filter(commentid=comment_id).delete()
            return Response({"message": "success"}, status=status.HTTP_200_OK)
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class DeleteReply(APIView):
    @swagger_auto_schema(
        operation_summary="Delete reply",
        operation_description="Delete a reply using reply_id",
    )
    def delete(self, request, reply_id):
        try:
            reply = Reply.objects.get(replyid=reply_id)
        except Reply.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        reply.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class AddAndDeleteReviewImage(
    mixins.CreateModelMixin, mixins.DestroyModelMixin, generics.GenericAPIView
):
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        operation_summary="Add review images", request_body=ReviewImageSerializer
    )
    def post(self, request, comment_id):

        """Add multiple images to a comment. images can be added only to the comments with type review."""

        form_data = {}
        form_data["commentid"] = request.data["commentid"]
        form_data["created_by"] = request.data["created_by"]
        success = True
        image_list = []
        images = request.FILES.getlist("image")

        for image in images:
            form_data["image"] = image  # store product_images to the dictionary
            serializer = ReviewImageSerializer(data=form_data)
            if serializer.is_valid():
                serializer.save()
                image_list.append(serializer.data)
            else:
                success = False
        if success:
            return Response(
                {"message": "success", "data": image_list}, status=status.HTTP_200_OK
            )

        return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Delete review images",
        operation_description="Delete review images using comment_id",
    )
    def delete(self, request, comment_id):
        try:
            comment_review_image = ReviewImages.objects.get(pk=comment_id)
        except ReviewImages.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        comment_review_image.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)
