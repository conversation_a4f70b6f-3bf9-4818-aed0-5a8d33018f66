from rest_framework import generics, mixins
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from .notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from orders.order_api.models import SubOrder, OrderOtp
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import random
import math
import logging
import datetime
import pytz
from general.models import AppConfigurations
from rest_framework.permissions import AllowAny


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class GenerateOtp(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Otp generation",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["phone_number"],
            properties={"phone_number": openapi.Schema(type=openapi.TYPE_STRING)},
        ),
    )
    def post(self, request, *args, **kwargs):
        """
        Generate otp for a phone number and send it using sms provider. while order delivery, this otp will use to
        varify the customer.
        """
        logger.info("Entered generate otp api")
        phone_number = request.data["phone_number"]
        otp_type = request.data["otp_type"]
        user_reference = request.data["user_reference"]
        package_number = request.data["package_number"]

        if OrderOtp.objects.filter(
            user_reference=user_reference,
            package_number=package_number,
            otp_status=OrderOtp.Status.UNUSED,
        ).exists():
            # fetch the otp and otp created time, for this package number which is not used.
            otp_instance = OrderOtp.objects.get(
                user_reference=user_reference,
                package_number=package_number,
                otp_status=OrderOtp.Status.UNUSED,
            )
            otp = otp_instance.otp
        else:
            digits = "**********"
            otp = ""
            # generate a four digit otp.
            for i in range(4):
                otp += digits[math.floor(random.random() * 10)]
            OrderOtp.objects.create(
                user_reference=user_reference, package_number=package_number, otp=otp
            )

        # keygen = generateKey()
        # key = base64.b32encode(keygen.returnValue(user_reference, package_number, otp).encode())  # create a key using phone number and otp

        if otp_type == "DELIVERY":
            notifications_type = Notifications.Notifications_Type.DELIVERY_OTP
            message = "Your delivery code is: " + otp
        elif otp_type == "RETURN":
            notifications_type = Notifications.Notifications_Type.RETURN_OTP
            message = "Your return pickup code is: " + otp
        # publish otp to phone number using sms provider, here aws sms service is used.
        # try:
        #     response = client.publish(
        #             PhoneNumber=phone_number, Message=message)
        #     message_id = response['MessageId']
        #     logger.info("Published message to %s %s.", phone_number, message_id)
        logger.info("Exited generate otp api")
        if package_number.startswith("P"):
            order_reference_lst = list(
                SubOrder.objects.filter(package_number=package_number)
                .values_list("order_number", flat=True)
                .distinct("order_number")
            )
        elif package_number.startswith("R"):
            order_reference_lst = list(
                SubOrder.objects.filter(return_package_number=package_number)
                .values_list("order_number", flat=True)
                .distinct("order_number")
            )
        try:
            for order_reference in order_reference_lst:
                sub_order = SubOrder.objects.filter(
                    order_number=order_reference
                ).first()
                if sub_order.product_image:
                    image = sub_order.product_image
                    package_name = sub_order.display_package_number
                else:
                    image = None
                    package_name = None

                notification_handler = NotificationHandler(
                    notified_user=user_reference,
                    notification_type=notifications_type,
                    notification_about=order_reference,
                    image=image,
                    otp=otp,
                    package_name=package_name,
                )
                notification_handler.create_notification(notification_handler)
                # notification_gen = GenerateNotifications()
                # notification_gen.create_notifications(
                #     notified_user=user_reference,
                #     notification_type=notifications_type,
                #     notification_about=order_reference,
                #     otp=otp,
                #     image=image,
                # )
        except:
            logger.info("generating otp notification has failed.")
        # In response send generated key as a token
        return Response({"message": "success", "otp": otp}, status=status.HTTP_200_OK)
        # except:
        #     return Response({"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VerifyOtp(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Otp verification",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["user_reference", "package_number", "otp"],
            properties={
                "user_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "package_number": openapi.Schema(type=openapi.TYPE_STRING),
                "otp": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        """validate the otp against phone number."""
        logger.info("Entered validate otp api")

        input_otp = request.data["otp"]
        user_reference = request.data["user_reference"]
        package_number = request.data["package_number"]

        otp_object = OrderOtp.objects.filter(
            user_reference=user_reference,
            package_number=package_number,
        ).order_by("-generated_time").last()

        if otp_object:
            # fetch the otp and otp created time, for this package number which is not used.
            saved_otp = otp_object.otp
            otp_created_time = otp_object.generated_time

            # expiry time is kept as 5 minutes.
            otp_expiry_time = otp_created_time + datetime.timedelta(minutes=5)

            # convert the fetched time to local time.
            converted_otp_expiry_time = otp_expiry_time.astimezone(
                pytz.timezone("Asia/Kolkata")
            )

            # find the now time and convert it to local time.
            now = datetime.datetime.now().astimezone(pytz.timezone("Asia/Kolkata"))

            # compare current time with the converted otp expiry time. if it's true that means otp expired, if not
            # otp can be used for validation.
            otp_expired = now > converted_otp_expiry_time

            # Match the input otp and saved otp, If both matches we can confirm otp verified.
            if input_otp == saved_otp and not otp_expired:
                # update the otp status as USED.
                otp_object.otp_status = OrderOtp.Status.USED
                otp_object.save(update_fields=["otp_status"])
                logger.info("Exited validate otp api")
                return Response({"message": "success", "is_valid": True}, status=status.HTTP_200_OK)

            elif input_otp == saved_otp and otp_expired:
                # update the otp status as EXPIRED.
                otp_object.otp_status = OrderOtp.Status.EXPIRED
                otp_object.save(update_fields=["otp_status"])
                logger.error("otp expired")
                return Response(
                    {"message": "otp expired", "is_valid": False}, status=status.HTTP_200_OK
                )
            elif input_otp != saved_otp:
                logger.error("Wrong otp entered")
                return Response(
                    {"message": "error", "is_valid": False}, status=status.HTTP_200_OK
                )
            else:
                logger.error("something went wrong in validate otp api")
                return Response(
                    {"message": "error"}, status=status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class AppMetaDataView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        app_meta_data = {
          "6L3FGJSS2NZXFUYTONJ": "PMFCAIBAEARGIZLWEI5CA6YKEAQCAIBAEAQCAITCMFZWKX3VOJWCEORAEJUHI5DQOM5C6L3FGJSS2NZXFUYTONJOONZWIY3MN52WI2LOMRUWCLTOMV2C6ZDFOYRAUIBAEAQH2LAKEAQCAIBCOFQSEORAPMFCAIBAEAQCAIBAEJRGC43FL52XE3BCHIQCE2DUORYHGORPF5STEZJNG43S2MJXGUXHG43EMNWG65LENFXGI2LBFZXGK5BPOFQSECRAEAQCA7JMBIQCAIBAEJYHE33EEI5CA6YKEAQCAIBAEAQCAITCMFZWKX3VOJWCEORAEJUHI5DQOM5C6L3FGJSS2NRVFUYTONZOONZWIY3MN52WI2LOMRUWCLTOMV2C64DSN5SCECRAEAQCA7JMBIQCAIBAEJUXGX3NMFXGIYLUN5ZHSX3VOBSGC5DFEI5CA5DSOVSSYCRAEAQCAITBOBYF65TFOJZWS33OEI5CAIRRFYZS4NRCBJ6Q===="
        }

        return Response(app_meta_data, status=status.HTTP_200_OK)


class GetAppConfigurations(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        app_config = AppConfigurations.objects.first()
        response_data = {
            'post_image_limit': app_config.post_image_limit,
            'product_image_limit': app_config.product_image_limit,
        }
        return Response({"app_config": response_data}, status=status.HTTP_200_OK)
