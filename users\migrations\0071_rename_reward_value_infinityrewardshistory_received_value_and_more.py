# Generated by Django 4.2.7 on 2024-07-13 11:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0070_infinityrewardshistory_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="infinityrewardshistory",
            old_name="reward_value",
            new_name="received_value",
        ),
        migrations.RenameField(
            model_name="infinityrewardshistory",
            old_name="received_user",
            new_name="receiver",
        ),
        migrations.RenameField(
            model_name="infinityrewardshistory",
            old_name="sent_user",
            new_name="sender",
        ),
        migrations.AddField(
            model_name="infinityrewardshistory",
            name="reward_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("SUCCESS", "Success"),
                    ("PENDING", "Pending"),
                    ("FAILED", "Failed"),
                ],
                max_length=10,
                null=True,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="infinityrewardshistory",
            name="sent_value",
            field=models.Char<PERSON>ield(blank=True, max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name="infinityrewardshistory",
            name="reward_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                    ("U2U_TRANSFER", "U2U Transfer"),
                    ("S2U_TRANSFER", "S2U Transfer"),
                ],
                max_length=20,
                null=True,
            ),
        ),
    ]
