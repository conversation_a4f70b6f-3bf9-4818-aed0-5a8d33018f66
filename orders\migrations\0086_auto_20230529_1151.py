# Generated by Django 3.2.13 on 2023-05-29 06:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0085_alter_escalation_suborder_reference'),
    ]

    operations = [
        migrations.AddField(
            model_name='suborder',
            name='transaction_fee',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='suborder',
            name='suborder_status',
            field=models.Char<PERSON>ield(choices=[('ORDER_INITIATED', 'Order initiated'), ('PAYMENT_INITIATED', 'Payment initiated'), ('PAYMENT_SUCCESS', 'Payment success'), ('WAITING_FOR_CONFIRMATION', 'waiting for confirmation'), ('PAYMENT_PENDING', 'Payment pending'), ('PAYMENT_FAILED', 'Payment failed'), ('ORDER_CONFIRMED', 'Order confirmed'), ('DELIVERY_IN_PROGRESS', 'Delivery in progress'), ('ORDER_DELIVERED', 'Order delivered'), ('ORDER_CANCELLED', 'Order cancelled'), ('ORDER_CANCELLED_BY_BUYER', 'Order cancelled by buyer'), ('ORDER_CANCELLED_BY_SELLER', 'Order cancelled by seller'), ('DELIVERY_FAILED', 'Delivery failed'), ('RETURN_REQUESTED', 'Return requested'), ('RETURN_CONFIRMED', 'Return confirmed'), ('RETURN_IN_PROGRESS', 'Return in progress'), ('RETURNED_TO_SELLER', 'Return to seller'), ('RETURN_FAILED', 'Return failed'), ('ORDER_AUTO_CANCELLED', 'Order auto cancelled')], default='ORDER_INITIATED', max_length=50),
        ),
    ]
