# Generated by Django 3.2.22 on 2023-11-01 17:02

from django.db import migrations, models
import django.db.models.deletion

def populate_user_follower_and_store_follower(apps, schema_editor):
    UserFollow = apps.get_model('users', 'UserFollow')
    User = apps.get_model('users', 'User')
    Store = apps.get_model('stores', 'Store')

    for user_follow in UserFollow.objects.all():
        if user_follow.follower_reference.startswith('U'):
            user_reference = user_follow.follower_reference
            try:
                user_follow.user_follower = User.objects.get(user_reference=user_reference)
            except User.DoesNotExist:
                user_follow.user_follower = None

        elif user_follow.follower_reference.startswith('S'):
            store_reference = user_follow.follower_reference
            try:
                user_follow.store_follower = Store.objects.get(store_reference=store_reference)
            except User.DoesNotExist:
                user_follow.user_follower = None
        user_follow.save()


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0045_merge_20231005_1006'),
        ('users', '0044_alter_userfollow_follower_reference'),
    ]


    operations = [

        migrations.AddField(
            model_name='userfollow',
            name='store_follower',
            field=models.ForeignKey(blank=True, db_column='store_follower', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='storefollower', to='stores.store', to_field='store_reference'),
        ),
        migrations.AddField(
            model_name='userfollow',
            name='user_follower',
            field=models.ForeignKey(blank=True, db_column='user_follower', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='userfollower', to='users.user', to_field='user_reference'),
        ),

        migrations.RunPython(populate_user_follower_and_store_follower),

        migrations.RemoveField(
            model_name='userfollow',
            name='follower_reference',
        ),
    ]
