from django.urls import path
from .views import BannerDetailsAV,BannerListAV, BulkCreateReservedHandles, UpdatedBannerDetailsAV, UpdatedBannerListAV

urlpatterns = [
    path("banner_list/", BannerListAV.as_view(), name="banner-list"),
    path(
        "banner_details/<int:banner_id>/",
        BannerDetailsAV.as_view(),
        name="banner-details",
    ),
    path("banner_list/v2/", UpdatedBannerListAV.as_view(), name="banner-list-v2"),
    path(
        "banner_details/<int:banner_id>/v2/",
        UpdatedBannerDetailsAV.as_view(),
        name="banner-details-v2",
    ),
    path("add_reserved_handles/", BulkCreateReservedHandles.as_view(), name="add-reserved-handles"),
]