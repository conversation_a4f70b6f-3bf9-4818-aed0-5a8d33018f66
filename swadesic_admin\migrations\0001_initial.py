# Generated by Django 4.2.7 on 2024-11-14 22:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SubscriptionPlan",
            fields=[
                ("plan_reference", models.Char<PERSON>ield(max_length=25, unique=True)),
                ("plan_id", models.AutoField(primary_key=True, serialize=False)),
                ("plan_name", models.CharField(max_length=100)),
                ("plan_description", models.TextField(blank=True, null=True)),
                ("plan_details", models.TextField(blank=True, null=True)),
                (
                    "plan_period",
                    models.Char<PERSON>ield(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("yearly", "Yearly"),
                        ],
                        max_length=10,
                    ),
                ),
                ("plan_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("razorpay_plan_id", models.<PERSON>r<PERSON><PERSON>(max_length=100, unique=True)),
                (
                    "plan_type",
                    models.Char<PERSON>ield(
                        choices=[("user", "User Plan"), ("store", "Store Plan")],
                        max_length=5,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Subscription Plan",
                "db_table": '"public"."subscription_plan"',
            },
        ),
    ]
