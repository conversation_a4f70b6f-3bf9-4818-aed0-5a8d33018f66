import os
import graphene
from graphene_file_upload.scalars import Upload
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import transaction
from .models import Posts, PostImages, PostLikes
from .types import PostsType, PostImagesType, PostLikesType
from users.user_api.models import User
from stores.store_api.models import Store
from .serializers import PostsCreateSerializer, PostImagesSerializer


class CreatePostMutation(graphene.Mutation):
    class Arguments:
        user_reference = graphene.String(required=False)
        store_reference = graphene.String(required=False)
        post_text = graphene.String(required=False)
        post_image = Upload()

    post = graphene.Field(PostsType)

    def mutate(root, info, user_reference=None, store_reference=None, post_text=None, post_image=None):
        user = None
        store = None

        # Check if the user with the given reference exists
        if user_reference:
            try:
                user = User.objects.get(user_reference=user_reference)
            except User.DoesNotExist:
                raise Exception("User with the given reference does not exist.")

        # Check if store_reference is provided
        if store_reference:
            # Check if the store with the given reference exists
            try:
                store = Store.objects.get(store_reference=store_reference)
            except Store.DoesNotExist:
                raise Exception("Store with the given reference does not exist.")

        post = Posts(user_reference=user, store_reference=store, post_text=post_text)
        post.save()

        if post_image:
                post_image_obj = PostImages(post_reference=post, post_image=post_image)
                post_image_obj.save()

        return CreatePostMutation(post=post)


class UpdatePostMutation(graphene.Mutation):
    class Arguments:
        post_reference = graphene.String(required=True)
        post_text = graphene.String(required=False)
        post_images = graphene.List(graphene.String, required=False)

    post = graphene.Field(PostsType)

    def mutate(root, info, post_reference, post_text=None, post_images=None):
        try:
            post_instance = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        except Posts.DoesNotExist:
            raise Exception(f"Entry with post reference {post_reference} does not exist.")

        partial_update_data = {}

        if post_text:
            post_instance.post_text = post_text
            post_instance.save()
            partial_update_data['post_text'] = post_text

        if post_images:
            post_images_data = [{'post_reference': post_reference, 'post_image': image} for image in post_images]
            existing_post_images = PostImages.objects.filter(post_reference=post_reference)

            with transaction.atomic():
                existing_post_images.exclude(post_image__in=post_images).delete()
                post_images_serializer = PostImagesSerializer(data=post_images_data, many=True)

                if post_images_serializer.is_valid():
                    post_images_serializer.save()
                    partial_update_data['post_images'] = post_images_serializer.data

        serializer = PostsCreateSerializer(post_instance, data=partial_update_data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return UpdatePostMutation(post=post_instance)


class DeletePostMutation(graphene.Mutation):
    class Arguments:
        post_reference = graphene.String(required=True)

    success = graphene.Boolean()

    def mutate(root, info, post_reference):
        try:
            post_instance = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        except Posts.DoesNotExist:
            raise Exception(f"Entry with post reference {post_reference} does not exist.")

        post_instance.is_deleted = True
        post_instance.save()

        post_images_queryset = PostImages.objects.filter(post_reference=post_instance.post_reference, is_deleted=False)

        if post_images_queryset.exists():
            post_images_queryset.update(is_deleted=True)

        return DeletePostMutation(success=True)


class LikePostMutation(graphene.Mutation):
    class Arguments:
        post_reference = graphene.String(required=True)
        user_reference = graphene.String(required=False)
        store_reference = graphene.String(required=False)

    is_liked = graphene.Boolean()

    def mutate(root, info, post_reference, user_reference=None, store_reference=None):
        user = None
        store = None
        post = Posts.objects.get(post_reference=post_reference)

        if user_reference:
            user = User.objects.get(user_reference=user_reference)

        if store_reference:
            store = Store.objects.get(store_reference=store_reference)

        like_instance, created = PostLikes.objects.get_or_create(
            post_reference=post,
            liked_by_user=user,
            liked_by_store=store,
            defaults={'is_liked': True}
        )

        if not created:
            # If the instance already exists, toggle the 'is_liked' field
            like_instance.is_liked = not like_instance.is_liked
            like_instance.save()

        return LikePostMutation(is_liked=like_instance.is_liked)






