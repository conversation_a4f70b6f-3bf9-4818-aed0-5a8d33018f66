from django.urls import path
from .views import (
    PostsCreateView, GetPostsAV, LikeUnlikePost, DeletePostImagesView, GetSinglePostAV, GetFollowersOfPostCreator,
    StoryAPIView, GetStoriesOfProduct, StoryImageUploadView, DeleteStoryImageView, GetStoriesofEntity, TagContent,
    CreateExternalReviewRequest, CheckExternalReviewRequestExists, CreateExternalStoreReviewRequest, CheckExternalStoreReviewRequestExists)
    
urlpatterns = [
    path('post/', PostsCreateView.as_view(), name='posts-create'),
    path('get_all_posts/', GetPostsAV.as_view(), name ='get-all-posts'),
    path('like_unlike_post/', LikeUnlikePost.as_view(), name='like_unlike_post'),
    path('delete_post_image/<int:post_image_id>/', DeletePostImagesView.as_view(), name='delete_post_images'),
    path('get_single_post/', GetSinglePostAV.as_view(), name='get_single_post'),
    path('get_followers_list/',GetFollowersOfPostCreator.as_view(), name= 'get-followers'),
    path('story/', StoryAPIView.as_view(), name='story-api'),
    path('get_stories_of_product/', GetStoriesOfProduct.as_view(), name='get-stories-of-product'),
    path('story_image_upload/', StoryImageUploadView.as_view(), name='story-image-upload'),
    path('delete_story_image/', DeleteStoryImageView.as_view(), name='delete-story-image'),
    path('get_stories_of_entity/', GetStoriesofEntity.as_view(), name='get-stories-of-entity'),
    path('tag_content/',TagContent.as_view(), name='tag-content'),

    path('create_external_review_request/', CreateExternalReviewRequest.as_view(), name='create-external-review-request'),
    path('check_external_review_request_exists/', CheckExternalReviewRequestExists.as_view(), name='check-external-review-request-exists'),
    path('create_external_store_review_request/', CreateExternalStoreReviewRequest.as_view(), name='create-external-store-review-request'),
    path('check_external_store_review_request_exists/', CheckExternalStoreReviewRequestExists.as_view(), name='check-external-store-review-request-exists')

]
