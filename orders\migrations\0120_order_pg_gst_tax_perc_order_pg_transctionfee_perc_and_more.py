# Generated by Django 4.2.7 on 2024-08-09 14:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0119_order_razorpay_payment_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="order",
            name="pg_gst_tax_perc",
            field=models.FloatField(default=0, null=True),
        ),
        migrations.AddField(
            model_name="order",
            name="pg_transctionfee_perc",
            field=models.FloatField(default=0, null=True),
        ),
        migrations.AddField(
            model_name="order",
            name="pg_transctionfee_with_tax_perc",
            field=models.FloatField(default=0, null=True),
        ),
        migrations.AddField(
            model_name="order",
            name="transaction_fee",
            field=models.FloatField(default=0, null=True),
        ),
        migrations.AddField(
            model_name="order",
            name="transaction_fee_tax",
            field=models.FloatField(default=0, null=True),
        ),
    ]
