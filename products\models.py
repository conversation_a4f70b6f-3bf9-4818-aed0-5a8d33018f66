from django.db import models
import datetime
import string
import random
import time
from stores.store_api.models import Store
from users.notification_api.models import Notifications
from common.util.support_helper import GenerateNotifications, compress
from django.forms.models import model_to_dict
import logging
from django.utils.translation import gettext_lazy as _
from PIL import Image
import io
from django.core.files.base import ContentFile

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class ModelDiffMixin(object):
    """
    A model mixin that tracks model fields' values and provide some useful api
    to know what fields have been changed.
    """

    def __init__(self, *args, **kwargs):
        super(ModelDiffMixin, self).__init__(*args, **kwargs)
        self.__initial = self._dict

    @property
    def diff(self):
        d1 = self.__initial
        d2 = self._dict
        diffs = [(k, (v, d2[k])) for k, v in d1.items() if v != d2[k]]
        return dict(diffs)

    @property
    def has_changed(self):
        return bool(self.diff)

    @property
    def changed_fields(self):
        return self.diff.keys()

    def get_field_diff(self, field_name):
        """
        Returns a diff for field if it's changed and None otherwise.
        """
        return self.diff.get(field_name, None)

    def save(self, *args, **kwargs):
        """
        Saves model and set initial state.
        """
        super(ModelDiffMixin, self).save(*args, **kwargs)
        self.__initial = self._dict

    @property
    def _dict(self):
        return model_to_dict(self, fields=[field.name for field in self._meta.fields])


class ProductVersion(models.Model):
    product_version_id = models.AutoField(primary_key=True)
    productid = models.IntegerField()
    product_reference = models.CharField(max_length=25)
    storeid = models.IntegerField(null=True)
    store_reference = models.CharField(max_length=20, null=True, blank=True)
    product_name = models.CharField(max_length=200, null=True, blank=True)
    product_description = models.TextField(null=True, blank=True)
    brand_name = models.CharField(max_length=50, null=True, blank=True)
    promotion_link = models.CharField(max_length=255, null=True, blank=True)
    in_stock = models.PositiveIntegerField(null=True, blank=True, default=0)
    mrp_price = models.PositiveIntegerField(null=True, blank=True, default=0)
    selling_price = models.PositiveIntegerField(null=True, blank=True, default=0)
    product_images_ids = models.CharField(max_length=255, null=True, blank=True)
    hashtags = models.CharField(max_length=300, null=True, blank=True)
    product_version = models.CharField(max_length=100, null=True, blank=True)
    change_type = models.CharField(max_length=100, null=True, blank=True)
    changed_fields = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)

    is_promotion_enabled = models.BooleanField(default=False)
    promotion_amount = models.IntegerField(default=0)

    class Meta:
        verbose_name_plural = "product version"
        db_table = '"product"."product_version"'


class ProductVersionManagement(models.Model):

    MAJOR = "MAJOR"
    MINOR = "MINOR"
    IDLE = "IDLE"
    CHANGE_TYPE_CHOICES = [
        (MAJOR, "major"),
        (MINOR, "minor"),
        (IDLE, "idle"),
    ]

    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    OPERATION_CHOICES = [
        (INSERT, "Insert"),
        (UPDATE, "Update"),
        (DELETE, "Delete"),
    ]
    product_column_name = models.CharField(max_length=255, null=True, blank=True)
    operations = models.CharField(
        max_length=255, choices=OPERATION_CHOICES, null=True, blank=True
    )
    change_type = models.CharField(
        max_length=200, choices=CHANGE_TYPE_CHOICES, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = "product version management"
        db_table = '"product"."product_version_management"'


class Product(ModelDiffMixin, models.Model):
    class Swadeshi_Made_Labels(models.TextChoices):
        NOT_SWADESHI_MADE = "NOT_SWADESHI_MADE", _("Not Swadeshi Made")
        PARTIALLY_SWADESHI_MADE = "PARTIALLY_SWADESHI_MADE", _("Partially Swadeshi Made")
        MAINLY_SWADESHI_MADE = "MAINLY_SWADESHI_MADE", _("Mainly Swadeshi Made")
        FULLY_SWADESHI_MADE = "FULLY_SWADESHI_MADE", _("Fully Swadeshi Made")

    class Swadeshi_Brand_Labels(models.TextChoices):
        NOT_SWADESHI_BRAND = "NOT_SWADESHI_BRAND", _("Not Swadeshi Brand")
        PARTIALLY_SWADESHI_BRAND = "PARTIALLY_SWADESHI_BRAND", _("Partially Swadeshi Brand")
        MAINLY_SWADESHI_BRAND = "MAINLY_SWADESHI_BRAND", _("Mainly Swadeshi Brand")
        FULLY_SWADESHI_BRAND = "FULLY_SWADESHI_BRAND", _("Fully Swadeshi Brand")

    class Targeted_Gender_Choices(models.TextChoices):
        F = "F", _("Female")
        M = "M", _("Male")
        U = "U", _("Unisex")

    productid = models.AutoField(primary_key=True)
    product_reference = models.CharField(max_length=25, unique=True)
    product_name = models.CharField(max_length=200)
    brand_name = models.CharField(max_length=50, default=True)
    product_category = models.CharField(max_length=100, null=True, blank=True)
    storeid = models.IntegerField(null=True, blank=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        null=True,
        to_field="store_reference",
        related_name="store_products",
        on_delete=models.CASCADE,
        db_column="store_reference",
    )
    active = models.BooleanField(default=True)
    product_description = models.TextField()
    promotion_link = models.CharField(max_length=255, null=True, blank=True)
    hashtags = models.CharField(max_length=255, null=True, blank=True)
    in_stock = models.PositiveIntegerField(default=0)   
    mrp_price = models.PositiveIntegerField(default=0)
    selling_price = models.PositiveIntegerField(default=0)
    hide = models.BooleanField(default=False)
    deleted = models.BooleanField(default=False)
    rating = models.FloatField(null=True, blank=True)
    count_of_ratings = models.IntegerField(default=0)
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="created_person",
        db_column="created_by",
    )
    created_date = models.DateTimeField(auto_now_add=True)
    modified_by = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="modified_person",
        db_column="modified_by",
    )
    modified_date = models.DateTimeField(auto_now=True)
    product_version = models.CharField(max_length=255, default="1.0.0")

    swadeshi_made = models.CharField(max_length=50, choices=Swadeshi_Made_Labels.choices, null=True, blank=True)
    swadeshi_brand = models.CharField(max_length=50, choices=Swadeshi_Brand_Labels.choices,null=True,blank=True)
    targeted_gender = models.CharField(max_length=10, choices=Targeted_Gender_Choices.choices, blank=True, null=True, default=Targeted_Gender_Choices.U)

    ## counts
    like_count = models.IntegerField(default=0, blank=True, null=True)
    comment_count = models.IntegerField(default=0, blank=True, null=True)
    repost_count = models.IntegerField(default=0, blank=True, null=True)
    repost_plus_count = models.IntegerField(default=0, blank=True, null=True)
    save_count = models.IntegerField(default=0, blank=True, null=True)
    share_count = models.IntegerField(default=0, blank=True, null=True)
    tagged_posts = models.IntegerField(default=0, blank=True, null=True)

    orders_count = models.IntegerField(default=0, blank=True, null=True) #successfully delivered (-1 if returned after delivery)
    returns_count = models.IntegerField(default=0, blank=True, null=True) #returned after delivery (+1 if returned after delivery)
    cancels_count = models.IntegerField(default=0, blank=True, null=True) #cancelled before delivery

    #promotion fields
    is_promotion_enabled = models.BooleanField(default=False)
    promotion_amount = models.IntegerField(default=0)

    #analytics fields
    analytics_view_count = models.IntegerField(default=0)

    #product slug 
    product_slug = models.CharField(max_length=255, null=True, blank=True)
    product_code = models.CharField(max_length=20, null=True, blank=True)

    #product options
    #Eg: {
    #"color": ["blue", "red"],
    #"size": ["M","S"]
    #}

    options = models.JSONField(null=True, blank=True, default=dict)
    

    def __str__(self):
        return self.brand_name

    def available_product_images(self):
        return ProductImages.objects.filter(product_reference=self.product_reference, is_deleted=False)

    def save(self, *args, **kwargs):

        if self.productid is None:
            self.product_reference = get_product_reference()
            self.storeid = Store.objects.values_list("storeid", flat=True).get(
                store_reference=self.store_reference.store_reference
            )
            super(Product, self).save(*args, **kwargs)

            ProductVersion.objects.create(
                productid=self.productid,
                product_reference=self.product_reference,
                storeid=self.storeid,
                store_reference=self.store_reference.store_reference,
                product_name=self.product_name,
                product_description=self.product_description,
                brand_name=self.brand_name,
                selling_price=self.selling_price,
                mrp_price=self.mrp_price,
                in_stock=self.in_stock,
                promotion_link=self.promotion_link,
                hashtags=self.hashtags,
                product_version=self.product_version,
            )
        else:
            list_of_updated_fields = list(self.changed_fields)
            all_product_column_name = list(
                ProductVersionManagement.objects.values_list(
                    "product_column_name", flat=True
                )
            )

            common_in = any(
                name in all_product_column_name for name in list_of_updated_fields
            )

            if common_in:
                piped_updated_fields = "|".join(list_of_updated_fields)
                list_of_severity = set()
                for fields in list_of_updated_fields:
                    change_types = ProductVersionManagement.objects.filter(
                        product_column_name=fields, operations="UPDATE"
                    ).values_list("change_type", flat=True)
                    list_of_severity.update(change_types)

                if "MAJOR" in list_of_severity:
                    severity = "MAJOR"
                elif "MINOR" in list_of_severity:
                    severity = "MINOR"
                elif "IDLE" in list_of_severity:
                    severity = "IDLE"
                else:
                    severity = "IDLE"  # Default to IDLE if no matching severity is found

                product_version = self.product_version
                available_product_image_ids = ProductVersion.objects.values_list(
                    "product_images_ids", flat=True
                ).get(
                    product_reference=self.product_reference,
                    product_version=self.product_version,
                )
                new_product_version = calculate_product_version(
                    product_version, severity
                )
                logger.info(
                    "product version changed from %s to %s",
                    self.product_version,
                    new_product_version,
                )
                ProductVersion.objects.create(
                    productid=self.productid,
                    product_reference=self.product_reference,
                    storeid=self.storeid,
                    store_reference=self.store_reference.store_reference,
                    product_name=self.product_name,
                    product_description=self.product_description,
                    brand_name=self.brand_name,
                    selling_price=self.selling_price,
                    mrp_price=self.mrp_price,
                    in_stock=self.in_stock,
                    promotion_link=self.promotion_link,
                    hashtags=self.hashtags,
                    product_version=new_product_version,
                    product_images_ids=available_product_image_ids,
                    change_type=severity,
                    changed_fields=piped_updated_fields,
                )
                prod_image = ProductImages.objects.filter(product_reference=self.product_reference, is_deleted=False).first()
                if prod_image and prod_image.product_image:
                    image = prod_image.product_image
                else:
                    image = None
                self.product_version = new_product_version
                notification_gen = GenerateNotifications()
                notification_gen.create_notifications(
                    notified_user=None,
                    notification_type=Notifications.Notifications_Type.PRODUCT_VERSION_CHANGED,
                    notification_about=self.product_reference,
                    product_id=self.productid,
                    image=image,
                )
                logger.info(
                    "product version entry created and new product version updated in product table"
                )
            logger.info("product saved")
            super(Product, self).save(*args, **kwargs)

        store_product_count = Product.objects.filter(store_reference=self.store_reference.store_reference,deleted=False).count()

        '''
        change the store_product_count to give the min product limit to activate a store 
        '''
        if store_product_count >= 0 and Store.objects.filter(
                store_reference=self.store_reference.store_reference,
                add_products=False,
                deleted=False,
        ).exists():
            instance = Store.objects.get(
                store_reference=self.store_reference.store_reference,
                add_products=False,
                deleted=False,
            )
            instance.add_products = True
            instance.save(update_fields=["add_products"])
        # if store has less than 3 products, mark the add_product, is_active and open_for_order in store check list to false.
        # so that store won't open for orders.


        # elif store_product_count < 3 and Store.objects.filter(
        #         store_reference=self.store_reference.store_reference,
        #         add_products=True,
        #         deleted=False,
        # ).exists():
        #
        #     instance = Store.objects.get(
        #         store_reference=self.store_reference.store_reference,
        #         add_products=True,
        #         deleted=False,
        #     )
        #     instance.add_products = False
        #     instance.is_active = False
        #     instance.open_for_order = False
        #     instance.save(update_fields=["add_products", "is_active"])

        #     Store.objects.filter(
        #         store_reference=self.store_reference.store_reference,
        #         add_products=True,
        #         deleted=False,
        #     ).update(add_products=False)

    class Meta:
        verbose_name_plural = "products"
        db_table = '"product"."product"'


def calculate_product_version(product_version, severity):

    current_product_version = product_version
    major_part = int(current_product_version.split(".")[0])
    minor_part = int(current_product_version.split(".")[1])
    idle_part = int(current_product_version.split(".")[2])

    if severity == "MAJOR":
        major_part += 1
        new_product_version = str(major_part) + "." + "0" + "." + "0"
    elif severity == "MINOR":
        minor_part += 1
        new_product_version = str(major_part) + "." + str(minor_part) + "." + "0"
    elif severity == "IDLE":
        idle_part += 1
        new_product_version = (
            str(major_part) + "." + str(minor_part) + "." + str(idle_part)
        )
    return new_product_version


def calculate_product_variant_version(variant_version, severity):
    """
    Calculate new version for product variants based on severity
    """
    current_variant_version = variant_version
    major_part = int(current_variant_version.split(".")[0])
    minor_part = int(current_variant_version.split(".")[1])
    idle_part = int(current_variant_version.split(".")[2])

    if severity == "MAJOR":
        major_part += 1
        new_variant_version = str(major_part) + "." + "0" + "." + "0"
    elif severity == "MINOR":
        minor_part += 1
        new_variant_version = str(major_part) + "." + str(minor_part) + "." + "0"
    elif severity == "IDLE":
        idle_part += 1
        new_variant_version = (
            str(major_part) + "." + str(minor_part) + "." + str(idle_part)
        )
    return new_variant_version


def get_product_reference():
    random_four_length_character = "".join(random.choices(string.ascii_uppercase, k=4))
    current_timestamp = int(time.time() * 1000000)
    # current_time_str = current_time.strftime("%m%d%y")
    final_code = ("P", str(current_timestamp), random_four_length_character)
    product_reference = "".join(final_code)
    if Product.objects.filter(product_reference=product_reference).exists():
        product_reference = get_product_reference()
        return product_reference
    return product_reference


class ProductImages(models.Model):
    productimageid = models.AutoField(primary_key=True)
    productid = models.IntegerField(default=0, blank=True, null=True)
    product_reference = models.ForeignKey(
        "products.Product",
        to_field="product_reference",
        on_delete=models.CASCADE,
        related_name="prod_images",
        db_column="product_reference",
    )
    product_image = models.ImageField(upload_to="post_images")
    active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_by = models.CharField(max_length=100)
    modified_date = models.DateField(auto_now=True)
    reorder = models.IntegerField(null=True, blank=True, default=0)
    is_deleted = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if self.productimageid is None:
            self.productid = self.product_reference.productid
            count_of_product_image = ProductImages.objects.filter(
                product_reference=self.product_reference
            ).count()
            logger.info("Count of existing product_image %s", count_of_product_image)
            self.reorder = count_of_product_image + 1
        
        if self.product_image:
            # Open the uploaded image
            img = Image.open(self.product_image)
            
            # Convert to RGB if the image has an alpha channel
            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                img = img.convert('RGB')
            
            # Compress and resize the image
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=70)  # Adjust quality as needed
            output.seek(0)
            
            # Save the processed image
            self.product_image = ContentFile(output.getvalue(), name=self.product_image.name)
        
        super(ProductImages, self).save(*args, **kwargs)

    class Meta:
        ordering = ["reorder"]
        verbose_name_plural = "product images"
        db_table = '"product"."product_images"'


class ProductVariants(ModelDiffMixin, models.Model):
    product_variantid = models.AutoField(primary_key=True)
    variant_reference = models.CharField(max_length=25, unique=True)
    product_reference = models.ForeignKey(
        "products.Product",
        to_field="product_reference",
        on_delete=models.CASCADE,
        related_name="product_variants",
        db_column="product_reference",
    )
    combinations = models.JSONField(null=True, blank=True, default=dict)
    mrp_price = models.PositiveIntegerField(null=True, blank=True, default=0)
    selling_price = models.PositiveIntegerField(null=True, blank=True, default=0)
    stock = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateField(auto_now=True)
    variant_version = models.CharField(max_length=255, default="1.0.0")

    class Meta:
        verbose_name_plural = "product variants"
        db_table = '"product"."product_variants"'

    def __str__(self):
        return self.variant_reference

    def get_variant_reference(self):
        current_timestamp = int(time.time() * 1000000)
        final_code = ("PV", str(current_timestamp))
        variant_reference = "".join(final_code)
        if ProductVariants.objects.filter(variant_reference=variant_reference).exists():
            variant_reference = self.get_variant_reference()
            return variant_reference
        return variant_reference

    def save(self, *args, **kwargs):
        if self.product_variantid is None:
            # New variant creation
            self.variant_reference = self.get_variant_reference()
            super(ProductVariants, self).save(*args, **kwargs)

            # Create initial version snapshot
            ProductVariantHistory.objects.create(
                variant_reference=self.variant_reference,
                product_reference=self.product_reference.product_reference,
                combinations=self.combinations,
                mrp_price=self.mrp_price,
                selling_price=self.selling_price,
                stock=self.stock,
                is_active=self.is_active,
                product_variant_version=self.variant_version,
                change_type="INSERT",
                changed_fields="initial_creation"
            )
        else:
            # Update existing variant
            list_of_updated_fields = list(self.changed_fields)
            if list_of_updated_fields:
                piped_updated_fields = "|".join(list_of_updated_fields)

                severity = "IDLE"  # Default to IDLE if no matching severity is found

                variant_version = self.variant_version
                new_variant_version = calculate_product_variant_version(
                    variant_version, severity
                )

                logger.info(
                    "variant version changed from %s to %s",
                    self.variant_version,
                    new_variant_version,
                )

                # Create version snapshot
                ProductVariantHistory.objects.create(
                    variant_reference=self.variant_reference,
                    product_reference=self.product_reference.product_reference,
                    combinations=self.combinations,
                    mrp_price=self.mrp_price,
                    selling_price=self.selling_price,
                    stock=self.stock,
                    is_active=self.is_active,
                    product_variant_version=new_variant_version,
                    change_type=severity,
                    changed_fields=piped_updated_fields
                )

                self.variant_version = new_variant_version
                logger.info(
                    "variant version entry created and new variant version updated in variant table"
                )

            logger.info("product variant saved")
            super(ProductVariants, self).save(*args, **kwargs)


class ProductVariantHistory(models.Model):
    product_variant_historyid = models.AutoField(primary_key=True)
    variant_reference = models.CharField(max_length=25)
    product_reference = models.CharField(max_length=25)
    combinations = models.JSONField(null=True, blank=True, default=dict)
    mrp_price = models.PositiveIntegerField(null=True, blank=True, default=0)
    selling_price = models.PositiveIntegerField(null=True, blank=True, default=0)
    stock = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateField(auto_now=True)
    product_variant_version = models.CharField(max_length=100, null=True, blank=True)
    change_type = models.CharField(max_length=100, null=True, blank=True)
    changed_fields = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name_plural = "product variant history"
        db_table = '"product"."product_variant_history"'
