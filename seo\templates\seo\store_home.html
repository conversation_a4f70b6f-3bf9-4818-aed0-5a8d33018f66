{% extends 'seo/base.html' %}

{% block schema %}
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "Store",
    "name": "{{ store.store_name }}",
    "description": "{{ store.store_desc }}",
    "image": "{{ store.icon.url }}",
    {% if store_location %}
    "address": {
        "@type": "PostalAddress",
        "streetAddress": "{{ store_location.address }}",
        "addressLocality": "{{ store_location.city }}",
        "addressRegion": "{{ store_location.state }}",
        "postalCode": "{{ store_location.pincode }}"
    },
    "geo": {
        "@type": "GeoCoordinates",
        "latitude": "{{ store_location.latitude }}",
        "longitude": "{{ store_location.longitude }}"
    },
    {% endif %}
    "url": "{{ request.build_absolute_uri }}"
}
</script>
{% endblock %}

{% block content %}
<div class="store-header">
    <div class="store-info">
        <img src="{{ store.icon.url }}" alt="{{ store.store_name }} logo" class="store-logo">
        <h1>{{ store.store_name }}</h1>
        <p class="store-category">{{ store.category_name }}</p>
        {% if store.storehandle %}
        <p class="store-handle">@{{ store.storehandle }}</p>
        {% endif %}
    </div>

    {% if store_location %}
    <div class="store-location">
        <h2>Store Location</h2>
        {% if store_location.isphysicalstore %}
        <p class="physical-store">Physical Store Available</p>
        {% endif %}
        <address>
            {{ store_location.address }}<br>
            {{ store_location.city }}, {{ store_location.state }}<br>
            PIN: {{ store_location.pincode }}
        </address>
        {% if store_location.swadeshi_owned %}
        <p class="swadeshi-label">{{ store_location.get_swadeshi_owned_display }}</p>
        {% endif %}
    </div>
    {% endif %}

    {% if store.store_desc %}
    <div class="store-description">
        <p>{{ store.store_desc }}</p>
    </div>
    {% endif %}

    {% if store.storelinks.exists %}
    <div class="store-links">
        <h2>Store Links</h2>
        {% for link in store.storelinks.all %}
        <a href="{{ link.link }}" class="store-link" target="_blank">{{ link.link_name }}</a>
        {% endfor %}
    </div>
    {% endif %}
</div>

<div class="store-content">
    {% if recent_products %}
    <section class="recent-products">
        <h2>Recent Products</h2>
        <div class="products-grid">
            {% for product in recent_products %}
            <div class="product-card">
                {% if product.prod_images.exists %}
                <img src="{{ product.prod_images.first.product_image.url }}" alt="{{ product.product_name }}">
                {% endif %}
                <h3>{{ product.product_name }}</h3>
                <p class="price">₹{{ product.selling_price }}</p>
                <a href="{% url 'seo:product_detail' product.product_reference %}" class="view-product">View Product</a>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    {% if recent_posts %}
    <section class="recent-posts">
        <h2>Recent Posts</h2>
        <div class="posts-grid">
            {% for post in recent_posts %}
            <div class="post-card">
                {% if post.post_images.exists %}
                <img src="{{ post.post_images.first.post_image.url }}" alt="Post image">
                {% endif %}
                <p class="post-text">{{ post.post_text|truncatechars:100 }}</p>
                <p class="post-date">{{ post.created_date|timesince }} ago</p>
                <a href="{% url 'seo:post_detail' post.post_reference %}" class="view-post">Read More</a>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    {% if recent_stories %}
    <section class="recent-stories">
        <h2>Recent Stories</h2>
        <div class="stories-grid">
            {% for story in recent_stories %}
            <div class="story-card">
                <h3>{{ story.title }}</h3>
                <p class="story-date">{{ story.created_at|timesince }} ago</p>
                <a href="{% url 'seo:story_detail' story.story_reference %}" class="view-story">Read Story</a>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}
</div>
{% endblock %}
