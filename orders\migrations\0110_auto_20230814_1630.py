# Generated by Django 3.2.13 on 2023-08-14 11:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0109_auto_20230814_1220'),
    ]

    operations = [
        migrations.AlterField(
            model_name='refundedamount',
            name='suborder_reference',
            field=models.ForeignKey(blank=True, db_column='suborder_number', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='refund_details', to='orders.suborder', to_field='suborder_number'),
        ),
        migrations.AlterField(
            model_name='suborder',
            name='suborder_status',
            field=models.CharField(choices=[('ORDER_INITIATED', 'Order initiated'), ('PAYMENT_INITIATED', 'Payment initiated'), ('PAYMENT_SUCCESS', 'Payment success'), ('WAITING_FOR_CONFIRMATION', 'waiting for confirmation'), ('PAYMENT_PENDING', 'Payment pending'), ('PAYMENT_FAILED', 'Payment failed'), ('ORDER_CONFIRMED', 'Order confirmed'), ('DELIVERY_IN_PROGRESS', 'Delivery in progress'), ('ORDER_DELIVERED', 'Order delivered'), ('ORDER_CANCELLED', 'Order cancelled'), ('ORDER_CANCELLED_BY_BUYER', 'Order cancelled by buyer'), ('ORDER_CANCELLED_BY_SELLER', 'Order cancelled by seller'), ('DELIVERY_FAILED', 'Delivery failed'), ('RETURN_REQUESTED', 'Return requested'), ('RETURN_CONFIRMED', 'Return confirmed'), ('RETURN_IN_PROGRESS', 'Return in progress'), ('RETURNED_TO_SELLER', 'Return to seller'), ('RETURN_FAILED', 'Return failed'), ('REFUND_INITIATED', 'Refund initiated'), ('REFUND_INITIATE_FAILED', 'Refund initiate failed'), ('REFUND_HOLD', 'Refund hold'), ('REFUNDED', 'Refunded'), ('ORDER_AUTO_CANCELLED', 'Order auto cancelled')], default='ORDER_INITIATED', max_length=50),
        ),
    ]
