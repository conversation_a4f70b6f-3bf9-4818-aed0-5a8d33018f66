from django.db import transaction
from rest_framework.response import Response
from rest_framework import status
from rest_framework import generics, mixins
from rest_framework.views import APIView

from decouple import config

from django.db.models import Sum

from ..order_api.views import UpdateOrderStatus_single
from ..payout_api.models import OrderPayout
from ..order_api.models import Order, SubOrder, OrderLifeCycle, RefundedAmount, OrderConfiguration
from ..cart_api.models import CartItem
from ..payment_api.models import SubOrderPaymentDetails
from ..helpers import get_order_request_number, PayoutCalculation
from users.notification_api.models import Notifications
from users.user_api.models import User
from products.models import Product
from common.util.notification_handler import NotificationHandler
from .utils import *

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from requests.auth import HTTPBasicAuth

import requests
import logging
import datetime
import pytz

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

SKIP_RAZORPAY_API = config("SKIP_RAZORPAY_API", cast=bool)
RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")


class PaymentInitiate(mixins.CreateModelMixin, generics.GenericAPIView):
    @swagger_auto_schema(
        operation_summary="open create payment token",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "order_number",
                "delivery_fee",
                "cart_total",
                "total_amount",
                "user",
            ],
            properties={
                "order_number": openapi.Schema(type=openapi.TYPE_STRING),
                "delivery_fee": openapi.Schema(type=openapi.TYPE_INTEGER),
                "cart_total": openapi.Schema(type=openapi.TYPE_INTEGER),
                "total_amount": openapi.Schema(type=openapi.TYPE_INTEGER),
                "user": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        order_number = request.data["order_number"] # todo change to order_request_number
        delivery_fee = request.data["delivery_fee"]
        cart_total = request.data["cart_total"]
        total_amount = request.data["total_amount"]
        user = request.data["user"]

        # todo validate the input order details

        user_instance = User.objects.get(user_reference=user, deleted=False)

        # for an order if payment already initiated and not completed, then create a new order_request_number for that payment
        # and continue the payment process.
        if Order.new_objects.filter(
                order_request_number=order_number,
                order_status=Order.Order_Status.PAYMENT_INITIATED,
        ).exists():
            new_order_request_number = get_order_request_number()
            Order.new_objects.filter(order_request_number=order_number).update(
                order_request_number=new_order_request_number
            )
            # since the order_request_number changed update the change in associated tables.
            SubOrderPaymentDetails.objects.filter(
                order_request_number=order_number
            ).update(order_request_number=new_order_request_number)
            new_order_number = (
                Order.new_objects.filter(order_request_number=new_order_request_number)
                .values_list("order_request_number", flat=True)
                .first()
            )
            order_id = Order.new_objects.filter(
                order_request_number=new_order_request_number
            ).values_list("orderid", flat=True)

        # if payment for an order is for first time.
        else:
            Order.new_objects.filter(order_request_number=order_number).update(
                order_status=Order.Order_Status.PAYMENT_INITIATED,
                delivery_fee=delivery_fee,
                store_total=cart_total,
                total_amount=total_amount,
            )
            order_payment_details_instance = SubOrderPaymentDetails.objects.filter(
                order_request_number=order_number
            )
            # whenever for an order, payment initiated update it in OrderPaymentDetails table.
            # to keep track of all the payment details separately
            for order_payment_details in order_payment_details_instance:
                order_payment_details.payment_status = (
                    SubOrderPaymentDetails.Payment_Status.PAYMENT_INITIATED
                )
                order_payment_details.save(update_fields=["payment_status"])
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)
            sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
            for order in sub_order_items:
                order.suborder_status = SubOrder.Suborder_Status.PAYMENT_INITIATED
                order.save(update_fields=["suborder_status"])
            new_order_number = (
                Order.new_objects.filter(order_request_number=order_number)
                .values_list("order_request_number", flat=True)
                .first()
            )
            print(new_order_number)

        dummy_token = get_dummy_txn_token()

        if SKIP_RAZORPAY_API:
            response_data = {
                "key": RAZORPAY_ID,
                "amount": total_amount,
                "currency": "INR",
                "name": "Swadesic ☑️",
                "description": "dummy_order_Oet4sfJJESHafo",
                "image": "https://raw.githubusercontent.com/Socially-X/public_assets/MASTER/Swadesic%20logo%20(1).png",
                "order_id": "dummy_order_Oet4sfJJESHafo",
                "prefill": {
                    "name": user_instance.display_name if user_instance.display_name else user_instance.user_name,
                    "email": user_instance.email,
                    "contact": user_instance.phonenumber,
                    },
                "notes": {
                    "order_request_number": order_number,
                    "txn_token": dummy_token
                    },
                "theme": {
                    "color": "#10c057"
                    }
            }

        else:
            try:
                headers = {
                    "Content-Type": "application/json",
                }
                data = {
                    "amount": int(total_amount)*100,
                    "currency": "INR",
                    "receipt": dummy_token,
                    "notes": {
                        "user_name": user_instance.display_name if user_instance.display_name else user_instance.user_name,
                        "user_email": user_instance.email,
                        "user_contact": str(user_instance.phonenumber),
                        "order_request_number": order_number,
                        "transaction_id": dummy_token
                    }
                }
                response = requests.post('https://api.razorpay.com/v1/orders',
                                         headers=headers,
                                         auth=HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY),
                                         json=data)
                api_response = response.json()
            except Exception as e:
                return Response({"message": "error", "data": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)

            if response.status_code == 200:
                try:
                    response_data = {
                        "key": RAZORPAY_ID,
                        "amount": api_response["amount"],
                        "currency": api_response["currency"],
                        "name": "Swadesic ☑️",
                        "description": api_response["id"],
                        "image": "https://raw.githubusercontent.com/Socially-X/public_assets/MASTER/Swadesic%20logo%20(1).png",
                        "order_id": api_response["id"],
                        "prefill": {
                            "name": api_response["notes"]["user_name"],
                            "email": api_response["notes"]["user_email"],
                            "contact": api_response["notes"]["user_contact"],
                        },
                        "notes": {
                            "order_request_number": api_response["notes"]["order_request_number"],
                            "txn_token": api_response["receipt"]
                        },
                        "theme": {
                            "color": "#10c057"
                        }
                    }
                except Exception as e:
                    return Response({"message": "error", "data": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"message": "error", "data": f"Razorpay Order could not be created{response.content}"}, status=status.HTTP_400_BAD_REQUEST)
        txn_token = response_data["notes"]["txn_token"]
        Order.new_objects.filter(order_request_number=new_order_number).update(
            txn_token=txn_token
        )
        OrderLifeCycle.objects.filter(orderid__in=order_id).update(txn_token=txn_token)

        return Response(
            response_data,
            status=status.HTTP_200_OK,
        )


class CheckPaymentStatus(APIView):
    def get_date(self):
        date = datetime.datetime.now()
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%Y-%m-%d %H:%M:%S")

    def post(self, request):
        payment_data = self.extract_payment_data(request.data)
        order_info = self.get_order_info(payment_data['order_number']) # this is order request number not order number
        self.order_request_number = payment_data['order_number']
        if not self.is_valid_payment_data(payment_data):
            return self.handle_failed_payment(payment_data, order_info)

        payment_details = get_payment_details(payment_data['razorpay_pay_id'])
        response_data = (
            self.create_test_success_response(payment_data)
            if SKIP_RAZORPAY_API
            else self.process_payment(payment_data, payment_details)
        )

        if response_data["body"]["resultInfo"]["resultStatus"] == "PAYMENT_SUCCESS":

            return self.handle_successful_payment(response_data, order_info, payment_details)
        else:
            return self.handle_failed_payment(response_data, order_info)

    def extract_payment_data(self, data):
        return {
            'transaction_id': data.get('transaction_id'),
            'amount': data.get('amount'),
            'order_number': data['order_id'],
            'razorpay_order_id': data['razorpay_order_id'],
            'razorpay_pay_id': data['razorpay_payment_id'],
            'razorpay_signature': data['razorpay_signature']
        }

    def get_order_info(self, order_number):
        order_instance = Order.objects.filter(order_request_number=order_number).values_list(
            "order_number", flat=True
        )
        _sub_order_instance = SubOrder.objects.filter(
            order_number__in=order_instance
        )

        return {
            'items_ordered': _sub_order_instance.aggregate(
                items_ordered=Sum("product_quantity")
            )["items_ordered"],
            'ordered_from': _sub_order_instance.distinct("order_number").count(),
            'estimated_delivery_date': _sub_order_instance.first().estimated_delivery_date
        }

    def is_valid_payment_data(self, payment_data):
        return all(
            [payment_data['razorpay_order_id'], payment_data['razorpay_pay_id'], payment_data['razorpay_signature']])

    def handle_failed_payment(self, payment_data, order_info):
        response_data = self.create_failed_payment_response(payment_data)
        return Response({
            "message": "success",
            "items_ordered": order_info['items_ordered'],
            "ordered_from": order_info['ordered_from'],
            "estimated_delivery_date": order_info['estimated_delivery_date'],
            "data": response_data,
        }, status=200)

    def create_failed_payment_response(self, payment_data):
        return {
            "head": {
                "responseTimestamp": self.get_date(),
                "version": "v1",
                "clientId": "C11",
                "signature": ""
            },
            "body": {
                "resultInfo": {
                    "resultStatus": "PAYMENT_FAILED",
                    "resultCode": "01",
                    "resultMsg": "Txn Failed"
                },
                "txnId": payment_data['transaction_id'],
                "bankTxnId": "xxxxxxxxxxxxxxx",
                "orderId": payment_data['order_number'],
                "txnAmount": str(payment_data['amount']),
                "txnType": "SALE",
                "gatewayName": "RAZORPAY",
                "bankName": "",
                "mid": RAZORPAY_ID,
                "paymentMode": "",
                "refundAmt": "",
                "txnDate": self.get_date(),
                "authRefId": "********"
            },
        }

    def create_test_success_response(self, payment_data):
        return {
            "head": {
                "responseTimestamp": self.get_date(),
                "version": "v1",
                "clientId": "C11",
                "signature": ""
            },
            "body": {
                "resultInfo": {
                    "resultStatus": "PAYMENT_SUCCESS",
                    "resultCode": "01",
                    "resultMsg": "Txn Success"
                },
                "txnId": payment_data['transaction_id'],
                "bankTxnId": "xxxxxxxxxxxxxxx",
                "orderId": payment_data['order_number'],
                "txnAmount": str(payment_data['amount']),
                "txnType": "SALE",
                "gatewayName": "HDFC",
                "bankName": "HSBC",
                "mid": RAZORPAY_ID,
                "paymentMode": "CC",
                "refundAmt": "0.00",
                "txnDate": self.get_date(),
                "authRefId": "********",
                "vpa": "**********@ybl",
                "razorpayPayId": "pay_dummy_qwert123"
            },
        }

    def process_payment(self, payment_data, payment_details):
        result_message = verify_signature(
            razorpay_order_id=payment_data['razorpay_order_id'],
            razorpay_payment_id=payment_data['razorpay_pay_id'],
            razorpay_signature=payment_data['razorpay_signature'],
            secret=RAZORPAY_KEY
        )
        is_payment_success = result_message == "PAYMENT_SUCCESS"

        if not is_payment_success:
            return self.create_failed_payment_response(payment_data)

        return self.create_success_payment_response(payment_details)

    def create_success_payment_response(self, payment_details):

        
        return {
                "head": {
                    "responseTimestamp": self.get_date(),  # Dynamic
                    "version": "v1",
                    "clientId": "C11",
                    "signature": "abcdef1234567890"
                },
                "body": {
                    "resultInfo": {
                        "resultStatus": "PAYMENT_SUCCESS",
                        "resultCode": "01",
                        "resultMsg": payment_details["status"],  # Dynamic
                    },
                    "txnId": "T12345678901234",
                    "bankTxnId": "B98765432109876",
                    "orderId": payment_details["notes"]["order_request_number"],  # Dynamic
                    "txnAmount": "{:.2f}".format(payment_details["amount"] / 100),  # Dynamic
                    "txnType": "SALE",
                    "gatewayName": "RAZORPAY",
                    "bankName": "UBIN",  # Matching netbanking response
                    "mid": "rzp_test_Rv2f6i9k9TtTZg",  # Matching netbanking response
                    "paymentMode": "netbanking",  # Matching netbanking response
                    "refundAmt": "0.00",
                    "txnDate": self.get_date(),  # Dynamic
                    "authRefId": "",  # Matching netbanking response
                    "vpa": None,  # Matching netbanking response
                    "razorpayPayId": payment_details["id"]  # Dynamic
                }
            }

    def handle_successful_payment(self, response_data, order_info, payment_details):
        
        pg_transctionfee_with_tax_perc = self.update_order_details(response_data, payment_details)
        self.update_suborder_payment_details(response_data, pg_transctionfee_with_tax_perc)
        self.update_sub_order_status(response_data)
        self.update_order_life_cycle(response_data, payment_details)
        self.create_order_payout_entries(pg_transctionfee_with_tax_perc, response_data, payment_details )
        self.update_product_inventory(response_data)
        self.update_cart_status(response_data)
        self.send_notification(response_data)

        return Response(
            {
                "message": "success",
                "items_ordered": order_info['items_ordered'],
                "ordered_from": order_info['ordered_from'],
                "estimated_delivery_date": order_info['estimated_delivery_date'],
                "data": response_data,
            },
            status=status.HTTP_200_OK,
        )

    #     grace_period_timedelta = datetime.timedelta(minutes=grace_period)
    def create_order_payout_entries(self,pg_transctionfee_with_tax_perc, suborder_number, order_reference):
    
        # Let's create order payout entries for each suborder payment detail entry for every order_number of order table with the above order request number
        orders = Order.objects.filter(order_request_number=self.order_request_number)#.values_list('order_number', flat=True)
        for order in orders:
            suborder_payment_details = SubOrderPaymentDetails.objects.filter(order_number=order.order_number) # every instance including store-df
            for suborder_payment_detail in suborder_payment_details:
                store_df_record = True if suborder_payment_detail.suborder_number is None else False
                payout_calculation = PayoutCalculation()
                payout_calculation = payout_calculation.calculate_expected_payout_values(suborder_payment_detail = suborder_payment_detail, store_df_record=store_df_record, pg_transctionfee_with_tax_perc=pg_transctionfee_with_tax_perc)
                OrderPayout.objects.create(
                    store_reference=order.store_reference,
                    suborder_number=suborder_payment_detail.suborder_number,
                    order_number=order,
                    order_date=order.created_date,
                    user_reference=order.user_reference,
                    product_amount=suborder_payment_detail.product_amount,
                    product_delivery_fee=suborder_payment_detail.product_delivery_fee,
                    store_delivery_fee=suborder_payment_detail.store_delivery_fee,
                    order_amount= payout_calculation.order_amount,
                    transaction_fee_calculated=payout_calculation.transaction_fee_calculated,
                    swadesic_fee=payout_calculation.commission_calculated,
                    transaction_fee_percentage=payout_calculation.transaction_fee_percentage,
                    expected_swadesic_fee=payout_calculation.commission_fee,
                    promotional_value=payout_calculation.commission_fee_discount,
                    tds_calculated=payout_calculation.tds_calculated,
                    payout_amount=payout_calculation.expected_payout_amount,
                    expected_payout_amount=payout_calculation.expected_payout_amount,
                    payout_status=OrderPayout.Payout_Status.WAITING_FOR_CONFIRMATION,
                    order_type=OrderPayout.OrderType.STORE_DF if store_df_record else OrderPayout.OrderType.PRODUCT
                    # payout_release_date=release_date, # payout release date should only show up after delivery
                )

    # def create_order_payout_entries_old():
    #     # store_reference = #Store.objects.get(store_reference=self.store_reference)
    #     user_reference = User.objects.get(userid=self.order_number.userid)
    #
    #     order_configuration_instance = OrderConfiguration.objects.all().first()
    #
    #     grace_period = order_configuration_instance.grace_period
    #
    #     return_period = self.get_return_period()
    #     fulfillment_window_days = order_configuration_instance.fulfillment_window / (
    #             24 * 60)  # Convert minutes to days
    #     return_period_timedelta = datetime.timedelta(days=max(return_period, fulfillment_window_days))
    #
    #     total_timedelta = return_period_timedelta + grace_period_timedelta
    #     release_date = datetime.date.today() + total_timedelta
    # #     order_date = self.order_number.date
    #
    #     # order_amount = self.calculate_order_amount(store_amount_order_df=store_df_record)
    #     order_amount, expected_payout_amount, transaction_fee_calculated, commission_calculated, transaction_fee_percentage, \
    #         commission_fee, commission_fee_discount, tds_calculated = PayoutCalculation.calculate_expected_payout_values(
    #         store_df_record=store_df_record, pg_transctionfee_with_tax_perc=pg_transctionfee_with_tax_perc)
    #
    #     if not store_df_record:
    #         OrderPayout.objects.create(
    #             store_reference=store_reference,
    #             suborder_number=self.suborder_number,
    #             order_number=self.order_number,
    #             user_reference=user_reference,
    #             product_total=self.product_amount,
    #             order_amount=order_amount,
    #             transaction_fee_calculated=transaction_fee_calculated,
    #             commission_calculated=commission_calculated,
    #             transaction_fee_percentage=transaction_fee_percentage,
    #             commission_fee=commission_fee,
    #             promotional_value=commission_fee_discount,
    #             tds_calculated=tds_calculated,
    #             payout_amount=expected_payout_amount,
    #             expected_payout_amount=expected_payout_amount,
    #             product_df=self.product_delivery_fee,
    #             # store_order_df=self.get_store_level_fee(),
    #             order_date=order_date,
    #             payout_status=OrderPayout.Payout_Status.WAITING_FOR_CONFIRMATION,
    #             payout_release_date=release_date,
    #         )
    #     else:
    #         OrderPayout.objects.create(
    #             store_reference=store_reference,
    #             # suborder_number=self.order_number.order_number + '-00',
    #             order_number=self.order_number,
    #             user_reference=user_reference,
    #             product_total=0,
    #             order_amount=order_amount,
    #             transaction_fee_calculated=transaction_fee_calculated,
    #             commission_calculated=commission_calculated,
    #             transaction_fee_percentage=transaction_fee_percentage,
    #             commission_fee=commission_fee,
    #             promotional_value=commission_fee_discount,
    #             tds_calculated=tds_calculated,
    #             payout_amount=expected_payout_amount,
    #             expected_payout_amount=expected_payout_amount,
    #             # product_df=self.delivery_fee,
    #             store_order_df=self.get_store_level_fee(),
    #             order_date=order_date,
    #             payout_status=OrderPayout.Payout_Status.WAITING_FOR_CONFIRMATION,
    #             payout_release_date=release_date,
    #         )

    def get_transaction_fee_breakup(self, amount, fee, tax):

        pg_transctionfee_with_tax_perc = round((fee / amount)* 100, 2)
        pg_transctionfee_perc = round(((fee - tax) / amount) * 100, 2)
        pg_gst_tax_perc = round((tax / (fee - tax)) * 100)
        return pg_transctionfee_with_tax_perc, pg_transctionfee_perc, pg_gst_tax_perc

    def update_order_details(self, response_data, payment_details):
        pg_transctionfee_with_tax_perc, pg_transctionfee_perc, pg_gst_tax_perc = self.get_transaction_fee_breakup(
            payment_details["amount"],
            payment_details["fee"],
            payment_details["tax"],
        )



        Order.new_objects.filter(order_request_number=response_data["body"]["orderId"]).update(
            razorpay_payment_id=response_data["body"]["razorpayPayId"],
            order_status=Order.Order_Status.PAYMENT_SUCCESS,
            transaction_id=response_data["body"]["txnId"],
            transaction_date=response_data["body"]["txnDate"],
            bank_transaction_id=response_data["body"]["bankTxnId"],
            payment_mode=response_data["body"]["paymentMode"],
            payment_channel=get_payment_channel(payment_details),
            # transaction_fee
            # transaction_fee_tax

            pg_transctionfee_with_tax_perc=pg_transctionfee_with_tax_perc,
            pg_transctionfee_perc=pg_transctionfee_perc,
            pg_gst_tax_perc=pg_gst_tax_perc,

        )
        # update Order table entries with swadesic fee using order numbers on condition of Order amount > 500
        
        orders = Order.new_objects.filter(order_request_number=response_data["body"]["orderId"])

        for order in orders:
            swadesic_fee = self.calculate_swadesic_fee(order.total_order_amount)
            order.transaction_fee = order.total_order_amount*order.pg_transctionfee_with_tax_perc/100
            order.swadesic_fee = swadesic_fee
            order.expected_swadesic_fee = swadesic_fee

            order.save(update_fields=['swadesic_fee','expected_swadesic_fee', 'transaction_fee'])

        return pg_transctionfee_with_tax_perc

    def calculate_swadesic_fee(self,total_order_amount):
        order_config = OrderConfiguration.objects.all().first()
        swadesic_fee = order_config.commission_fee if total_order_amount >= order_config.min_cart_value_for_commission_fee else 0
        return swadesic_fee


    def update_suborder_payment_details(self, response_data, pg_transctionfee_with_tax_perc):
        suborder_payment_details_instance = SubOrderPaymentDetails.objects.filter(
            order_request_number=response_data["body"]["orderId"]
        )
        for order_payment_details in suborder_payment_details_instance:
            order_payment_details.payment_status = SubOrderPaymentDetails.Payment_Status.PAYMENT_SUCCESS
            order_payment_details.save(update_fields=["payment_status"],
                                       pg_transctionfee_with_tax_perc=pg_transctionfee_with_tax_perc)

    def update_sub_order_status(self, response_data):
        order_id = Order.new_objects.filter(
            order_request_number=response_data["body"]["orderId"]
        ).values_list("orderid", flat=True)
        sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
        for suborder in sub_order_items:

            update_order_status_single = UpdateOrderStatus_single(suborder_number=suborder.suborder_number,
                                                                  status="WAITING_FOR_CONFIRMATION")
            update_order_status_single.update_order_status()

            # suborder.suborder_status = SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION
            # suborder.save(update_fields=["suborder_status"])

    # TODO: Remove this is redundant data - the details are already captured in Order(order request) table

    def update_order_life_cycle(self, response_data, payment_details):
        order_id = Order.new_objects.filter(
            order_request_number=response_data["body"]["orderId"]
        ).values_list("orderid", flat=True)
        OrderLifeCycle.objects.filter(orderid__in=order_id).update(
            razorpay_payment_id=response_data["body"]["razorpayPayId"],
            transaction_id=response_data["body"]["txnId"],
            bank_transaction_id=response_data["body"]["bankTxnId"],
            transaction_date=response_data["body"]["txnDate"],
            payment_mode=response_data["body"]["paymentMode"],
            payment_channel=get_payment_channel(payment_details)
        )

    def update_product_inventory(self, response_data):
        cart_instance = CartItem.new_objects.filter(order_request_number=response_data["body"]["orderId"])
        for item in cart_instance:
            product = Product.objects.get(productid=item.productid)
            if product.in_stock != 0 and product.in_stock >= item.product_quantity:
                product.in_stock -= item.product_quantity
                product.save(update_fields=['in_stock'])

    def update_cart_status(self, response_data):
        CartItem.new_objects.filter(order_request_number=response_data["body"]["orderId"]).update(
            cart_product_status="CART_DONE"
        )

    def send_notification(self, response_data):
        try:
            orders = Order.new_objects.filter(order_request_number=response_data["body"]["orderId"]).select_related(
                'store_reference').prefetch_related('suborderitems')
            for order in orders:
                store_reference = order.store_reference.store_reference
                user_instance = User.objects.get(userid=order.userid, deleted=False)
                user_image = user_instance.icon if user_instance.icon else None
                notification_handler = NotificationHandler(
                    notified_user=store_reference,
                    notification_type=Notifications.Notifications_Type.NEW_ORDER,
                    notification_about=order.order_number,
                    image=user_image
                )
                notification_handler.create_notification(notification_handler)
        except Exception as e:
            logger.error('An error occurred with the notifications: %s', str(e))


class RefundInitiate(mixins.CreateModelMixin, generics.GenericAPIView):
    @swagger_auto_schema(
        operation_summary="Initiate refund for multiple suborders",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["suborder_numbers", "razorpay_payment_id"],
            properties={
                "suborder_numbers": openapi.Schema(type=openapi.TYPE_ARRAY,
                                                   items=openapi.Schema(type=openapi.TYPE_STRING)),
                "razorpay_payment_id": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        suborder_numbers = request.data.get("suborder_numbers", [])
        razorpay_payment_id = request.data.get("razorpay_payment_id")

        if not suborder_numbers or not razorpay_payment_id:
            return Response({"message": "Missing required parameters"}, status=400)

        results = []
        for suborder_number in suborder_numbers:
            try:
                with transaction.atomic():
                    result = self.process_refund(suborder_number, razorpay_payment_id)
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing refund for suborder {suborder_number}: {str(e)}")
                results.append({"suborder_number": suborder_number, "status": "error", "message": str(e)})

        return Response({"results": results}, status=200)

    def process_refund(self, suborder_number, razorpay_payment_id):
        order_number = suborder_number.split("-")[0]
        order = Order.new_objects.get(order_number=order_number)

        refund_instance = RefundedAmount.objects.get(
            suborder_reference=suborder_number,
            refund_status=RefundedAmount.Status.UNPROCESSED,
        )
        refund_amount = refund_instance.refunded_amount # todo
        refund_id = create_refund_id(suborder_number)

        if SKIP_RAZORPAY_API:
            response = self.get_mock_response(order, refund_amount, refund_id)
        else:
            response = self.call_razorpay_api(razorpay_payment_id, refund_amount, refund_id, order)

        return self.handle_refund_response(response, suborder_number, order_number, refund_instance)

    def get_mock_response(self, order, refund_amount, refund_id):
        return {
            "body": {
                "txnTimestamp": get_date(),
                "orderId": order.order_request_number,
                "mid": RAZORPAY_ID,
                "refId": "123456789",
                "resultInfo": {
                    "resultStatus": "PENDING",
                    "resultCode": "601",
                    "resultMsg": "Refund request was raised for this transaction. But it is pending state",
                },
                "refundId": refund_id,
                "txnId": "arn_12345678",
                "refundAmount": str(refund_amount),
                "razorpayRefundId": "dummy_rfnd_qwert123",
                "acquirerData": {"utr": "123456789"}
            },
        }

    def call_razorpay_api(self, razorpay_payment_id, refund_amount, refund_id, order):
        url = f'https://api.razorpay.com/v1/payments/{razorpay_payment_id}/refund'
        payload = {
            'amount': refund_amount * 100,
            'receipt': refund_id,
            'notes': {
                'order_request_number': order.order_request_number,
                'order_number': order.order_number,
                'transaction_id': order.transaction_id,
            }
        }

        response = requests.post(
            url,
            json=payload,
            auth=HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY),
            headers={'Content-Type': 'application/json'}
        )
        api_response = response.json()

        if "error" in api_response:
            raise Exception(f"Razorpay API error: {api_response['error']['description']}")

        return {
            "body": {
                "txnTimestamp": api_response["created_at"],
                "orderId": api_response["notes"]["order_request_number"],
                "mid": RAZORPAY_ID,
                "refId": "123456789",
                "resultInfo": {
                    "resultStatus": api_response["status"],
                    "resultCode": "601",
                    "resultMsg": api_response["status"]
                },
                "refundId": api_response["receipt"],
                "txnId": get_refund_txn_id_from_acquirer_data(acquirer_data=api_response["acquirer_data"]),
                "refundAmount": str(api_response["amount"] / 100),
                "razorpayRefundId": api_response["id"],
                "acquirerData": api_response["acquirer_data"]
            },
        }

    def handle_refund_response(self, response, suborder_number, order_number, refund_instance):
        body = response["body"]
        initiate_response_status = body["resultInfo"]["resultStatus"]
        initiate_response_msg = body["resultInfo"]["resultMsg"]
        razorpay_refund_id = body["razorpayRefundId"]
        refund_transaction_id = body["txnId"]
        refund_id = body["refundId"]
        refund_amount = float(body["refundAmount"])
        refunded_date_unix = body["txnTimestamp"]
        refunded_date = datetime.datetime.fromtimestamp(refunded_date_unix)

        order_payment_details = SubOrderPaymentDetails.objects.get(
            suborder_number=SubOrder.objects.get(suborder_number=suborder_number)
        )

        status_mapping = {
            "processed": (RefundedAmount.Status.REFUNDED, SubOrderPaymentDetails.Payment_Status.REFUND_SUCCESS),
            "pending": (RefundedAmount.Status.PENDING, SubOrderPaymentDetails.Payment_Status.REFUND_PENDING),
            "failed": (RefundedAmount.Status.REFUND_FAILED, SubOrderPaymentDetails.Payment_Status.REFUND_FAILED),
        }

        refund_status, payment_status = status_mapping.get(
            initiate_response_status.lower(),
            (RefundedAmount.Status.REFUND_FAILED, SubOrderPaymentDetails.Payment_Status.REFUND_FAILED)
        )

        # Update RefundedAmount instance
        refund_instance.refund_status = refund_status
        refund_instance.refund_id = refund_id
        refund_instance.initiate_refund_status = initiate_response_status
        refund_instance.initiate_refund_message = initiate_response_msg
        refund_instance.refund_transaction_id = refund_transaction_id
        refund_instance.razorpay_refund_id = razorpay_refund_id
        refund_instance.refunded_date = refunded_date
        refund_instance.save(update_fields=[
            "refund_status", "refund_id", "initiate_refund_status",
            "initiate_refund_message", "refund_transaction_id", "razorpay_refund_id", "refunded_date"
        ])

        # Update OrderPaymentDetails instance
        order_payment_details.payment_status = payment_status
        order_payment_details.refund_id = refund_id
        order_payment_details.refund_amount = refund_amount
        order_payment_details.save(update_fields=["payment_status", "refund_id", "refund_amount"])

        if initiate_response_status.lower() == "processed":
                self.create_refund_notification(order_number, suborder_number)

        return {
            "suborder_number": suborder_number,
            "status": initiate_response_status.lower(),
            "message": initiate_response_msg
        }

    def create_refund_notification(self, order_number, suborder_number):
        order = Order.objects.get(order_number=order_number)
        suborder = SubOrder.objects.get(suborder_number=suborder_number)

        notification_handler = NotificationHandler(
            notified_user=order.user_reference.user_reference,
            notification_type=Notifications.Notifications_Type.REFUND_INITIATED,
            notification_about=order_number,
            image=suborder.product_image,
        )
        notification_handler.create_notification(notification_handler)


