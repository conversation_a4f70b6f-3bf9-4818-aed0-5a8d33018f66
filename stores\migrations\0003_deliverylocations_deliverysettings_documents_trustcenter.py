# Generated by Django 3.2.13 on 2022-07-26 17:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0002_address_storelink_userstore"),
    ]

    operations = [
        migrations.CreateModel(
            name="DeliveryLocations",
            fields=[
                (
                    "deliverylocationid",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("pincode", models.CharField(max_length=6)),
                ("city", models.CharField(max_length=200)),
                ("state", models.CharField(max_length=200)),
                ("created_by", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "modified_by",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("modified_date", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "delivery locations",
                "db_table": '"store"."delivery_locations"',
            },
        ),
        migrations.CreateModel(
            name="TrustCenter",
            fields=[
                ("trustcenterid", models.AutoField(primary_key=True, serialize=False)),
                ("joiningdate", models.DateTimeField(auto_now=True)),
                ("trustscore", models.IntegerField(blank=True, null=True)),
                (
                    "sellerlevel",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "phonenumber",
                    models.CharField(blank=True, max_length=100000, null=True),
                ),
                ("emailid", models.CharField(blank=True, max_length=100000, null=True)),
                (
                    "primarycontacttype",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("isphysicalstore", models.BooleanField(blank=True, null=True)),
                ("address", models.CharField(blank=True, max_length=255, null=True)),
                ("city", models.CharField(blank=True, max_length=100, null=True)),
                ("pincode", models.CharField(blank=True, max_length=6, null=True)),
                ("state", models.CharField(blank=True, max_length=100, null=True)),
                ("longitude", models.FloatField(blank=True, null=True)),
                ("latitude", models.FloatField(blank=True, null=True)),
                (
                    "storeid",
                    models.ForeignKey(
                        db_column="storeid",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trustcenter",
                        to="stores.store",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "trustcenter",
                "db_table": '"store"."trust_center"',
            },
        ),
        migrations.CreateModel(
            name="Documents",
            fields=[
                ("documentid", models.AutoField(primary_key=True, serialize=False)),
                ("document_file", models.FileField(upload_to="documents")),
                ("documentname", models.CharField(max_length=100, null=True)),
                ("show_to_public", models.BooleanField(default=False)),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                (
                    "storeid",
                    models.ForeignKey(
                        db_column="storeid",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.store",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "documents",
                "db_table": '"store"."documents"',
            },
        ),
        migrations.CreateModel(
            name="DeliverySettings",
            fields=[
                (
                    "deliverysettingid",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("productid", models.IntegerField(blank=True, null=True)),
                ("deliverymethod_whitelabel", models.BooleanField(default=False)),
                ("deliverymethod_logistics", models.BooleanField(default=False)),
                ("deliverymethod_self", models.BooleanField(default=False)),
                (
                    "delivery_locations",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("deliveryfeetype_all_free", models.BooleanField(default=False)),
                ("deliveryfeetype_standard", models.BooleanField(default=False)),
                ("deliveryfeetype_distance", models.BooleanField(default=False)),
                ("deliveryfee_value", models.IntegerField(blank=True, null=True)),
                (
                    "deliveryfee_valuetype",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "distance_based_max_deliveryfee",
                    models.IntegerField(blank=True, null=True),
                ),
                ("no_deliveryfee_maxvalue", models.IntegerField(blank=True, null=True)),
                ("no_deliveryfee_maxvalue_enabled", models.BooleanField(default=False)),
                ("no_deliveryfee_products", models.IntegerField(blank=True, null=True)),
                ("no_deliveryfee_products_enabled", models.BooleanField(default=False)),
                (
                    "no_deliveryfee_samepincode_enabled",
                    models.BooleanField(default=False),
                ),
                (
                    "no_deliveryfee_samecity_enablded",
                    models.BooleanField(default=False),
                ),
                ("created_by", models.CharField(blank=True, max_length=100, null=True)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "modified_by",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("modified_date", models.DateTimeField(auto_now=True)),
                (
                    "delivery_personal_name",
                    models.CharField(blank=True, max_length=300, null=True),
                ),
                (
                    "delivery_personal_phone",
                    models.CharField(blank=True, max_length=300, null=True),
                ),
                (
                    "default_logistic_partner",
                    models.CharField(blank=True, max_length=300, null=True),
                ),
                (
                    "storeid",
                    models.ForeignKey(
                        db_column="storeid",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deliverysettings",
                        to="stores.store",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "delivery settings",
                "db_table": '"store"."delivery_settings"',
            },
        ),
    ]
