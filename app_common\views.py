from django.shortcuts import render
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from .models import AppFeatureRequests

# Create your views here.

@api_view(['POST'])
def update_feature_request(request):
    """
    API to add or update a feature request for a user/store.
    
    Request body:
    {
        "reference": "string",  # user_reference or store_reference
        "store_ai": boolean,    # optional, interest in store AI feature
        "product_affiliation": boolean  # optional, interest in product affiliation feature
    }
    """
    try:
        reference = request.data.get('reference')
        if not reference:
            return Response({'error': 'Reference is required'}, status=status.HTTP_400_BAD_REQUEST)
            
        feature_request, created = AppFeatureRequests.objects.get_or_create(reference=reference)
        
        # Update only if the fields are provided in the request
        if 'store_ai' in request.data:
            feature_request.requested_store_ai = request.data['store_ai']
        if 'product_affiliation' in request.data:
            feature_request.requested_product_affiliation = request.data['product_affiliation']
            
        feature_request.save()
        
        return Response({"message": "success", "data": {
            'reference': feature_request.reference,
            'requested_store_ai': feature_request.requested_store_ai,
            'requested_product_affiliation': feature_request.requested_product_affiliation
        }}, status=status.HTTP_200_OK if not created else status.HTTP_201_CREATED)
        
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_feature_requests(request):
    """
    API to fetch feature requests for a given reference.
    
    Query parameters:
    - reference: string (required) - user_reference or store_reference
    """
    try:
        reference = request.query_params.get('reference')
        if not reference:
            return Response({'error': 'Reference is required'}, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            feature_request = AppFeatureRequests.objects.get(reference=reference)
            return Response({"message": "success", "data": {
                'reference': feature_request.reference,
                'requested_store_ai': feature_request.requested_store_ai,
                'requested_product_affiliation': feature_request.requested_product_affiliation
            }}, status=status.HTTP_200_OK)
        except AppFeatureRequests.DoesNotExist:
            return Response({"message": "error", "data": "No feature requests found for the reference"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
