# Generated by Django 3.2.13 on 2023-04-12 07:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0035_alter_user_invite_type'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='add_comment',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='add_products',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='add_question',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='add_review',
            field=models.Char<PERSON>ield(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='buy_product',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='user',
            name='clap_comment_question_review',
            field=models.<PERSON><PERSON><PERSON><PERSON>(default='1', max_length=2),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='user',
            name='create_promotion',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='create_store',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='edit_community_forum',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='follow_people',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='follow_stores',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='giving_suggestions',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='include_in_analytics',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='include_in_rating_calculation',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='include_in_self_trust_score',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='included_in_others_trust_score',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='message_people',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='receive_orders',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='receive_payments',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='report_comment_question_review',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='report_people',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='report_product',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='report_stores',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='save_product',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='search_and_view',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='send_member_invites',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='send_seller_invites',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='store_live',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='view_comment_question_review',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='view_community_forum',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='view_product',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='view_store_contact',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='view_store_documents',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='view_stores',
            field=models.CharField(default='1', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='vote_for_membership',
            field=models.CharField(default='1', max_length=2),
        ),
    ]
