# Generated by Django 4.2.7 on 2024-11-20 19:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("stores", "0083_alter_store_subscription_type"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SubscriptionPayment",
            fields=[
                ("payment_id", models.AutoField(primary_key=True, serialize=False)),
                ("payment_reference", models.Char<PERSON>ield(max_length=100, unique=True)),
                ("subscription_id", models.CharField(max_length=100)),
                ("invoice_id", models.Char<PERSON>ield(max_length=100)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("currency", models.Char<PERSON>ield(default="INR", max_length=3)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("SUCCESS", "Success"),
                            ("FAILED", "Failed"),
                            ("PENDING", "Pending"),
                        ],
                        max_length=10,
                    ),
                ),
                ("payment_method", models.<PERSON>r<PERSON><PERSON>(max_length=20)),
                ("fee_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("tax_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("fee_percentage", models.DecimalField(decimal_places=2, max_digits=5)),
                ("tax_percentage", models.DecimalField(decimal_places=2, max_digits=5)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("payment_date", models.DateTimeField()),
                ("billing_period_start", models.DateTimeField()),
                ("billing_period_end", models.DateTimeField()),
                (
                    "store_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="store_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscription_payments",
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
                (
                    "user_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="user_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscription_payments",
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_reference",
                    ),
                ),
            ],
            options={
                "db_table": '"user"."subscription_payment"',
                "indexes": [
                    models.Index(
                        fields=["payment_reference"],
                        name="subscriptio_payment_780e03_idx",
                    ),
                    models.Index(
                        fields=["subscription_id"],
                        name="subscriptio_subscri_fbac44_idx",
                    ),
                    models.Index(
                        fields=["invoice_id"], name="subscriptio_invoice_556578_idx"
                    ),
                ],
            },
        ),
    ]
