from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db.models import Prefetch
from products.models import Product, ProductImages
from stores.store_api.models import Store, StoreConfig
from stores.store_settings_api.models import TrustCenter, RefundAndWarranty, DeliverySettings
from content.models import Posts, Comments, CommentImages, PostImages
from GraphDB.queries import dbqueries, sqlqueries
from GraphDB.models import Neo4jEntity, Neo4jContent
from .serializers import (
    LeanStoreProductsSerializer,
    LeanProductDetailsSerializer,
    LeanProductPartialDetailsSerializer,
    LeanStoreProductFullDetailsSerializer,
    LeanPostPartialDetailsSerializer,
    LeanCommentsSerializer
)
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging
import time
import json
from general.views import BuyStatusCheckerBatch
from django.db.models import Prefetch
from .utils import prepare_context_data


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)



class LeanStoreProductsView(APIView):
    """
    A lean API view to fetch store products with minimal fields.
    Optimized for performance with reduced DB queries and memory usage.
    """

    @swagger_auto_schema(
        operation_summary="Get store products (lean version)",
        operation_description="Fetch store products with only essential fields for better performance",
        manual_parameters=[
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description="Number of products to return",
                type=openapi.TYPE_INTEGER,
                default=20
            ),
            openapi.Parameter(
                'offset',
                openapi.IN_QUERY,
                description="Number of products to skip",
                type=openapi.TYPE_INTEGER,
                default=0
            ),
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'total_count': openapi.Schema(type=openapi.TYPE_INTEGER),
                    }
                )
            ),
            404: "Store not found",
            500: "Server error"
        }
    )
    def get(self, request, store_reference):
        """
        Get store products with only essential fields.

        Args:
            request: HTTP request
            store_reference: Store reference ID

        Returns:
            Response with products data
        """
        try:
            # Check if store exists and is not deleted
            if not Store.objects.filter(store_reference=store_reference, deleted=False).exists():
                return Response(
                    {"message": "Store not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get query parameters
            limit = int(request.query_params.get('limit', 20))
            offset = int(request.query_params.get('offset', 0))

            # Optimize query with prefetch_related to avoid N+1 queries
            # Only fetch active, non-hidden, non-deleted products
            products_query = Product.objects.filter(
                store_reference=store_reference,
                hide=False,
                deleted=False
            ).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                    to_attr='prefetched_images'
                )
            ).order_by('-created_date')

            # Apply pagination
            products = products_query[offset:offset + limit]

            # Serialize data
            serializer = LeanStoreProductsSerializer(products, many=True)

            # Get total count for pagination
            total_count = products_query.count()

            return Response({
                "message": "success",
                "data": serializer.data,
                "total_count": total_count
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching store products: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LeanStoreProductsNewView(APIView):
    """
    A lean API view to fetch store products with minimal fields.
    Optimized for performance with reduced DB queries and memory usage.
    """

    @swagger_auto_schema(
        operation_summary="Get store products (lean version)",
        operation_description="Fetch store products with only essential fields for better performance",
        manual_parameters=[
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description="Number of products to return",
                type=openapi.TYPE_INTEGER,
                default=20
            ),
            openapi.Parameter(
                'offset',
                openapi.IN_QUERY,
                description="Number of products to skip",
                type=openapi.TYPE_INTEGER,
                default=0
            ),
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                        'total_count': openapi.Schema(type=openapi.TYPE_INTEGER),
                    }
                )
            ),
            404: "Store not found",
            500: "Server error"
        }
    )
    def get(self, request, store_reference):
        """
        Get store products with only essential fields.

        Args:
            request: HTTP request
            store_reference: Store reference ID

        Returns:
            Response with products data
        """
        try:
            start = time.perf_counter()
            # Check if store exists and is not deleted
            if not Store.objects.filter(store_reference=store_reference, deleted=False).exists():
                return Response(
                    {"message": "Store not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get query parameters
            limit = int(request.query_params.get('limit', 20))
            offset = int(request.query_params.get('offset', 0))

            # Optimize query with prefetch_related to avoid N+1 queries
            # Only fetch active, non-hidden, non-deleted products
            products_query = Product.objects.filter(
                store_reference=store_reference,
                hide=False,
                deleted=False
            ).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                    to_attr='prefetched_images'
                )
            ).order_by('-created_date')

            # Apply pagination
            products = products_query[offset:offset + limit]

            # Serialize data
            serializer = LeanStoreProductsSerializer(products, many=True)

            # Get total count for pagination
            total_count = products_query.count()

            response = Response({
                "message": "success",
                "data": serializer.data,
                "total_count": total_count
            }, status=status.HTTP_200_OK)
            end = time.perf_counter()
            logger.info(f"Api Time: {end - start:.6f} seconds")
            return response

        except Exception as e:
            logger.error(f"Error fetching store products: {str(e)}")
            end = time.perf_counter()
            logger.info(f"Time taken: {end - start:.6f} seconds")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LeanProductFullDetailsView(APIView):
    """
    A highly optimized lean API view to fetch product details for multiple product references.
    Uses batch loading and minimizes database queries for maximum performance.
    """

    @swagger_auto_schema(
        operation_summary="Get product details by references (optimized lean version)",
        operation_description="Fetch product details for multiple product references with the same structure as ProductDetailsAV API",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['product_references'],
            properties={
                'product_references': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of product references"
                ),
                'visitor_reference': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Visitor reference (optional)"
                ),
                'user_pincode': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="User pincode for deliverability check (optional)"
                )
            }
        ),
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def post(self, request):
        """
        Get product details for multiple product references using batch loading.

        Args:
            request: HTTP request with product_references in the body

        Returns:
            Response with product details
        """
        try:
            # Get request data
            data = request.data

            # Validate input
            if not data or 'product_references' not in data:
                return Response(
                    {"message": "product_references is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            product_references = data.get('product_references', [])
            visitor_reference = data.get('visitor_reference')
            user_pincode = data.get('user_pincode')

            if not isinstance(product_references, list):
                return Response(
                    {"message": "product_references must be a list"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if len(product_references) == 0:
                return Response(
                    {"message": "product_references cannot be empty"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Limit the number of products to prevent overloading
            max_products = 50
            if len(product_references) > max_products:
                product_references = product_references[:max_products]
                logger.warning(f"Product references list truncated to {max_products} items")

            # Optimize query with select_related and prefetch_related to avoid N+1 queries
            products = Product.objects.filter(
                product_reference__in=product_references,
                deleted=False
            ).select_related(
                'store_reference'
            ).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                    to_attr='prefetched_images'
                )
            )

            # Batch load related data for all products at once
            store_references = []
            for product in products:
                if product.store_reference and product.store_reference.store_reference:
                    store_references.append(product.store_reference.store_reference)

            # Batch load trust centers
            trust_centers = {}
            if store_references:
                trust_center_objects = TrustCenter.objects.filter(
                    store_reference__store_reference__in=store_references,
                )
                for tc in trust_center_objects:
                    if tc.store_reference and tc.store_reference.store_reference:
                        trust_centers[tc.store_reference.store_reference] = tc

            # Batch load refund and warranty settings
            refund_warranties = {}
            if store_references:
                refund_warranty_objects = RefundAndWarranty.objects.filter(
                    store_reference__store_reference__in=store_references,
                    is_deleted=False
                )
                for rw in refund_warranty_objects:
                    if rw.store_reference and rw.store_reference.store_reference:
                        refund_warranties[rw.store_reference.store_reference] = rw

            # Batch load delivery settings
            delivery_settings = {}
            # First load product-specific delivery settings
            if product_references:
                product_delivery_objects = DeliverySettings.objects.filter(
                    product_reference__in=product_references,
                    is_deleted=False
                )
                for ds in product_delivery_objects:
                    if ds.product_reference:
                        delivery_settings[ds.product_reference] = ds

            # Then load store-level delivery settings
            if store_references:
                store_delivery_objects = DeliverySettings.objects.filter(
                    store_reference__store_reference__in=store_references,
                    product_reference__isnull=True,
                    is_deleted=False
                )
                for ds in store_delivery_objects:
                    if ds.store_reference and ds.store_reference.store_reference:
                        delivery_settings[ds.store_reference.store_reference] = ds

            # Batch load store configs
            store_configs = {}
            if store_references:
                store_config_objects = StoreConfig.objects.filter(
                    store_reference__store_reference__in=store_references
                )
                for sc in store_config_objects:
                    if sc.store_reference and sc.store_reference.store_reference:
                        store_configs[sc.store_reference.store_reference] = sc

            # Create context for serializer with all batch-loaded data
            context = {
                'trust_centers': trust_centers,
                'refund_warranties': refund_warranties,
                'delivery_settings': delivery_settings,
                'store_configs': store_configs
            }

            if visitor_reference:
                context['visitor_reference'] = visitor_reference
            if user_pincode:
                context['user_pincode'] = user_pincode

            # Serialize data using the optimized serializer
            serializer = LeanProductDetailsSerializer(products, many=True, context=context)

            return Response({
                "message": "success",
                "data": serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching product details: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LeanProductPartialDetailsView(APIView):
    """
    A lean API view to fetch partial product details with only specific fields.
    Optimized for performance with reduced DB queries and memory usage.
    """

    @swagger_auto_schema(
        operation_summary="Get partial product details by references (lean version)",
        operation_description="Fetch partial product details with only essential fields for better performance",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['product_references'],
            properties={
                'product_references': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of product references"
                ),
                'visitor_reference': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Visitor reference (optional)"
                ),
                'user_pincode': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="User pincode for deliverability check (optional)"
                )
            }
        ),
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request):
        """
        Get partial product details for multiple product references.

        Args:
            request: HTTP request with product_references in the body

        Returns:
            Response with partial product details
        """
        try:
            start = time.perf_counter()
            # Get request data
            data = request.data

            # Validate input
            if not data or 'store_reference' not in data:
                return Response(
                    {"message": "store_reference is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # product_references = data.get('product_references', [])
            store_reference = request.query_params.get('store_reference')
            visitor_reference = request.query_params.get('visitor_reference')
            user_pincode = request.query_params.get('user_pincode')
            limit = int(request.query_params.get('limit', 20))
            offset = int(request.query_params.get('offset', 0))

            if not isinstance(store_reference, str):
                return Response(
                    {"message": "store_references must be a string"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Optimize query with select_related and prefetch_related to avoid N+1 queries
            products = Product.objects.filter(
                store_reference=store_reference,
                deleted=False
            ).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                    to_attr='prefetched_images'
                )
            )[offset:offset+limit]

            # Create context for serializer
            context = {}
            if visitor_reference:
                context['visitor_reference'] = visitor_reference
            if user_pincode:
                context['user_pincode'] = user_pincode

            # Serialize data using the lean partial details serializer
            serializer = LeanProductPartialDetailsSerializer(products, many=True, context=context)
            data = serializer.data
            end = time.perf_counter()
            logger.info(f"Api Time: {end - start:.6f} seconds")
            return Response({
                "message": "success",
                "data": data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching partial store product details: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LeanPaginatedStoreProductsView(APIView):
    """
    A highly optimized lean API view to fetch store products with pagination.
    Uses batch loading for related data to minimize queries and maximize performance.
    """

    @swagger_auto_schema(
        operation_summary="Get store products with pagination (lean version)",
        operation_description="Fetch store products with pagination and full product details",
        manual_parameters=[
            openapi.Parameter(
                name='store_reference',
                in_=openapi.IN_PATH,
                type=openapi.TYPE_STRING,
                required=True,
                description='Store reference'
            ),
            openapi.Parameter(
                name='offset',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description='Offset for pagination (default: 0)'
            ),
            openapi.Parameter(
                name='limit',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description='Limit for pagination (default: 10, max: 50)'
            ),
            openapi.Parameter(
                name='visitor_reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Visitor reference for personalized data'
            ),
            openapi.Parameter(
                name='user_pincode',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='User pincode for deliverability check'
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            404: "Store not found",
            500: "Server error"
        }
    )

    def get(self, request, store_reference):
        """
        Get store products with pagination and full product details.
        """
        start_total = time.perf_counter()

        try:
            # ################# Parse query parameters ##################
            start_params = time.perf_counter()
            offset = int(request.query_params.get('offset', 0))
            limit = min(int(request.query_params.get('limit', 10)), 50)
            visitor_reference = request.query_params.get('visitor_reference')
            user_pincode = request.query_params.get('user_pincode')
            logger.info(f"Params parsed in {time.perf_counter() - start_params:.4f} seconds")

            # ################# Fetch store ##################
            start_store = time.perf_counter()
            try:
                store = Store.objects.get(store_reference=store_reference)
            except Store.DoesNotExist:
                return Response(
                    {"message": f"Store with reference {store_reference} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            logger.info(f"Store fetch in {time.perf_counter() - start_store:.4f} seconds")

            # ################# Fetch products ##################
            start_products = time.perf_counter()
            products = Product.objects.filter(
                store_reference=store,
                deleted=False
            ).select_related(
                'store_reference'
            ).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                    to_attr='prefetched_images'
                )
            ).order_by('-created_date')[offset:offset+limit]
            logger.info(f"Products fetch in {time.perf_counter() - start_products:.4f} seconds")

            # ################# Batch load related data ##################
            store_references = [store_reference]
            context = prepare_context_data(
                store_references=store_references,
                products=products,
                visitor_reference=visitor_reference,
                user_pincode=user_pincode
            )

            if visitor_reference:
                context['visitor_reference'] = visitor_reference
            if user_pincode:
                context['user_pincode'] = user_pincode

            serializer = LeanStoreProductFullDetailsSerializer(products, many=True, context=context)

            total_time = time.perf_counter() - start_total
            logger.info(f"Total time for GET store products: {total_time:.4f} seconds")

            # Return response
            return Response({
                "message": "success",
                "data": serializer.data,
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching paginated store products: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



class LeanPaginatedStorePartialProductsView(APIView):
    """
    A highly optimized lean API view to fetch store products with pagination.
    Uses batch loading for related data to minimize queries and maximize performance.
    """

    @swagger_auto_schema(
        operation_summary="Get store products with pagination (lean version)",
        operation_description="Fetch store products with pagination and full product details",
        manual_parameters=[
            openapi.Parameter(
                name='store_reference',
                in_=openapi.IN_PATH,
                type=openapi.TYPE_STRING,
                required=True,
                description='Store reference'
            ),
            openapi.Parameter(
                name='offset',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description='Offset for pagination (default: 0)'
            ),
            openapi.Parameter(
                name='limit',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
                description='Limit for pagination (default: 10, max: 50)'
            ),
            openapi.Parameter(
                name='visitor_reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Visitor reference for personalized data'
            ),
            openapi.Parameter(
                name='user_pincode',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='User pincode for deliverability check'
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            404: "Store not found",
            500: "Server error"
        }
    )

    def get(self, request, store_reference):
        """
        Get store products with pagination and full product details.
        """
        start_total = time.perf_counter()

        try:
            # ################# Parse query parameters ##################
            start_params = time.perf_counter()
            offset = int(request.query_params.get('offset', 0))
            limit = min(int(request.query_params.get('limit', 10)), 50)
            visitor_reference = request.query_params.get('visitor_reference')
            user_pincode = request.query_params.get('user_pincode')
            logger.info(f"Params parsed in {time.perf_counter() - start_params:.4f} seconds")

            # ################# Fetch store ##################
            start_store = time.perf_counter()
            try:
                store = Store.objects.get(store_reference=store_reference)
            except Store.DoesNotExist:
                return Response(
                    {"message": f"Store with reference {store_reference} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            logger.info(f"Store fetch in {time.perf_counter() - start_store:.4f} seconds")

            # ################# Fetch products ##################
            start_products = time.perf_counter()
            products = Product.objects.filter(
                store_reference=store,
                deleted=False
            ).select_related(
                'store_reference'
            ).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                    to_attr='prefetched_images'
                )
            ).order_by('-created_date')[offset:offset+limit]
            logger.info(f"Products fetch in {time.perf_counter() - start_products:.4f} seconds")

            # ################# Batch load related data ##################
            # store_references = [store_reference]
            # context = prepare_context_data(
            #     store_references=store_references,
            #     products=products,
            #     visitor_reference=visitor_reference,
            #     user_pincode=user_pincode
            # )

            
            # get context for all references (neo4j_interactions)
            context = {}
            product_refs = products.values_list('product_reference', flat=True)
            neo4j_interactions = get_batched_interaction_details(visitor_reference, product_refs)
            context['neo4j_interactions'] = {k: neo4j_interactions[k] for k in product_refs if k in neo4j_interactions}
            
            # get buy status for products
            checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
            buy_status_map = checker.get_buy_status_map()

            context["buy_status_map"] = buy_status_map        

            if visitor_reference:
                context['visitor_reference'] = visitor_reference
            if user_pincode:
                context['user_pincode'] = user_pincode

            serializer = LeanProductPartialDetailsSerializer(products, many=True, context=context)

            total_time = time.perf_counter() - start_total
            logger.info(f"Total time for GET store products: {total_time:.4f} seconds")

            # Return response
            return Response({
                "message": "success",
                "data": serializer.data,
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching paginated store products: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



class LeanFeedAPIView(APIView):
    """
    A lean API view to fetch feed content with optimized performance.
    Handles different content types (Posts, Products, Comments) with appropriate serializers.
    """
    @swagger_auto_schema(
        operation_summary="Get feed (lean version)",
        operation_description="Fetch feed with optimized performance",
        manual_parameters=[
            openapi.Parameter(
                name='reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description='Reference of the user or store'
            ),
            openapi.Parameter(
                name='limit',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=10,
                description='Number of feed items to return'
            ),
            openapi.Parameter(
                name='offset',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=0,
                description='Number of feed items to skip'
            ),
            openapi.Parameter(
                name='user_pincode',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='User pincode for deliverability check'
            ),
            openapi.Parameter(
                name='visitor_reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Visitor reference for personalized content'
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request):
        """
        Get feed content with optimized performance.
        Handles different content types (Posts, Products, Comments) with appropriate serializers.
        """
        start_time = time.perf_counter()

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            user_pincode = request.query_params.get('user_pincode')
            visitor_reference = request.query_params.get('visitor_reference')
            
            # neo4j fetch
            start_db_query = time.perf_counter()

            feed_references_dict = dbqueries.get_batched_feed_details_new(entity_reference=visitor_reference, limit=limit, offset=offset)
            interaction_and_creator_details = feed_references_dict[0]
            post_references = feed_references_dict[1]
            product_references = feed_references_dict[2]
            comment_references = feed_references_dict[3]

            print(f"interaction_and_creator_detail_references: {[detail['reference'] for detail in interaction_and_creator_details]}")
            print(f"post_references: {post_references}")
            print(f"product_references: {product_references}")
            print(f"comment_references: {comment_references}")
                        

            neo4j_end = time.perf_counter()
            logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


            #sql fetch
            #sql data fetch
            sql_instance = sqlqueries()
            post_data = sql_instance.get_posts_full(post_refs=post_references)
            product_data = sql_instance.get_products_hybrid(product_refs=product_references, store_ref=None, limit=limit, offset=0 )

            post_data_json = json.loads(post_data)
            product_data_json =  json.loads(product_data)

            # print(f"post_data_json: {post_data_json}")
            # print(f"product_data_json: {product_data_json}")

            #buy statuc batch fetch
            products = Product.objects.filter(product_reference__in=product_references)
            checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
            buy_status_map = checker.get_buy_status_map()

            # # method 1
            
            # Build a single lookup for all references
            reference_lookup = {}

            for post in post_data_json:
                reference_lookup[post["post_reference"]] = post

            for product in product_data_json:
                reference_lookup[product["product_reference"]] = product

            # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
            # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

            # Build consolidated output in one pass
            consolidated_output = [
                {
                    **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                    **item["interaction"],
                    **item["creator"],
                    **buy_status_map.get(item["reference"], {})
                }
                for item in interaction_and_creator_details
            ]

            # print(f"consolidated_output: {consolidated_output}")

            total_time = time.perf_counter() - neo4j_end
            logger.info(f"Total time for SQL products: {total_time:.4f} seconds")

            return Response({"message":"success", "data":consolidated_output}, status=200)

            
        except Exception as e:
            logger.error(f"Error in LeanFeedAPIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    

class LeanFeedAPIView_old(APIView):
    """
    A lean API view to fetch feed content with optimized performance.
    Handles different content types (Posts, Products, Comments) with appropriate serializers.
    """
    @swagger_auto_schema(
        operation_summary="Get feed (lean version)",
        operation_description="Fetch feed with optimized performance",
        manual_parameters=[
            openapi.Parameter(
                name='reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description='Reference of the user or store'
            ),
            openapi.Parameter(
                name='limit',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=10,
                description='Number of feed items to return'
            ),
            openapi.Parameter(
                name='offset',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=0,
                description='Number of feed items to skip'
            ),
            openapi.Parameter(
                name='user_pincode',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='User pincode for deliverability check'
            ),
            openapi.Parameter(
                name='visitor_reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Visitor reference for personalized content'
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request):
        """
        Get feed content with optimized performance.
        Handles different content types (Posts, Products, Comments) with appropriate serializers.
        """
        start_time = time.perf_counter()

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            user_pincode = request.query_params.get('user_pincode')
            visitor_reference = request.query_params.get('visitor_reference')
            
            # core-logic
            start_db_query = time.perf_counter()
            feed_references_dict = dbqueries.get_sorted_feed_content_references(entity_reference=visitor_reference, limit=limit, offset=offset)
            end_db_query = time.perf_counter()
            logger.info(f"DB query in {end_db_query - start_db_query:.4f} seconds")

            start_django_fetch = time.perf_counter()
            posts = Posts.objects.filter(post_reference__in=feed_references_dict['post_references'], is_deleted=False).prefetch_related(Prefetch('post_images', queryset=PostImages.objects.filter(is_deleted=False).order_by('reorder'), to_attr='prefetched_images'))
            comments = Comments.objects.filter(comment_reference__in=feed_references_dict['comment_references'], is_deleted=False).prefetch_related(Prefetch('comment_images', queryset=CommentImages.objects.filter(is_deleted=False).order_by('reorder'), to_attr='prefetched_images'))
            products = Product.objects.filter(product_reference__in=feed_references_dict['product_references'], deleted=False).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False, product_reference__in=feed_references_dict['product_references']).order_by('reorder'),
                    to_attr='prefetched_images'
                ),
            ).prefetch_related('store_reference__store_details')

            end_django_fetch = time.perf_counter()
            logger.info(f"Django fetch in {end_django_fetch - start_django_fetch:.4f} seconds")


            # prepare context for products, posts & comments
            
            # get context for products except neo4j_interactions
            # if products:
            #     store_references = products.values_list('store_reference', flat=True).distinct()
            #     product_context = prepare_context_data(
            #         store_references=store_references,
            #         products=products,
            #         visitor_reference=visitor_reference,
            #         user_pincode=user_pincode,
            #         get_neo4j_interactions=False
            #     ) 
            # else: 
            product_context = {}

            # get context for all references (neo4j_interactions)
            start_neo4j_fetch = time.perf_counter()
            neo4j_interactions = dbqueries.get_batched_interaction_details(visitor_reference, feed_references_dict['references'])
            product_context['neo4j_interactions'] = {k: neo4j_interactions[k] for k in feed_references_dict['product_references'] if k in neo4j_interactions}
            end_neo4j_fetch = time.perf_counter()
            logger.info(f"Neo4j fetch in {end_neo4j_fetch - start_neo4j_fetch:.4f} seconds")

            # get buy status for products
            start_buy_status = time.perf_counter()
            checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
            buy_status_map = checker.get_buy_status_map()

            product_context["buy_status_map"] = buy_status_map
            end_buy_status = time.perf_counter()
            logger.info(f"Buy status check in {end_buy_status - start_buy_status:.4f} seconds")

            
            #get batched creator details for all content
            start_creator_details = time.perf_counter()
            creator_details = dbqueries.get_batched_content_creator_details(feed_references_dict['references'])
            end_creator_details = time.perf_counter()
            logger.info(f"Creator details fetch in {end_creator_details - start_creator_details:.4f} seconds")
            product_context['creator_details'] = creator_details

            # serialize products

            if products:
                product_serialize_start = time.perf_counter()
                product_serializer = LeanProductPartialDetailsSerializer(products, many=True, context=product_context)
                serialized_products = product_serializer.data
                product_serialize_end = time.perf_counter()
                logger.info(f"Product serialize in {product_serialize_end - product_serialize_start:.4f} seconds")
            else:
                serialized_products = []

            if posts:
                post_serialize_start = time.perf_counter()
                post_context = {'neo4j_interactions': neo4j_interactions}
                post_context['creator_details'] = creator_details
                post_serializer = LeanPostPartialDetailsSerializer(posts, many=True, context=post_context)
                serialized_posts = post_serializer.data
                post_serialize_end = time.perf_counter()
                logger.info(f"Post serialize in {post_serialize_end - post_serialize_start:.4f} seconds")
            else:
                serialized_posts = []

            if comments:
                comment_serialize_start = time.perf_counter()
                comment_context = {'neo4j_interactions': neo4j_interactions}
                comment_context['creator_details'] = creator_details
                comment_serializer = LeanCommentsSerializer(comments, many=True, context=comment_context)
                serialized_comments = comment_serializer.data
                comment_serialize_end = time.perf_counter()
                logger.info(f"Comment serialize in {comment_serialize_end - comment_serialize_start:.4f} seconds")
            else:
                serialized_comments = []

            

            # Create dictionaries with reference as key and corresponding items as value
            # print(serialized_products, serialized_posts, serialized_comments)

            # Initialize the reference-to-data map
            start_create_dicts = time.perf_counter()
            reference_to_data_map = {
                **{product['product_reference']: product for product in serialized_products},
                **{post['post_reference']: post for post in serialized_posts},
                **{comment['comment_reference']: comment for comment in serialized_comments}
            }
            end_create_dicts = time.perf_counter()
            logger.info(f"Create dictionaries in {end_create_dicts - start_create_dicts:.4f} seconds")

            # Construct the ordered list using the sorted references
            start_construct_list = time.perf_counter()
            ordered_serialized_data = [reference_to_data_map.get(ref, None) for ref in feed_references_dict['references'] if reference_to_data_map.get(ref, None)]
            end_construct_list = time.perf_counter()
            logger.info(f"Construct list in {end_construct_list - start_construct_list:.4f} seconds")
            
            end_time = time.perf_counter()
            logger.info(f"Feed API processed in {end_time - start_time:.4f} seconds")

            return Response({
                "message": "success",
                "data": ordered_serialized_data
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Error in LeanFeedAPIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    

class LeanAllFeedAPIView(APIView):
    """
    A lean API view to fetch feed content with optimized performance.
    Handles different content types (Posts, Products, Comments) with appropriate serializers.
    """
    @swagger_auto_schema(
        operation_summary="Get feed (lean version)",
        operation_description="Fetch feed with optimized performance",
        manual_parameters=[
            openapi.Parameter(
                name='reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description='Reference of the user or store'
            ),
            openapi.Parameter(
                name='limit',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=10,
                description='Number of feed items to return'
            ),
            openapi.Parameter(
                name='offset',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=0,
                description='Number of feed items to skip'
            ),
            openapi.Parameter(
                name='user_pincode',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='User pincode for deliverability check'
            ),
            openapi.Parameter(
                name='visitor_reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Visitor reference for personalized content'
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request):
        """
        Get feed content with optimized performance.
        Handles different content types (Posts, Products, Comments) with appropriate serializers.
        """
        start_time = time.perf_counter()

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            user_pincode = request.query_params.get('user_pincode')
            visitor_reference = request.query_params.get('visitor_reference')
            
            # neo4j fetch
            start_db_query = time.perf_counter()

            feed_references_dict = dbqueries.get_batched_all_feed_details_new(entity_reference=visitor_reference, limit=limit, offset=offset)
            interaction_and_creator_details = feed_references_dict[0]
            post_references = feed_references_dict[1]
            product_references = feed_references_dict[2]
            comment_references = feed_references_dict[3]

            print(f"interaction_and_creator_detail_references: {[detail['reference'] for detail in interaction_and_creator_details]}")
            print(f"post_references: {post_references}")
            print(f"product_references: {product_references}")
            print(f"comment_references: {comment_references}")
                        

            neo4j_end = time.perf_counter()
            logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


            #sql fetch
            #sql data fetch
            sql_instance = sqlqueries()
            post_data = sql_instance.get_posts_full(post_refs=post_references)
            product_data = sql_instance.get_products_hybrid(product_refs=product_references, store_ref=None, limit=limit, offset=0 )

            post_data_json = json.loads(post_data)
            product_data_json =  json.loads(product_data)

            # print(f"post_data_json: {post_data_json}")
            # print(f"product_data_json: {product_data_json}")

            #buy statuc batch fetch
            products = Product.objects.filter(product_reference__in=product_references)
            checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
            buy_status_map = checker.get_buy_status_map()

            # # method 1
            
            # Build a single lookup for all references
            reference_lookup = {}

            for post in post_data_json:
                reference_lookup[post["post_reference"]] = post

            for product in product_data_json:
                reference_lookup[product["product_reference"]] = product

            # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
            # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

            # Build consolidated output in one pass
            consolidated_output = [
                {
                    **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                    **item["interaction"],
                    **item["creator"],
                    **buy_status_map.get(item["reference"], {})
                }
                for item in interaction_and_creator_details
            ]

            # print(f"consolidated_output: {consolidated_output}")

            total_time = time.perf_counter() - neo4j_end
            logger.info(f"Total time for SQL products: {total_time:.4f} seconds")

            return Response({"message":"success", "data":consolidated_output}, status=200)

            
        except Exception as e:
            logger.error(f"Error in LeanFeedAPIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    

class LeanAllFeedAPIView_old(APIView):
    """
    A lean API view to fetch feed content with optimized performance.
    Handles different content types (Posts, Products, Comments) with appropriate serializers.
    """
    @swagger_auto_schema(
        operation_summary="Get feed (lean version)",
        operation_description="Fetch feed with optimized performance",
        manual_parameters=[
            openapi.Parameter(
                name='reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description='Reference of the user or store'
            ),
            openapi.Parameter(
                name='limit',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=10,
                description='Number of feed items to return'
            ),
            openapi.Parameter(
                name='offset',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=0,
                description='Number of feed items to skip'
            ),
            openapi.Parameter(
                name='user_pincode',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='User pincode for deliverability check'
            ),
            openapi.Parameter(
                name='visitor_reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Visitor reference for personalized content'
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request):
        """
        Get feed content with optimized performance.
        Handles different content types (Posts, Products, Comments) with appropriate serializers.
        """
        start_time = time.perf_counter()

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            user_pincode = request.query_params.get('user_pincode')
            visitor_reference = request.query_params.get('visitor_reference')
            
            # core-logic
            start_db_query = time.perf_counter()
            feed_references_dict = dbqueries.get_content_references(limit=limit, offset=offset)
            end_db_query = time.perf_counter()
            logger.info(f"DB query in {end_db_query - start_db_query:.4f} seconds")

            start_django_fetch = time.perf_counter()
            posts = Posts.objects.filter(post_reference__in=feed_references_dict['post_references'], is_deleted=False).prefetch_related(Prefetch('post_images', queryset=PostImages.objects.filter(is_deleted=False).order_by('reorder'), to_attr='prefetched_images'))
            comments = Comments.objects.filter(comment_reference__in=feed_references_dict['comment_references'], is_deleted=False).prefetch_related(Prefetch('comment_images', queryset=CommentImages.objects.filter(is_deleted=False).order_by('reorder'), to_attr='prefetched_images'))
            products = Product.objects.filter(product_reference__in=feed_references_dict['product_references'], deleted=False).prefetch_related(
                Prefetch(
                    'prod_images',
                    queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
                    to_attr='prefetched_images'
                )
            )
            end_django_fetch = time.perf_counter()
            logger.info(f"Django fetch in {end_django_fetch - start_django_fetch:.4f} seconds")


            # prepare context for products, posts & comments
            
            # get context for products except neo4j_interactions
            # if products:
            #     store_references = products.values_list('store_reference', flat=True).distinct()
            #     product_context = prepare_context_data(
            #         store_references=store_references,
            #         products=products,
            #         visitor_reference=visitor_reference,
            #         user_pincode=user_pincode,
            #         get_neo4j_interactions=False
            #     ) 
            # else: 
            product_context = {}

            # get context for all references (neo4j_interactions)
            start_neo4j_fetch = time.perf_counter()
            neo4j_interactions = get_batched_interaction_details(visitor_reference, feed_references_dict['references'])
            product_context['neo4j_interactions'] = {k: neo4j_interactions[k] for k in feed_references_dict['product_references'] if k in neo4j_interactions}
            end_neo4j_fetch = time.perf_counter()
            logger.info(f"Neo4j fetch in {end_neo4j_fetch - start_neo4j_fetch:.4f} seconds")

            # get buy status for products
            start_buy_status = time.perf_counter()
            checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
            buy_status_map = checker.get_buy_status_map()

            product_context["buy_status_map"] = buy_status_map
            end_buy_status = time.perf_counter()
            logger.info(f"Buy status check in {end_buy_status - start_buy_status:.4f} seconds")
        

            # serialize products

            if products:
                product_serialize_start = time.perf_counter()
                product_serializer = LeanProductPartialDetailsSerializer(products, many=True, context=product_context)
                serialized_products = product_serializer.data
                product_serialize_end = time.perf_counter()
                logger.info(f"Product serialize in {product_serialize_end - product_serialize_start:.4f} seconds")
            else:
                serialized_products = []

            if posts:
                post_serialize_start = time.perf_counter()
                post_context = {'neo4j_interactions': neo4j_interactions}
                post_serializer = LeanPostPartialDetailsSerializer(posts, many=True, context=post_context)
                serialized_posts = post_serializer.data
                post_serialize_end = time.perf_counter()
                logger.info(f"Post serialize in {post_serialize_end - post_serialize_start:.4f} seconds")
            else:
                serialized_posts = []

            if comments:
                comment_serialize_start = time.perf_counter()
                comment_context = {'neo4j_interactions': neo4j_interactions}
                comment_serializer = LeanCommentsSerializer(comments, many=True, context=comment_context)
                serialized_comments = comment_serializer.data
                comment_serialize_end = time.perf_counter()
                logger.info(f"Comment serialize in {comment_serialize_end - comment_serialize_start:.4f} seconds")
            else:
                serialized_comments = []

            

            # Create dictionaries with reference as key and corresponding items as value
            # print(serialized_products, serialized_posts, serialized_comments)

            # Initialize the reference-to-data map
            start_create_dicts = time.perf_counter()
            reference_to_data_map = {
                **{product['product_reference']: product for product in serialized_products},
                **{post['post_reference']: post for post in serialized_posts},
                **{comment['comment_reference']: comment for comment in serialized_comments}
            }
            end_create_dicts = time.perf_counter()
            logger.info(f"Create dictionaries in {end_create_dicts - start_create_dicts:.4f} seconds")

            # Construct the ordered list using the sorted references
            start_construct_list = time.perf_counter()
            ordered_serialized_data = [reference_to_data_map.get(ref, None) for ref in feed_references_dict['references'] if reference_to_data_map.get(ref, None)]
            end_construct_list = time.perf_counter()
            logger.info(f"Construct list in {end_construct_list - start_construct_list:.4f} seconds")
            
            end_time = time.perf_counter()
            logger.info(f"Feed API processed in {end_time - start_time:.4f} seconds")

            return Response({
                "message": "success",
                "data": ordered_serialized_data
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Error in LeanAllFeedAPIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    

class StoreProductsFullNew(APIView):
    """
    A lean API view to fetch feed content with optimized performance.
    Handles different content types (Posts, Products, Comments) with appropriate serializers.
    """
    @swagger_auto_schema(
        operation_summary="Get feed (lean version)",
        operation_description="Fetch feed with optimized performance",
        manual_parameters=[
            openapi.Parameter(
                name='reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=True,
                description='Reference of the user or store'
            ),
            openapi.Parameter(
                name='limit',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=10,
                description='Number of feed items to return'
            ),
            openapi.Parameter(
                name='offset',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                default=0,
                description='Number of feed items to skip'
            ),
            openapi.Parameter(
                name='user_pincode',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='User pincode for deliverability check'
            ),
            openapi.Parameter(
                name='visitor_reference',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description='Visitor reference for personalized content'
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    }
                )
            ),
            400: "Bad request",
            500: "Server error"
        }
    )
    def get(self, request, store_reference):
        """
        Get feed content with optimized performance.
        Handles different content types (Posts, Products, Comments) with appropriate serializers.
        """
        start_time = time.perf_counter()

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            user_pincode = request.query_params.get('user_pincode')
            visitor_reference = request.query_params.get('visitor_reference')
            
            # neo4j fetch
            start_db_query = time.perf_counter()

            store_product_references_dict = dbqueries.get_store_product_interaction_details(visitor_reference=visitor_reference, store_reference= store_reference, limit=limit, offset=offset)
            interaction_and_creator_details = store_product_references_dict[0]
            product_references = store_product_references_dict[1]

            neo4j_end = time.perf_counter()
            logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


            #sql fetch
            #sql data fetch
            sql_instance = sqlqueries()
            product_data = sql_instance.get_products_hybrid(product_refs=None, store_ref=store_reference, limit=limit, offset=offset )

            product_data_json =  json.loads(product_data)

            #buy statuc batch fetch
            products = Product.objects.filter(product_reference__in=product_references)
            checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
            buy_status_map = checker.get_buy_status_map()

            # # method 1
            
            # Build a single lookup for all references
            reference_lookup = {}

            for product in product_data_json:
                reference_lookup[product["product_reference"]] = product


            # Build consolidated output in one pass
            consolidated_output = [
                {
                    **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                    **item["interaction"],
                    **item["creator"],
                    **buy_status_map.get(item["reference"], {})
                }
                for item in interaction_and_creator_details
            ]

            total_time = time.perf_counter() - neo4j_end
            logger.info(f"Total time for SQL products: {total_time:.4f} seconds")

            return Response({"message":"success", "data":consolidated_output}, status=200)

            
        except Exception as e:
            logger.error(f"Error in LeanFeedAPIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    

class GetUserOrStorePosts(APIView):
    
    def get(self, request):

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            visitor_reference = request.query_params.get('visitor_reference')
            entity_reference =  request.query_params.get('entity_reference')
            
            # neo4j fetch
            start_db_query = time.perf_counter()

            post_references_dict = dbqueries.get_user_or_store_post_interaction_details(visitor_reference=visitor_reference, entity_reference=entity_reference, limit=limit, offset=offset)
            interaction_and_creator_details = post_references_dict[0]
            post_references = post_references_dict[1]

            print(f"interaction_and_creator_detail_references: {[detail['reference'] for detail in interaction_and_creator_details]}")
            print(f"post_references: {post_references}")

            neo4j_end = time.perf_counter()
            logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


            #sql fetch
            #sql data fetch
            sql_instance = sqlqueries()
            post_data = sql_instance.get_posts_full(post_refs=post_references)
            post_data_json = json.loads(post_data)



            # # method 1
            
            # Build a single lookup for all references
            reference_lookup = {}

            for post in post_data_json:
                reference_lookup[post["post_reference"]] = post


            # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
            # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

            # Build consolidated output in one pass
            consolidated_output = [
                {
                    **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                    **item["interaction"],
                    **item["creator"],
                }
                for item in interaction_and_creator_details
            ]

            # print(f"consolidated_output: {consolidated_output}")

            total_time = time.perf_counter() - neo4j_end
            logger.info(f"Total time for SQL products: {total_time:.4f} seconds")

            return Response({"message":"success", "data":consolidated_output}, status=200)

            
        except Exception as e:
            logger.error(f"Error in LeanFeedAPIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetRecommendedProducts(APIView):

    def get(self, request):
        """
        Get recommended Products with optimized performance.
        """
        start_time = time.perf_counter()

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            user_pincode = request.query_params.get('user_pincode')
            visitor_reference = request.query_params.get('visitor_reference')
            
            # neo4j fetch
            start_db_query = time.perf_counter()

            product_qs = Product.objects.filter(
                store_reference__is_active=True,
                store_reference__is_test_store=False,
                store_reference__deleted=False,
                deleted=False).order_by('-modified_date')[offset:offset+limit]
            
            product_references = list(product_qs.values_list('product_reference', flat=True))

            product_references_dict = dbqueries.get_content_interaction_details(visitor_reference=visitor_reference, content_references=product_references)
            interaction_and_creator_details = product_references_dict[0]
            product_references = product_references_dict[1]

            neo4j_end = time.perf_counter()
            logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


            #sql fetch
            #sql data fetch
            sql_instance = sqlqueries()
            product_data = sql_instance.get_products_hybrid_without_pagination(product_refs=product_references, store_ref=None)

            product_data_json =  json.loads(product_data)

            #buy statuc batch fetch
            checker = BuyStatusCheckerBatch(products=product_qs, user_pincode=user_pincode)
            buy_status_map = checker.get_buy_status_map()

            # # method 1
            
            # Build a single lookup for all references
            reference_lookup = {}

            for product in product_data_json:
                reference_lookup[product["product_reference"]] = product


            # Build consolidated output in one pass
            consolidated_output = [
                {
                    **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                    **item["interaction"],
                    **item["creator"],
                    **buy_status_map.get(item["reference"], {})
                }
                for item in interaction_and_creator_details
            ]

            total_time = time.perf_counter() - start_time
            logger.info(f"Total time for SQL products: {total_time:.4f} seconds")

            return Response({"message":"success", "data":consolidated_output}, status=200)

            
        except Exception as e:
            logger.error(f"Error in recommended Products APIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



class GetStoreComments(APIView):
    
    def get(self, request):

        try:
            # Get query parameters
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            visitor_reference = request.query_params.get('visitor_reference')
            entity_reference =  request.query_params.get('entity_reference')
            comment_types_list_string = request.query_params.get('comment_types')
            comment_types = comment_types_list_string.split(',')
            review_type = request.query_params.get('review_type',"PRODUCT")

            for comment_type in comment_types:
                if comment_type not in Comments.CommentTypes.values:
                    return Response({"message":"Invalid comment type"}, status=status.HTTP_400_BAD_REQUEST)
            
            if review_type not in ["PRODUCT", "STORE", ""]:
                return Response({"message":"Invalid review type"}, status=status.HTTP_400_BAD_REQUEST)
            
            # neo4j fetch
            start_db_query = time.perf_counter()

            #sql fetch
            #sql data fetch to fetch all the comment data
            sql_instance = sqlqueries()
            comment_data, comment_references = sql_instance.get_comments_full(store_reference=entity_reference, limit=limit, offset=offset, comment_types=comment_types, review_type=review_type)
            comment_data_json = json.loads(comment_data)


            post_references_dict = dbqueries.get_comment_interaction_details(visitor_reference=visitor_reference, comment_references=comment_references)
            interaction_and_creator_details = post_references_dict[0]

            print(f"interaction_and_creator_detail_references: {[detail['reference'] for detail in interaction_and_creator_details]}")

            neo4j_end = time.perf_counter()
            logger.info(f"Total time for Neo4j products: {neo4j_end-start_db_query:.4f} seconds")


            # # method 1
            
            # Build a single lookup for all references
            reference_lookup = {}

            for comment in comment_data_json:
                reference_lookup[comment["comment_reference"]] = comment


            # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
            # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

            # Build consolidated output in one pass
            consolidated_output = [
                {
                    **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                    **item["interaction"],
                    **item["creator"]
                }
                for item in interaction_and_creator_details
            ]

            # print(f"consolidated_output: {consolidated_output}")

            total_time = time.perf_counter() - neo4j_end
            logger.info(f"Total time for SQL products: {total_time:.4f} seconds")

            return Response({"message":"success", "data":consolidated_output}, status=200)

            
        except Exception as e:
            logger.error(f"Error in LeanFeedAPIView: {str(e)}")
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# class LeanFeedAPIView_old(APIView):
#     """
#     A lean API view to fetch feed content with optimized performance.
#     Handles different content types (Posts, Products, Comments) with appropriate serializers.
#     """
#     @swagger_auto_schema(
#         operation_summary="Get feed (lean version)",
#         operation_description="Fetch feed with optimized performance",
#         manual_parameters=[
#             openapi.Parameter(
#                 name='reference',
#                 in_=openapi.IN_QUERY,
#                 type=openapi.TYPE_STRING,
#                 required=True,
#                 description='Reference of the user or store'
#             ),
#             openapi.Parameter(
#                 name='limit',
#                 in_=openapi.IN_QUERY,
#                 type=openapi.TYPE_INTEGER,
#                 default=10,
#                 description='Number of feed items to return'
#             ),
#             openapi.Parameter(
#                 name='offset',
#                 in_=openapi.IN_QUERY,
#                 type=openapi.TYPE_INTEGER,
#                 default=0,
#                 description='Number of feed items to skip'
#             ),
#             openapi.Parameter(
#                 name='user_pincode',
#                 in_=openapi.IN_QUERY,
#                 type=openapi.TYPE_STRING,
#                 required=False,
#                 description='User pincode for deliverability check'
#             ),
#             openapi.Parameter(
#                 name='visitor_reference',
#                 in_=openapi.IN_QUERY,
#                 type=openapi.TYPE_STRING,
#                 required=False,
#                 description='Visitor reference for personalized content'
#             )
#         ],
#         responses={
#             200: openapi.Response(
#                 description="Success",
#                 schema=openapi.Schema(
#                     type=openapi.TYPE_OBJECT,
#                     properties={
#                         'message': openapi.Schema(type=openapi.TYPE_STRING),
#                         'data': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
#                     }
#                 )
#             ),
#             400: "Bad request",
#             500: "Server error"
#         }
#     )
#     def get(self, request):
#         """
#         Get feed content with optimized performance.
#         Handles different content types (Posts, Products, Comments) with appropriate serializers.
#         """
#         start_time = time.perf_counter()

#         try:
#             # Get query parameters
#             get_query_start = time.perf_counter()
#             limit = int(request.query_params.get('limit', 10))
#             offset = int(request.query_params.get('offset', 0))
#             user_pincode = request.query_params.get('user_pincode')
#             visitor_reference = request.query_params.get('visitor_reference')
#             get_query_end = time.perf_counter()
#             logger.info(f"Get query params in {get_query_end - get_query_start:.4f} seconds")

#             if not visitor_reference:
#                 return Response(
#                     {"message": "visitor_reference parameter is required"},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )

#             # Get feed content references from Neo4j
#             get_feed_refs_start = time.perf_counter()
#             feed_content_references = dbqueries.get_sorted_feed_content_references(
#                 entity_reference=visitor_reference,
#                 limit=limit,
#                 offset=offset
#             )
#             get_feed_refs_end = time.perf_counter()
#             logger.info(f"Get feed content references in {get_feed_refs_end - get_feed_refs_start:.4f} seconds")

#             if not feed_content_references:
#                 return Response({
#                     "message": "success",
#                     "data": []
#                 }, status=status.HTTP_200_OK)

#             # Fetch content from different models
#             fetch_posts_start = time.perf_counter()
#             required_posts = Posts.objects.filter(
#                 post_reference__in=feed_content_references,
#                 is_deleted=False
#             ).prefetch_related('post_images')
#             fetch_posts_end = time.perf_counter()
#             logger.info(f"Fetch posts in {fetch_posts_end - fetch_posts_start:.4f} seconds")

#             fetch_comments_start = time.perf_counter()
#             required_comments = Comments.objects.filter(
#                 comment_reference__in=feed_content_references,
#                 is_deleted=False
#             ).prefetch_related('comment_images')
#             fetch_comments_end = time.perf_counter()
#             logger.info(f"Fetch comments in {fetch_comments_end - fetch_comments_start:.4f} seconds")

#             fetch_products_start = time.perf_counter()
#             required_products = Product.objects.filter(
#                 product_reference__in=feed_content_references,
#                 deleted=False
#             ).select_related('store_reference').prefetch_related(
#                 Prefetch(
#                     'prod_images',
#                     queryset=ProductImages.objects.filter(is_deleted=False).order_by('reorder'),
#                     to_attr='prefetched_images'
#                 )
#             )
#             fetch_products_end = time.perf_counter()
#             logger.info(f"Fetch products in {fetch_products_end - fetch_products_start:.4f} seconds")

#             # # Create dictionaries with reference as key and corresponding items as value
#             # create_dicts_start = time.perf_counter()
#             # posts_dict = {post.post_reference: post for post in required_posts}
#             # comments_dict = {comment.comment_reference: comment for comment in required_comments}
#             # products_dict = {product.product_reference: product for product in required_products}
#             # create_dicts_end = time.perf_counter()
#             # logger.info(f"Create dictionaries in {create_dicts_end - create_dicts_start:.4f} seconds")

#             # Get all store references from products for context preparation
#             get_store_refs_start = time.perf_counter()
#             store_references = []
#             store_references = list(set(required_products.values_list('store_reference__store_reference', flat=True)))
#             get_store_refs_end = time.perf_counter()
#             logger.info(f"Get store references in {get_store_refs_end - get_store_refs_start:.4f} seconds")

#             # Prepare context data for products using utility function
#             prepare_context_start = time.perf_counter()
#             product_context = {}
#             if required_products:
#                 product_context = prepare_context_data(
#                     store_references=store_references,
#                     products=required_products,
#                     visitor_reference=visitor_reference,
#                     user_pincode=user_pincode
#                 )
#             prepare_context_end = time.perf_counter()
#             logger.info(f"Prepare context in {prepare_context_end - prepare_context_start:.4f} seconds")

#             # Prepare Neo4j interaction data for posts and comments
#             get_neo4j_interactions_start = time.perf_counter()
#             neo4j_interactions = {}
#             if visitor_reference and (required_posts or required_comments):
#                 post_refs = [post.post_reference for post in required_posts]
#                 comment_refs = [comment.comment_reference for comment in required_comments]
#                 content_refs = post_refs + comment_refs

#                 if content_refs:
#                     try:
#                         neo4j_interactions = get_batched_interaction_details(visitor_reference, content_refs)
#                     except Exception as e:
#                         logger.error(f"Error fetching Neo4j interactions: {str(e)}")
#             get_neo4j_interactions_end = time.perf_counter()
#             logger.info(f"Get Neo4j interactions in {get_neo4j_interactions_end - get_neo4j_interactions_start:.4f} seconds")

#             # Prepare serialized data for each content type
#             serialized_data = []

#             # Process each reference in the original order
#             serialize_content_start = time.perf_counter()
#             for ref in feed_content_references:
#                 if ref in posts_dict:
#                     # Serialize post
#                     post = posts_dict[ref]
#                     post_context = {'neo4j_interactions': neo4j_interactions}
#                     post_serializer = LeanPostPartialDetailsSerializer(post, context=post_context)
#                     item_data = post_serializer.data
#                     item_data['content_type'] = 'POST'
#                     serialized_data.append(item_data)

#                 elif ref in products_dict:
#                     # Serialize product
#                     product = products_dict[ref]
#                     product_serializer = LeanStoreProductFullDetailsSerializer(product, context=product_context)
#                     item_data = product_serializer.data
#                     item_data['content_type'] = 'PRODUCT'
#                     serialized_data.append(item_data)

#                 elif ref in comments_dict:
#                     # Serialize comment
#                     comment = comments_dict[ref]
#                     comment_context = {'neo4j_interactions': neo4j_interactions}
#                     comment_serializer = LeanCommentsSerializer(comment, context=comment_context)
#                     item_data = comment_serializer.data
#                     item_data['content_type'] = 'COMMENT'
#                     serialized_data.append(item_data)
#             serialize_content_end = time.perf_counter()
#             logger.info(f"Serialize content in {serialize_content_end - serialize_content_start:.4f} seconds")

#             end_time = time.perf_counter()
#             logger.info(f"Feed API processed in {end_time - start_time:.4f} seconds")

#             return Response({
#                 "message": "success",
#                 "data": serialized_data
#             }, status=status.HTTP_200_OK)

#         except Exception as e:
#             logger.error(f"Error in LeanFeedAPIView: {str(e)}")
#             return Response(
#                 {"message": f"An error occurred: {str(e)}"},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

