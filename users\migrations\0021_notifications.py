# Generated by Django 3.2.13 on 2022-12-13 05:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0020_inviteuser_invitee_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Notifications",
            fields=[
                (
                    "notification_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "notification_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "notification_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("OB", "on boarding"),
                            ("PV", "Product version changed"),
                            ("SC", "New store created near you"),
                            ("0C", "Order confirmed"),
                            ("OS", "Order shipped"),
                        ],
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "notification_message",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "notification_status",
                    models.CharField(
                        blank=True,
                        choices=[("SEEN", "Seen"), ("NOT_SEEN", "Not seen")],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "notification_about",
                    models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=20, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "user_reference",
                    models.ForeignKey(
                        db_column="user_id",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "notifications",
                "db_table": '"user"."notifications"',
            },
        ),
    ]
