# Generated by Django 3.2.13 on 2023-06-13 04:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0098_auto_20230608_1256'),
    ]

    operations = [
        migrations.AddField(
            model_name='orderpayout',
            name='commission_calculated',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='orderpayout',
            name='payout_amount',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='suborder',
            name='delivery_settings_type',
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='suborder',
            name='refund_warranty_settings_type',
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AlterField(
            model_name='payouttransactions',
            name='transaction_type',
            field=models.CharField(blank=True, choices=[('CREDITED', 'Credited'), ('DEBITED', 'Debited'), ('WITHDRAWAL', 'Withdrawal')], max_length=10, null=True),
        ),
    ]
