# Generated by Django 4.2.7 on 2024-03-12 07:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0051_alter_productversion_product_reference"),
    ]

    operations = [
        migrations.AddField(
            model_name="product",
            name="cancels_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="comment_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="like_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="orders_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="repost_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="repost_plus_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="returns_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="save_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="share_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="product",
            name="tagged_posts",
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
