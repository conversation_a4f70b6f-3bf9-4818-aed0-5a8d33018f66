# Generated by Django 3.2.13 on 2022-09-22 03:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0014_remove_inviteactivity_invite_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="InviteConfiguration",
            fields=[
                (
                    "configuration_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("invite_type", models.CharField(max_length=100)),
                ("max_invite_limit", models.PositiveIntegerField()),
            ],
            options={
                "verbose_name_plural": "invite configurations",
                "db_table": '"user"."invite_configuration"',
            },
        ),
        migrations.AddField(
            model_name="user",
            name="member_invite_balance",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="seller_invite_balance",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
    ]
