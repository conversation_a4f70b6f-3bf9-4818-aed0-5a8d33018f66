"""swadesic URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include, re_path
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from django.conf.urls.static import static
from django.conf import settings

from content.schema import schema
from GraphDB.schema import schema as gdbschema
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.views.decorators.csrf import csrf_exempt
from graphene_django.views import GraphQLView
import datetime


class CustomTokenRefreshView(TokenRefreshView):
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        refresh_token = response.data.get('refresh')
        access_token = response.data.get('access')

        if refresh_token and access_token:
            refresh_lifetime, access_lifetime = self.get_token_lifetime(refresh_token)

            response.data['refresh_lifetime'] = datetime.datetime.now() + refresh_lifetime
            response.data['access_lifetime'] = datetime.datetime.now() + access_lifetime

        return response

    def get_token_lifetime(self, token):
        try:
            token_obj = RefreshToken(token)
            return token_obj.lifetime, token_obj.access_token.lifetime
        except Exception as e:
            return None



schema_view = get_schema_view(
   openapi.Info(
      title="Snippets API",
      default_version='v1',
      description="Test description",
      terms_of_service="https://www.google.com/policies/terms/",
      contact=openapi.Contact(email="<EMAIL>"),
      license=openapi.License(name="BSD License"),
   ),
   public=True,
   permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    re_path(r'^redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('admin/', admin.site.urls),

    path('seo/', include('seo.urls')),

    path('product/', include('products.api.urls')),
    path('product/', include('products.comment_and_reply_api.urls')),

    path('store/', include('stores.store_api.urls')),
    path('store/', include('stores.search_api.urls')),
    path('store/', include('stores.store_settings_api.urls')),
    path('store/', include('stores.feedback_api.urls')),
    path('store/', include('stores.store_reward_api.urls')),

    path('user/', include('users.user_api.urls')),
    path('user/', include('users.invite_api.urls')),
    path('user/', include('users.notification_api.urls')),
    path('user/email/', include('users.email_auth.urls')),

    path('order/', include('orders.order_api.urls')),
    path('order/', include('orders.cart_api.urls')),
    path('order/', include('orders.payout_api.urls')),
    path('order/', include('orders.payment_api.urls')),
    path('order/', include('orders.escalation_api.urls')),
    path('order/', include('orders.swadesic_shipping.urls')),

    path('common/', include('common.util.urls')),

    path('general/', include('general.general_api.urls')),

    path('content/', include('content.urls')),
    path('content/', include('content.feed_api.urls')),
    path('graphdb/', include('GraphDB.urls')),
    path('graphql/', csrf_exempt(GraphQLView.as_view(graphiql=True, schema=schema))),
    path('graphDBql/', csrf_exempt(GraphQLView.as_view(graphiql=True, schema=gdbschema))),

    path('api/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('swadesic_admin/', include('swadesic_admin.urls')),
    path('subscriptions/', include('subscriptions.urls')),
    path('messaging/', include('messaging.urls')),
    path('app_common/', include('app_common.urls')),
    path('lean/', include('lean_apis.urls')),
    path('faq/', include('faq.urls')),
]
urlpatterns = urlpatterns + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.DEBUG:
    urlpatterns += path("__debug__/", include("debug_toolbar.urls")),