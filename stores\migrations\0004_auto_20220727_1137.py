# Generated by Django 3.2.13 on 2022-07-27 06:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0003_deliverylocations_deliverysettings_documents_trustcenter"),
    ]

    operations = [
        migrations.CreateModel(
            name="RefundAndWarranty",
            fields=[
                (
                    "refundandwarrantyid",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("productid", models.IntegerField(blank=True, null=True)),
                ("has_warranty", models.BooleanField(default=False)),
                ("warranty_period_no", models.IntegerField(blank=True, null=True)),
                (
                    "warranty_period",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("warranty_card_available", models.BooleanField(default=False)),
                ("return_type", models.BooleanField(default=False)),
                ("replacement_type", models.<PERSON><PERSON>anField(default=False)),
                ("return_period", models.Integer<PERSON>ield(blank=True, null=True)),
                ("return_conditions", models.Text<PERSON>ield(blank=True, null=True)),
                (
                    "return_pickup",
                    models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=400, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "refund and warranty",
                "db_table": '"store"."refund_and_warranty"',
            },
        ),
        migrations.RemoveField(
            model_name="userstore",
            name="storeid",
        ),
        migrations.RemoveField(
            model_name="userstore",
            name="userid",
        ),
    ]
