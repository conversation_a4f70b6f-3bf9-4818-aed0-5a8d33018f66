from rest_framework import serializers
from .models import InviteUser, RewardsHistory
from ..user_api.models import User
from stores.store_api.models import Store

class GetInviteSerializer(serializers.ModelSerializer):
    class Meta:
        model = InviteUser
        fields = "__all__"


class InviteSerializer(serializers.ModelSerializer):
    class Meta:
        model = InviteUser
        fields = [
            "invite_code",
            "invite_type",
            "invited_user_role",
            "invited_user",
            "phone_number",
            "invitee_name",
            "created_by",
        ]


class GroupInviteSerializer(serializers.ModelSerializer):
    class Meta:
        model = InviteUser
        fields = [
            "invite_code",
            "invite_type",
            "invited_user_role",
            "invited_user",
            "number_of_invites",
            "invite_expiry_date",
            "remaining_invites",
            "event_name",
            "event_note",
            "created_by",
        ]

class GetAllInvitesCreatedByOneUserSerializer(serializers.ModelSerializer):
    member = serializers.SerializerMethodField()
    seller = serializers.SerializerMethodField()

    def _invite_type_list(self, invite_type):
        userid = self.context.get("user_id")
        invite_instance = InviteUser.objects.filter(
            invite_type=invite_type, invited_user=userid, is_deleted=False
        )
        serializer = GetAllInvitesSerializer(invite_instance, many=True)
        return serializer.data

    def get_member(self, obj):
        return self._invite_type_list(InviteUser.Invite_Type_Choices.MEMBER)

    def get_seller(self, obj):
        return self._invite_type_list(InviteUser.Invite_Type_Choices.SELLER)

    class Meta:
        model = InviteUser
        fields = ["member", "seller"]


class GetAllInvitesSerializer(serializers.ModelSerializer):

    name = serializers.CharField(source="invitee_name")
    invite_type_by_this_user = serializers.CharField(source="invite_type")
    invite_code_by_this_user = serializers.CharField(source="invite_code")

    class Meta:
        model = InviteUser
        fields = [
            "phone_number",
            "name",
            "invite_type_by_this_user",
            "invite_code_by_this_user",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        phone_number = instance.phone_number

        if User.objects.filter(phonenumber=phone_number).exists():
            user_instance = User.objects.get(phonenumber=phone_number)
            representation["user_reference"] = user_instance.user_reference
            representation["user_id"] = user_instance.userid
            representation["membership_type"] = user_instance.invite_type
            representation["is_onboarded"] = True
            if user_instance.invite_code == instance.invite_code:
                representation["onboarded_by_this_user"] = True
            else:
                representation["onboarded_by_this_user"] = False
        else:
            representation["user_reference"] = None
            representation["user_id"] = None
            representation["membership_type"] = None
            representation["is_onboarded"] = False
            representation["onboarded_by_this_user"] = False

        return representation


class ListOfUserContactsSerializer(serializers.ModelSerializer):
    membership_type = serializers.CharField(source="invite_type")

    class Meta:
        model = User
        fields = [
            "phonenumber",
            "user_reference",
            "userid",
            "user_name",
            "membership_type",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        userid = self.context.get("user_id")
        phone_number = instance.phonenumber

        representation["is_onboarded"] = True
        if InviteUser.objects.filter(
            phone_number=phone_number, invited_user=userid, is_deleted=False
        ).exists():
            invite_user_instance = (
                InviteUser.objects.filter(
                    phone_number=phone_number, invited_user=userid
                )
                .order_by("-created_date")
                .first()
            )
            representation[
                "invite_type_by_this_user"
            ] = invite_user_instance.invite_type
            representation[
                "invite_code_by_this_user"
            ] = invite_user_instance.invite_code
        else:
            representation["invite_type_by_this_user"] = None
            representation["invite_code_by_this_user"] = None
        if InviteUser.objects.filter(
            phone_number=phone_number, invite_code=instance.invite_code
        ).exists():
            invited_user = InviteUser.objects.values_list(
                "invited_user", flat=True
            ).get(phone_number=phone_number, invite_code=instance.invite_code)
            if userid == invited_user:
                representation["onboarded_by_this_user"] = True
            else:
                representation["onboarded_by_this_user"] = False
        return representation


class RewardsHistorySerializer(serializers.ModelSerializer):
    transaction_title = serializers.SerializerMethodField("get_transaction_title")
    reward_category = serializers.SerializerMethodField("get_reward_category")
    reward_value = serializers.CharField(source="sent_value")
    store_reference = serializers.CharField(source="sender_reference")
    class Meta:
        model = RewardsHistory
        fields = [
            "reward_id",
            "transaction_title",
            "reward_reference",
            "reward_category",
            "reward_value", 
            "reward_type",
            "transaction_type",
            "store_reference",
            "event_reference",
            "reward_status",
            "created_date",
            "bank_transaction_id",
            "bank_reference_number",
            "bank_transaction_date",
            "bank_transaction_status",
            "bank_account_number",
            "bank_account_name",
            "bank_ifsc_code",
        ]

    def get_reward_category(self, obj):
        # Map reward types to categories
        return obj.reward_type

    def get_transaction_title(self, obj):
        try:
            if obj.reward_type == 'ONBOARDING_REFERRAL':
                referee = User.objects.get(user_reference=obj.event_reference, deleted=False) \
                    if obj.event_reference.startswith('U') \
                    else Store.objects.get(store_reference=obj.event_reference, deleted=False)
                referee_handle = referee.user_name if obj.event_reference.startswith('U') else referee.storehandle
                return f"Reward for using @{referee_handle}'s referral code"
                
            elif obj.reward_type == 'INVITE_CODE_REWARD':
                referred_user = User.objects.get(user_reference=obj.event_reference, deleted=False) \
                    if obj.event_reference.startswith('U') \
                    else Store.objects.get(store_reference=obj.event_reference, deleted=False)
                referred_handle = referred_user.user_name if obj.event_reference.startswith('U') else referred_user.storehandle
                return f"@{referred_handle}'s Onboarding"
                
            elif obj.reward_type in ['U2U_TRANSFER', 'S2U_TRANSFER']:
                entity_instance = User.objects.get(user_reference=obj.sender_reference, deleted=False) \
                    if obj.sender_reference.startswith('U') \
                    else Store.objects.get(store_reference=obj.sender_reference, deleted=False)
                entity_handle = entity_instance.user_name if isinstance(entity_instance, User) else entity_instance.storehandle
                
                if obj.transaction_type == 'DEBIT':
                    receiver = User.objects.get(user_reference=obj.receiver_reference, deleted=False)
                    return f"Transfer to @{receiver.user_name}"
                else:
                    return f"Transfer from @{entity_handle}"
                    
            elif obj.reward_type == 'STORE_VERIFICATION':
                return "Store Verification Reward" if obj.receiver_reference.startswith('S') \
                    else "Reward for inviting a Store"

            elif obj.reward_type == 'RUPEE':
                return "Affiliate Reward"
            return None
            
        except (User.DoesNotExist, Store.DoesNotExist):
            return None