# Generated by Django 3.2.13 on 2022-10-11 03:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0017_alter_inviteuser_invite_type"),
        ("orders", "0022_suborder_return_confirmation_date"),
    ]

    operations = [
        migrations.AddField(
            model_name="order",
            name="billing_address_id",
            field=models.ForeignKey(
                blank=True,
                db_column="billing_address_id",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_addresses",
                to="users.useraddress",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="order_phone_number",
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
    ]
