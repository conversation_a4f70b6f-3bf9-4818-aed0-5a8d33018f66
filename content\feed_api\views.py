from rest_framework import generics, status
from rest_framework.response import Response
from .models import FeedPost
from .serializers import FeedPostSerializer
from ..serializers import PostsSerializer
from ..models import Posts


class GetFeedPostsAV(generics.RetrieveAPIView):
    def get(self, request, *args, **kwargs):
        reference = self.request.query_params.get("reference")
        feed_post_entries = FeedPost.objects.filter(reference=reference, is_seen=False).values_list("post_reference")
        posts = Posts.objects.filter(post_reference__in=feed_post_entries, is_deleted=False)

        serializer = PostsSerializer(posts, many=True, context={'visitor_reference': reference})

        return Response(
            {
                "message": "success",
                "data": serializer.data
            }, status=status.HTTP_200_OK
        )

#TODO Create a new api for basic similar to feed post but remove the following condition
class FeedPostMarkAsSeenAPIView(generics.UpdateAPIView):
    queryset = FeedPost.objects.all()
    serializer_class = FeedPostSerializer

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.is_seen = True
        instance.save()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

#
# class FeedPostListCreateAPIView(generics.ListCreateAPIView):
#     serializer_class = FeedPostSerializer
#
#     def create(self, request, *args, **kwargs):
#         serializer = self.get_serializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         serializer.save()
#         return Response(serializer.data, status=status.HTTP_201_CREATED)
#
#
# class FeedPostRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
#     queryset = FeedPost.objects.all()
#     serializer_class = FeedPostSerializer
#
#     def update(self, request, *args, **kwargs):
#         partial = kwargs.pop('partial', False)
#         instance = self.get_object()
#         serializer = self.get_serializer(instance, data=request.data, partial=partial)
#         serializer.is_valid(raise_exception=True)
#         serializer.save()
#         return Response(serializer.data)
#
#





