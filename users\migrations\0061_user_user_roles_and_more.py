# Generated by Django 4.2.7 on 2024-05-22 11:16

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0060_alter_user_user_name_alter_user_website_link"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="user_roles",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("BUYER", "Buyer"),
                        ("SELLER", "Seller"),
                        ("CONTENT_CREATOR", "Content_Creator"),
                        ("INVESTOR", "Investor"),
                        ("VOLUNTEER", "Volunteer"),
                        ("COLLABORATOR", "Collaborator"),
                    ],
                    max_length=100,
                ),
                default=list,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="notifications",
            name="notification_type",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("ONBOARDING", "onboarding"),
                    ("PRODUCT_VERSION_CHANGED", "Product version changed"),
                    ("STORE_CREATED", "New store created near you"),
                    ("NEW_ORDER", "New order"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("ORDER_SHIPPED", "Order shipped"),
                    ("SHIPPING_PACAKGE_UPDATED", "Shipping package updated"),
                    ("DELIVERY_OTP", "Delivery otp"),
                    ("PACKAGE_DELIVERED", "Package delivered"),
                    ("DELIVERY_FAILED", "Delivery failed"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("SELLER_CANCELLED_ORDER", "Seller cancelled order"),
                    ("BUYER_CANCELLED_ORDER", "Buyer cancelled order"),
                    ("AUTO_CANCELLED", "Auto cancelled"),
                    ("RETURN_REQUESTED", "Return requested"),
                    ("RETURN_ACCEPTED", "Return accepted"),
                    ("RETURN_RECEIVED", "Return received"),
                    ("REFUND_INITIATED", "Refund initiated"),
                    ("REFUND_HOLD", "Refund hold"),
                    ("REFUND_RELEASED", "Refund released"),
                    ("RETURN_OTP", "Return otp"),
                    ("COMMENTED", "Commented"),
                    ("CONTENT_LIKED", "content_liked"),
                    ("SOMEONE_FOLLOWED_ENTIY", "Someone_followed_entity"),
                    ("SOMEONE_COMMENTED_ON_CONTENT", "Someone_commented_on_content"),
                    ("SOMEONE_REPOSTED_CONTENT", "Someone reposted content"),
                ],
                max_length=100,
                null=True,
            ),
        ),
    ]
