from rest_framework import generics, status, mixins
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging
from users.notification_api.models import Notifications
from common.util.notification_handler import NotificationHandler
import time

from .models import (
    TrustCenter,
    Address,
    Documents,
    DeliverySettings,
    DeliveryLocations,
    RefundAndWarranty,
)
from .serializers import (
    TrustCenterSerializer,
    TrustCenterContactSerializer,
    TrustCenterLocationSerializer,
    DocumentsSerializer,
    AddDeliverySettingsSerializer,
    DeliverySettingsSerializer,
    DeliveryLocationsSerializer,
    RefundAndWarrantySerializer,
    AddressSerializer, TrustCenterSwadeshiLabelSerializer, StoreConfigSerializer

    # DeliveryLocationsSerializerTest
)

from stores.store_api.models import Store, StoreConfig
from products.models import Product
from GraphDB.models import Neo4jStore
from ..store_settings_api.models import StoreByState, StoresByCategory

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class AddTrustCenterAV(generics.ListCreateAPIView):
    def get(self, request):
        trust_center = TrustCenter.objects.all()
        serializer = TrustCenterSerializer(trust_center, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def post(self, request):

        """Trust center has 3 sections contact and location, contact, address, swadesic_labels"""
        # try:

        # Get the store instance using store_reference
        store_reference = request.data["store_reference"]
        store = Store.objects.get(
            store_reference=request.data["store_reference"], deleted=False
        )
        store_id = store.storeid

        # Initializing this value as None.
        store_address_serializer = None

        #  Trust center section has 3 section. saving contact, address and swadesic owned label.
        #  There is no order in filling this sections. when any of this above section get filled a trust center will get
        #  created for that store. And for saving the rest, it will be an update to the existing one.
        if TrustCenter.objects.filter(store_reference=store_reference).exists():

            # trust center already exists, so take the trust center instance
            trust_center_instance = TrustCenter.objects.get(
                store_reference=store_reference
            )

            # The data in the key "page" will specify if that particular request is sending a trust center's contact
            # details or address details. Here it's contact details.
            if "contact" in request.data["page"]:

                # Since it's an update, pass the instance and request data to the serializer.
                serializer = TrustCenterSerializer(
                    trust_center_instance, data=request.data
                )

            # Here the page key specifies that the data is about trust center's address/location
            elif "location" in request.data["page"]:

                # Here also it's an update, pass the instance and request data to the serializer.
                serializer = TrustCenterSerializer(
                    trust_center_instance, data=request.data
                )

                # While saving a trust center's location details, it should be added in store's address table also.
                # For that create a context data using the location details that has sent, and pass it to respective
                # serializer.
                store_address_context = {
                    "store_reference": store_reference,
                    "storeid": store_id,
                    "address": request.data["address"],
                    "city": request.data["city"],
                    "pincode": request.data["pincode"],
                    "state": request.data["state"],
                    "name": store.created_by.user_name,
                    "default_address": True,
                }
                store_address_serializer = AddressSerializer(data=store_address_context)
                try:
                    neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
                    neo4j_store.city = request.data["city"]
                    neo4j_store.pincode = request.data["pincode"]
                    neo4j_store.state = request.data["state"]
                    neo4j_store.save()
                    logger.info("Store Node Location details updated")
                except Neo4jStore.DoesNotExist:
                    logger.info("Store Node does not exist")

        else:

            # If the trust center not exists, here we create a trust center for that respective store. same logic as
            # above, instead of updating, creating the trust center using the serializer.
            if "contact" in request.data["page"]:
                serializer = TrustCenterSerializer(data=request.data)

            elif "location" in request.data["page"]:
                serializer = TrustCenterSerializer(data=request.data)

                store_address_context = {
                    "store_reference": store_reference,
                    "storeid": store_id,
                    "address": request.data["address"],
                    "city": request.data["city"],
                    "pincode": request.data["pincode"],
                    "state": request.data["state"],
                    "name": store.created_by.user_name,
                    "default_address": True,
                }
                store_address_serializer = AddressSerializer(data=store_address_context)

        # If it's a trust center create or update, First save the trust center.
        if serializer.is_valid():
            serializer.save()
            try:
                neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
                neo4j_store.city = request.data["city"]
                neo4j_store.pincode = request.data["pincode"]
                neo4j_store.state = request.data["state"]
                neo4j_store.save()
                logger.info("Store Node Location details updated")
            except Neo4jStore.DoesNotExist:
                logger.info("Store Node does not exist")

            # if store_address_serializer exists, then save address.
            if store_address_serializer and store_address_serializer.is_valid():
                store_address_serializer.save()
                logger.info("trust center address saved in Address table")

            return Response(
                {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # except:
        #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class GetTrustCenterAV(APIView):
    @swagger_auto_schema(operation_summary="get trust center of a store")
    def get(self, request, store_reference):
        try:
            trust_center = TrustCenter.objects.get(store_reference=store_reference)
        except TrustCenter.DoesNotExist:
            return Response(
                {"message": "Trust center not created"}, status=status.HTTP_200_OK
            )
        serializer = TrustCenterSerializer(trust_center)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(operation_summary="delete trust center of a store")
    def delete(self, request, store_reference):
        try:
            trust_center_instance = TrustCenter.objects.get(store_reference=store_reference)
        except TrustCenter.DoesNotExist:
            return Response(
                {"message": "not found"}, status=status.HTTP_400_BAD_REQUEST
            )
        trust_center_instance.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class EditTrustCenterContact(APIView):
    @swagger_auto_schema(
        operation_summary="edit trust center contacts",
        request_body=TrustCenterContactSerializer,
    )
    def put(self, request, trust_center_id):
        try:
            store = Store.objects.get(
                store_reference=request.data["store_reference"], deleted=False
            )
            request.data["storeid"] = store.storeid
            trust_center_instance = TrustCenter.objects.get(pk=trust_center_id)
        except TrustCenter.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = TrustCenterSerializer(
            trust_center_instance, data=request.data, partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class EditTrustCenterLocation(APIView):
    @swagger_auto_schema(
        operation_summary="edit trust center contacts",
        request_body=TrustCenterLocationSerializer,
    )
    def put(self, request, trust_center_id):
        store_reference = request.data["store_reference"]
        try:
            store = Store.objects.get(
                store_reference=request.data["store_reference"], deleted=False
            )
            request.data["storeid"] = store.storeid
            trust_center_instance = TrustCenter.objects.get(pk=trust_center_id)
            old_state_name = trust_center_instance.state
        except TrustCenter.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = TrustCenterSerializer(
            trust_center_instance, data=request.data, partial=True
        )
        if serializer.is_valid():
            new_state_name = serializer.validated_data.get('state')
            serializer.save()
            if old_state_name != new_state_name:
            #     old_state = StoreByState.objects.filter(state=old_state_name).first()
            #     if old_state:
            #         old_state.total_number_of_stores -= 1
            #         old_state.save()
            #
            #     new_state = StoreByState.objects.filter(state=new_state_name).first()
            #
            #     if new_state:
            #         new_state.total_number_of_stores += 1
            #         new_state.save()
            #     else:
            #         StoreByState.objects.create(
            #             state=new_state_name,
            #             icons=store.icon,
            #             total_number_of_stores=1
            #         )

                # Clear delivery locations
                DeliverySettings.objects.filter(store_reference=store_reference).update(delivery_locations=[])

                # Send notification
                notification_handler = NotificationHandler(
                    notified_user=store.created_by.user_reference,
                    notification_type=Notifications.Notifications_Type.STATE_CHANGE,
                    notification_about=store.store_reference,
                    old_state_name=old_state_name,
                )
                notification_handler.create_notification(notification_handler)

            try:
                neo4j_store = Neo4jStore.nodes.get(reference=store_reference)
                neo4j_store.city = request.data["city"]
                neo4j_store.pincode = request.data["pincode"]
                neo4j_store.state = request.data["state"]
                neo4j_store.save()
                logger.info("Store Node Location details updated")
            except Neo4jStore.DoesNotExist:
                logger.info("Store Node does not exist")

            address_instance = Address.objects.get(
                store_reference=request.data["store_reference"], default_address=True
            )
            address_instance.address = request.data["address"]
            address_instance.city = request.data["city"]
            address_instance.pincode = request.data["pincode"]
            address_instance.state = request.data["state"]
            address_instance.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class TrustCenterSwadeshiLabel(APIView):
    def post(self, request, store_reference):
        # If trust center already exists for this store, then it will update the swadeshi label field
        if TrustCenter.objects.filter(store_reference=store_reference).exists():
            trust_center_instance = TrustCenter.objects.get(store_reference=store_reference)
            serializer = TrustCenterSerializer(trust_center_instance, data=request.data, partial=True)

        # If the store doesn't have a trust center at the time of adding this label, it will create one
        # which will save this field alone.
        else:

            # when saving this, need to specify the store, so adding the store reference to
            # the request body before passing to serializer.
            request.data["store_reference"] = store_reference
            serializer = TrustCenterSerializer(data=request.data)

        # validating and saving the trust center.
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

    # def put(self, request, trust_center_id):
    #     try:
    #         trust_center_instance = TrustCenter.objects.get(pk=trust_center_id)
    #     except TrustCenter.DoesNotExist:
    #         return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
    #     serializer = TrustCenterSwadeshiLabelSerializer(
    #         trust_center_instance, data=request.data
    #     )
    #     if serializer.is_valid():
    #         serializer.save()
    #         return Response(
    #             {"message": "success", "data": serializer.data},
    #             status=status.HTTP_200_OK,
    #         )
    #     else:
    #         return Response(
    #             {"message": "error", "data": serializer.errors},
    #             status=status.HTTP_400_BAD_REQUEST,
    #         )


class AddDocumentsAV(APIView):
    parser_classes = [MultiPartParser, FormParser]

    def get(self, request):
        documents = Documents.objects.all()
        serializer = DocumentsSerializer(documents, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="add documents of a store", request_body=DocumentsSerializer
    )
    def post(self, request):
        store = Store.objects.get(store_reference=request.data["store_reference"])
        request.data["storeid"] = store.storeid
        serializer = DocumentsSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class GetDocumentAV(APIView):
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(operation_summary="get documents of a store")
    def get(self, request, *args, **kwargs):
        """get all the documents added to a store."""
        store_reference = self.kwargs.get("store_reference")
        try:
            documents = Documents.objects.filter(store_reference=store_reference)
        except Documents.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = DocumentsSerializer(documents, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class EditDocumentAV(APIView):
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        operation_summary="edit documents of a store", request_body=DocumentsSerializer
    )
    def put(self, request, document_id):
        store = Store.objects.get(
            store_reference=request.data["store_reference"], deleted=False
        )
        request.data["storeid"] = store.storeid
        document = Documents.objects.get(pk=document_id)
        serializer = DocumentsSerializer(document, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class EditDocumentName(APIView):
    @swagger_auto_schema(
        operation_summary="edit document's name", request_body=DocumentsSerializer
    )
    def patch(self, request, document_id):
        document_instance = Documents.objects.get(pk=document_id)
        serializer = DocumentsSerializer(
            document_instance, data=request.data, partial=True
        )  # set partial=True to update a data partially
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class DeleteDocument(APIView):
    @swagger_auto_schema(operation_summary="delete documents")
    def delete(self, request, document_id):
        try:
            document = Documents.objects.get(pk=document_id)
        except Documents.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        document.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class DeliverySettingsAV(APIView):
    @swagger_auto_schema(
        operation_summary="add delivery settings",
        request_body=DeliverySettingsSerializer,
    )
    def post(self, request):
        """create a delivery settings for a store. Default store delivery setting has product_Id
        as null. if delivery settings specific to a product then pass the product_id"""

        serializer = AddDeliverySettingsSerializer(data=request.data)
        if serializer.is_valid():
            instance = serializer.save()
            if instance:
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "error", "data": "Failed to create delivery settings"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class DeliverySettingsDetailsAV(APIView):
    @swagger_auto_schema(operation_summary="get delivery settings")
    def get(self, request, delivery_settings_id):
        try:
            delivery_settings = DeliverySettings.objects.get(pk=delivery_settings_id)
        except DeliverySettings.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_404_NOT_FOUND)
        serializer = DeliverySettingsSerializer(delivery_settings)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def put(self, request, delivery_settings_id):
        delivery_settings = DeliverySettings.objects.get(pk=delivery_settings_id)
        serializer = DeliverySettingsSerializer(delivery_settings, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete(self, request, delivery_settings_id):

        delivery_settings = DeliverySettings.objects.get(pk=delivery_settings_id)
        delivery_settings.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


# class TestLocations(APIView):
#     def get(self, request, *args, **kwargs):
#         store_reference = self.kwargs["store_reference"]
#         product_id = self.kwargs["product_id"]
#
#         # queryset = DeliveryLocations.objects.all().order_by('state').distinct()
#         # serializer = DeliveryLocationsSerializerTest(queryset, many=True)
#         locations = DeliveryLocations.objects.all().order_by('state').distinct()
#         data = []
#         for location in locations:
#             data["all_bharath"] = 0
#             data["states"] = []
#             # if location.state not in data:
#             #     data[location.state] = {}
#             # if location.city not in data[location.state]:
#             #     data[location.state][location.city] = []
#             # data[location.state][location.city].append({"pincode": location.pincode, "pincode_marked": 0})
#             data.append (data)
#         return Response ({"message": "success", "data": data})


class DeliveryLocationListAV(APIView):
    def get(self, request, *args, **kwargs):
        store_reference = self.kwargs["store_reference"]
        product_id = self.kwargs["product_id"]

        queryset = DeliveryLocations.objects.all()

        if store_reference == "0" and product_id == 0:
            all_states = (
                queryset.values_list("state", flat=True).order_by("state").distinct()
            )
            lst_final = []
            final_dict = {}
            lst = []
            for states in all_states:
                dic = {}
                all_cities = (
                    queryset.filter(state=states)
                    .values("city")
                    .order_by("city")
                    .distinct()
                )
                cities = [city["city"] for city in list(all_cities)]

                lst_2 = []
                for city in cities:
                    dic_2 = {}
                    all_pincodes = (
                        queryset.filter(city=city, state=states)
                        .values("pincode")
                        .distinct()
                    )

                    lst3 = []
                    for pincode in list(all_pincodes):
                        dic_3 = {}
                        dic_3["pincode"] = pincode["pincode"]
                        dic_3["pincode_marked"] = 0
                        lst3.append(dic_3)

                    dic_2["city"] = city
                    dic_2["city_marked"] = 0
                    dic_2["pincodes"] = lst3
                    lst_2.append(dic_2)

                dic["state"] = states
                dic["state_marked"] = 0
                dic["cities"] = lst_2
                lst.append(dic)

            final_dict["all_bharath"] = 0
            final_dict["states"] = lst
            lst_final.append(final_dict)

            return Response({"message": "success", "data": lst_final})
        else:
            queryset2 = DeliverySettings.objects.all()
            if queryset2.filter(
                store_reference=store_reference, productid=product_id, is_deleted=False
            ).exists():

                delivery_settings = queryset2.filter(
                    store_reference=store_reference,
                    productid=product_id,
                    is_deleted=False,
                ).last()
                delivery_locations = delivery_settings.delivery_locations
            else:
                delivery_settings = queryset2.filter(
                    store_reference=store_reference,
                    productid__isnull=True,
                    is_deleted=False,
                ).last()
                delivery_locations = delivery_settings.delivery_locations
            if delivery_locations:
                final_lst = delivery_locations.split("|")
            else:
                return Response(
                    {"message": "error"}, status=status.HTTP_400_BAD_REQUEST
                )
            all_states = (
                queryset.values_list("state", flat=True).order_by("state").distinct()
            )
            lst_final = []
            final_dict = {}
            lst = []
            for states in all_states:
                dict = {}
                all_cities = (
                    queryset.filter(state=states)
                    .values("city")
                    .order_by("city")
                    .distinct()
                )
                cities = [city["city"] for city in list(all_cities)]

                lst_2 = []
                for city in cities:
                    dic_2 = {}
                    all_pincode = (
                        queryset.filter(city=city, state=states)
                        .values("pincode")
                        .distinct()
                    )

                    lst3 = []
                    for pincode in list(all_pincode):
                        dic_3 = {}
                        dic_3["pincode"] = pincode["pincode"]

                        if (
                            states in final_lst
                            or city in final_lst
                            or pincode["pincode"] in final_lst
                        ):
                            dic_3["pincode_marked"] = 2
                        else:
                            dic_3["pincode_marked"] = 0
                        lst3.append(dic_3)

                    dic_2["city"] = city
                    if all(i["pincode_marked"] == 0 for i in lst3):
                        dic_2["city_marked"] = 0
                    elif all(i["pincode_marked"] == 2 for i in lst3):
                        dic_2["city_marked"] = 2
                    else:
                        dic_2["city_marked"] = 1
                    dic_2["pincodes"] = lst3
                    lst_2.append(dic_2)

                dict["state"] = states
                if all(i["city_marked"] == 0 for i in lst_2):
                    dict["state_marked"] = 0
                elif all(i["city_marked"] == 2 for i in lst_2):
                    dict["state_marked"] = 2
                else:
                    dict["state_marked"] = 1

                dict["cities"] = lst_2
                lst.append(dict)

            if all(i["state_marked"] == 0 for i in lst):
                final_dict["all_bharath"] = 0
            elif all(i["state_marked"] == 2 for i in lst):
                final_dict["all_bharath"] = 2
            else:
                final_dict["all_bharath"] = 1
            final_dict["states"] = lst
            lst_final.append(final_dict)

            return Response({"message": "success", "data": lst_final})


class AddDeliveryLocation(generics.CreateAPIView):
    serializer_class = DeliveryLocationsSerializer

    def get_serializer(self, *args, **kwargs):
        if isinstance(kwargs.get("data", {}), list):
            kwargs["many"] = True

        return super(AddDeliveryLocation, self).get_serializer(*args, **kwargs)


class DeliveryLocationDetailsAV(generics.RetrieveUpdateDestroyAPIView):
    queryset = DeliveryLocations.objects.all()
    serializer_class = DeliveryLocationsSerializer


class GetDeliverySettingsAV(APIView):
    def get(self, request, product_reference, store_reference, version=None):
        store_reference = store_reference

        store_instance = Store.objects.filter(store_reference=store_reference).first()
        trustcenter_instance = TrustCenter.objects.filter(store_reference=store_reference).first()
        is_gst_exists = False
        is_pan_exists = False
        trustcenter_location_state = None
        if store_instance:
            if store_instance.gst_number:
                is_gst_exists = True
            if store_instance.pan_number:
                is_pan_exists = True
        if trustcenter_instance:
            if trustcenter_instance.state:
                trustcenter_location_state = trustcenter_instance.state

        if product_reference != "0":
            product = Product.objects.get(product_reference=product_reference)
            product_id = product.productid
        else:
            product_id = "0"

        # If version is given we need to fetch delivery settings of that specific version
        if version:
            # check if product specific delivery setting exists
            if DeliverySettings.objects.filter(
                productid=product_id,
                store_reference=store_reference,
                delivery_settings_version=version,
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.get(
                    store_reference=store_reference,
                    productid=product_id,
                    delivery_settings_version=version,
                )
                serializer = DeliverySettingsSerializer(delivery_settings_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            # check if store specific delivery settings exist
            elif DeliverySettings.objects.filter(
                store_reference=store_reference,
                productid__isnull=True,
                delivery_settings_version=version,
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.get(
                    store_reference=store_reference,
                    productid__isnull=True,
                    delivery_settings_version=version,
                )
                serializer = DeliverySettingsSerializer(delivery_settings_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response({
                    "message": "not exists",
                    "is_pan_exists": is_pan_exists,
                    "is_gst_exists": is_gst_exists,
                    "trustcenter_location_state": trustcenter_location_state
                }, status=status.HTTP_200_OK)
        else:
            # check if product specific delivery setting exists
            if DeliverySettings.objects.filter(
                productid=product_id, store_reference=store_reference, is_deleted=False
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    store_reference=store_reference,
                    productid=product_id,
                    is_deleted=False,
                ).last()
                serializer = DeliverySettingsSerializer(delivery_settings_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            # check if store specific delivery settings exist
            elif DeliverySettings.objects.filter(
                store_reference=store_reference,
                productid__isnull=True,
                is_deleted=False,
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    store_reference=store_reference,
                    productid__isnull=True,
                    is_deleted=False,
                ).last()
                serializer = DeliverySettingsSerializer(delivery_settings_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response({
                    "message": "not exists",
                    "is_pan_exists": is_pan_exists,
                    "is_gst_exists": is_gst_exists,
                    "trustcenter_location_state": trustcenter_location_state
                }, status=status.HTTP_200_OK)


class AddRefundAndWarranty(APIView):
    def post(self, request):
        store_reference = request.data["store_reference"]
        product_reference = request.data["product_reference"]
        # if RefundAndWarranty.objects.filter(
        #     store_reference=store_reference, product_reference=product_reference
        # ).exists():
        #     return Response({"message": "refund warranty exists"})
        # else:
        serializer = RefundAndWarrantySerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class RefundAndWarrantyDetails(APIView):
    def get(self, request, refund_warranty_id):
        try:
            refund_and_warranty_instance = RefundAndWarranty.objects.get(
                pk=refund_warranty_id
            )
        except RefundAndWarranty.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        serializer = RefundAndWarrantySerializer(refund_and_warranty_instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def put(self, request, refund_warranty_id):
        try:
            store = Store.objects.get(
                store_reference=request.data["store_reference"], deleted=False
            )
            request.data["storeid"] = store.storeid
            refund_and_warranty_instance = RefundAndWarranty.objects.get(
                pk=refund_warranty_id
            )
        except RefundAndWarranty.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        serializer = RefundAndWarrantySerializer(
            refund_and_warranty_instance, data=request.data
        )
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete(self, request, refund_warranty_id):
        try:
            refund_and_warranty_instance = RefundAndWarranty.objects.get(
                pk=refund_warranty_id
            )
        except RefundAndWarranty.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        refund_and_warranty_instance.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetRefundAndWarrantyAV(APIView):
    def get(self, request, product_reference, store_reference, version=None):
        """If refund warranty settings available for the given product_id it will return that, otherwise will return
        default settings, ie, refund warranty settings of store level."""
        store_reference = store_reference
        if product_reference != "0":
            product = Product.objects.get(product_reference=product_reference)
            product_id = product.productid
        else:
            product_id = "0"
        if version:
            if RefundAndWarranty.objects.filter(
                productid=product_id,
                store_reference=store_reference,
                refund_warranty_version=version,
            ).exists():
                refund_and_warranty_instance = RefundAndWarranty.objects.get(
                    productid=product_id,
                    store_reference=store_reference,
                    refund_warranty_version=version,
                )
                serializer = RefundAndWarrantySerializer(refund_and_warranty_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            elif RefundAndWarranty.objects.filter(
                store_reference=store_reference,
                productid__isnull=True,
                refund_warranty_version=version,
            ).exists():
                refund_and_warranty_instance = RefundAndWarranty.objects.get(
                    store_reference=store_reference,
                    productid__isnull=True,
                    refund_warranty_version=version,
                )
                serializer = RefundAndWarrantySerializer(refund_and_warranty_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response({"message": "not exists"}, status=status.HTTP_200_OK)

        else:
            if RefundAndWarranty.objects.filter(
                productid=product_id, store_reference=store_reference, is_deleted=False
            ).exists():
                refund_and_warranty_instance = RefundAndWarranty.objects.filter(
                    productid=product_id,
                    store_reference=store_reference,
                    is_deleted=False,
                ).last()
                serializer = RefundAndWarrantySerializer(refund_and_warranty_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            elif RefundAndWarranty.objects.filter(
                store_reference=store_reference,
                productid__isnull=True,
                is_deleted=False,
            ).exists():
                refund_and_warranty_instance = RefundAndWarranty.objects.filter(
                    store_reference=store_reference,
                    productid__isnull=True,
                    is_deleted=False,
                ).last()
                serializer = RefundAndWarrantySerializer(refund_and_warranty_instance)
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response({"message": "not exists"}, status=status.HTTP_200_OK)


class AddAddress(
    mixins.CreateModelMixin,
    mixins.UpdateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.DestroyModelMixin,
    generics.GenericAPIView,
):

    queryset = Address.objects.all()
    serializer_class = AddressSerializer

    def post(self, request, *args, **kwargs):
        store = Store.objects.get(
            store_reference=request.data["store_reference"], deleted=False
        )
        request.data["storeid"] = store.storeid
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, *args, **kwargs):
        store = Store.objects.get(
            store_reference=request.data["store_reference"], deleted=False
        )
        request.data["storeid"] = store.storeid
        return self.update(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        address_id = str(instance.addressid)  # Convert to string to match JSON field

        # Find all delivery settings that have this address ID in pickup_location_ids
        affected_delivery_settings = DeliverySettings.objects.filter(
            pickup_location_ids__contains=address_id
        )

        # Update each affected delivery setting
        for delivery_setting in affected_delivery_settings:
            # Safely handle the case where pickup_location_ids might be None
            current_locations = delivery_setting.pickup_location_ids or []
            
            # Remove the address ID from pickup_location_ids
            updated_locations = [
                loc for loc in current_locations 
                if loc != address_id
            ]
            
            # Update the pickup_location_ids
            delivery_setting.pickup_location_ids = updated_locations
            if not updated_locations:
                delivery_setting.fulfillment_options = "DELIVERY" if delivery_setting.fulfillment_options == "DELIVERY_AND_IN_STORE_PICKUP" else ""
            delivery_setting.save()

        # Perform the actual deletion of the address
        self.perform_destroy(instance)
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetAllStoreAddress(generics.ListAPIView):
    serializer_class = AddressSerializer

    def list(self, request, *args, **kwargs):
        """store can set multiple addresses, this will give all the address specified to a particular store."""
        store_reference = self.kwargs["store_reference"]
        is_pickup_addresses = request.query_params.get("is_pickup_addresses", "").lower() == "true"
        is_swadesic_shipping_address = request.query_params.get("is_swadesic_shipping_address", "").lower() == "true"
        
        if is_swadesic_shipping_address:
            queryset = Address.objects.filter(
                store_reference=store_reference,
                store_address_type__contains=['SWADESIC_SHIPPING_PICKUP']
            )
        elif is_pickup_addresses:
            queryset = Address.objects.filter(
                store_reference=store_reference,
                store_address_type__contains=['IN_STORE_PICKUP']
            )
        else:
            queryset = Address.objects.filter(
                store_reference=store_reference,
            )
        
        if not queryset.exists():
            return Response({
                'message': 'No addresses found for the specified type',
                'data': []
            })
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )



class GetAddressForProductPickup(generics.ListAPIView):
    def list(self, request, *args, **kwargs):
        product_reference = self.kwargs["product_reference"]
        product_instance = Product.objects.get(product_reference=product_reference, deleted=False)
        product_delvery_settings = DeliverySettings.objects.filter(product_reference=product_reference,
                                                                   is_deleted=False).last()
        if product_delvery_settings:
            pickup_location_ids = product_delvery_settings.pickup_location_ids
        else:
            store_delvery_settings = DeliverySettings.objects.filter(store_reference=product_instance.store_reference,
                                                                     is_deleted=False).last()
            pickup_location_ids = store_delvery_settings.pickup_location_ids

        if pickup_location_ids:
            integer_ids = [int(id) for id in pickup_location_ids]
            addresses = Address.objects.filter(addressid__in=integer_ids, store_address_type__contains=["IN_STORE_PICKUP"])
            serializer = AddressSerializer(addresses, many=True)
            return Response({"message": "success", "data": serializer.data}, status=status.HTTP_200_OK)
        else:
            return Response({"message": "no pickup location found"}, status=status.HTTP_200_OK)


class ResetDefaultDS(APIView):
    def get(self, request, *args, **kwargs):
        store_reference = kwargs["store_reference"]
        product_reference = kwargs["product_reference"]
        try:
            DeliverySettings.objects.filter(
                store_reference=store_reference, product_reference=product_reference
            ).update(is_deleted=True)
            return Response({"message": "success"}, status=status.HTTP_200_OK)
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class ResetDefaultRefundSettings(APIView):
    def get(self, request, *args, **kwargs):
        store_reference = kwargs["store_reference"]
        product_reference = kwargs["product_reference"]
        try:
            RefundAndWarranty.objects.filter(
                store_reference=store_reference, product_reference=product_reference
            ).update(is_deleted=True)
            return Response({"message": "success"}, status=status.HTTP_200_OK)
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class GetStoreConfig(APIView):
    serializer_class = StoreConfigSerializer

    def get(self, request, *args, **kwargs):
        start_perf = time.perf_counter()
        store_reference = self.kwargs['store_reference']
        try:
            store_config_instance = StoreConfig.objects.get(store_reference=store_reference)
            serializer = StoreConfigSerializer(store_config_instance)
            print(f"GetStoreConfig time: {time.perf_counter() - start_perf:.4f} seconds")
            return Response(
                {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
            )
        except StoreConfig.DoesNotExist:
            return Response(
                {"message": "Store configuration not found"}, status=status.HTTP_404_NOT_FOUND
            )


class GetStoresByStates(APIView):
    def get(self, request):
        store_infos = StoreByState.objects.all()

        base_url = request.build_absolute_uri('/')

        response_data = []
        for info in store_infos:
            icon_urls = [base_url + icon_path for icon_path in info.icons.split(', ')]

            response_data.append({
                'tier': info.state,
                'icons': icon_urls,
                'total_number_of_stores': info.total_number_of_stores
            })

        return Response({
            'message': 'Stores by State',
            'data': response_data
        }, status=status.HTTP_200_OK)


class GetStoresByCategories(APIView):
    def get(self, request):
        try:
            base_url = request.build_absolute_uri('/media/')

            populated_data = StoresByCategory.objects.all().values('category_name', 'total_number_of_stores', 'icons')
            if not populated_data:
                return Response({'message': 'No stores found in StoresByCategory table', 'data': []},
                                status=status.HTTP_200_OK)

            response_data = [
                {
                    'tier': data['category_name'],
                    'total_number_of_stores': data['total_number_of_stores'],
                    'icons': [base_url + icon for icon in data['icons'].split(', ')]
                }
                for data in populated_data
            ]
            return Response({
                'message': 'Stores by category',
                'data': response_data
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
