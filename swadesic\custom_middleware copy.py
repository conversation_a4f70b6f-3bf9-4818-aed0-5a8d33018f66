
# import logging
# import time
# import re
# import base64
# from general.models import Request
# import json
# from django.http import JsonResponse
# from graphql import GraphQLError
# from rest_framework_simplejwt.authentication import JWTAuthentication
# from rest_framework_simplejwt.exceptions import InvalidToken
# from django.urls import resolve
# from common.util.support_helper import extract_input_arguments_with_values
# from decouple import config
# from stores.store_api.models import Store
# from channels.auth import AuthMiddlewareStack
# from channels.middleware import BaseMiddleware

# logger = logging.getLogger(__name__)
# logger.setLevel(logging.INFO)


# # ENCRYPTION = config("ENCRYPTION", cast=bool)


# class SaveRequest:
#     def __init__(self, get_response):
#         self.get_response = get_response

#         # Filter to log all request to url's that start with any of the strings below.
#         # With example below:
#         # /example/test/ will be logged.
#         # /other/ will not be logged.
#         self.prefixs = [
#             '/general',
#             '/store',
#             '/order',
#             '/product',
#             '/common',
#             '/user',
#             '/graphDBql',
#             '/content',
#             '/lean'
#         ]

#     def __call__(self, request):
#         start_time = time.time()  # Calculated execution time.
#         response = self.get_response(request)  # Get response from view function.
#         end_time = time.time()
#         _t = int((end_time - start_time) * 1000)

#         # If the url does not start with on of the prefixes above, then return response and dont save log.
#         # (Remove these two lines below to log everything)
#         if not list(filter(request.get_full_path().startswith, self.prefixs)):
#             return response

#         response_data = response.content
#         decoded_response_data = response_data.decode()
#         try:
#             # request_body = request.body
#             # decoded_request_body = request_body.decode()
#             request_data = getattr(request, '_body', request.body)
#             decoded_request_body = json.loads(request_data)
#         except:
#             decoded_request_body = ""
#         try:
#             user_reference = getattr(request.user, 'user_reference', None)
#             exceptions = getattr(response, '_exceptions', [])
#             async_flow_exception = None
#             async_flow_exception_details = None
#             if exceptions:
#                 async_flow_exception = ''.join(exception.get("message") for exception in exceptions)
#                 async_flow_exception_details = ''.join(exception.get("data") for exception in exceptions)
#             # Create instance of our model and assign values
#             request_log_db = Request(
#                 endpoint=request.get_full_path(),
#                 response_status_code=response.status_code,
#                 request_method=request.method,
#                 remote_address=self.get_client_ip(request),
#                 start_time=start_time,
#                 end_time=end_time,
#                 exec_time=_t,
#                 request_body=decoded_request_body,
#                 http_user_agent=request.META.get('HTTP_USER_AGENT'),
#                 query_string=request.META.get('QUERY_STRING'),
#                 user_reference=user_reference,
#                 async_flow_exception=async_flow_exception,
#                 async_flow_exception_details=async_flow_exception_details
#             )
#             request_log_db.save()
#         except Exception as e:
#             logger.info(f"Something went wrong, couldn't save request in db: {e}")
#         request_log = {
#             "endpoint": request.get_full_path(),
#             "response_status_code": response.status_code,
#             "response_method": request.method,
#             "remote_address": self.get_client_ip(request),
#             "start_time": start_time,
#             "end_time": end_time,
#             "exec_time_in_ms": _t,
#             "response_body": decoded_response_data,
#             "request_body": decoded_request_body,
#             "http_user_agent": request.META.get('HTTP_USER_AGENT'),
#             "query_string": request.META.get('QUERY_STRING'),
#             "request_method": request.META.get('REQUEST_METHOD'),
#         }

#         # logger.info ("Metadd %s", request.META.HTTP_USER_AGENT)
#         # logger.info(request_log)
#         # # Save log in db
#         return response

#     # get clients ip address
#     def get_client_ip(self, request):
#         x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
#         if x_forwarded_for:
#             _ip = x_forwarded_for.split(',')[0]
#         else:
#             _ip = request.META.get('REMOTE_ADDR')
#         return _ip


# class JWTMiddleware:
#     """
#     This middleware class is responsible for handling JWT authentication and request processing.
#     It checks for the presence of a 'Cache-Control-Speed' header in the request. If the header is not present,
#     it calls the handle_no_encryption method. If the header is present and its value is 'medium', it calls the
#     handle_medium_encryption method. Both of these methods handle the request processing and authentication.
#     """
#     def __init__(self, get_response):
#         self.get_response = get_response
#         self.excluded_paths = [
#             '/user/userlogin/',
#             '/media/',
#             '/data/',  # Add this line to exclude data paths
#             '/api/token/refresh',
#             '/user/newuserlogin/',
#             '/user/googlelogin/',
#             '/user/signin/',
#             '/user/check_user_info/',
#             '/user/verify_signin/',
#             '/user/resend_otp/',
#             '/common/app_meta_data/',
#             '/common/app_configurations/',
#             '/ws/', 
#             '/order/update-swadesic-shipping-details-wh/',
#             '/user/email/check_user_info/',
#             '/user/email/signin/',
#             '/user/email/verify_signin/',
#             '/user/email/resend_otp/'
#         ]
#         self.static_user_allowed_paths = {
#             '/graphdb/check_access/': self.handle_check_access_request,
#             '/store/getting_reference_from_handle/': self.handle_getting_reference_request
#         }

#     def __call__(self, request):
#         # Bypass middleware for WebSocket connections

#         if request.path.startswith('/ws/'):
#             return self.get_response(request)

#         # Existing middleware logic
#         self.is_encryption = request.headers.get('Cache-Control-Speed')
#         if self.is_encryption is None:
#             return self.handle_no_encryption(request)
#         elif self.is_encryption == 'medium':
#             return self.handle_medium_encryption(request)
#         return self.get_response(request)

#     def handle_no_encryption(self, request):
#         """
#         This method is called when the 'Cache-Control-Speed' header is not present in the request.
#         It first checks if the request path is in the excluded paths. If it is, it calls the get_response method.
#         If the request path is not in the excluded paths, it checks for authentication. If the request is not
#         authenticated, it returns a 401 error. If the request is authenticated, it checks if the user is a static user.
#         If the user is a static user, it calls the handle_static_user method. If the user is not a static user, it
#         calls the get_response method.
#         """
#         if self.is_excluded_path(request):
#             return self.get_response(request)

#         if not self.authenticate_request(request):
#             return self.error_response('Authentication credentials were not provided.', 401)

#         if self.is_static_user(request):
#             return self.handle_static_user(request)

#         return self.get_response(request)

#     def handle_medium_encryption(self, request):
#         """
#         This method is called when the 'Cache-Control-Speed' header is present in the request and its value is 'medium'.
#         It first checks if the request has already been processed by the middleware. If it has, it calls the get_response
#         method.
#         If the request has not been processed, it checks the request body.
#         If the request body is not valid JSON, it returns a 400 error.
#         If the request body is valid JSON, it checks if the request path is in the excluded paths.
#         If it is, it calls the handle_excluded_path method.
#         If the request path is not in the excluded paths, it checks for authentication.
#         If the request is not authenticated, it returns a 401 error. If the request is authenticated,
#         it checks if the user is a static user. If the user is a static user, it calls the handle_static_user method.
#         If the user is not a static user, it calls the get_response method.
#         """
#         if hasattr(request, '_jwt_middleware_processed'):
#             return self.get_response(request)

#         if not self.process_request_body(request):
#             return self.error_response('Invalid JSON data in request body.', 400)

#         if self.is_excluded_path(request):
#             return self.handle_excluded_path(request)

#         if not self.authenticate_request(request):
#             return self.error_response('Authentication credentials were not provided.', 401)

#         if self.is_static_user(request):
#             return self.handle_static_user(request)

#         request._jwt_middleware_processed = True
#         return self.encode_response(self.get_response(request))

#     def is_excluded_path(self, request):
#         """
#         This method checks if the request path is in the excluded paths.
#         """
#         return any(request.path.startswith(path) for path in self.excluded_paths)

#     def authenticate_request(self, request):
#         """
#         This method uses JWTAuthentication to authenticate the request.
#         """
#         jwt_auth = JWTAuthentication()
#         try:
#             user, token = jwt_auth.authenticate(request)
#             if user is None:
#                 return False
#             request.user = user
#             return True
#         except InvalidToken:
#             return False

#     def is_static_user(self, request):
#         """
#         This method checks if the user is a static user.
#         """
#         return request.user.user_reference == 'U9999999999999'

#     def handle_static_user(self, request):
#         """
#         This method handles the request processing for static users.
#         """
#         if request.path in self.static_user_allowed_paths:
#             return self.static_user_allowed_paths[request.path](request)
#         elif request.path.startswith('/graphDBql/'):
#             return self.handle_graphql_request(request)
#         elif request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
#             return self.error_response('POST, PUT, PATCH, and DELETE methods are not allowed for unregistered users.', 401)
#         return self.get_response(request) if not self.is_encryption else self.encode_response(self.get_response(request))

#     def handle_graphql_request(self, request):
#         """
#         This method handles the request processing for GraphQL requests.
#         """
#         try:
#             request_data = json.loads(request.body.decode('utf-8'))
#             if 'query IntrospectionQuery' not in request_data.get("query", ""):
#                 if request.method in ['POST', 'PUT', 'PATCH', 'DELETE'] and 'query' not in request_data.get("query", ""):
#                     return self.error_response('POST, PUT, PATCH, and DELETE methods are not allowed for unregistered users.', 401)
#         except json.JSONDecodeError:
#             return self.error_response('Invalid JSON data in request body.', 400)
#         request._jwt_middleware_processed = True
#         return self.get_response(request) if not self.is_encryption else self.encode_response(self.get_response(request))

#     def handle_check_access_request(self, request):
#         """
#         This method handles the /graphdb/check_access/ request.
#         """
#         response = self.get_response(request)
#         return self.encode_response(response) if self.is_encryption else response

#     def handle_getting_reference_request(self, request):
#         """
#         This method handles the /store/getting_reference_from_handle/ request.
#         """
#         # Handle the /store/getting_reference_from_handle/ request
#         response = self.get_response(request)
#         return self.encode_response(response) if self.is_encryption else response

#     def process_request_body(self, request):
#         """
#         This method processes the request body.
#         """
#         try:
#             if request and 'form-data' not in request.headers['Content-Type']:
#                 if request.body:
#                     regular_string = request.body.decode('utf-8')
#                     json_data = json.loads(regular_string)
#                     if not json_data.get('query'):
#                         decoded_request_body = base64.b32decode(json_data.get('basket')).decode('utf-8')
#                         request._body = decoded_request_body.encode('utf-8')
#                     elif 'query IntrospectionQuery' in json_data['query']:
#                         return True
#         except Exception:
#             return False
#         return True

#     def handle_excluded_path(self, request):
#         """
#         This method handles the request processing for excluded paths.
#         """
#         response = self.get_response(request)
#         if request.path.startswith('/media/'):
#             return response
#         return self.encode_response(response) if self.is_encryption else response

#     def encode_response(self, response):
#         """
#         This method encodes the response.
#         """
#         response_buds = base64.b32encode(response.content).decode('utf-8')
#         return JsonResponse({'buds': response_buds}, status=response.status_code)

#     def error_response(self, message, status_code):
#         response = JsonResponse({'error': message}, status=status_code)
#         return self.encode_response(response) if self.is_encryption else response

#     # def get_store_reference(self, request):
#     #     # Check in URL parameters
#     #     store_reference = request.path.split('/')[-2]  # Adjust based on your URL structure
#     #
#     #     # Check in query parameters
#     #     if 'store_reference' in request.query_params:
#     #         return request.query_params['store_reference']
#     #
#     #     # Check in request body
#     #     if request.method in ['POST', 'PUT', 'PATCH']:
#     #         if 'store_reference' in request.data:
#     #             return request.data['store_reference']
#     #
#     #     return store_reference if store_reference else None
#     #
#     # def validate_api_caller(self, request, jwt_user):
#     #     if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
#     #         if request.path.startswith('/store/'):
#     #             store_reference = self.get_store_reference(request)
#     #             store_instance = Store.objects.filter(store_reference=store_reference).first()


# def validate_user_reference(user_reference):
#     """
#     Validates a user reference in the format 'U<13 digits>'.
#     Example: U1234567890123
#     """
#     return bool(re.match(r'^U\d{13}$', user_reference)) or bool(re.match(r'^U\d{9}$', user_reference))


# def validate_key_values(key_value_pair_list):
#     """
#     Validates a user reference in the format 'U<13 digits>'.
#     Example: U1234567890123
#     """
#     final_list = []
#     for key, value in key_value_pair_list:
#         validated_value = True
#         if key in ['user_reference', 'user_follower', ]:
#             validated_value = validate_user_reference(value)

#         final_list.append(validated_value)

#         # Logging inputs which didn't qualify validation
#     invalid_key_values = 'Invalid Inputs received for these fields:' + str(
#         [key_value_pair_list[i[0]] for i in enumerate(final_list) if not i[1]])

#     return all(final_list), invalid_key_values


# class InputValidationMiddleware:
#     def __init__(self, get_response):
#         self.get_response = get_response

#     def __call__(self, request):
#         # Pre-process request
#         response = self.validate_inputs(request)
#         if response:
#             return response

#         # Call the next middleware or the view
#         response = self.get_response(request)

#         # Post-process response
#         return response

#     def validate_inputs(self, request):
#         try:
#             # Check if the request is for the user endpoint
#             url_parameters = {}
#             post_args = {}
#             if request.path.startswith('/user/'):
#                 # Check URL parameters
#                 url_parameters = resolve(request.path_info).kwargs

#                 # POST request body arguments
#                 # TODO add layer to handle images during validation
#                 post_args = json.loads(request.body) if request.body else {}

#             if request.path.startswith('/graphDBql/'):
#                 url_parameters = resolve(request.path_info).kwargs

#                 # POST request body arguments
#                 gql_query = json.loads(request.body) if request.body else {}

#                 query = gql_query['query']
#                 post_args = extract_input_arguments_with_values(query)

#             # Create a list of key-value pairs as tuples combining x and y
#             combined_list = [(key, value) for key, value in url_parameters.items()] + [(key, value) for key, value in
#                                                                                        post_args.items()]

#             validation_result, invalid_key_values = validate_key_values(
#                 key_value_pair_list=combined_list) if combined_list else (True, '')
#             if validation_result:
#                 return None
#             else:
#                 return JsonResponse({'error': 'Invalid user reference', 'data': invalid_key_values}, status=400)
#         except:
#             return None


# # Add new WebSocket JWT Middleware
# class WebSocketJWTMiddleware(BaseMiddleware):
#     async def __call__(self, scope, receive, send):
#         # Initialize the scope with an anonymous user by default
#         scope['user'] = None
        
#         # Extract headers from scope
#         headers = dict(scope['headers'])
        
#         # If there's a token in the headers, validate it
#         if b'authorization' in headers:
#             try:
#                 token = headers[b'authorization'].decode('utf-8').split(' ')[1]
#                 # You can add JWT validation here if needed for initial connection
#                 # For now, we'll handle authentication through the login message
#                 pass
#             except Exception as e:
#                 logger.error(f"WebSocket JWT validation error: {str(e)}")
        
#         return await super().__call__(scope, receive, send)
