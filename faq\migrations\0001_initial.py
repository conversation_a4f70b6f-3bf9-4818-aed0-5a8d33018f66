# Generated by Django 3.2.22 on 2025-06-01 14:18

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FaqCategory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('category_key', models.CharField(help_text='Unique key for the category (used in deep links)', max_length=100, unique=True, validators=[django.core.validators.RegexValidator(message='Category key must contain only lowercase letters, numbers, and underscores', regex='^[a-z0-9_]+$')])),
                ('name', models.CharField(help_text='Display name for the category', max_length=200)),
                ('order', models.PositiveIntegerField(default=0, help_text='Order in which categories should be displayed')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active and should be displayed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'FAQ Category',
                'verbose_name_plural': 'FAQ Categories',
                'db_table': '"public"."faq_category"',
            },
        ),
        migrations.CreateModel(
            name='FaqItem',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('item_key', models.CharField(help_text='Unique key for the FAQ item (used in deep links)', max_length=100, unique=True, validators=[django.core.validators.RegexValidator(message='Item key must contain only lowercase letters, numbers, and underscores', regex='^[a-z0-9_]+$')])),
                ('question', models.TextField(help_text='The FAQ question text')),
                ('answer', models.TextField(help_text='The FAQ answer text (can contain HTML for formatting)')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order in which items should be displayed within the category')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this FAQ item is active and should be displayed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(help_text='Category this FAQ item belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='items', to='faq.faqcategory')),
            ],
            options={
                'verbose_name': 'FAQ Item',
                'verbose_name_plural': 'FAQ Items',
                'db_table': '"public"."faq_item"',
                'ordering': ['category__order', 'order', 'question'],
            },
        ),
        migrations.CreateModel(
            name='FaqItemImages',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('image', models.ImageField(help_text='The FAQ item image', upload_to='faq_images')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order in which images should be displayed within the item')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('item', models.ForeignKey(help_text='FAQ item this image belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='images', to='faq.faqitem')),
            ],
            options={
                'verbose_name': 'FAQ Item Image',
                'verbose_name_plural': 'FAQ Item Images',
                'db_table': '"public"."faq_item_images"',
                'ordering': ['order'],
            },
        ),
    ]
