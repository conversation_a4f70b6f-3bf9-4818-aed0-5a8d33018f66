# Generated by Django 4.2.7 on 2024-09-12 10:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0084_rename_userpoints_userrewards_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="infinityrewardshistory",
            name="reward_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("SUCCESS", "Success"),
                    ("PENDING", "Pending"),
                    ("FAILED", "Failed"),
                    ("INVALID", "Invalid"),
                ],
                default="SUCCESS",
                max_length=10,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="infinityrewardshistory",
            name="reward_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                    ("STORE_VERIFICATION", "Store Verification"),
                    ("U2U_TRANSFER", "U2U Transfer"),
                    ("S2U_TRANSFER", "S2U Transfer"),
                ],
                max_length=20,
                null=True,
            ),
        ),
    ]
