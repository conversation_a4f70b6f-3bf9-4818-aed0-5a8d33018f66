We are integrating Shiprocket shipping services into Swadesic App to provide Swadesic Shipping. 

We already have Shipping options for <PERSON><PERSON> to make Self delivery by themselves or Delivery by Logistics partner. Swadesic Shipping is third option to fulfill. 

The intention of Swadesic Shipping is to get stores started on doing logistics in a automated and hassle free way - From checking courier service availability to Choosing a courier serviceto creating an order, getting the product picked up and tracking

# **Swadesic Shipping Integration with Shiprocket**

## **Objective**

Swadesic Shipping allows sellers to fulfill orders using Shiprocket’s logistics services in an automated and hassle-free manner. This includes:

- Checking courier service availability.
- Selecting a suitable courier partner.
- Scheduling pickups and generating shipping labels.
- Tracking shipments via Shiprocket.
- Handling cancellations efficiently.

---

## **Database Changes**

### **1. Address Table Modifications**

- **New Column:** `store_address_type` (ArrayField) to store pickup types (`SwadesicShippingPickup`, `InStorePickup`).
- **New Column:** `address_name_in_shiprocket` (CharField) to store Shiprocket’s registered pickup location name.

### **2. Swadesic Shipping Package Details Table**

This table stores shipment package details, including tracking status and timeline milestones.

#### **Table Schema**

```python
class SwadesicShippingPackageDetails(models.Model):
    package_reference = models.AutoField(primary_key=True)
    order_reference = models.CharField(max_length=50, unique=True)  # Swadesic Order ID
    store_reference = models.IntegerField()  # Store reference
    
    # Shiprocket-related details
    shiprocket_order_id = models.CharField(max_length=50, null=True, blank=True)  # Shiprocket Order ID
    shipment_id = models.CharField(max_length=50, null=True, blank=True)  # Shiprocket Shipment ID
    awb_number = models.CharField(max_length=50, null=True, blank=True)  # Air Waybill (AWB) number
    courier_name = models.CharField(max_length=100, null=True, blank=True)  # Assigned courier
    tracking_url = models.URLField(null=True, blank=True)  # Shiprocket tracking URL

    # Address details stored as JSON
    pickup_address = models.JSONField(default=dict)  # Store's pickup address
    delivery_address = models.JSONField(default=dict)  # Buyer's delivery address
    
    # Package dimensions & weight
    volumetric_weight = models.FloatField(null=True, blank=True)
    dead_weight = models.FloatField(null=True, blank=True)
    length = models.FloatField(null=True, blank=True)
    breadth = models.FloatField(null=True, blank=True)
    height = models.FloatField(null=True, blank=True)

    # Product details
    package_contents = models.JSONField(default=dict)  
    # Example: {"products": [{"sku": "P001", "name": "Product 1", "quantity": 2, "price": 499}]}

    # Shipping balance check
    shipping_cost = models.FloatField(null=True, blank=True)
    is_shipping_balance_deducted = models.BooleanField(default=False)

    # Tracking timeline
    tracking_milestones = models.JSONField(default=dict)  
    # Example: {"timeline": [{"status": "Out for Pickup", "timestamp": "2025-03-21 10:30"}]}

    # Status Flags
    status = models.CharField(
        max_length=50,
        choices=[
            ("Pending", "Pending"),
            ("Pickup Scheduled", "Pickup Scheduled"),
            ("In Transit", "In Transit"),
            ("Delivered", "Delivered"),
            ("Cancelled", "Cancelled"),
        ],
        default="Pending"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Swadesic Shipping Package Details"
        db_table = "store.swadesic_shipping_package_details"
```

---

## **Workflow**

### **1. Seller Explores Shipping Options (No Entry in DB Yet)**

- The seller selects **Swadesic Shipping**.
- Enters package weight and dimensions.
- Calls:
  ```python
  ShiprocketAPI.check_serviceability(pickup_pincode, delivery_pincode, weight)
  ```
- Swadesic **does NOT create a package entry** yet since the seller might still choose self-delivery.

---

### **2. Seller Confirms "Ready for Pickup" → Create Entry in DB**

- When the seller finalizes **Swadesic Shipping**, we:
  - Create an order in Shiprocket:
    ```python
    response = ShiprocketAPI.create_order(order_details)
    ```
  - Assign an AWB:
    ```python
    awb_response = ShiprocketAPI.assign_awb(response["shipment_id"], selected_courier_id)
    ```
  - **Now we create the entry in **``, storing:
    ```python
    SwadesicShippingPackageDetails.objects.create(
        order_reference=order_id,
        store_reference=store_id,
        shiprocket_order_id=response["order_id"],
        shipment_id=response["shipment_id"],
        awb_number=awb_response["awb_code"],
        courier_name=response["courier_name"],
        tracking_url=response["tracking_data"]["track_url"],
        pickup_address=pickup_address_json,
        delivery_address=delivery_address_json,
        package_contents=package_contents_json,
        shipping_cost=shipping_cost,
        status="Pickup Scheduled",
    )
    ```

---

### **3. Shipment Tracking**

- When tracking is updated, append the timeline to `tracking_milestones`:
  ```python
  tracking_info = ShiprocketAPI.track_by_awb(awb_number)
  package.tracking_milestones["timeline"].append({
      "status": tracking_info["tracking_data"]["shipment_status"],
      "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
  })
  package.status = tracking_info["tracking_data"]["shipment_status"]
  package.save()
  ```

---

### **4. Order Delivered**

- Once detected as **"Delivered"**, update:
  ```python
  package.status = "Delivered"
  package.save()
  ```

---

### **5. Order Cancellation Handling**

- If the order is **canceled before pickup**:
  ```python
  ShiprocketAPI.cancel_order(package.shiprocket_order_id)
  package.is_shipping_balance_deducted = False
  package.status = "Cancelled"
  package.save()
  ```

---

## **Final Thoughts**

- These changes **optimize database space**, ensuring an entry is **only created when shipping is confirmed**.
- The JSON storage for **pickup and delivery addresses** makes retrieval easier and avoids unnecessary joins.
- The design provides **end-to-end shipment tracking and management** in Swadesic.

🚀 **This document is ready for implementation.**

