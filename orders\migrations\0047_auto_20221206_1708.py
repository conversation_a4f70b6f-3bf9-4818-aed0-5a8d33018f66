# Generated by Django 3.2.13 on 2022-12-06 11:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0046_payoutbalance_missed_revenue"),
    ]

    operations = [
        migrations.RenameField(
            model_name="orderlifecycle",
            old_name="estimated_return_date",
            new_name="estimated_pickup_date",
        ),
        migrations.RenameField(
            model_name="suborder",
            old_name="estimated_return_date",
            new_name="estimated_pickup_date",
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="return_additional_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="return_by_logistic_partner",
            field=models.Boolean<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="return_pickup_logistic_partner",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="orderlifecycle",
            name="return_tracking_number",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="orderlifecycle",
            name="self_return_by_store",
            field=models.<PERSON>oleanField(default=False),
        ),
        migrations.AddField(
            model_name="suborder",
            name="return_additional_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="suborder",
            name="return_by_logistic_partner",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="suborder",
            name="return_pickup_logistic_partner",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="suborder",
            name="return_tracking_number",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="suborder",
            name="self_return_by_store",
            field=models.BooleanField(default=False),
        ),
    ]
