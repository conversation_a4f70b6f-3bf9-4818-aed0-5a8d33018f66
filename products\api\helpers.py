def calculate_product_version(product_version, change_type):
    """
    This function will calculate the product version whenever a product or product image get updated
    :param product_version:
        (str) : current version of product
        example : '1.0.0'
    :param change_type:
        (str) : each field of product had a change_type, Which has already assigned
        example : "MAJOR", "MINOR", "IDLE"
    :return: newly calculated product version
    """

    current_product_version = product_version
    major_part = int(current_product_version.split(".")[0])
    minor_part = int(current_product_version.split(".")[1])
    idle_part = int(current_product_version.split(".")[2])

    #  Increment the major part by one and reset minor and idle part to 0
    if change_type == "MAJOR":
        major_part += 1
        new_product_version = str(major_part) + "." + "0" + "." + "0"

    #  Increment the minor part by one and reset idle part to 0
    elif change_type == "MINOR":
        minor_part += 1
        new_product_version = str(major_part) + "." + str(minor_part) + "." + "0"

    #  Increment the idle part
    elif change_type == "IDLE":
        idle_part += 1
        new_product_version = (
            str(major_part) + "." + str(minor_part) + "." + str(idle_part)
        )

    return new_product_version
