{% extends 'seo/base.html' %}
{% load seo_filters %}

{% block schema %}
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "{{ product.product_name }}",
    "description": "{{ product.product_description }}",
    "brand": {
        "@type": "Brand",
        "name": "{{ product.brand_name }}"
    },
    "category": "{{ product.product_category }}",
    "offers": {
        "@type": "Offer",
        "price": "{{ product.selling_price }}",
        "priceCurrency": "INR",
        "availability": "{% if product.in_stock > 0 %}https://schema.org/InStock{% else %}https://schema.org/OutOfStock{% endif %}"
    }
    {% if product.prod_images.exists %}
    ,"image": "{{ product.prod_images.first.product_image.url }}"
    {% endif %}
}
</script>
{% endblock %}

{% block content %}
<div class="product-detail">
    <div class="product-header">
        <h1>{{ product.product_name }}</h1>
        <p class="price">₹{{ product.selling_price }}</p>
        {% if product.mrp_price > product.selling_price %}
        <p class="mrp">MRP: ₹{{ product.mrp_price }}</p>
        {% endif %}
    </div>

    <div class="product-images">
        {% for image in product.prod_images.all %}
        <img src="{{ image.product_image.url }}" alt="{{ product.product_name }} image {{ forloop.counter }}" class="product-image">
        {% endfor %}
    </div>

    <div class="product-meta">
        <p>Brand: {{ product.brand_name }}</p>
        <p>Category: {{ product.product_category }}</p>
        
        <div class="swadeshi-labels">
            {% if product.swadeshi_made %}
            <p>This product is {{ product.get_swadeshi_made_display }}</p>
            {% endif %}
            {% if product.swadeshi_brand %}
            <p>This brand is {{ product.get_swadeshi_brand_display }}</p>
            {% endif %}
        </div>

        {% if product.promotion_link %}
        <p>Demo: <a href="{{ product.promotion_link }}" target="_blank">View Demo</a></p>
        {% endif %}

        <p class="stock-status">
            {% if product.in_stock > 0 %}
            <span class="in-stock">In Stock ({{ product.in_stock }} available)</span>
            {% else %}
            <span class="out-of-stock">Out of Stock</span>
            {% endif %}
        </p>
    </div>

    {% if delivery_settings and delivery_settings.delivery_locations %}
    <div class="delivery-info">
        <h2>Delivery Information</h2>
        <p>Available for delivery in: {{ delivery_settings.delivery_locations }}</p>
    </div>
    {% endif %}

    {% if store_location %}
    <div class="store-location">
        <h2>Store Location</h2>
        {% if store_location.isphysicalstore %}
        <p class="physical-store">Physical Store Available</p>
        {% endif %}
        <address>
            {{ store_location.address }}<br>
            {{ store_location.city }}, {{ store_location.state }}<br>
            PIN: {{ store_location.pincode }}
        </address>
    </div>
    {% endif %}

    {% if product.product_description %}
    <div class="description">
        <h2>Description</h2>
        <p>{{ product.product_description }}</p>
    </div>
    {% endif %}

    {% if hashtags %}
    <div class="hashtags">
        <h2>Tags</h2>
        {% for tag in hashtags %}
        <span class="tag">#{{ tag }}</span>
        {% endfor %}
    </div>
    {% endif %}

    {% if product_comments %}
    <div class="comments">
        <h2>Customer Reviews</h2>
        {% for comment in product_comments %}
        <div class="comment">
            <p class="comment-text">{{ comment.comment_text }}</p>
            <p class="comment-meta">Posted {{ comment.created_date|timesince }} ago</p>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endblock %}