from django.db import models
from django.utils.translation import gettext_lazy as _

class SubscriptionPayment(models.Model):
    class PaymentStatus(models.TextChoices):
        SUCCESS = "SUCCESS", _("Success")
        FAILED = "FAILED", _("Failed")
        PENDING = "PENDING", _("Pending")

    payment_id = models.AutoField(primary_key=True)
    payment_reference = models.CharField(max_length=100, unique=True)  # Razorpay payment ID
    subscription_id = models.CharField(max_length=100)  # Razorpay subscription ID
    invoice_id = models.CharField(max_length=100)  # Razorpay invoice ID
    
    # References to our entities
    store_reference = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        db_column="store_reference",
        related_name="subscription_payments"
    )
    user_reference = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="user_reference",
        db_column="user_reference",
        related_name="subscription_payments"
    )
    
    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default="INR")
    status = models.CharField(max_length=10, choices=PaymentStatus.choices)
    payment_method = models.CharField(max_length=20)
    
    # Fee details
    fee_amount = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2)
    fee_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    payment_date = models.DateTimeField()
    billing_period_start = models.DateTimeField()
    billing_period_end = models.DateTimeField()

    class Meta:
        db_table = '"user"."subscription_payment"'
        indexes = [
            models.Index(fields=['payment_reference']),
            models.Index(fields=['subscription_id']),
            models.Index(fields=['invoice_id']),
        ]
