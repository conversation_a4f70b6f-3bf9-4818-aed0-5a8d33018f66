# Generated by Django 4.2.7 on 2024-07-16 09:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0074_infinityrewardshistory_reward_reference"),
    ]

    operations = [
        migrations.RenameField(
            model_name="infinityrewardshistory",
            old_name="receiver",
            new_name="receiver_reference",
        ),
        migrations.RenameField(
            model_name="infinityrewardshistory",
            old_name="sender",
            new_name="sender_reference",
        ),
        migrations.AddField(
            model_name="infinityrewardshistory",
            name="event_reference",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="infinityrewardshistory",
            name="transaction_type",
            field=models.CharField(
                blank=True,
                choices=[("CREDIT", "Credit"), ("DEBIT", "Debit")],
                max_length=7,
                null=True,
            ),
        ),
    ]
