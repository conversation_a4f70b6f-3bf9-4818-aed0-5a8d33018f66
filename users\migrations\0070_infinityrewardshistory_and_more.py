# Generated by Django 4.2.7 on 2024-07-13 06:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0069_userpoints_storepoints"),
    ]

    operations = [
        migrations.CreateModel(
            name="InfinityRewardsHistory",
            fields=[
                ("reward_id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "reward_value",
                    models.CharField(blank=True, max_length=15, null=True),
                ),
                (
                    "reward_ratio",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                (
                    "reward_type",
                    models.CharField(
                        choices=[("REFERRAL", "Referral"), ("GIFT", "Gift")],
                        max_length=10,
                    ),
                ),
                ("invite_code", models.CharField(blank=True, max_length=15, null=True)),
                ("sent_user", models.CharField(blank=True, max_length=20, null=True)),
                (
                    "received_user",
                    models.Char<PERSON>ield(blank=True, max_length=20, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "rewards histories",
                "db_table": '"user"."infinity_rewards_history"',
            },
        ),
        migrations.AlterField(
            model_name="notifications",
            name="notification_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING", "onboarding"),
                    ("PRODUCT_VERSION_CHANGED", "Product version changed"),
                    ("STORE_CREATED", "New store created near you"),
                    ("NEW_ORDER", "New order"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("ORDER_SHIPPED", "Order shipped"),
                    ("SHIPPING_PACAKGE_UPDATED", "Shipping package updated"),
                    ("DELIVERY_OTP", "Delivery otp"),
                    ("PACKAGE_DELIVERED", "Package delivered"),
                    ("DELIVERY_FAILED", "Delivery failed"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("SELLER_CANCELLED_ORDER", "Seller cancelled order"),
                    ("BUYER_CANCELLED_ORDER", "Buyer cancelled order"),
                    ("AUTO_CANCELLED", "Auto cancelled"),
                    ("RETURN_REQUESTED", "Return requested"),
                    ("RETURN_ACCEPTED", "Return accepted"),
                    ("RETURN_RECEIVED", "Return received"),
                    ("REFUND_INITIATED", "Refund initiated"),
                    ("REFUND_HOLD", "Refund hold"),
                    ("REFUND_RELEASED", "Refund released"),
                    ("RETURN_OTP", "Return otp"),
                    ("COMMENTED", "Commented"),
                    ("CONTENT_LIKED", "content_liked"),
                    ("SOMEONE_FOLLOWED_ENTIY", "Someone_followed_entity"),
                    ("SOMEONE_COMMENTED_ON_CONTENT", "Someone_commented_on_content"),
                    ("SOMEONE_REPOSTED_CONTENT", "Someone reposted content"),
                    (
                        "SOMEONE_SIGNED_UP_WITH_YOUR_CODE",
                        "Someone signed up with your code",
                    ),
                    (
                        "YOU_SIGNED_UP_WITH_SOMEONES_CODE",
                        "You signed up with someones code",
                    ),
                    (
                        "YOU_SIGNED_UP_WITH_SWADESIC_CODE",
                        "You signed up with Swadesic code",
                    ),
                ],
                max_length=100,
                null=True,
            ),
        ),
    ]
