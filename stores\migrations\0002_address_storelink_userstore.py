# Generated by Django 3.2.13 on 2022-07-26 16:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserStore",
            fields=[
                ("userstoreid", models.AutoField(primary_key=True, serialize=False)),
                ("is_following", models.BooleanField(default=False)),
                ("is_visited", models.BooleanField(default=False)),
                ("modified_date", models.DateTimeField(auto_now=True)),
                (
                    "storeid",
                    models.ForeignKey(
                        blank=True,
                        db_column="storeid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.store",
                    ),
                ),
                (
                    "userid",
                    models.ForeignKey(
                        blank=True,
                        db_column="userid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "user stores",
                "db_table": '"store"."user_store"',
            },
        ),
        migrations.CreateModel(
            name="Storelink",
            fields=[
                ("storelinkid", models.AutoField(primary_key=True, serialize=False)),
                ("storelink_name", models.CharField(max_length=100)),
                ("store_link", models.CharField(max_length=255)),
                (
                    "storeid",
                    models.ForeignKey(
                        db_column="storeid",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="storelinks",
                        to="stores.store",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "store links",
                "db_table": '"store"."store_link"',
            },
        ),
        migrations.CreateModel(
            name="Address",
            fields=[
                ("addressid", models.AutoField(primary_key=True, serialize=False)),
                ("address", models.CharField(blank=True, max_length=255, null=True)),
                ("city", models.CharField(blank=True, max_length=100, null=True)),
                ("pincode", models.CharField(blank=True, max_length=6, null=True)),
                ("state", models.CharField(blank=True, max_length=100, null=True)),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("default_address", models.BooleanField(default=False)),
                (
                    "storeid",
                    models.ForeignKey(
                        db_column="storeid",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.store",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "address",
                "db_table": '"store"."address"',
            },
        ),
    ]
