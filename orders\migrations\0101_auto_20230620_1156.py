# Generated by Django 3.2.13 on 2023-06-20 06:26

from django.db import migrations


def data_population(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in "delivery_settings_id", "refund_warranty_id" column, of suborder table.

    Suborder = apps.get_model("orders", "suborder")
    DeliverySettings = apps.get_model("stores", "DeliverySettings")
    RefundAndWarranty = apps.get_model("stores", "RefundAndWarranty")
    for item in Suborder.objects.all():
        store_reference = item.store_reference
        product_reference = item.product_reference
        delivery_settings_version = item.delivery_settings_version
        refund_warranty_version = item.refund_warranty_version
        if DeliverySettings.objects.filter(store_reference=store_reference,
                                           product_reference=product_reference,
                                           delivery_settings_version=delivery_settings_version).exists():
            del_instance = DeliverySettings.objects.get(store_reference=store_reference,
                                                           product_reference=product_reference,
                                                           delivery_settings_version=delivery_settings_version)
            delivery_settings_id = del_instance.deliverysettingid

        elif DeliverySettings.objects.filter(store_reference=store_reference,
                                             product_reference__isnull=True,
                                             delivery_settings_version=delivery_settings_version).exists():
            del_instance = DeliverySettings.objects.get(store_reference=store_reference,
                                                           product_reference__isnull=True,
                                                           delivery_settings_version=delivery_settings_version)
            delivery_settings_id = del_instance.deliverysettingid

        if RefundAndWarranty.objects.filter(store_reference=store_reference,
                                            product_reference=product_reference,
                                            refund_warranty_version=refund_warranty_version).exists():
            ref_instance = RefundAndWarranty.objects.get(store_reference=store_reference,
                                                            product_reference=product_reference,
                                                            refund_warranty_version=refund_warranty_version)
            refund_warranty_id = ref_instance.refundandwarrantyid

        elif RefundAndWarranty.objects.filter(store_reference=store_reference,
                                              product_reference__isnull=True,
                                              refund_warranty_version=refund_warranty_version).exists():
            ref_instance = RefundAndWarranty.objects.get(store_reference=store_reference,
                                                            product_reference__isnull=True,
                                                            refund_warranty_version=refund_warranty_version)
            refund_warranty_id = ref_instance.refundandwarrantyid
        item.delivery_settings_id = delivery_settings_id
        item.refund_warranty_id = refund_warranty_id
        item.save(update_fields=["delivery_settings_id", "refund_warranty_id"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    Suborder = apps.get_model("orders", "suborder")
    for item in Suborder.objects.all():
        item.delivery_settings_id = None
        item.refund_warranty_id = None
        item.save(update_fields=["delivery_settings_id", "refund_warranty_id"])


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0100_auto_20230619_1410'),
    ]

    operations = [migrations.RunPython(data_population, reverse_func)
    ]
