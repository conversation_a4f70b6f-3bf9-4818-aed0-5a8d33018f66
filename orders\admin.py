from django.contrib import admin

from .order_api.models import Order, OrderLifeCycle, SubOrder, ShippingHistory, OrderConfiguration,\
    LogisticPartner, OrderOtp, RefundedAmount, OrderRating
from .payment_api.models import SubOrderPaymentDetails, OrderPaymentDetailsLifeCycle
from .payout_api.models import OrderPayout, PayoutBalance, PayoutTransactions
from .cart_api.models import CartItem
from .escalation_api.models import Escalation
from .swadesic_shipping.models import StoreShippingAccountBalance, SwadesicShippingPackageDetails

admin.site.register(Order)
admin.site.register(OrderLifeCycle)
admin.site.register(SubOrder)
admin.site.register(CartItem)
admin.site.register(SubOrderPaymentDetails)
admin.site.register(OrderPaymentDetailsLifeCycle)
admin.site.register(OrderPayout)
admin.site.register(PayoutBalance)
admin.site.register(PayoutTransactions)
admin.site.register(ShippingHistory)
admin.site.register(Escalation)
admin.site.register(OrderConfiguration)
admin.site.register(LogisticPartner)
admin.site.register(OrderOtp)
admin.site.register(RefundedAmount)
admin.site.register(OrderRating)
admin.site.register(StoreShippingAccountBalance)
admin.site.register(SwadesicShippingPackageDetails)