import requests
import random
import string
import logging
from decouple import config

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def create_store_messaging_user(store, storehandle=None):
    """
    Create user in Swadesic messaging server and return username, password, and token if successful
    """
    try:
        # Generate username based on user reference
        username = store.storehandle
        token1=None
        token2=None

        # Generate a secure random password
        password = ''.join(random.choices(string.ascii_letters + string.digits + string.punctuation, k=12))

        # Prepare registration data
        register_data = {
            "username": username,
            "user_reference": store.store_reference,
            "email": f"{store.store_reference}@swadesic.com",
            "password": password
        }

        # Make API request to messaging server 1
        # register_response1 = requests.post(
        #     f"{config('JS_MESSAGING_SERVER_URL', default='http://localhost:3000')}/api/auth/register",
        #     json=register_data
        # )
        #
        # if register_response1.status_code == 201:
        #     # Get token from registration response
        #     token1 = register_response1.json().get('token')
        #     logger.info(f"Successfully registered user {username} in messaging server1")
        #     if not token1:
        #         logger.error(f"No token received for user {username}")
        # else:
        #     logger.error(f"Failed to register user in messaging server2: {register_response1.status_code} - {register_response1.text}")

        # Messaging server 2
        register_response = requests.post(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/auth/register",
            json=register_data
        )

        if register_response.status_code == 201:
            login_response = requests.post(
                f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/auth/login",
                json={
                    "email": f'{store.store_reference}@swadesic.com',
                    "password": password
                }
            )
            token2 = login_response.json().get('token')
            new_messaging_user_id = login_response.json().get('user', {}).get('messaging_user_id')
        else:
            logger.error(f"Failed to login user in messaging server2")
            return None, None, None, None

        return username, password, token2, new_messaging_user_id
    except Exception as e:
        logger.error(f"Error creating messaging server user: {str(e)}")
        return None, None, None, None


def create_store_ai_messaging_user(store):
    try:
        # Generate username based on user reference
        username = store.store_reference.replace('S','SAI_')
        token = None
        user_id = None

        # Generate a secure random password
        password = ''.join(random.choices(string.ascii_letters + string.digits + string.punctuation, k=12))

        # Prepare registration data
        register_data = {
            "username": username,
            "user_reference": username,
            "email": f"{username}@swadesic.com",
            "password": password
        }

        # Make API request to messaging server 1
        register_response = requests.post(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/auth/register",
            json=register_data
        )

        if register_response.status_code == 201:
            # Get token from registration response
            token = register_response.json().get('token')
            logger.info("response", register_response.json())
            user_id = register_response.json().get('user', {}).get('id')

            logger.info(f"Successfully registered user {username} in messaging server1")
            if not token:
                logger.error(f"No token received for user {username}")
        else:
            logger.error(f"Failed to register user in messaging server2: {register_response.status_code} - {register_response.text}")

        return user_id, username, token

    except Exception as e:
        logger.error(f"Error creating messaging server user: {str(e)}")
        return None, None, None


def create_store_admin_group_chat(store):
    """
    Create a group chat between static user and store when store is created.
    Store becomes ADMIN, static user becomes MEMBER.
    Uses messaging server API calls.
    """
    try:
        # Get static user ID from environment
        static_user_id = config('STATIC_USER_ID', default=None)
        if not static_user_id:
            logger.error("STATIC_USER_ID not found in environment variables")
            return None

        # Check if store has messaging user ID and token
        if not store.new_messaging_user_id or not store.new_messaging_token:
            logger.error(f"Store {store.store_reference} does not have messaging user ID or token")
            return None

        # Create group chat name
        store_name = store.store_name or store.storehandle or store.store_reference
        chat_name = f"{store_name}'s updates"

        # Create group chat using messaging API
        create_chat_response = requests.post(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/chats/create_chat",
            headers={
                'Authorization': f'Bearer {store.new_messaging_token}',
                'Content-Type': 'application/json'
            },
            json={
                'chat_type': 'GROUP',
                'chat_name': chat_name,
                'member_ids': [static_user_id],
                'message_access': 'ADMIN_ONLY'
            },
            timeout=10
        )

        if create_chat_response.status_code != 201:
            logger.error(f"Failed to create group chat for store {store.store_reference}: {create_chat_response.status_code} - {create_chat_response.text}")
            return None

        chat_data = create_chat_response.json()
        chat_id = chat_data.get('chat', {}).get('chat_id')

        if not chat_id:
            logger.error(f"No chat_id received in response for store {store.store_reference}")
            return None

        # Update store role to ADMIN using messaging API
        update_store_role_response = requests.put(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/chats/chatId={chat_id}/members/userId={store.new_messaging_user_id}/role",
            headers={
                'Authorization': f'Bearer {store.new_messaging_token}',
                'Content-Type': 'application/json'
            },
            json={
                'role_type': 'ADMIN'
            },
            timeout=10
        )

        if update_store_role_response.status_code != 200:
            logger.warning(f"Failed to update store role to ADMIN for chat {chat_id}: {update_store_role_response.status_code} - {update_store_role_response.text}")
            # Don't return None here as the chat was created successfully

        # Update static user role to MEMBER using messaging API
        update_visitor_role_response = requests.put(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/chats/chatId={chat_id}/members/userId={static_user_id}/role",
            headers={
                'Authorization': f'Bearer {store.new_messaging_token}',
                'Content-Type': 'application/json'
            },
            json={
                'role_type': 'MEMBER'
            },
            timeout=10
        )

        if update_visitor_role_response.status_code != 200:
            logger.warning(f"Failed to update static user role to MEMBER for chat {chat_id}: {update_visitor_role_response.status_code} - {update_visitor_role_response.text}")
            # Don't return None here as the chat was created successfully

        logger.info(f"Successfully created group chat {chat_id} for store {store.store_reference} (Store: ADMIN, Static User: MEMBER)")
        return chat_id

    except Exception as e:
        logger.error(f"Error creating group chat for store {store.store_reference}: {str(e)}")
        return None
