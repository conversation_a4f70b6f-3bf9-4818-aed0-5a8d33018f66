# Generated by Django 4.2.7 on 2024-06-11 05:59

from django.db import migrations, models


def set_is_email_verified(apps, schema_editor):
    User = apps.get_model("users", "User")
    User.objects.filter(is_phonenumber_verified=True, email__isnull=False).update(is_email_verified=True)


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0065_user_registration_method"),
    ]

    operations = [
        migrations.RenameField(
            model_name="user",
            old_name="is_verified",
            new_name="is_phonenumber_verified",
        ),
        migrations.AddField(
            model_name="user",
            name="is_email_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(set_is_email_verified),

    ]
