from rest_framework import serializers
import datetime
from django.utils.timezone import utc
from django.db.models import Prefetch
from ..models import Product, ProductImages, ProductVersion, ProductVariants
from ..comment_and_reply_api.models import Comment
from stores.store_api.models import Store
from GraphDB.models import Neo4jEntity, Neo4jContent
from stores.store_settings_api.models import TrustCenter, RefundAndWarranty, DeliverySettings, DeliveryLocations
import pytz
import logging
from users.user_api.models import User
from general.views import <PERSON><PERSON>tatusChecker
from django.db.models.functions import Upper
from django.core.cache import cache

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class ProductImagesSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImages
        fields = [
            "productimageid",
            "product_reference",
            "product_image",
            "created_by",
            "modified_by",
            "reorder",
            "is_deleted"
        ]


def get_return_info(obj, req_type):
    return_info = None
    if req_type == "return_period":
        return_info = None
    elif req_type == "return_conditions":
        return_info = []
    elif req_type == "return_pick_up":
        return_info = None
    elif req_type == "return_cost_on":
        return_info = None

    if RefundAndWarranty.objects.filter(store_reference=obj.store_reference, product_reference=obj.product_reference, is_deleted=False).exists():
        refund_instance = RefundAndWarranty.objects.filter(store_reference=obj.store_reference, product_reference=obj.product_reference, is_deleted=False).last()

    elif RefundAndWarranty.objects.filter(store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False).exists():
        refund_instance = RefundAndWarranty.objects.filter(store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False).last()
    else:
        refund_instance = None

    if refund_instance:
        if req_type == "return_period":
            if refund_instance.return_type:
                return_info = refund_instance.return_period
            else:
                return_info = None

        elif req_type == "return_conditions":
            return_conditions = refund_instance.return_conditions
            if return_conditions:
                return_info = return_conditions.split('|')

        elif req_type == "return_pick_up":
            return_pick_up = refund_instance.return_pickup
            if return_pick_up:
                return_info = return_pick_up

        elif req_type == "return_cost_on":
            if refund_instance.return_cost_on_customer:
                return_info = "customer"
            elif refund_instance.return_cost_on_seller:
                return_info = "seller"
    return return_info


def get_delivery_info(obj, req_type):

    if req_type == "delivery_by":
        delivery_info = 0
    else:
        delivery_info = None

    if DeliverySettings.objects.filter(store_reference=obj.store_reference, product_reference=obj.product_reference, is_deleted=False).exists():
        delivery_settings_instance = DeliverySettings.objects.filter(store_reference=obj.store_reference, product_reference=obj.product_reference, is_deleted=False).last()

    elif DeliverySettings.objects.filter(store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False).exists():
        delivery_settings_instance = DeliverySettings.objects.filter(store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False).last()
    else:
        delivery_settings_instance = None

    if delivery_settings_instance:
        if req_type == "delivery_settings_type":
            if delivery_settings_instance.product_reference:
                delivery_info = "product_settings"
            else:
                delivery_info = "store_settings"

        if req_type == "delivery_by":
            delivery_info = delivery_settings_instance.time_to_deliver

        if req_type == "delivery_partner" and delivery_settings_instance.deliverymethod_logistics:
            delivery_info = "logistics"

        if req_type == "log_partner_name":
            if delivery_settings_instance.deliverymethod_logistics:
                delivery_info = delivery_settings_instance.default_logistic_partner

        if req_type == "delivery_fee":

            if delivery_settings_instance.deliveryfee_value:
                df_value = delivery_settings_instance.deliveryfee_value
                df_value_type = delivery_settings_instance.deliveryfee_valuetype
                if df_value_type == "per product":
                    delivery_info = "₹{} {}".format(df_value, "per quantity")
                else:
                    if delivery_settings_instance.product_reference:
                        delivery_info = "₹{} {}".format(df_value, "per product order")
                    else:
                        delivery_info = "₹{} {}".format(df_value, "per store order")
    return delivery_info


def convert_delivery_locations_to_pincode(delivery_locations):
    queryset = DeliveryLocations.objects.all()
    all_states = set(queryset.values_list(Upper('state'), flat=True).distinct())
    all_cities = set(queryset.values_list(Upper('city'), flat=True).distinct())
    
    list_of_locations = delivery_locations.split("|")
    lst = []
    
    for elem in list_of_locations:
        elem_upper = elem.upper()
        if elem_upper in all_states:
            pincode = list(queryset.filter(state__iexact=elem).values_list("pincode", flat=True))
            lst.extend(pincode)
        elif elem_upper in all_cities:
            pincode = list(queryset.filter(city__iexact=elem).values_list("pincode", flat=True))
            lst.extend(pincode)
        else:
            lst.append(elem)
    
    return lst


class GetProductListSerializer(serializers.ModelSerializer):
    saved_or_not = serializers.BooleanField(default=False)
    prod_images = ProductImagesSerializer(many=True)
    storehandle = serializers.CharField(source="store_reference.storehandle")
    store_icon = serializers.ImageField(source="store_reference.icon")
    store_name = serializers.CharField(source="store_reference.store_name")
    location = serializers.CharField(default=None)
    # comment_count = serializers.SerializerMethodField("get_comment_count")
    updated_date = serializers.SerializerMethodField("get_updated_date")
    created_date = serializers.SerializerMethodField("get_created_date")
    return_period = serializers.SerializerMethodField("get_return_period")
    return_cost_on = serializers.SerializerMethodField("get_return_cost_on")
    return_conditions = serializers.SerializerMethodField("get_return_conditions")
    return_pick_up = serializers.SerializerMethodField("get_return_pick_up")
    delivery_by = serializers.SerializerMethodField("get_delivery_by")
    delivery_partner = serializers.SerializerMethodField("get_delivery_partner")
    delivery_fee = serializers.SerializerMethodField("get_delivery_fee")
    logistic_partner_name = serializers.SerializerMethodField("get_logistic_partner_name")
    delivery_settings_type = serializers.SerializerMethodField("get_delivery_settings_type")
    deliverability = serializers.SerializerMethodField("get_deliverability")
    swadeshi_owned = serializers.SerializerMethodField("get_swadeshi_owned")
    like_status = serializers.SerializerMethodField("get_like_status")
    save_status = serializers.SerializerMethodField("get_save_status")
    repost_status = serializers.SerializerMethodField("get_repost_status")
    refund_responsibility = serializers.SerializerMethodField("get_refund_responsibility")

    is_buy_enabled = serializers.SerializerMethodField("get_is_product_buyable")
    product_status_message = serializers.SerializerMethodField("get_product_status_message")

    content_category = serializers.SerializerMethodField("get_content_category")
    content_headers = serializers.SerializerMethodField("get_content_headers")
    content_header_text = serializers.SerializerMethodField("get_content_header_text")
    fulfillment_options = serializers.SerializerMethodField("get_fulfillment_options")
    disclaimer_message = serializers.SerializerMethodField("get_disclaimer_message")

    class Meta:
        model = Product
        fields = [
            "productid",
            "product_reference",
            "product_name",
            "product_description",
            "product_category",
            "product_version",
            "updated_date",
            "created_date",
            "promotion_link",
            "brand_name",
            "mrp_price",
            "selling_price",
            "location",
            "count_of_ratings",
            "rating",
            "in_stock",
            "hashtags",
            "store_reference",
            "storeid",
            "store_icon",
            "store_name",
            "storehandle",
            "return_period",
            "return_cost_on",
            "return_conditions",
            "return_pick_up",
            "delivery_by",
            "delivery_partner",
            "delivery_fee",
            "delivery_settings_type",
            "logistic_partner_name",
            "saved_or_not",
            "deliverability",
            "prod_images",
            "swadeshi_owned",
            "swadeshi_brand",
            "swadeshi_made",
            "deleted",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "orders_count",
            "returns_count",
            "cancels_count",
            "like_status",
            "save_status",
            "repost_status",
            "refund_responsibility",
            "is_buy_enabled",
            "product_status_message",
            "content_category",
            "content_headers",
            "content_header_text",
            "fulfillment_options",
            "disclaimer_message",
            "analytics_view_count",
            "product_slug",
            "product_code"
        ]

    def get_disclaimer_message(self, obj):
        if obj.store_reference.store_supporters_count < 30 or obj.store_reference.store_order_count < 5:
            return "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        else:
            return ""

    def get_fulfillment_options(self, obj):
        product_delvery_settings = DeliverySettings.objects.filter(product_reference=obj.product_reference,
                                                                   is_deleted=False).last()
        if product_delvery_settings:
            return product_delvery_settings.fulfillment_options
        else:
            store_delvery_settings = DeliverySettings.objects.filter(store_reference=obj.store_reference,
                                                                     is_deleted=False).last()
            return store_delvery_settings.fulfillment_options

    
    def _get_content_attribute(self, obj, attribute_method):
        """Helper method to get content attributes from Neo4j
        
        Args:
            obj: Product instance
            attribute_method: String name of the method to call on Neo4jContent
        
        Returns:
            Result of the method call or None if there's an error
        """
        try:
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            method = getattr(content_node, attribute_method)
            return method(visitor_reference=self.context.get("visitor_reference"))
        except Exception as e:
            return None

    def get_content_category(self, obj):
        return self._get_content_attribute(obj, 'get_content_category')

    def get_content_headers(self, obj):
        return self._get_content_attribute(obj, 'get_content_headers')

    def get_content_header_text(self, obj):
        return self._get_content_attribute(obj, 'get_content_header_text')


    def get_is_product_buyable(self, obj):
        checker = BuyStatusChecker(store=obj.store_reference, product=obj, user_pincode=self.context.get("user_pincode"))
        return checker.is_buy_enabled()

    def get_product_status_message(self, obj):
        checker = BuyStatusChecker(store=obj.store_reference, product=obj, user_pincode=self.context.get("user_pincode"))
        return checker.get_status_message()

    def get_refund_responsibility(self, obj):
        refund_responsibility = [
            {
                "item_heading": "Refund policy",
                "item_text": "Full refund based on conditions",
                "item_subtext": ""
            },
            {
                "item_heading": "If the seller cancels or if the order is auto-canceled",
                "item_text": "Full Refund",
                "item_subtext": ""
            },
            {
                "item_heading": "If the customer cancels before order confirmation",
                "item_text": "Partial Refund",
                "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
            },
            {
                "item_heading": "On product returns",
                "item_text": "Full Refund",
                "item_subtext": ""
            }
        ]
        return refund_responsibility

    def get_deliverability(self, obj):
        deliverability = False
        if self.context.get("user_pincode"):
            user_pincode = self.context.get("user_pincode")
            if DeliverySettings.objects.filter(
                    product_reference=obj.product_reference, store_reference=obj.store_reference, is_deleted=False
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    product_reference=obj.product_reference, store_reference=obj.store_reference, is_deleted=False
                ).last()
            elif DeliverySettings.objects.filter(
                    store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False
                ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False
                ).last()
            else:
                delivery_settings_instance = None

            if delivery_settings_instance:
                delivery_locations = delivery_settings_instance.delivery_locations
                if delivery_settings_instance.fulfillment_options == 'IN_STORE_PICKUP':
                    return deliverability

                if delivery_locations:
                    list_of_pincode = convert_delivery_locations_to_pincode(delivery_locations)
                    if user_pincode in list_of_pincode:
                        deliverability = True
        return deliverability

    def get_config_receive_orders(self,obj):
        try:
            store_config = obj.store_reference.store_configurations.get()
            return store_config.enable_orders
        except Exception as e:
            return False

    def get_like_status(self, obj):
        like_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            like_status = content_node.liked_by.is_connected(entity_node)
        return like_status

    def get_save_status(self, obj):
        save_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            save_status = content_node.saved_by.is_connected(entity_node)
        return save_status

    def get_repost_status(self, obj):
        repost_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            repost_status = content_node.reposted_by.is_connected(entity_node)
        return repost_status

    @staticmethod
    def setup_eager_loading(queryset):
        """ Perform necessary eager loading of data. """
        queryset = queryset.prefetch_related(Prefetch('prod_images', queryset=ProductImages.objects.filter(is_deleted=False)), 'product_comments', 'saved_products')
        queryset = queryset.select_related('store_reference')
        return queryset

    def get_delivery_fee(self, obj):
        return get_delivery_info(obj, "delivery_fee")

    def get_delivery_settings_type(self, obj):
        return get_delivery_info(obj, "delivery_settings_type")

    def get_delivery_by(self, obj):
        return get_delivery_info(obj, "delivery_by")

    def get_delivery_partner(self, obj):
        return get_delivery_info(obj, "delivery_partner")

    def get_swadeshi_owned(self,obj):
        if TrustCenter.objects.filter(store_reference=obj.store_reference).exists ():
            trust_center = TrustCenter.objects.get (store_reference=obj.store_reference)
            swadeshi_owned = trust_center.swadeshi_owned
        else:
            swadeshi_owned = None
        return swadeshi_owned

    def get_return_period(self, obj):
        return get_return_info(obj, "return_period")

    def get_return_conditions(self, obj):
        return get_return_info(obj, "return_conditions")

    def get_return_cost_on(self, obj):
        return get_return_info(obj, "return_cost_on")

    def get_return_pick_up(self, obj):
        return get_return_info(obj, "return_pick_up")

    def get_logistic_partner_name(self, obj):
        return get_delivery_info(obj, "log_partner_name")

    def get_created_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    def get_updated_date(self, obj):
        date = obj.modified_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")


class GetProductSerializer(serializers.ModelSerializer):
    saved_or_not = serializers.BooleanField(default=False)
    prod_images = ProductImagesSerializer(many=True, source="available_product_images")
    storehandle = serializers.CharField(source="store_reference.storehandle")
    store_icon = serializers.ImageField(source="store_reference.icon")
    store_name = serializers.CharField(source="store_reference.store_name")
    config_receive_orders = serializers.SerializerMethodField("get_config_receive_orders")
    is_test_store = serializers.BooleanField(source="store_reference.is_test_store")
    location = serializers.SerializerMethodField("get_location")
    # comment_count = serializers.SerializerMethodField("get_comment_count")
    updated_date = serializers.SerializerMethodField("get_updated_date")
    created_date = serializers.SerializerMethodField("get_created_date")
    return_period = serializers.SerializerMethodField("get_return_period")
    return_conditions = serializers.SerializerMethodField("get_return_conditions")
    return_cost_on = serializers.SerializerMethodField("get_return_cost_on")
    return_pick_up = serializers.SerializerMethodField("get_return_pick_up")
    delivery_by = serializers.SerializerMethodField("get_delivery_by")
    delivery_partner = serializers.SerializerMethodField("get_delivery_partner")
    delivery_fee = serializers.SerializerMethodField("get_delivery_fee")
    delivery_settings_type = serializers.SerializerMethodField("get_delivery_settings_type")
    logistic_partner_name = serializers.SerializerMethodField("get_logistic_partner_name")
    deliverability = serializers.SerializerMethodField("get_deliverability")
    swadeshi_owned = serializers.SerializerMethodField("get_swadeshi_owned")
    like_status = serializers.SerializerMethodField("get_like_status")
    save_status = serializers.SerializerMethodField("get_save_status")
    repost_status = serializers.SerializerMethodField("get_repost_status")
    refund_responsibility = serializers.SerializerMethodField("get_refund_responsibility")
    is_buy_enabled = serializers.SerializerMethodField("get_is_product_buyable")
    product_status_message = serializers.SerializerMethodField("get_product_status_message")
    content_category = serializers.SerializerMethodField("get_content_category")
    content_headers = serializers.SerializerMethodField("get_content_headers")
    content_header_text = serializers.SerializerMethodField("get_content_header_text")
    subscription_type = serializers.SerializerMethodField("get_subscription_type")
    fulfillment_options = serializers.SerializerMethodField("get_fulfillment_options")
    disclaimer_message = serializers.SerializerMethodField("get_disclaimer_message")

    class Meta:
        model = Product
        fields = [
            "productid",
            "product_reference",
            "product_name",
            "product_description",
            "product_category",
            "product_version",
            "updated_date",
            "created_date",
            "promotion_link",
            "brand_name",
            "mrp_price",
            "selling_price",
            "location",
            "count_of_ratings",
            "rating",
            "in_stock",
            "hashtags",
            "store_reference",
            "storeid",
            "store_icon",
            "store_name",
            "storehandle",
            "return_period",
            "return_cost_on",
            "return_conditions",
            "return_pick_up",
            "delivery_by",
            "delivery_partner",
            "delivery_fee",
            "delivery_settings_type",
            "logistic_partner_name",
            "saved_or_not",
            "deliverability",
            "prod_images",
            "swadeshi_owned",
            "swadeshi_made",
            "swadeshi_brand",
            "deleted",
            "config_receive_orders",
            "is_test_store",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "orders_count",
            "returns_count",
            "cancels_count",
            "like_status",
            "save_status",
            "repost_status",
            "refund_responsibility",
            "is_buy_enabled",
            "product_status_message",
            "content_category",
            "content_headers",
            "content_header_text",
            "subscription_type",
            "fulfillment_options",
            "disclaimer_message",
            "analytics_view_count",
            "product_slug",
            "product_code"
        ]

    def get_disclaimer_message(self, obj):
        if obj.store_reference.store_supporters_count < 30 or obj.store_reference.store_order_count < 5:
            return "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        else:
            return ""

    def get_fulfillment_options(self, obj):
        product_delvery_settings = DeliverySettings.objects.filter(product_reference=obj.product_reference,
                                                                   is_deleted=False).last()
        if product_delvery_settings:
            return product_delvery_settings.fulfillment_options
        else:
            store_delvery_settings = DeliverySettings.objects.filter(store_reference=obj.store_reference,
                                                                     is_deleted=False).last()
            return store_delvery_settings.fulfillment_options

    def get_subscription_type(self, obj):
        return obj.store_reference.subscription_type

    def _get_content_attribute(self, obj, attribute_method):
        """Helper method to get content attributes from Neo4j
        
        Args:
            obj: Product instance
            attribute_method: String name of the method to call on Neo4jContent
        
        Returns:
            Result of the method call or None if there's an error
        """
        try:
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            method = getattr(content_node, attribute_method)
            return method(visitor_reference=self.context.get("visitor_reference"))
        except Exception as e:
            return None

    def get_content_category(self, obj):
        return self._get_content_attribute(obj, 'get_content_category')

    def get_content_headers(self, obj):
        return self._get_content_attribute(obj, 'get_content_headers')

    def get_content_header_text(self, obj):
        return self._get_content_attribute(obj, 'get_content_header_text')

    def get_is_product_buyable(self, obj):
        checker = BuyStatusChecker(store=obj.store_reference, product=obj, user_pincode=self.context.get("user_pincode"))
        return checker.is_buy_enabled()

    def get_product_status_message(self, obj):
        checker = BuyStatusChecker(store=obj.store_reference, product=obj, user_pincode=self.context.get("user_pincode"))
        return checker.get_status_message()

    def get_refund_responsibility(self, obj):
        refund_responsibility = [
            {
                "item_heading": "Refund policy",
                "item_text": "Full refund based on conditions",
                "item_subtext": ""
            },
            {
                "item_heading": "If the seller cancels or if the order is auto-canceled",
                "item_text": "Full Refund",
                "item_subtext": ""
            },
            {
                "item_heading": "If the customer cancels before order confirmation",
                "item_text": "Partial Refund",
                "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
            },
            {
                "item_heading": "On product returns",
                "item_text": "Full Refund",
                "item_subtext": ""
            }
        ]
        return refund_responsibility

    def get_config_receive_orders(self,obj):
        try:
            store_config = obj.store_reference.store_configurations.get()
            return store_config.enable_orders
        except Exception as e:
            return False

    def get_like_status(self, obj):
        like_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            like_status = content_node.liked_by.is_connected(entity_node)
        return like_status

    def get_save_status(self, obj):
        save_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            save_status = content_node.saved_by.is_connected(entity_node)
        return save_status

    def get_repost_status(self, obj):
        repost_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            repost_status = content_node.reposted_by.is_connected(entity_node)
        return repost_status

    @staticmethod
    def setup_eager_loading(queryset):
        """ Perform necessary eager loading of data. """
        queryset = queryset.prefetch_related('prod_images', 'product_comments')
        queryset = queryset.select_related('store_reference').prefetch_related('store_reference__store_details')
        return queryset

    def get_swadeshi_owned(self,obj):
        if TrustCenter.objects.filter (store_reference=obj.store_reference).exists ():
            trust_center = TrustCenter.objects.get (store_reference=obj.store_reference)
            swadeshi_owned = trust_center.swadeshi_owned
        else:
            swadeshi_owned = None
        return swadeshi_owned

    def get_deliverability(self, obj):
        deliverability = False
        if self.context.get("user_pincode"):
            user_pincode = self.context.get("user_pincode")
            if DeliverySettings.objects.filter(
                    product_reference=obj.product_reference, store_reference=obj.store_reference, is_deleted=False
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter (
                    product_reference=obj.product_reference, store_reference=obj.store_reference, is_deleted=False
                ).last()
            elif DeliverySettings.objects.filter (
                    store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False
                ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter (
                    store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False
                ).last()
            else:
                delivery_settings_instance = None

            if delivery_settings_instance:
                delivery_locations = delivery_settings_instance.delivery_locations

                if delivery_settings_instance.fulfillment_options == 'IN_STORE_PICKUP':
                    return deliverability

                if delivery_locations:
                    list_of_pincode = convert_delivery_locations_to_pincode(delivery_locations)
                    if user_pincode in list_of_pincode:
                        deliverability = True
        return deliverability

    def get_delivery_settings_type(self, obj):
        return get_delivery_info(obj, "delivery_settings_type")

    def get_delivery_fee(self, obj):
        return get_delivery_info(obj, "delivery_fee")

    def get_delivery_by(self, obj):
        return get_delivery_info(obj, "delivery_by")

    def get_delivery_partner(self, obj):
        return get_delivery_info(obj, "delivery_partner")

    def get_logistic_partner_name(self, obj):
        return get_delivery_info(obj, "log_partner_name")

    def get_return_period(self, obj):
        return get_return_info(obj, "return_period")

    def get_return_conditions(self, obj):
        return get_return_info(obj, "return_conditions")

    def get_return_cost_on(self, obj):
        return get_return_info(obj, "return_cost_on")

    def get_return_pick_up(self, obj):
        return get_return_info(obj, "return_pick_up")

    def get_location(self, obj):
        if TrustCenter.objects.filter(store_reference=obj.store_reference).exists ():
            trust_center = TrustCenter.objects.get(store_reference=obj.store_reference)
            location = trust_center.city
        else:
            location = None
        return location

    def get_created_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    def get_updated_date(self, obj):
        date = obj.modified_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    # def get_comment_count(self, obj):
    #     comment_count = obj.product_comments.count()
    #     return comment_count


class ProductDetailsSerializer(serializers.ModelSerializer):
    storehandle = serializers.CharField(source="store_reference.storehandle")
    store_icon = serializers.ImageField(source="store_reference.icon")
    store_name = serializers.CharField(source="store_reference.store_name")
    config_receive_orders = serializers.SerializerMethodField("get_config_receive_orders")
    is_test_store = serializers.BooleanField(source='store_reference.is_test_store')
    location = serializers.SerializerMethodField("get_location")
    updated_date = serializers.SerializerMethodField("get_updated_date")
    created_date = serializers.SerializerMethodField("get_created_date")
    return_period = serializers.SerializerMethodField("get_return_period")
    return_cost_on = serializers.SerializerMethodField("get_return_cost_on")
    return_conditions = serializers.SerializerMethodField("get_return_conditions")
    return_pick_up = serializers.SerializerMethodField ("get_return_pick_up")
    delivery_by = serializers.SerializerMethodField("get_delivery_by")
    delivery_partner = serializers.SerializerMethodField("get_delivery_partner")
    delivery_fee = serializers.SerializerMethodField("get_delivery_fee")
    delivery_settings_type = serializers.SerializerMethodField("get_delivery_settings_type")
    logistic_partner_name = serializers.SerializerMethodField("get_logistic_partner_name")
    swadeshi_owned = serializers.SerializerMethodField("get_swadeshi_owned")
    like_status = serializers.SerializerMethodField("get_like_status")
    save_status = serializers.SerializerMethodField("get_save_status")
    repost_status = serializers.SerializerMethodField("get_repost_status")
    refund_responsibility = serializers.SerializerMethodField("get_refund_responsibility")
    is_buy_enabled = serializers.SerializerMethodField("get_is_product_buyable")
    product_status_message = serializers.SerializerMethodField("get_product_status_message")

    content_category = serializers.SerializerMethodField("get_content_category")
    content_headers = serializers.SerializerMethodField("get_content_headers")
    content_header_text = serializers.SerializerMethodField("get_content_header_text")
    fulfillment_options = serializers.SerializerMethodField("get_fulfillment_options")
    disclaimer_message = serializers.SerializerMethodField("get_disclaimer_message")


    class Meta:
        model = Product
        fields = [
            "productid",
            "product_reference",
            "product_name",
            "product_description",
            "product_category",
            "product_version",
            "updated_date",
            "created_date",
            "promotion_link",
            "brand_name",
            "mrp_price",
            "selling_price",
            "location",
            "count_of_ratings",
            "rating",
            "in_stock",
            "hashtags",
            "targeted_gender",
            "store_reference",
            "storeid",
            "store_icon",
            "store_name",
            "storehandle",
            "return_period",
            "return_cost_on",
            "return_conditions",
            "return_pick_up",
            "delivery_fee",
            "delivery_by",
            "delivery_partner",
            "delivery_settings_type",
            "logistic_partner_name",
            "swadeshi_owned",
            "swadeshi_brand",
            "swadeshi_made",
            "deleted",
            "config_receive_orders",
            "is_test_store",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "orders_count",
            "returns_count",
            "cancels_count",
            "like_status",
            "save_status",
            "repost_status",
            "refund_responsibility",
            "is_buy_enabled",
            "product_status_message",
            "content_category",
            "content_headers",
            "content_header_text",
            "fulfillment_options",
            "is_promotion_enabled",
            "promotion_amount",
            "analytics_view_count",
            "disclaimer_message",
            "product_slug",
            "product_code"
        ]

    def get_disclaimer_message(self, obj):
        if obj.store_reference.store_supporters_count < 30 or obj.store_reference.store_order_count < 5:
            return "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        else:
            return ""

    def get_fulfillment_options(self, obj):
        product_delvery_settings = DeliverySettings.objects.filter(product_reference=obj.product_reference,
                                                                   is_deleted=False).last()
        if product_delvery_settings:
            return product_delvery_settings.fulfillment_options
        else:
            store_delvery_settings = DeliverySettings.objects.filter(store_reference=obj.store_reference,
                                                                     is_deleted=False).last()
            return store_delvery_settings.fulfillment_options

    def _get_content_attribute(self, obj, attribute_method):
        """Helper method to get content attributes from Neo4j
        
        Args:
            obj: Product instance
            attribute_method: String name of the method to call on Neo4jContent
        
        Returns:
            Result of the method call or None if there's an error
        """
        try:
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            method = getattr(content_node, attribute_method)
            return method(visitor_reference=self.context.get("visitor_reference"))
        except Exception as e:
            return None

    def get_content_category(self, obj):
        return self._get_content_attribute(obj, 'get_content_category')

    def get_content_headers(self, obj):
        return self._get_content_attribute(obj, 'get_content_headers')

    def get_content_header_text(self, obj):
        return self._get_content_attribute(obj, 'get_content_header_text')

    def get_is_product_buyable(self, obj):
        checker = BuyStatusChecker(store=obj.store_reference, product=obj, user_pincode=self.context.get("user_pincode"))
        return checker.is_buy_enabled()

    def get_product_status_message(self, obj):
        checker = BuyStatusChecker(store=obj.store_reference, product=obj, user_pincode=self.context.get("user_pincode"))
        return checker.get_status_message()

    def get_refund_responsibility(self, obj):
        refund_responsibility = [
            {
                "item_heading": "Refund policy",
                "item_text": "Full refund based on conditions",
                "item_subtext": ""
            },
            {
                "item_heading": "If the seller cancels or if the order is auto-canceled",
                "item_text": "Full Refund",
                "item_subtext": ""
            },
            {
                "item_heading": "If the customer cancels before order confirmation",
                "item_text": "Partial Refund",
                "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
            },
            {
                "item_heading": "On product returns",
                "item_text": "Full Refund",
                "item_subtext": ""
            }
        ]
        return refund_responsibility

    def get_delivery_settings_type(self, obj):
        try:
            store_config = obj.store_reference.store_configurations.get()
            return store_config.delivery_settings_type
        except Exception as e:
            return None

    def get_config_receive_orders(self,obj):
        try:
            store_config = obj.store_reference.store_configurations.get()
            return store_config.enable_orders
        except Exception as e:
            return False

    def get_like_status(self, obj):
        like_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            like_status = content_node.liked_by.is_connected(entity_node)
        return like_status

    def get_save_status(self, obj):
        save_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            save_status = content_node.saved_by.is_connected(entity_node)
        return save_status

    def get_repost_status(self, obj):
        repost_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            repost_status = content_node.reposted_by.is_connected(entity_node)
        return repost_status

    def get_swadeshi_owned(self,obj):
        if TrustCenter.objects.filter (store_reference=obj.store_reference).exists ():
            trust_center = TrustCenter.objects.get (store_reference=obj.store_reference)
            swadeshi_owned = trust_center.swadeshi_owned
        else:
            swadeshi_owned = None
        return swadeshi_owned

    def get_delivery_fee(self, obj):
        return get_delivery_info(obj, "delivery_fee")

    def get_delivery_by(self, obj):
        return get_delivery_info(obj, "delivery_by")

    def get_delivery_partner(self, obj):
        return get_delivery_info(obj, "delivery_partner")

    def get_logistic_partner_name(self, obj):
        return get_delivery_info(obj, "log_partner_name")

    def get_return_period(self, obj):
        return get_return_info(obj, "return_period")

    def get_return_conditions(self, obj):
        return get_return_info(obj, "return_conditions")

    def get_return_cost_on(self, obj):
        return get_return_info(obj, "return_cost_on")

    def get_return_pick_up(self, obj):
        return get_return_info(obj, "return_pick_up")

    def get_location(self, obj):
        if TrustCenter.objects.filter(store_reference=obj.store_reference).exists():
            trust_center = TrustCenter.objects.get(store_reference=obj.store_reference)
            location = trust_center.city
        else:
            location = None
        return location

    def get_created_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    def get_updated_date(self, obj):
        date = obj.modified_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    # def get_comment_count(self, obj):
    #     product_reference = obj.product_reference
    #     if Comment.objects.filter(product_reference=product_reference).exists():
    #         comment_count = Comment.objects.filter(
    #             product_reference=product_reference
    #         ).count()
    #     else:
    #         comment_count = 0
    #     return comment_count


class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            "productid",
            "product_name",
            "brand_name",
            "product_description",
            "product_category",
            "created_by",
            "modified_by",
            "promotion_link",
            "hashtags",
            "targeted_gender",
            "in_stock",
            "mrp_price",
            "selling_price",
            "storeid",
            "store_reference",
            "product_version",
            "swadeshi_brand",
            "swadeshi_made",
            "is_promotion_enabled",
            "promotion_amount",
            "product_slug",
            "product_code",
            "options"
        ]


class ProductVersionSerializer(serializers.ModelSerializer):
    deleted = serializers.SerializerMethodField('product_deleted')
    like_status = serializers.SerializerMethodField("get_like_status")
    save_status = serializers.SerializerMethodField("get_save_status")
    repost_status = serializers.SerializerMethodField("get_repost_status")
    config_receive_orders = serializers.SerializerMethodField("get_config_receive_orders")
    is_buy_enabled = serializers.SerializerMethodField("get_is_buy_enabled")
    product_status_message = serializers.SerializerMethodField("get_product_status_message")

    content_category = serializers.SerializerMethodField("get_content_category")
    content_headers = serializers.SerializerMethodField("get_content_headers")
    content_header_text = serializers.SerializerMethodField("get_content_header_text")

    class Meta:
        model = ProductVersion
        fields = [
            "productid",
            "product_name",
            "brand_name",
            "product_description",
            "promotion_link",
            "hashtags",
            "in_stock",
            "mrp_price",
            "selling_price",
            "storeid",
            "store_reference",
            "product_version",
            "product_reference",
            "deleted",
            "like_status",
            "save_status",
            "repost_status",
            "config_receive_orders",
            "is_buy_enabled",
            "product_status_message",
            "content_category",
            "content_headers",
            "content_header_text",
            "is_promotion_enabled",
            "promotion_amount"
        ]

    def _get_content_attribute(self, obj, attribute_method):
        """Helper method to get content attributes from Neo4j
        
        Args:
            obj: Product instance
            attribute_method: String name of the method to call on Neo4jContent
        
        Returns:
            Result of the method call or None if there's an error
        """
        try:
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            method = getattr(content_node, attribute_method)
            return method(visitor_reference=self.context.get("visitor_reference"))
        except Exception as e:
            return None

    def get_content_category(self, obj):
        return self._get_content_attribute(obj, 'get_content_category')

    def get_content_headers(self, obj):
        return self._get_content_attribute(obj, 'get_content_headers')

    def get_content_header_text(self, obj):
        return self._get_content_attribute(obj, 'get_content_header_text')

    def get_is_buy_enabled(self, obj):
        store_instance = Store.objects.get(store_reference=obj.store_reference)
        checker = BuyStatusChecker(store=store_instance, product=obj.product_reference, user_pincode=self.context.get("user_pincode"))
        return checker.is_buy_enabled()

    def get_product_status_message(self, obj):
        store_instance = Store.objects.get(store_reference=obj.store_reference)
        checker = BuyStatusChecker(store=store_instance, product=obj.product_reference, user_pincode=self.context.get("user_pincode"))
        return checker.get_status_message()

    def product_deleted(self,instance):
        product_instance = Product.objects.get(product_reference=instance.product_reference)
        deleted = product_instance.deleted
        return deleted

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if representation.get("store_reference"):
            store_reference = representation.get("store_reference")
            logger.info("store_reference %s", store_reference)
            store_instance = Store.objects.get(
                store_reference=store_reference, deleted=False
            )
            representation["storehandle"] = store_instance.storehandle
            representation["store_name"] = store_instance.store_name
            return representation
        else:
            return representation

    def get_config_receive_orders(self,obj):
        try:
            product_object = Product.objects.get(product_reference=obj.product_reference)
            store_config = product_object.store_reference.store_configurations.get()
            return store_config.enable_orders
        except Exception as e:
            return False

    def get_like_status(self, obj):
        like_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            like_status = content_node.liked_by.is_connected(entity_node)
        return like_status

    def get_save_status(self, obj):
        save_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            save_status = content_node.saved_by.is_connected(entity_node)
        return save_status

    def get_repost_status(self, obj):
        repost_status = False
        if self.context.get("visitor_reference"):
            visitor_reference = self.context.get("visitor_reference")
            content_node = Neo4jContent.nodes.get(reference=obj.product_reference)
            entity_node = Neo4jEntity.nodes.get(reference=visitor_reference)
            repost_status = content_node.reposted_by.is_connected(entity_node)
        return repost_status


class ProductHistorySerializer(serializers.ModelSerializer):
    version = serializers.CharField(source="product_version")

    class Meta:

        fields = ["version", "change_type", "created_date"]
        model = ProductVersion
        ordering = ["-created_date"]

class ProductVariantsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariants
        fields = "__all__"