from rest_framework.response import Response
from rest_framework import status
from rest_framework import generics, mixins
from rest_framework.decorators import api_view
from rest_framework.views import APIView

from decouple import config

from django.db.models import Sum
from django.utils import timezone

from ..order_api.models import Order, SubOrder, OrderLifeCycle, RefundedAmount
from ..cart_api.models import CartItem
from ..helpers import initiate_refund, get_order_request_number#, update_payout_after_cancel_or_return
from shared_utils.payout_utils import update_payout_after_cancel_or_return
from .models import SubOrderPaymentDetails
from users.notification_api.models import Notifications
from users.user_api.models import User
from products.models import Product
from common.util.support_helper import GenerateNotifications
from common.util.notification_handler import NotificationHandler

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

import paytmchecksum
import requests
import datetime
import json
import pytz
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

M_ID = config("TEST_MERCHANT_ID")
WEBSITE = config("WEBSITE")
PAYTM_CALL_BACK_URL = config("PAYTM_CALL_BACK_URL")
M_KEY = config("TEST_MERCHANT_KEY")
SKIP_PAYTM_API = config("SKIP_PAYTM_API", cast=bool)


class PaymentCreation(mixins.CreateModelMixin, generics.GenericAPIView):
    @swagger_auto_schema(
        operation_summary="payment creation",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "order_number",
                "delivery_fee",
                "cart_total",
                "total_amount",
                "user",
            ],
            properties={
                "order_number": openapi.Schema(type=openapi.TYPE_STRING),
                "delivery_fee": openapi.Schema(type=openapi.TYPE_INTEGER),
                "cart_total": openapi.Schema(type=openapi.TYPE_INTEGER),
                "total_amount": openapi.Schema(type=openapi.TYPE_INTEGER),
                "user": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def get_dummy_txn_token(self):
        now = datetime.datetime.now()
        code = now.strftime("%y%m%d%H%M%S")
        my_code = ("test_", code)
        return "".join(my_code)

    def post(self, request, *args, **kwargs):
        """for the particular order request number a payment is created."""

        order_number = request.data["order_number"]
        delivery_fee = request.data["delivery_fee"]
        cart_total = request.data["cart_total"]
        total_amount = request.data["total_amount"]
        user = request.data["user"]
        # for an order if payment already initiated and not completed, then create a new order_request_number for that payment
        # and continue the payment process.
        if Order.new_objects.filter(
            order_request_number=order_number,
            order_status=Order.Order_Status.PAYMENT_INITIATED,
        ).exists():
            new_order_request_number = get_order_request_number()
            Order.new_objects.filter(order_request_number=order_number).update(
                order_request_number=new_order_request_number
            )
            # since the order_request_number changed update the change in associated tables.
            SubOrderPaymentDetails.objects.filter(
                order_request_number=order_number
            ).update(order_request_number=new_order_request_number)
            new_order_number = (
                Order.new_objects.filter(order_request_number=new_order_request_number)
                .values_list("order_request_number", flat=True)
                .first()
            )
            order_id = Order.new_objects.filter(
                order_request_number=new_order_request_number
            ).values_list("orderid", flat=True)

        # if payment for an order is for first time.
        else:
            Order.new_objects.filter(order_request_number=order_number).update(
                order_status=Order.Order_Status.PAYMENT_INITIATED,
                delivery_fee=delivery_fee,
                store_total=cart_total,
                total_amount=total_amount,
            )
            order_payment_details_instance = SubOrderPaymentDetails.objects.filter(
                order_request_number=order_number
            )
            # whenever for an order, payment initiated update it in OrderPaymentDetails table.
            # to keep track of all the payment details separately
            for order_payment_details in order_payment_details_instance:
                order_payment_details.payment_status = (
                    SubOrderPaymentDetails.Payment_Status.PAYMENT_INITIATED
                )
                order_payment_details.save(update_fields=["payment_status"])
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)
            sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
            for order in sub_order_items:
                order.suborder_status = SubOrder.Suborder_Status.PAYMENT_INITIATED
                order.save(update_fields=["suborder_status"])
            new_order_number = (
                Order.new_objects.filter(order_request_number=order_number)
                .values_list("order_request_number", flat=True)
                .first()
            )
            print(new_order_number)
        # here starts the paytm payment.
        paytm_params = dict()
        paytm_params["body"] = {
            "requestType": "Payment",
            "mid": M_ID,
            "websiteName": WEBSITE,
            "orderId": new_order_number,
            "callbackUrl": PAYTM_CALL_BACK_URL,
            "txnAmount": {
                "value": str(total_amount),
                "currency": "INR",
            },
            "userInfo": {
                "custId": user,
            },
        }
        # Generate checksum by parameters we have in body
        checksum = paytmchecksum.generateSignature(
            json.dumps(paytm_params["body"]), M_KEY
        )
        paytm_params["head"] = {"signature": checksum}
        post_data = json.dumps(paytm_params)
        # for Staging
        if SKIP_PAYTM_API:
            initiate_transaction_api_response = {
                                                "head": {
                                                "responseTimestamp": "1526969112101",
                                                "version": "v1",
                                                "clientId": "C11",
                                                "signature": "TXBw50YPUKIgJd8gR8RpZuOMZ+csvCT7i0/YXmG//J8+BpFdY5goPBiLAkCzKlCkOvAQip/Op5aD6Vs+cNUTjFmC55JBxvp7WunZ45Ke2q0="
                                                 },
                                                "body": {
                                                "resultInfo": {
                                                "resultStatus": "S",
                                                "resultCode": "0000",
                                                "resultMsg": "Success"
                                                },
                                                "txnToken": self.get_dummy_txn_token(),
                                                "isPromoCodeValid": False,
                                                "authenticated": False
                                                }
                                                }

        else:
            url = f"https://securegw-stage.paytm.in/theia/api/v1/initiateTransaction?mid={M_ID}&orderId={new_order_number}"
            # try:
            initiate_transaction_api_response = requests.post(
                url, data=post_data, headers={"Content-type": "application/json"}
            ).json()
        # except:
        #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        if initiate_transaction_api_response["body"]["resultInfo"]["resultStatus"] == "S":
            txn_token = initiate_transaction_api_response["body"]["txnToken"]
            Order.new_objects.filter(order_request_number=new_order_number).update(
                txn_token=txn_token
            )
            OrderLifeCycle.objects.filter(orderid__in=order_id).update(txn_token=txn_token)

            paytmParams = dict()
            paytmParams["body"] = {
                "mid": M_ID,
                "orderId": new_order_number,
                "returnToken": True,
            }
            paytmParams["head"] = {"tokenType": "TXN_TOKEN", "token": txn_token}
            post_data = json.dumps(paytmParams)
            # for Staging
            if SKIP_PAYTM_API:
                fetch_payment_option_api_response = {
    "head": {
        "requestId": None,
        "responseTimestamp": "1596023582343",
        "version": "v2"
    },
    "body": {
        "resultInfo": {
            "resultStatus": "S",
            "resultCode": "0000",
            "resultMsg": "Success"
        },
        "merchantDetails": {
            "mcc": "8398",
            "merchantVpa": "test-merchant@paytm",
            "merchantName": "Test Merchant",
            "merchantLogo": "https://merchant-static.paytm.com/merchant-dashboard/logo/INTEGR7769XXXXXX9383/org/logo"
        },
        "addMoneyMerchantDetails": {
            "mcc": "6540",
            "merchantVpa": "testadd-money@paytm",
            "merchantName": "Paytm Add Money",
            "merchantLogo": None
        },
        "walletOnly": False,
        "zeroCostEmi": False,
        "pcfEnabled": False,
        "nativeJsonRequestSupported": True,
        "activeMerchant": True,
        "addMoneyDestination": "MAIN",
        "oneClickMaxAmount": "2000",
        "userDetails": {
            "email": "<EMAIL>",
            "paytmCCEnabled": False,
            "kyc": False,
            "username": "Test Merchant",
            "mobile": "**********"
        },
        "loginInfo": {
            "userLoggedIn": True,
            "pgAutoLoginEnabled": False,
            "mobileNumberNonEditable": False
        },
        "iconBaseUrl": "https://staticgw1.paytm.in/native/bank/",
        "addDescriptionMandatory": False,
        "onTheFlyKYCRequired": False,
        "paymentFlow": "ADDANDPAY",
        "merchantPayOption": {
            "savedMandateBanks": None,
            "paymentModes": [{
                    "displayName": "Paytm Balance",
                    "payChannelOptions": [{
                        "iconUrl": None,
                        "balanceInfo": {
                            "subWalletDetails": None,
                            "accountBalance": {
                                "currency": "INR",
                                "value": "0.00"
                            },
                            "payerAccountExists": True
                        },
                        "isHybridDisabled": False
                    }],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "onboarding": False,
                    "paymentMode": "BALANCE",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "Rawat Voucher",
                    "payChannelOptions": [

                    ],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "storeFrontUrl": "paytmmp://homepage?url=https://storefront.paytm.com/v2/mobile/mystore?kybid=INTEGR7769XXXXXX9383&isMGV=true",
                    "onboarding": False,
                    "paymentMode": "GIFT_VOUCHER",
                    "isHybridDisabled": True
                },
                {
                    "displayName": "Net Banking",
                    "payChannelOptions": [{
                        "iconUrl": "AAXS.png",
                        "isHybridDisabled": False,
                        "channelCode": "AAXS",
                        "channelName": "AXIS"
                    }],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "onboarding": False,
                    "paymentMode": "NET_BANKING",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "Credit Card",
                    "payChannelOptions": [

                    ],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "prepaidCardSupported": False,
                    "onboarding": False,
                    "paymentMode": "CREDIT_CARD",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "Debit Card",
                    "payChannelOptions": [

                    ],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "prepaidCardSupported": False,
                    "onboarding": False,
                    "paymentMode": "DEBIT_CARD",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "BHIM UPI",
                    "payChannelOptions": [{
                            "iconUrl": "UPI.png",
                            "isHybridDisabled": False,
                            "channelCode": "UPI",
                            "channelName": "Unified Payment Interace"
                        },
                        {
                            "iconUrl": "UPIPUSH.png",
                            "isHybridDisabled": False,
                            "channelCode": "UPIPUSH",
                            "channelName": "Unified Payment Interface - PUSH"
                        },
                        {
                            "iconUrl": "UPIPUSHEXPRESS.png",
                            "isHybridDisabled": False,
                            "channelCode": "UPIPUSHEXPRESS",
                            "channelName": "Unified Payment Interface - PUSH Express"
                        }
                    ],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "onboarding": False,
                    "paymentMode": "UPI",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "Paytm Payments Bank",
                    "payChannelOptions": [{
                        "iconUrl": "PPBL.png",
                        "balanceInfo": None,
                        "isHybridDisabled": False
                    }],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "onboarding": False,
                    "paymentMode": "PPBL",
                    "isHybridDisabled": False
                }
            ],
            "savedInstruments": [{
                "iconUrl": "https://staticgw4.paytm.in/33.1.1/",
                "oneClickSupported": False,
                "cardDetails": {
                    "cardId": "6c11070XXXXXX1b9cac3489",
                    "cardType": "CREDIT_CARD",
                    "expiryDate": "012022",
                    "firstSixDigit": "411111",
                    "lastFourDigit": "1111",
                    "status": "1",
                    "cvvLength": "3",
                    "cvvRequired": True,
                    "indian": True
                },
                "issuingBank": "HDFC",
                "isEmiAvailable": False,
                "authModes": [
                    "otp"
                ],
                "displayName": "HDFC Bank Credit Card",
                "priority": None,
                "paymentOfferDetails": None,
                "savedCardEmisubventionDetail": None,
                "prepaidCard": False,
                "isHybridDisabled": False,
                "channelCode": "VISA",
                "channelName": "Visa Inc.",
                "isEmiHybridDisabled": False
            }, ],
            "userProfileSarvatra": None,
            "activeSubscriptions": None,
            "upiProfile": {
                "respDetails": {
                    "profileDetail": {
                        "vpaDetails": [{
                            "name": "**********@paytm",
                            "defaultCreditAccRefId": "96XXX38",
                            "defaultDebitAccRefId": "96XXX38",
                            "primary": True
                        }],
                        "bankAccounts": [{
                            "bank": "Paytm Payments Bank",
                            "ifsc": "PYTM0123456",
                            "accRefId": "96XXX38",
                            "maskedAccountNumber": "XXXXXXXX7777",
                            "accountType": "SAVINGS",
                            "credsAllowed": [{
                                    "CredsAllowedDLength": "4",
                                    "CredsAllowedDType": "Numeric",
                                    "CredsAllowedSubType": "MPIN",
                                    "CredsAllowedType": "PIN",
                                    "dLength": "4"
                                },
                                {
                                    "CredsAllowedDLength": "6",
                                    "CredsAllowedDType": "Numeric",
                                    "CredsAllowedSubType": "SMS",
                                    "CredsAllowedType": "OTP",
                                    "dLength": "6"
                                }
                            ],
                            "name": "Test Merchant",
                            "mpinSet": "Y",
                            "txnAllowed": "ALL",
                            "branchAddress": "All Branches",
                            "pgBankCode": "PAYTM",
                            "bankMetaData": {
                                "perTxnLimit": "200000",
                                "bankHealth": {
                                    "category": "GREEN",
                                    "displayMsg": ""
                                }
                            }
                        }],
                        "profileStatus": "ACTIVE",
                        "upiLinkedMobileNumber": "91**********",
                        "deviceBinded": False
                    },
                    "metaDetails": {
                        "npciHealthCategory": "GREEN",
                        "npciHealthMsg": ""
                    }
                },
                "upiOnboarding": False
            }
        },
        "addMoneyPayOption": {
            "savedMandateBanks": None,
            "paymentModes": [{
                    "displayName": "Net Banking",
                    "payChannelOptions": [{
                        "iconUrl": "AIRP.png",
                        "isHybridDisabled": False,
                        "channelCode": "AIRP",
                        "channelName": "AIRTEL PAYMENTS BANK"
                    }],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "onboarding": False,
                    "paymentMode": "NET_BANKING",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "Credit Card",
                    "payChannelOptions": [

                    ],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "prepaidCardSupported": False,
                    "onboarding": False,
                    "paymentMode": "CREDIT_CARD",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "Debit Card",
                    "payChannelOptions": [

                    ],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "prepaidCardSupported": False,
                    "onboarding": False,
                    "paymentMode": "DEBIT_CARD",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "BHIM UPI",
                    "payChannelOptions": [{
                            "iconUrl": "UPI.png",
                            "isHybridDisabled": False,
                            "channelCode": "UPI",
                            "channelName": "Unified Payment Interace"
                        },
                        {
                            "iconUrl": "UPIPUSH.png",
                            "isHybridDisabled": False,
                            "channelCode": "UPIPUSH",
                            "channelName": "Unified Payment Interface - PUSH"
                        },
                        {
                            "iconUrl": "UPIPUSHEXPRESS.png",
                            "isHybridDisabled": False,
                            "channelCode": "UPIPUSHEXPRESS",
                            "channelName": "Unified Payment Interface - PUSH Express"
                        }
                    ],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "onboarding": False,
                    "paymentMode": "UPI",
                    "isHybridDisabled": False
                },
                {
                    "displayName": "Paytm Payments Bank",
                    "payChannelOptions": [{
                        "iconUrl": "PPBL.png",
                        "balanceInfo": None,
                        "isHybridDisabled": False
                    }],
                    "feeAmount": None,
                    "taxAmount": None,
                    "totalTransactionAmount": None,
                    "priority": None,
                    "onboarding": False,
                    "paymentMode": "PPBL",
                    "isHybridDisabled": False
                }
            ],
            "savedInstruments": [{
                "iconUrl": "https://staticgw2.paytm.in/33.1.1/",
                "oneClickSupported": False,
                "cardDetails": {
                    "cardId": "201707291077050XXXXXX47678ef1041b96e6bcac3489",
                    "cardType": "CREDIT_CARD",
                    "expiryDate": "012022",
                    "firstSixDigit": "411111",
                    "lastFourDigit": "1111",
                    "status": "1",
                    "cvvLength": "3",
                    "cvvRequired": True,
                    "indian": True
                },
                "issuingBank": "HDFC",
                "isEmiAvailable": False,
                "authModes": [
                    "otp"
                ],
                "displayName": "HDFC Bank Credit Card",
                "priority": None,
                "paymentOfferDetails": None,
                "savedCardEmisubventionDetail": None,
                "prepaidCard": False,
                "isHybridDisabled": False,
                "channelCode": "VISA",
                "channelName": "Visa Inc.",
                "isEmiHybridDisabled": False
            }],
            "userProfileSarvatra": None,
            "activeSubscriptions": None,
            "upiProfile": {
                "respDetails": {
                    "profileDetail": {
                        "vpaDetails": [{
                            "name": "**********@paytm",
                            "defaultCreditAccRefId": "9XXXX38",
                            "defaultDebitAccRefId": "96XXXX8",
                            "primary": True
                        }],
                        "bankAccounts": [{
                            "bank": "Paytm Payments Bank",
                            "ifsc": "PYTM0123456",
                            "accRefId": "96XXXX8",
                            "maskedAccountNumber": "XXXXXXXX7777",
                            "accountType": "SAVINGS",
                            "credsAllowed": [{
                                    "CredsAllowedDLength": "4",
                                    "CredsAllowedDType": "Numeric",
                                    "CredsAllowedSubType": "MPIN",
                                    "CredsAllowedType": "PIN",
                                    "dLength": "4"
                                },
                                {
                                    "CredsAllowedDLength": "6",
                                    "CredsAllowedDType": "Numeric",
                                    "CredsAllowedSubType": "SMS",
                                    "CredsAllowedType": "OTP",
                                    "dLength": "6"
                                }
                            ],
                            "name": "Test Merchant",
                            "mpinSet": "Y",
                            "txnAllowed": "ALL",
                            "branchAddress": "All Branches",
                            "pgBankCode": "PAYTM",
                            "bankMetaData": {
                                "perTxnLimit": "200000",
                                "bankHealth": {
                                    "category": "GREEN",
                                    "displayMsg": ""
                                }
                            }
                        }],
                        "profileStatus": "ACTIVE",
                        "upiLinkedMobileNumber": "91**********",
                        "deviceBinded": False
                    },
                    "metaDetails": {
                        "npciHealthCategory": "GREEN",
                        "npciHealthMsg": ""
                    }
                },
                "upiOnboarding": False
            }
        },
        "merchantLimitInfo": {
            "merchantRemainingLimits": [{
                    "limitType": "DAILY",
                    "amount": "20000.00"
                },
                {
                    "limitType": "WEEKLY",
                    "amount": "50000.00"
                },
                {
                    "limitType": "MONTHLY",
                    "amount": "49729.00"
                }
            ],
            "excludedPaymodes": [
                "UPI",
                "NET_BANKING",
                "GIFT_VOUCHER"
            ],
            "message": "limit is breached for this pay-mode"
        }
    }
}
            else:
                url = f"https://securegw-stage.paytm.in/theia/api/v2/fetchPaymentOptions?mid={M_ID}&orderId={new_order_number}"
                # for Production
                # url = "https://securegw.paytm.in/theia/api/v2/fetchPaymentOptions?mid=YOUR_MID_HERE&orderId=ORDERID_98765"
                try:
                    fetch_payment_option_api_response = requests.post(
                        url, data=post_data, headers={"Content-type": "application/json"}
                    ).json()
                except:
                    return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

            return Response(
                {
                    "message": "success",
                    "txn_token": txn_token,
                    "order_number": order_number,
                    "mid": M_ID,
                    "result_message": initiate_transaction_api_response["body"][
                        "resultInfo"
                    ]["resultMsg"],
                    "fetch_payment_option_api_response": fetch_payment_option_api_response,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
def PaymentStatusCheck(request):
    """
    this is a callback url, this will varify the payment and Whenever a payment become success or failure(if payment
    mode is credit_card, debit_card, net_banking, and not work for upi) this will call. Here this is used for saving
    and updating order_payment status and details related to a transactions"""
    form = request.data
    paytm_params = {}
    order_number = request.data["ORDERID"]
    payment_mode = request.data["PAYMENTMODE"]
    transaction_id = request.data["TXNID"]
    transaction_date = request.data["TXNDATE"]
    response_message = request.data["RESPMSG"]
    bank_transaction_id = request.data["BANKTXNID"]

    for i in form.keys():
        paytm_params[i] = form[i]

    paytm_checksum = request.data["CHECKSUMHASH"]
    is_verify_signature = paytmchecksum.verifySignature(
        paytm_params, M_KEY, paytm_checksum
    )
    print("response_message", response_message)
    if is_verify_signature:
        if response_message == "Transaction Success":

            Order.new_objects.filter(order_request_number=order_number).update(
                order_status=Order.Order_Status.PAYMENT_SUCCESS,
                transaction_id=transaction_id,
                transaction_date=transaction_date,
                bank_transaction_id=bank_transaction_id,
            )
            order_payment_details_instance = SubOrderPaymentDetails.objects.filter(
                order_request_number=order_number
            )

            # if payment success update the status in order payment details
            for order_payment_details in order_payment_details_instance:
                order_payment_details.payment_status = (
                    SubOrderPaymentDetails.Payment_Status.PAYMENT_SUCCESS
                )
                order_payment_details.save(update_fields=["payment_status"])
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)
            sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)

            # update the payment related details in suborder
            for order in sub_order_items:
                order.suborder_status = SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION
                order.save(update_fields=["suborder_status"])

            # update order life cycle
            OrderLifeCycle.objects.filter(orderid__in=order_id).update(
                transaction_id=transaction_id,
                transaction_date=transaction_date,
                bank_transaction_id=bank_transaction_id,
            )

            # update cart
            CartItem.new_objects.filter(order_request_number=order_number).update(
                cart_product_status="CART_DONE"
            )
            return Response(status=status.HTTP_200_OK)
        else:
            Order.new_objects.filter(order_request_number=order_number).update(
                order_status=Order.Order_Status.PAYMENT_FAILED,
                transaction_id=transaction_id,
                transaction_date=transaction_date,
                bank_transaction_id=bank_transaction_id,
            )
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)
            sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
            for order in sub_order_items:
                order.suborder_status = SubOrder.Suborder_Status.PAYMENT_FAILED
                order.save(update_fields=["suborder_status"])
            OrderLifeCycle.objects.filter(orderid__in=order_id).update(
                transaction_id=transaction_id,
                transaction_date=transaction_date,
                bank_transaction_id=bank_transaction_id,
            )
            return Response(status=status.HTTP_200_OK)
    else:
        return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class PaymentStatusVarify(APIView):
    @swagger_auto_schema(
        operation_summary="payment status varify",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["order_id"],
            properties={"order_id": openapi.Schema(type=openapi.TYPE_STRING)},
        ),
    )

    def get_date(self):
        date = datetime.datetime.now()
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%Y-%m-%d %H:%M:%S")

    def post(self, request):
        """By passing the order_request_number check the status of a payment"""
        order_number = request.data["order_id"]
        paytm_params = dict()
        paytm_params["body"] = {
            "mid": M_ID,
            "orderId": order_number,
        }
        checksum = paytmchecksum.generateSignature(
            json.dumps(paytm_params["body"]), M_KEY
        )
        paytm_params["head"] = {"signature": checksum}
        post_data = json.dumps(paytm_params)
        if SKIP_PAYTM_API:
            order_instance = Order.objects.filter(order_request_number=order_number).last()
            amount = order_instance.total_order_amount
            response = {
    "head": {
        "responseTimestamp": "*************",
        "version": "v1",
        "clientId": "C11",
        "signature": "xxxxx"
    },
    "body": {
        "resultInfo": {
            "resultStatus": "TXN_SUCCESS",
            "resultCode": "01",
            "resultMsg": "Txn Success"
        },
        "txnId": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
        "bankTxnId": "xxxxxxxxxxxxxxx",
        "orderId": order_number,
        "txnAmount": str(amount),
        "txnType": "SALE",
        "gatewayName": "HDFC",
        "bankName": "HSBC",
        "mid": "xxxxxxxxxxxxxxxxxxxx",
        "paymentMode": "CC",
        "refundAmt": "0.00",
        "txnDate": self.get_date(),
        "authRefId": "********"
    }
}


        else:
            # for Staging
            url = "https://securegw-stage.paytm.in/v3/order/status"
            # for Production
            # url = "https://securegw.paytm.in/v3/order/status"
            try:
                response = requests.post(
                    url, data=post_data, headers={"Content-type": "application/json"}
                ).json()
            except:
                return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        order_instance = Order.new_objects.values_list(
            "order_number", flat=True
        ).filter(order_request_number=order_number)
        _sub_order_instance = SubOrder.new_objects.filter(
            order_number__in=order_instance
        )

        items_ordered = _sub_order_instance.aggregate(
            items_ordered=Sum("product_quantity")
        )["items_ordered"]
        ordered_from = _sub_order_instance.distinct("order_number").count()
        estimated_delivery_date = _sub_order_instance.first().estimated_delivery_date
        # order_value = 0
        # if response['body']['txnAmount']:
        #     order_value = response['body']['txnAmount']
        # result = {
        #     "order_request_number": order_number,
        #     "items_ordered": items_ordered,
        #     "ordered_from": ordered_from,
        #     "estimated_delivery_date": estimated_delivery_date,
        #     "order_value": order_value
        # }
        if response["body"]["resultInfo"]["resultStatus"] == "TXN_SUCCESS":
            transaction_id = response["body"]["txnId"]
            bank_transaction_id = response["body"]["bankTxnId"]
            txn_date = response["body"]["txnDate"]

            # update order table with status and other transaction details.
            Order.new_objects.filter(order_request_number=order_number).update(
                order_status=Order.Order_Status.PAYMENT_SUCCESS,
                transaction_id=transaction_id,
                bank_transaction_id=bank_transaction_id,
                transaction_date=txn_date,
            )

            # if Payment is success then update the same in OrderPaymentDetails table.
            order_payment_details_instance = SubOrderPaymentDetails.objects.filter(
                order_request_number=order_number
            )
            for order_payment_details in order_payment_details_instance:
                order_payment_details.payment_status = (
                    SubOrderPaymentDetails.Payment_Status.PAYMENT_SUCCESS
                )
                order_payment_details.save(update_fields=["payment_status"])
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)

            # update the details in SubOrder table.
            sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
            for order in sub_order_items:
                order.suborder_status = SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION
                order.save(update_fields=["suborder_status"])

            # update the details in OrderLifeCycle table.
            OrderLifeCycle.objects.filter(orderid__in=order_id).update(
                transaction_id=transaction_id,
                bank_transaction_id=bank_transaction_id,
                transaction_date=txn_date,
            )

            # update the product inventory
            cart_instance = CartItem.new_objects.filter(order_request_number=order_number)
            # for each product in the cart instance
            for item in cart_instance:
                product_id = item.productid
                brought_quantity = item.product_quantity
                pdt = Product.objects.get(productid=product_id)
                pdt_stock = pdt.in_stock
                if pdt_stock != 0 and pdt_stock >= brought_quantity:
                    updated_stock = pdt_stock - brought_quantity
                    pdt.in_stock = updated_stock
                    pdt.save(update_fields=['in_stock'])

            # since the order_payment is completed update all the items that are particular to this order in CartItem table
            # as "CART_DONE"
            CartItem.new_objects.filter(order_request_number=order_number).update(
                cart_product_status="CART_DONE"
            )
            try:
                orders = Order.new_objects.filter(order_request_number=order_number).select_related('store_reference').prefetch_related('orderitems')
                for order in orders:
                    _order_number = order.order_number
                    store_reference = order.store_reference.store_reference
                    # related_suborders = order.orderitems.filter(order_number=_order_number).first()
                    user_id = order.userid
                    # user_image = User.objects.get(userid=user_id).values('icon')
                    user_instance = User.objects.get(userid=user_id,deleted=False)
                    if user_instance.icon:
                        user_image = user_instance.icon
                    else:
                        user_image=None
                    notification_handler = NotificationHandler(notified_user=store_reference,
                                                                notification_type=Notifications.Notifications_Type.NEW_ORDER,
                                                                notification_about=_order_number,
                                                                image=user_image,
                                                               )
                    notification_handler.create_notification(notification_handler)
                    # notification_gen = GenerateNotifications()
                    # notification_gen.create_notifications(
                    #     notified_user=store_reference,
                    #     notification_type=Notifications.Notifications_Type.NEW_ORDER,
                    #     notification_about=_order_number,
                    #     image=related_suborders.product_image,
                    # )
            except Exception as e:
                logger.error('An error occurred with the notifications: %s', str(e))

            return Response(
                {
                    "message": "success",
                    "items_ordered": items_ordered,
                    "ordered_from": ordered_from,
                    "estimated_delivery_date": estimated_delivery_date,
                    "data": response,
                },
                status=status.HTTP_200_OK,
            )
        elif response["body"]["resultInfo"]["resultStatus"] == "PENDING":
            Order.new_objects.filter(order_request_number=order_number).update(
                order_status=Order.Order_Status.PAYMENT_PENDING
            )
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)
            sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
            for order in sub_order_items:
                order.suborder_status = SubOrder.Suborder_Status.PAYMENT_PENDING
                order.save(update_fields=["suborder_status"])
            return Response(
                {
                    "message": "success",
                    "items_ordered": items_ordered,
                    "ordered_from": ordered_from,
                    "estimated_delivery_date": estimated_delivery_date,
                    "data": response,
                },
                status=status.HTTP_200_OK,
            )
        else:
            if response["body"]["resultInfo"]["resultCode"] != "501":
                transaction_id = response["body"]["txnId"]
                bank_transaction_id = response["body"]["bankTxnId"]
                txn_date = response["body"]["txnDate"]
                Order.new_objects.filter(order_request_number=order_number).update(
                    order_status=Order.Order_Status.PAYMENT_FAILED,
                    transaction_id=transaction_id,
                    bank_transaction_id=bank_transaction_id,
                    transaction_date=txn_date,
                )
                order_id = Order.new_objects.filter(
                    order_request_number=order_number
                ).values_list("orderid", flat=True)
                sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
                for order in sub_order_items:
                    order.suborder_status = SubOrder.Suborder_Status.PAYMENT_FAILED
                    order.save(update_fields=["suborder_status"])
                OrderLifeCycle.objects.filter(orderid__in=order_id).update(
                    transaction_id=transaction_id,
                    bank_transaction_id=bank_transaction_id,
                    transaction_date=txn_date,
                )
                return Response(
                    {"message": "success", "data": response}, status=status.HTTP_200_OK
                )
            else:
                return Response(
                    {"message": "error", "data": response}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )


class ProcessTransaction(mixins.CreateModelMixin, generics.GenericAPIView):
    def post(self, request):
        payment_mode = request.data["payment_mode"]
        txn_token = request.data["txn_token"]
        order_number = request.data["order_number"]

        paytm_params = dict()
        paytm_params["head"] = {"txnToken": txn_token}
        if payment_mode == "BALANCE":
            pass
        elif payment_mode == "CREDIT_CARD":
            card_info = request.data["card_info"]
            paytm_params["body"] = {
                "requestType": "NATIVE",
                "mid": M_ID,
                "orderId": order_number,
                "paymentMode": "CREDIT_CARD",
                "cardInfo": card_info,
                "authMode": "otp",
            }
        elif payment_mode == "DEBIT_CARD":
            card_info = request.data["card_info"]
            paytm_params["body"] = {
                "requestType": "NATIVE",
                "mid": M_ID,
                "orderId": order_number,
                "paymentMode": "DEBIT_CARD",
                "cardInfo": card_info,
                "authMode": "otp",
            }
        elif payment_mode == "NET_BANKING":
            channel_code = request.data["channel_code"]
            paytm_params["body"] = {
                "requestType": "NATIVE",
                "mid": M_ID,
                "orderId": order_number,
                "paymentMode": "NET_BANKING",
                "channelCode": channel_code,
            }
        elif payment_mode == "UPI":
            payer_account = request.data["payer_account"]
            paytm_params["body"] = {
                "payerAccount": payer_account,
                "requestType": "NATIVE",
                "orderId": order_number,
                "paymentMode": "UPI",
                "mid": M_ID,
            }

        post_data = json.dumps(paytm_params)
        # for Staging
        if SKIP_PAYTM_API:
            response = {
    "head": {
        "responseTimestamp": "*************",
        "version": "v1"
    },
    "body": {
        "resultInfo": {
            "resultStatus": "S",
            "resultCode": "0000",
            "resultMsg": "Success"
        },
        "bankForm": {
            "pageType": "redirect",
            "isForceResendOtp": False,
            "redirectForm": {
                "actionUrl": "https://securegw-stage.paytm.in/theia/api/v1/upiPollPage?mid=INTEGR7769XXXXXX9383&orderId=ORDERID_98765",
                "method": "POST",
                "type": "redirect",
                "headers": {
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                "content": {
                    "MERCHANT_VPA": "tlalit5-testintegration@paym",
                    "CHANNEL": "PGPTM",
                    "externalSrNo": "90200728000284938800",
                    "MCC": "5641",
                    "payerVpa": "**********@paytm",
                    "txnAmount": "1",
                    "txnToken": "f0bed899539742309eebd8XXXX7edcf61588842333227"
                }
            }
        }
    }
}
        else:
            url = f"https://securegw-stage.paytm.in/theia/api/v1/processTransaction?mid={M_ID}&orderId={order_number}"
            # for Production
            # url = "https://securegw.paytm.in/theia/api/v1/processTransaction?mid=YOUR_MID_HERE&orderId=ORDERID_98765"
            try:
                response = requests.post(
                    url, data=post_data, headers={"Content-type": "application/json"}
                ).json()
            except:
                return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        if response["body"]["resultInfo"]["resultStatus"] == "S":
            Order.new_objects.filter(order_request_number=order_number).update(
                payment_mode=payment_mode
            )
            # CartItem.new_objects.filter(order_request_number=order_number).update(cart_product_status='CART_DONE')
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)
            OrderLifeCycle.objects.filter(orderid__in=order_id).update(
                payment_mode=payment_mode
            )
            # sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
            # for order in sub_order_items:
            #     order.suborder_status = SubOrder.Suborder_Status.PAYMENT_SUCCESS
            #     order.save()
            return Response(
                {"message": "success", "data": response}, status=status.HTTP_200_OK
            )
        else:
            Order.new_objects.filter(order_request_number=order_number).update(
                payment_mode=payment_mode
            )
            order_id = Order.new_objects.filter(
                order_request_number=order_number
            ).values_list("orderid", flat=True)
            OrderLifeCycle.objects.filter(orderid__in=order_id).update(
                payment_mode=payment_mode
            )

            # sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
            # for order in sub_order_items:
            #     order.suborder_status = S
            #     order.save()
            return Response(
                {"message": "success", "data": response}, status=status.HTTP_200_OK
            )


@api_view(["POST"])
def send_checkout_otp(request):
    if request.method == "POST":
        mobile_number = request.data["mobile_number"]
        txn_token = request.data["txn_token"]
        order_number = request.data["order_number"]

        paytm_params = dict()
        paytm_params["body"] = {"mobileNumber": mobile_number}
        paytm_params["head"] = {"txnToken": txn_token}
        post_data = json.dumps(paytm_params)
        # for Staging
        url = f"https://securegw-stage.paytm.in/login/sendOtp?mid={M_ID}&orderId={order_number}"
        # for Production
        # url = "https://securegw.paytm.in/login/sendOtp?mid=YOUR_MID_HERE&orderId=ORDERID_98765"

        response = requests.post(
            url, data=post_data, headers={"Content-type": "application/json"}
        ).json()
        if response["body"]["resultInfo"]["resultStatus"] == "SUCCESS":
            return Response(
                {"message": "success", "data": response}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "error", "data": response},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["POST"])
def validate_checkout_otp(request):
    otp = request.data["otp"]
    txn_token = request.data["txn_token"]
    order_number = request.data["order_number"]

    paytm_params = dict()
    paytm_params["body"] = {"otp": otp}
    paytm_params["head"] = {"txnToken": txn_token}
    post_data = json.dumps(paytm_params)
    # for Staging
    url = f"https://securegw-stage.paytm.in/login/validateOtp?mid={M_ID}&orderId={order_number}"
    # for Production
    # url = "https://securegw.paytm.in/login/validateOtp?mid=YOUR_MID_HERE&orderId=ORDERID_98765"
    response = requests.post(
        url, data=post_data, headers={"Content-type": "application/json"}
    ).json()
    if response["body"]["resultInfo"]["resultStatus"] == "SUCCESS":
        return Response(
            {"message": "success", "data": response}, status=status.HTTP_200_OK
        )
    else:
        return Response(
            {"message": "error", "data": response}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["POST"])
def net_banking_payment_channels(request):
    if request.method == "POST":
        txn_token = request.data["txn_token"]
        order_number = request.data["order_number"]
        paytm_params = dict()
        paytm_params["body"] = {
            "type": "MERCHANT",
        }
        paytm_params["head"] = {"tokenType": "TXN_TOKEN", "token": txn_token}
        post_data = json.dumps(paytm_params)
        # for Staging
        url = f"https://securegw-stage.paytm.in/theia/api/v1/fetchNBPaymentChannels?mid={M_ID}&orderId={order_number}"
        # for Production
        # url = "https://securegw.paytm.in/theia/api/v1/fetchNBPaymentChannels?mid=YOUR_MID_HERE&orderId=ORDERID_98765"
        try:
            response = requests.post(
                url, data=post_data, headers={"Content-type": "application/json"}
            ).json()
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {"message": "success", "data": response}, status=status.HTTP_200_OK
        )


@api_view(["POST"])
def fetch_card_details(request):

    if request.method == "POST":
        txn_token = request.data["txn_token"]
        order_number = request.data["order_number"]
        card_bin = request.data["card_bin"]
        paytm_params = dict()

        paytm_params["body"] = {"bin": card_bin}
        paytm_params["head"] = {"tokenType": "TXN_TOKEN", "token": txn_token}
        post_data = json.dumps(paytm_params)
        # for Staging
        url = f"https://securegw-stage.paytm.in/fetchBinDetail?mid={M_ID}&orderId={order_number}"
        # for Production
        # url = "https://securegw.paytm.in/fetchBinDetail?mid=YOUR_MID_HERE&orderId=ORDEID_98765"
        try:
            response = requests.post(
                url, data=post_data, headers={"Content-type": "application/json"}
            ).json()
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        return Response(
            {"message": "success", "data": response}, status=status.HTTP_200_OK
        )


@api_view(["POST"])
def validate_vpa(request):
    if request.method == "POST":
        txn_token = request.data["txn_token"]
        order_number = request.data["order_number"]
        vpa = request.data["vpa"]

        paytm_params = dict()
        paytm_params["body"] = {"vpa": vpa}
        paytm_params["head"] = {"tokenType": "TXN_TOKEN", "token": txn_token}
        post_data = json.dumps(paytm_params)
        # for Staging
        if SKIP_PAYTM_API:
            response = {
  "head": {
    "requestId": None,
    "responseTimestamp": "*************",
    "version": "v1"
  },
  "body": {
    "resultInfo": {
      "resultStatus": "S",
      "resultCode": "0000",
      "resultMsg": "Success"
    },
    "vpa": "**********@paytm",
    "recurringDetails": {
      "pspSupportedRecurring": True,
      "bankSupportedRecurring": True
    },
    "valid": True
  }
}
        else:
            url = f"https://securegw-stage.paytm.in/theia/api/v1/vpa/validate?mid={M_ID}&orderId={order_number}"
            # for Production
            # url = "https://securegw.paytm.in/theia/api/v1/vpa/validate?mid=YOUR_MID_HERE&orderId=ORDERID_98765"
            response = requests.post(
                url, data=post_data, headers={"Content-type": "application/json"}
            ).json()
        if response["body"]["resultInfo"]["resultStatus"] == "S":
            return Response(
                {"message": "success", "data": response}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "success", "data": response}, status=status.HTTP_200_OK
            )

@api_view(["POST"])
def refund_initiate_by_seller(request):
    # suborder_number_list = request.data["suborder_number"]
    # result = initiate_refund(suborder_number_list)
    # print("res", result)
    # update_payout_after_cancel_or_return(suborder_number_list[0])
    # return Response({"message": result}, status=status.HTTP_200_OK)

    refund_eligible_suborders = RefundedAmount.objects.filter(refund_requested_date__isnull=False,
                                                               refund_status=RefundedAmount.Status.UNPROCESSED)
    execution_count = 0
    for suborder in refund_eligible_suborders:
        # date in which refund request to paytm should raise.
        refund_requested_date = suborder.refund_requested_date
        refund_processing_date_str = refund_requested_date.astimezone(pytz.timezone('Asia/Kolkata')).strftime(
            '%Y-%m-%d %H:%M:%S')

        # current date time has taken and converted to str for the convenience of comparison.
        current_date_time = datetime.datetime.now()
        current_date_time_str = current_date_time.strftime('%Y-%m-%d %H:%M:%S')

        if current_date_time_str >= refund_processing_date_str:

            suborder_number_list = [suborder.suborder_reference.suborder_number]

            result = initiate_refund(suborder_number_list)
            execution_count += 1
            update_payout_after_cancel_or_return(suborder_number_list[0])
    return Response(
        {"message": "success", "data": execution_count}, status=status.HTTP_200_OK
    )


class CheckRefundStatus(APIView):
    @swagger_auto_schema(
        operation_summary="check the refund status",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["order_number", "refund_id"],
            properties={
                "suborder_number": openapi.Schema(type=openapi.TYPE_STRING),
                "refund_id": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request):
        """To check the refund status of a request."""
        logger.info("Entered refund status checking api")
        suborder_number = request.data["suborder_number"]
        refund_id = request.data["refund_id"]
        paytmParams = dict()

        # body parameters
        paytmParams["body"] = {
            "mid": M_ID,
            "orderId": suborder_number,
            "refId": refund_id,
        }

        # Generate checksum by parameters we have in body
        # Find your Merchant Key in your Paytm Dashboard at https://dashboard.paytm.com/next/apikeys
        checksum = paytmchecksum.generateSignature(
            json.dumps(paytmParams["body"]), M_KEY
        )
        paytmParams["head"] = {"signature": checksum}
        post_data = json.dumps(paytmParams)

        # for Staging
        if SKIP_PAYTM_API:

            response = {
            "head": {
                "clientId": "C11",
                "responseTimestamp": "1556719120393",
                "signature": "Stx6P9HpnEG3GADkMuOcj50dm7ZHmvMPd29b8K5rxi4aVzRcJ5hklZo//RZdtTA+zcll8sdelyAYsxqPxFs66RVE0F2b9RElTMqYSfBj89I=",
                "version": "v1"
            },
            "body": {
                "orderId": "YOUR_ORDER_ID",
                "userCreditInitiateStatus": "SUCCESS",
                "mid": "YOUR_MID_HERE",
                "merchantRefundRequestTimestamp": "2019-05-01 19:27:25.0",
                "resultInfo": {
                    "resultStatus": "TXN_SUCCESS",
                    "resultCode": "10",
                    "resultMsg": "Refund Successfull"
                },
                "txnTimestamp": "2019-05-01 19:25:41.0",
                "acceptRefundTimestamp": "2019-05-01 19:27:25.0",
                "acceptRefundStatus": "SUCCESS",
                "refundDetailInfoList": [{
                    "refundType": "TO_SOURCE",
                    "payMethod": "BALANCE",
                    "userCreditExpectedDate": "2019-05-02",
                    "userMobileNo": "91-******7777",
                    "refundAmount": "1.00"
                }],
                "userCreditInitiateTimestamp": "2019-05-01 19:27:26.0",
                "totalRefundAmount": "1.00",
                "refId": "UNIQUE_REFUND_ID",
                "txnAmount": "10.00",
                "refundId": "PAYTM_REFUND_ID",
                "txnId": "PAYTM_TRANSACTION_ID",
                "refundAmount": "1.00",
                "refundReason": "Testing refund reason",
                "agentInfo": {
                    "name": "Lalit",
                    "employeeId": "Em1-00`",
                    "phoneNo": "**********",
                    "email": "<EMAIL>"
                }
            }
        }

        else:

            url = "https://securegw-stage.paytm.in/v2/refund/status"

            # for Production
            # url = "https://securegw.paytm.in/v2/refund/status"

            response = requests.post(
                url, data=post_data, headers={"Content-type": "application/json"}
            ).json()

        logger.info("Got response from Paytm's refund status api")
        logger.info(
            "response message is ==> %s",
            response["body"]["resultInfo"]["resultMsg"],
        )
        logger.info(
            "response status is ==> %s",
            response["body"]["resultInfo"]["resultStatus"],
        )
        logger.info("res %s", response)

        if RefundedAmount.objects.filter(suborder_reference=suborder_number, refund_status=RefundedAmount.Status.PENDING).exists():

            refund_instance = RefundedAmount.objects.get(suborder_reference=suborder_number, refund_status=RefundedAmount.Status.PENDING)
            refunded_date = datetime.datetime.now()

            if response["body"]["resultInfo"]["resultStatus"] == "PENDING":

                refund_instance.refund_status = RefundedAmount.Status.PENDING
                refund_instance.save(update_fields=['refund_status'])

                order_payment_details_instance = SubOrderPaymentDetails.objects.get(
                    refund_id=refund_id
                )
                order_payment_details_instance.payment_status = "REFUND_PENDING"

            elif response["body"]["resultInfo"]["resultStatus"] == "TXN_SUCCESS":
                order_payment_details_instance = SubOrderPaymentDetails.objects.get(
                    refund_id=refund_id
                )
                order_payment_details_instance.payment_status = "REFUND_SUCCESS"
                order_payment_details_instance.refunded_date = datetime.datetime.now()

                refund_instance.refunded_date = refunded_date
                refund_instance.refund_status = RefundedAmount.Status.REFUNDED
                refund_instance.save(update_fields=["refunded_date", 'refund_status'])

                # Get data required to create notification
                suborder_instance = SubOrder.objects.get(suborder_number=suborder_number)
                notified_to = suborder_instance.user_reference
                order_number = suborder_instance.order_number
                image = suborder_instance.store_image

                # Creating a notification to notify the buyer about the refund initiation
                notification_handler = NotificationHandler (notified_user=notified_to,
                                                            notification_type=Notifications.Notifications_Type.REFUND_RELEASED,
                                                            notification_about=order_number,
                                                            image=image,
                                                            refund_id=refund_id,
                                                            )
                notification_handler.create_notification (notification_handler)

            else:

                refund_instance.refund_status = RefundedAmount.Status.REFUND_FAILED
                refund_instance.save(update_fields=['refund_status'])

                order_payment_details_instance = SubOrderPaymentDetails.objects.get(
                    refund_id=refund_id
                )
                order_payment_details_instance.payment_status = "REFUND_FAILED"

            order_payment_details_instance.save(update_fields=["payment_status"])

            logger.info("Exiting refund status checking api")
            return Response(
                {"message": "success", "data": response}, status=status.HTTP_200_OK
            )
        else:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        # except:
        #     logger.error("Refund status api has failed, need action")
        #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)