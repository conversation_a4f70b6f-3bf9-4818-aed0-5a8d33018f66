# Generated by Django 4.2.7 on 2024-08-17 06:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0065_store_store_signature"),
    ]

    operations = [
        migrations.CreateModel(
            name="StoreByState",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("state", models.CharField(max_length=100)),
                ("icons", models.CharField(max_length=1000)),
                ("total_number_of_stores", models.IntegerField()),
            ],
            options={
                "verbose_name_plural": "stores_by_state",
                "db_table": '"store"."stores_by_state"',
            },
        ),
        migrations.CreateModel(
            name="StoresByCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("category_name", models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ("icons", models.<PERSON><PERSON><PERSON><PERSON>(max_length=1000)),
                ("total_number_of_stores", models.IntegerField()),
            ],
            options={
                "verbose_name_plural": "stores_by_category",
                "db_table": '"store"."stores_by_category"',
            },
        ),
    ]
