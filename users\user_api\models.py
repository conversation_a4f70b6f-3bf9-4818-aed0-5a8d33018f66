from django.db import models
from django.utils.html import json_script
from phonenumber_field.modelfields import PhoneNumberField
import datetime
import time
import random
import string
import logging
from geopy.geocoders import Nominatim

from ..invite_api.models import InviteActivity, InviteUser
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.fields import ArrayField
# from stores.store_api.models import Store
# from common.util.support_helper import compress

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
# Create your models here.


class User(models.Model):

    class UserRoles(models.TextChoices):
        BUYER = "BUYER", _("Buyer")
        SELLER = "SELLER", _("Seller")
        CONTENT_CREATOR = "CONTENT_CREATOR", _("Content_Creator")
        INVESTOR = "INVESTOR", _("Investor")
        VOLUNTEER = "VOLUNTEER", _("Volunteer")
        COLLABORATOR = "COLLABORATOR", _("Collaborator")

    class SubscriptionType(models.TextChoices):
        FREE = "FREE", _("Free")
        PREMIUM = "PREMIUM", _("Premium")

    userid = models.AutoField(primary_key=True)
    user_reference = models.CharField(max_length=20, unique=True)
    phonenumber = PhoneNumberField()
    is_phonenumber_verified = models.BooleanField(default=False)
    is_email_verified = models.BooleanField(default=False)
    counter = models.IntegerField(default=0, blank=False)  # For HOTP Verification
    email = models.EmailField(max_length=100, blank=True, null=True)
    gender = models.CharField(max_length=50, blank=True, null=True)
    age = models.IntegerField(blank=True, null=True)

    icon = models.ImageField(upload_to="profile_image", blank=True, null=True)
    first_name = models.CharField(max_length=50, blank=True, null=True)
    last_name = models.CharField(max_length=50, blank=True, null=True)
    display_name = models.CharField(max_length=50, blank=True, null=True)
    website_link = models.CharField(max_length=500, blank=True, null=True)
    about_user = models.TextField(blank=True, null=True)
    profession = models.CharField(max_length=30, blank=True, null=True)
    date_of_birth = models.DateField(null=True, blank=True)

    user_name = models.CharField(max_length=35, blank=True, null=True)
    user_location = models.CharField(max_length=50, blank=True, null=True)
    pincode = models.CharField(max_length=6, default="524001", blank=True, null=True)

    invite_code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    invited_by_code = models.CharField(max_length=10, blank=True,null=True)
    invite_type = models.CharField(
        max_length=50, blank=True, null=True, default="SELLER"
    )
    seller_invite_balance = models.PositiveIntegerField(default=0)
    member_invite_balance = models.PositiveIntegerField(default=0)
    member_access = models.BooleanField(default=False)
    seller_access = models.BooleanField(default=False)
    registration_method = models.CharField(max_length=15, null=True, blank=True, default=None)

    search_and_view = models.CharField(max_length=2, default="1")
    view_stores = models.CharField(max_length=2, default="1")
    follow_stores = models.CharField(max_length=2, default="1")
    view_store_contact = models.CharField(max_length=2, default="1")
    report_stores = models.CharField(max_length=2, default="1")
    view_store_documents = models.CharField(max_length=2, default="1")

    view_product = models.CharField(max_length=2, default="1")
    save_product = models.CharField(max_length=2, default="1")
    buy_product = models.CharField(max_length=2, default="1")
    report_product = models.CharField(max_length=2, default="1")

    view_comment_question_review = models.CharField(max_length=2, default="1")
    add_comment = models.CharField(max_length=2, default="1")
    add_question = models.CharField(max_length=2, default="1")
    add_review = models.CharField(max_length=2, default="1")
    report_comment_question_review = models.CharField(max_length=2, default="1")
    clap_comment_question_review = models.CharField(max_length=2, default="1")

    follow_people = models.CharField(max_length=2, default="1")
    report_people = models.CharField(max_length=2, default="1")
    message_people = models.CharField(max_length=2, default="1")

    create_store = models.CharField(max_length=2, default="1")
    create_test_store = models.CharField(max_length=2, default="1")
    store_live = models.CharField(max_length=2, default="1")
    add_products = models.CharField(max_length=2, default="1")
    receive_orders = models.CharField(max_length=2, default="1")
    receive_payments = models.CharField(max_length=2, default="1")

    send_member_invites = models.CharField(max_length=2, default="1")
    send_seller_invites = models.CharField(max_length=2, default="1")
    vote_for_membership = models.CharField(max_length=2, default="1")

    giving_suggestions = models.CharField(max_length=2, default="1")
    create_promotion = models.CharField(max_length=2, default="1")
    view_community_forum = models.CharField(max_length=2, default="1")
    edit_community_forum = models.CharField(max_length=2, default="1")

    include_in_rating_calculation = models.CharField(max_length=2, default="1")
    include_in_analytics = models.CharField(max_length=2, default="1")
    included_in_others_trust_score = models.CharField(max_length=2, default="1")
    include_in_self_trust_score = models.CharField(max_length=2, default="1")

    deleted = models.BooleanField(default=False)
    created_date = models.DateTimeField(auto_now_add=True)

    xmpp_jid = models.CharField(max_length=255, unique=True, null=True, blank=True)
    xmpp_password = models.CharField(max_length=255, null=True, blank=True)
    js_messaging_token = models.CharField(max_length=300, unique=True, null=True, blank=True)
    user_roles = ArrayField(
        models.CharField(
            max_length=100,
            choices=UserRoles.choices
        ),
        default=list
    )
    subscription_type = models.CharField(max_length=10, choices=SubscriptionType.choices, default=SubscriptionType.FREE)

    # new messaging fields
    new_messaging_token = models.CharField(max_length=300, null=True, blank=True)
    new_messaging_user_id = models.CharField(max_length=200, unique=True, null=True, blank=True)

    #User support Score Field
    user_support_score = models.FloatField(default=0)
    user_level = models.CharField(max_length=5, blank=True, null=True, default="1")

    __invite_type = None
    __user_location = None

    REQUIRED_FIELDS = ['phonenumber']  # Add any other required fields here
    
    # Specify the unique identifier field for the user
    USERNAME_FIELD = 'user_reference'  # Change 'username' to the field you want to use as the unique identifier
    
    # Implement the is_anonymous attribute
    @property
    def is_anonymous(self):
        return False
    
    # Implement the is_authenticated attribute
    @property
    def is_authenticated(self):
        return True
    
    # Add the is_active attribute
    @property
    def is_active(self):
        return True

    def full_name(self):
        f_name = self.first_name
        l_name = self.last_name
        if f_name and l_name:
            full_name = " ".join([f_name, l_name])
        elif f_name and l_name is None:
            full_name = f_name
        else:
            full_name = self.user_name
        return full_name

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.__invite_type = self.invite_type
        # self.__user_location = self.user_location

    def get_user_reference(self):
        # current_time = datetime.datetime.now()
        # current_time_str = current_time.strftime("%m%d%y")
        # random_number = "".join(random.choices(string.digits, k=3))
        # final_code = ("U", current_time_str, random_number)
        # user_reference = "".join(final_code)
        user_reference = f'U{int(time.time() * 1000)}'
        if User.objects.filter(user_reference=user_reference).exists():
            user_reference = self.get_user_reference()
            return user_reference
        return user_reference

    @staticmethod
    def create_user_name(email, max_attempts=3, max_length=35):
        email = email.split('@')[0].replace(" ", "")
        for _ in range(max_attempts):
            base_user_name = f"{email.lower()}"
            if len(base_user_name) <= max_length:
                if not User.objects.filter(user_name=base_user_name, deleted=False).exists():
                    return base_user_name
            else:
                # Handle long usernames by truncating the first name and adding 6 digit random number at the end.

                # Truncating the first name so that 6 digit random number can accommodate.
                user_name = f"{base_user_name[:max_length]}"

                if not User.objects.filter(user_name=user_name, deleted=False).exists():
                    return user_name
        raise Exception("Failed to generate a unique user_name after multiple attempts")

    # def create_user_name(self, first_name):
    #     random_number = "".join(random.choices(string.digits, k=5))
    #     remove_space_first_name = first_name.replace(" ", "")
    #     final_code = (remove_space_first_name.lower(), '_', random_number)
    #     user_name = "".join(final_code)
    #     is_user_name_exists = User.objects.filter(user_name=user_name, deleted=False).exists()
    #     # is_store_handle_exists = Store.objects.filter(store_handle=user_name, deleted=False).exists()
    #     if is_user_name_exists:
    #         user_name = self.create_user_name()
    #         return user_name
    #     return user_name

    @staticmethod
    def create_pincode_from_city(user_location):
        geo_locator = Nominatim(user_agent="user")
        location = geo_locator.geocode(user_location)

        if location:
            data_1 = location.raw
            location_data = data_1['display_name'].split()
            pincode = location_data[-2]
            stripped_pincode = pincode.rstrip(pincode[-1])
            if stripped_pincode.isdigit():
                new_pincode = stripped_pincode
            else:
                new_pincode = None
        else:
            new_pincode = None
        return new_pincode

    def save(self, *args, **kwargs):
        if self.userid is None:
            self.user_reference = self.get_user_reference()
        super(User, self).save(*args, **kwargs)

        if self.__user_location is None and self.user_location is not None:
            pincode = self.create_pincode_from_city(self.user_location)
            if pincode:
                User.objects.filter(userid=self.userid).update(pincode=pincode)

        if self.user_name is None and self.email is not None:
            user_name_auto = self.create_user_name(self.email)
            User.objects.filter(userid=self.userid).update(user_name=user_name_auto)

        # user_id = User.objects.get(userid=self.userid)
        # if self.invite_type == "NON_MEMBER":
        #     InviteActivity.objects.create(user_id=user_id, invite_type=self.invite_type)
        # # if self.icon:
        # # new_image = compress(self.icon, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
        # #     self.icon = new_image
        # if self.invite_type != self.__invite_type:

        #     invite_user_obj = InviteUser.objects.get(invite_code=self.invite_code)
        #     invite_status = invite_user_obj.invite_status
        #     InviteActivity.objects.create(
        #         invite_id=invite_user_obj,
        #         user_id=user_id,
        #         invite_type=self.invite_type,
        #         invite_code=self.invite_code,
        #     )

        # self.__invite_type = self.invite_type
        self.__user_location = self.user_location

    class Meta:
        db_table = '"user"."user"'


class UserFollow(models.Model):
    user_follow_id = models.AutoField(primary_key=True)

    user_reference = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="user_reference",
        db_column="user_reference",
        related_name="following"
    )

    user_follower = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="user_reference",
        db_column="user_follower",
        related_name="userfollower"
    )

    store_follower = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        db_column="store_follower",
        related_name="storefollower"
    )

    is_following = models.BooleanField(default=False)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "user follow"
        db_table = '"user"."user_follow"'


class UserStore(models.Model):
    userstoreid = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        to_field="store_reference",
        null=True,
        blank=True,
        related_name="supportsss",
        db_column="store_reference",
    )
    user_supporter = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        to_field="user_reference",
        null=True,
        blank=True,
        related_name="usersupporter",
        db_column="user_supporter",
    )
    store_supporter = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        to_field="store_reference",
        null=True,
        blank=True,
        related_name="storesupporter",
        db_column="store_supporter",
    )

    is_following = models.BooleanField(default=False)
    is_visited = models.BooleanField(default=False)
    modified_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "user stores"
        db_table = '"user"."user_store"'


class UnregisteredUser(models.Model):
    unregistered_user_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    phonenumber = PhoneNumberField()
    user_follower = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="user_reference",
        db_column="user_follower",
        related_name="unregistereduserfollower"
    )
    store_follower = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        db_column="store_follower",
        related_name="unregisteredstorefollower"
    )
    is_following = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "unregistered users"
        db_table = '"user"."unregistered_users"'


class UserAddress(models.Model):
    BILLING = "BILLING"
    DELIVERY = "DELIVERY"

    ADDRESS_TYPE_CHOICES = [
        (DELIVERY, "delivery"),
        (BILLING, "billing"),
    ]
    useraddressid = models.AutoField(primary_key=True)
    userid = models.ForeignKey(
        "users.User", on_delete=models.CASCADE, db_column="userid"
    )
    address = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    pincode = models.CharField(max_length=6, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    address_type = models.CharField(
        max_length=200, choices=ADDRESS_TYPE_CHOICES, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = "user address"
        db_table = '"user"."user_address"'


class UserProduct(models.Model):
    userproductid = models.AutoField(primary_key=True)
    userid = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="get_users",
        db_column="userid",
    )
    productid = models.ForeignKey(
        "products.Product",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_column="productid",
        related_name="saved_products"
    )
    is_saved = models.BooleanField(default=False)
    is_ordered = models.BooleanField(default=False)
    is_visited = models.BooleanField(default=False)
    modified_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "user products"
        db_table = '"user"."user_product"'


class UserDeviceDetails(models.Model):
    user_device_details_id = models.AutoField(primary_key=True)
    user_reference = models.ForeignKey(
        "users.User",
        to_field="user_reference",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_column="user_reference",
        related_name="device_details"
    )
    device_id = models.CharField(max_length=30, blank=True, null=True)
    fcm_token = models.CharField(max_length=300, blank=True, null=True)
    user_app_version = models.CharField(max_length=10, blank=True, null=True, default=None)

    class Meta:
        verbose_name = "User Device Detail"
        verbose_name_plural = "User Device Details"
        db_table = '"user"."user_device_details"'


class UserReferralCodeTemp(models.Model):
    user_referral_code_temp_id = models.AutoField(primary_key=True)
    user_device_id = models.CharField(max_length=30, blank=True, null=True)
    referral_code = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        verbose_name = "User Referral Code Temp"
        verbose_name_plural = "User Referral Codes Temp"
        db_table = '"user"."user_referral_code_temp"'


class UserSubscription(models.Model):
    user_subscription_id = models.AutoField(primary_key=True)
    class Subscription_Status(models.TextChoices):
        ACTIVE = "ACTIVE", _("Active")
        INACTIVE = "INACTIVE", _("Inactive")
        EXPIRED = "EXPIRED", _("Expired")

    user_reference = models.ForeignKey(
        "users.User",
        to_field="user_reference",
        on_delete=models.CASCADE,
        related_name="user_subscriptions",
        db_column="user_reference",
    )
    subscription_plan_reference = models.ForeignKey(
        "swadesic_admin.SubscriptionPlan",
        to_field="plan_reference",
        on_delete=models.CASCADE,
        related_name="user_subscriptions",
        db_column="subscription_plan_reference",
    )
    subscription_start_date = models.DateTimeField(auto_now_add=True)
    subscription_end_date = models.DateTimeField(blank=True, null=True)
    subscription_status = models.CharField(max_length=100, choices=Subscription_Status.choices, default=Subscription_Status.INACTIVE)
    razorpay_payment_id = models.CharField(max_length=100, blank=True, null=True)
    razorpay_subscription_id = models.CharField(max_length=100, blank=True, null=True)
    auto_renew = models.BooleanField(default=False)

    # Add new fields for cancellation tracking
    is_scheduled_for_cancellation = models.BooleanField(default=False)
    scheduled_cancellation_date = models.DateTimeField(null=True, blank=True)
    cancellation_reason = models.TextField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = '"user"."user_subscription"'




