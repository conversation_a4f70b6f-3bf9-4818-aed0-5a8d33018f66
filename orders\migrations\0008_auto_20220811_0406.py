# Generated by Django 3.2.13 on 2022-08-10 22:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0007_order_transaction_date"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="order",
            name="txn_token",
            field=models.Char<PERSON>ield(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="bank_transaction_id",
            field=models.Char<PERSON>ield(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="payment_mode",
            field=models.Cha<PERSON><PERSON><PERSON>(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="transaction_date",
            field=models.Char<PERSON><PERSON>(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="transaction_id",
            field=models.Char<PERSON>ield(max_length=500, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="orderlifecycle",
            name="txn_token",
            field=models.<PERSON><PERSON><PERSON><PERSON>(max_length=500, null=True),
        ),
    ]
