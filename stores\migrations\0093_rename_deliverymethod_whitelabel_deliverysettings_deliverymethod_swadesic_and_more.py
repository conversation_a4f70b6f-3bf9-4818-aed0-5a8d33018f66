# Generated by Django 4.2.7 on 2025-03-21 18:29

import django.contrib.postgres.fields
from django.db import migrations, models

def populate_store_address_type(apps, schema_editor):
    Address = apps.get_model('stores', 'Address')
    for address in Address.objects.all():
        if address.is_pickup_location:
            address.store_address_type = ["IN_STORE_PICKUP"]
        else:
            address.store_address_type = ["NORMAL"]
        address.save()


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0092_store_store_ai_messaging_token_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="deliverysettings",
            old_name="deliverymethod_whitelabel",
            new_name="deliverymethod_swadesic",
        ),
        migrations.AddField(
            model_name="address",
            name="pickup_location_in_shiprocket",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="address",
            name="store_address_type",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(blank=True, max_length=100),
                blank=True,
                default=list,
                null=True,
                size=None,
            ),
        ),
        migrations.RunPython(
            populate_store_address_type,
            reverse_code=migrations.RunPython.noop
        ),
    ]
