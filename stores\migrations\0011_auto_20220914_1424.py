# Generated by Django 3.2.13 on 2022-09-14 08:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0010_storelink_store_reference"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="documents",
            name="storeid",
        ),
        migrations.RemoveField(
            model_name="trustcenter",
            name="storeid",
        ),
        migrations.AddField(
            model_name="documents",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                default="S3785476",
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.store",
                to_field="store_reference",
            ),
        ),
        migrations.AddField(
            model_name="trustcenter",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                default="S3785476",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="trustcenter",
                to="stores.store",
                to_field="store_reference",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="storelink",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="storelinks",
                to="stores.store",
                to_field="store_reference",
            ),
        ),
    ]
