# Generated by Django 3.2.13 on 2022-07-26 14:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("stores", "0001_initial"),
        ("products", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="product",
            name="created_by",
            field=models.ForeignKey(
                db_column="created_by",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_person",
                to="stores.user",
            ),
        ),
        migrations.AddField(
            model_name="product",
            name="modified_by",
            field=models.ForeignKey(
                db_column="modified_by",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="modified_person",
                to="stores.user",
            ),
        ),
        migrations.AddField(
            model_name="product",
            name="storeid",
            field=models.ForeignKey(
                db_column="storeid",
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.store",
            ),
        ),
    ]
