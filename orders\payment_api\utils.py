import base64
import json
import datetime
import string
import random
import pytz
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from rest_framework.response import Response
from ..payment_api.models import SubOrderPaymentDetails
import hmac
import hashlib
import requests
from requests.auth import HTTPBasicAuth
from decouple import config
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")


def calculate_sha256_string(input_string):
    # Create a hash object using the SHA-256 algorithm
    sha256 = hashes.Hash(hashes.SHA256(), backend=default_backend())
    # Update hash with the encoded string
    sha256.update(input_string.encode('utf-8'))
    # Return the hexadecimal representation of the hash
    return sha256.finalize().hex()


def base64_encode(input_dict):
    # Convert the dictionary to a JSON string
    json_data = json.dumps(input_dict)
    # Encode the JSON string to bytes
    data_bytes = json_data.encode('utf-8')
    # Perform Base64 encoding and return the result as a string
    return base64.b64encode(data_bytes).decode('utf-8')


def get_dummy_txn_token():
    now = datetime.datetime.now()
    code = now.strftime("%y%m%d%H%M%S")
    my_code = ("test_", code)
    return "".join(my_code)


def create_refund_id(suborder_number):
    random_four_length_character = "".join(random.choices(string.ascii_uppercase, k=4))
    id_from_suborder = suborder_number.replace("O", "RE")
    final_code = (id_from_suborder, random_four_length_character)
    refund_id = "".join(final_code)
    if SubOrderPaymentDetails.objects.filter(refund_id=refund_id).exists():
        refund_id = create_refund_id(suborder_number)
        return refund_id
    return refund_id


def get_date():
    date = datetime.datetime.now()
    local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
    return local_time.strftime("%Y-%m-%d %H:%M:%S")


def get_payment_channel_enum(payment_channel):
    # Dictionary to map UPI app names to their package names
    upi_app_packages = {
        "com.phonepe.app": "PHONEPE",
        "com.google.android.apps.nbu.paisa.user": "GPAY",
        "net.one97.paytm": "PAYTM",
        "com.lcode.allahabadupi": "ALLBANK_UPI",
        "in.amazon.mShop.android.shopping": "AMAZON_PAY",
        "com.upi.axispay": "AXIS_PAY",
        "com.bankofbaroda.upi": "BARODA_PAY",
        "in.org.npci.upiapp": "BHIM",
        "com.infra.boiupi": "BOI_UPI",
        "com.infrasofttech.centralbankupi": "CENT_UPI",
        "in.cointab.app": "COINTAB",
        "com.lcode.corpupi": "CORP_UPI",
        "com.lcode.csbupi": "CSB_UPI",
        "com.cub.wallet.gui": "CUB_UPI",
        "com.olive.dcb.upi": "DCB_UPI",
        "com.dbs.in.digitalbank": "DIGIBANK",
        "com.equitasbank.upi": "EQUITAS_UPI",
        "com.finopaytech.bpayfino": "FINO_BPAY",
        "com.freecharge.android": "FREECHARGE",
        "com.mgs.hsbcupi": "HSBC_SIMPLY_PAY",
        "com.csam.icici.bank.imobile": "IMOBILE_ICICI",
        "com.mgs.induspsp": "INDUS_PAY",
        "com.khaalijeb.inkdrops": "KHAALI_JEB",
        "com.msf.kbank.mobile": "KOTAK",
        "com.infrasofttech.mahaupi": "MAHA_UPI",
        "com.mipay.in.wallet": "MI_PAY_GLOBAL",
        "com.mipay.wallet.in": "MI_PAY_XIAOMI",
        "com.mobikwik_new": "MOBIKWIK",
        "com.mgs.obcbank": "ORIENTAL_PAY",
        "com.idbibank.paywiz": "PAYWIZ",
        "com.enstage.wibmo.hdfc": "PAYZAPP",
        "com.fss.pnbpsp": "PNB",
        "com.mobileware.upipsb": "PSB",
        "com.rblbank.upi": "RBL_PAY",
        "com.realmepay.payments": "REALME_PAYSA",
        "com.sbi.upi": "SBI_PAY",
        "com.truecaller": "TRUECALLER_UPI",
        "com.fss.unbipsp": "UNITED_UPI_PAY",
        "com.fss.vijayapsp": "VIJAYA_UPI",
        "com.YesBank": "YES_PAY"
    }
    if "@" in payment_channel:
        return payment_channel
    else:
        return upi_app_packages.get(payment_channel, None)


def verify_signature(razorpay_order_id, razorpay_payment_id, secret, razorpay_signature):
    try:
        # Combine the order_id and razorpay_payment_id with a pipe character
        message = f"{razorpay_order_id}|{razorpay_payment_id}"

        # Create a HMAC object using the secret key and the SHA256 hash algorithm
        generated_signature = hmac.new(secret.encode(), message.encode(), hashlib.sha256).hexdigest()

        # Compare the generated signature with the provided razorpay_signature
        if generated_signature == razorpay_signature:
            return "PAYMENT_SUCCESS"
        else:
            return "PAYMENT_FAILED"
    except Exception as e:
        # Log the error for debugging purposes
        logger.error(f"Error in verify_signature: {str(e)}")
        return "VERIFICATION_ERROR"


def capture_razorpay_payment(razorpay_payment_id, amount, currency):
    url = f'https://api.razorpay.com/v1/payments/{razorpay_payment_id}/capture'
    payload = {
        'amount': amount,
        'currency': currency
    }
    headers = {'content-type': 'application/json'}

    # Send the POST request
    response = requests.post(
        url,
        json=payload,
        auth=HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY),
        headers=headers
    )
    response_json = response.json()
    return response_json["captured"]


def get_payment_details(razorpay_payment_id):

    try:
        url = f'https://api.razorpay.com/v1/payments/{razorpay_payment_id}'

        # Send the GET request
        response = requests.get(
            url,
            auth=HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY)
        )
        return response.json()

    except Exception as e:
        return Response(
            {"message": "error", "data": f"Razorpay payment details could not be fetched-{e}"},
            status=400
        )


def get_txn_id_from_acquirer_data(acquirer_data, method):
    data = ""
    if method == 'netbanking':
        return data
    elif method == 'upi':
        return acquirer_data.get("rrn", "")
    elif method == 'card':
        return data
    elif method == 'wallet':
        return acquirer_data.get("transaction_id", "")
    else:
        return data


def get_bank_txn_id_from_acquirer_data(acquirer_data, method):
    data = ""
    if method == 'netbanking':
        return acquirer_data.get("bank_transaction_id", None)
    elif method == 'card':
        return data
    else:
        return data


def get_payment_channel(payment_details):
    data = ""
    if not payment_details:
        return data
    if payment_details["method"] == 'netbanking':
        return payment_details["bank"]
    elif payment_details["method"] == 'upi':
        return payment_details["vpa"]
    elif payment_details["method"] == 'card':
        return payment_details["card_id"]
    elif payment_details["method"] == 'wallet':
        return payment_details["wallet"]
    else:
        return data


def get_refund_txn_id_from_acquirer_data(acquirer_data):
    if acquirer_data.get("rrn"):
        return acquirer_data["rrn"]
    elif acquirer_data.get("arn"):
        return acquirer_data["arn"]
    elif acquirer_data.get("utr"):
        return acquirer_data["utr"]
    else:
        return ""








