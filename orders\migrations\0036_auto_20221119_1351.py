# Generated by Django 3.2.13 on 2022-11-19 08:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0035_orderpaymentdetails_order_request_number"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderpaymentdetails",
            name="refund_amount",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderpaymentdetails",
            name="refund_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.CreateModel(
            name="OrderPaymentDetailsLifeCycle",
            fields=[
                (
                    "order_payment_details_lifecycle_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "payment_status",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("refund_id", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "order_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="order_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.order",
                        to_field="order_number",
                    ),
                ),
                (
                    "suborder_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="suborder_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.suborder",
                        to_field="suborder_number",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "order payment details lifecycle",
                "db_table": '"order"."order_payment_details_lifecycle"',
            },
        ),
    ]
