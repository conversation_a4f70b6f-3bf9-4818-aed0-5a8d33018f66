from django.urls import path
from .views import (
    # NewUserLoginAV,
    # UserLoginAV,
    # OtpCheckAV,
    # NewOtpCheckAV,
    UpdatePincodeAV,
    AddAndGetUserProfile,
    CheckUserNameAvailability,
    UserAddressList,
    UserAddressDetails,
    GetAllUserAddress,
    DeleteUser,
    CheckUserRole,
    GetUserDetails,
    AddAndGetUserProfilePicture,
    FollowUnfollowUser,
    UserFollowerList,
    UserDeviceDetailsAV,
    CheckUserDeviceEntry,
    BulkDeviceDetailsDelete,
    FollowUnregisteredUser,
    GetUserAndStoreProfiles,
    FollowOrSupportAll,
    # GoogleLoginApi,
    UserSignIn,
    VerifyUserSignIn,
    ResendOtp,
    CheckUserInfo,
    AddReferralCodeTemp,
    GetChatEntityRecommendations,
    GetUserContributions
    )

urlpatterns = [
    # Authentication and Registration
    path("check_user_info/<str:phonenumber>/", CheckUserInfo.as_view(), name='check-user-info'),
    path("signin/", UserSignIn.as_view(), name='user-signin'),
    path("verify_signin/", VerifyUserSignIn.as_view(), name='verify-user-signin'),
    path("resend_otp/", ResendOtp.as_view(), name='resend-otp'),
    # path("googlelogin/", GoogleLoginApi.as_view(), name='google-user-login'),
    # path("newuserlogin/", NewUserLoginAV.as_view(), name='new-user-login'),
    # path("newcheckotp/", NewOtpCheckAV.as_view(), name='new-check-otp'),
    # path("userlogin/", UserLoginAV.as_view(), name="user-login"),
    # path("checkotp/", OtpCheckAV.as_view(), name="check-otp"),

    # User Profile Management
    path("userprofile/<str:user_reference>/", AddAndGetUserProfile.as_view(), name="user-profile"),
    path("user_name_availability/", CheckUserNameAvailability.as_view(), name="user-name-availability"),
    path("user_profile_picture/<str:user_reference>/", AddAndGetUserProfilePicture.as_view(), name="user-profile-picture"),
    path("delete_user/<int:user_id>/", DeleteUser.as_view(), name="delete-user"),
    path("get_user_details/<str:user_reference>/", GetUserDetails.as_view(), name="get-user-details"),
    path("get_user_details/<str:user_reference>/<str:private>/", GetUserDetails.as_view(), name="get-user-details"),
    path("check_user_role/<int:user_id>/", CheckUserRole.as_view(), name="check-user-role"),

    # User Address Management
    path("user_address_create/", UserAddressList.as_view(), name="user-address-create"),
    path("user_address_updates/<int:pk>/", UserAddressDetails.as_view(), name="user-address-updates"),
    path("get_all_user_address/<int:user_id>/", GetAllUserAddress.as_view(), name="get-all-user-address"),
    path("updatepincode/<int:pk>/", UpdatePincodeAV.as_view(), name="update-pincode"),

    # Social Features
    path("follow_unfollow_user/", FollowUnfollowUser.as_view(), name="follow-unfollow-user"),
    path("user_follower_list/<str:user_reference>/", UserFollowerList.as_view(), name="follow-unfollow-user"),
    path("follow_unregistered_user/", FollowUnregisteredUser.as_view(), name="follow-unregistered-user"),
    path("follow_or_support_all/", FollowOrSupportAll.as_view(), name="follow-or-support-all"),
    path("get_user_contributions/<str:user_reference>/", GetUserContributions.as_view(), name="get-user-contributions"),

    # Device Management
    path("user_device_details/<str:user_reference>/", UserDeviceDetailsAV.as_view(), name="user-device-details"),
    path("check_user_device_entry/<str:user_reference>/<str:device_id>/", CheckUserDeviceEntry.as_view(), name="check-user-device-entry"),
    path("bulk_delete_user_device_entries/<str:user_reference>/", BulkDeviceDetailsDelete.as_view(), name="bulk-device-detail-delete"),

    # Miscellaneous
    path("get_user_and_store_profiles/", GetUserAndStoreProfiles.as_view(), name="get-user-and-store-profiles"),
    path("save_referral_code_temp/", AddReferralCodeTemp.as_view(), name="save_referral_code_temp"),
    path("get_chat_entity_recommendations/", GetChatEntityRecommendations.as_view(), name="get-chat-entity-recommendations")
]
