from django.urls import path
from .views import (
    SearchAV,
    SearchHistoryAV,
    SearchedItemsAV,
    RecentSearch,
    ClearSearchHistory,
    ClearOneSearchHistory,
    GetSearchResultsCount,
    TypingSuggestionsAV,
    EntityContentSearchAV
)

urlpatterns = [
    path("search/", SearchAV.as_view(), name="search"),
    path("add_search_history/", SearchHistoryAV.as_view(), name="add-search-history"),
    path("add_searched_item/", SearchedItemsAV.as_view(), name="add-search-item"),
    path("search_history/<str:user_reference>/", RecentSearch.as_view(), name="search-history"),
    path("clear_search_history/<str:user_reference>/", ClearSearchHistory.as_view(), name="clear-search-history"),
    path("clear_one_search_history/", ClearOneSearchHistory.as_view(), name="clear-one-search-history"),
    path("get_search_results_count/", GetSearchResultsCount.as_view(), name="get-search-result-flags"),
    path("typing_suggestions/", TypingSuggestionsAV.as_view(), name="optimized-content-search"),
    path("entity/content_search/", EntityContentSearchAV.as_view(), name="entity-content-search"),
]
