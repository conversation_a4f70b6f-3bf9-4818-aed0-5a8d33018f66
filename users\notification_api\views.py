from rest_framework.response import Response
from rest_framework import generics, status
from rest_framework.views import APIView
from drf_yasg.utils import swagger_auto_schema
import datetime
from .models import Notifications
from .serializers import NotificationsSerializer
from django.db.models import Q
from drf_yasg import openapi
from ..user_api.models import User
from stores.store_api.models import Store
from django.utils import timezone




class AllNotifications(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="get all notifications")
    def get(self, request, *args, **kwargs):
        """
        get all notifications of given notified user. Notified user can be a user or a store.
        """
        notified_user = kwargs["notified_user"]
        # Default values for limit and offset
        default_limit = 10
        default_offset = 0

        # Retrieve limit and offset from query parameters
        limit = request.query_params.get('limit', default_limit)
        offset = request.query_params.get('offset', default_offset)

        # Convert limit and offset to integers
        limit = int(limit)
        offset = int(offset)
        not_seen_count = Notifications.objects.filter(
            notified_user=notified_user,
            is_hidden=False,
            notification_status=Notifications.Notification_Status.NOT_SEEN,
        ).count()

        # today = datetime.date.today()
        # yesterday = today - datetime.timedelta(1)
        # this_week = yesterday - datetime.timedelta(7)

        notifications = Notifications.objects.filter(notified_user=notified_user, is_hidden=False).exclude(notification_type=Notifications.Notifications_Type.MESSAGE_RECEIVED).order_by("-created_date")

        if notifications:
            notifications = notifications[offset:offset+limit] 
        else:
            notifications = []

        
    
        notifications_serialized_data = NotificationsSerializer(
            notifications, many=True
        )

        # today_notifications = Notifications.objects.filter(
        #     notified_user=notified_user, is_hidden=False, created_date__date=today
        # ).order_by("-created_date")
        # today_notifications_serialized_data = NotificationsSerializer(
        #     today_notifications, many=True
        # )

        # yesterday_notifications = Notifications.objects.filter(
        #     notified_user=notified_user, is_hidden=False, created_date__date=yesterday
        # ).order_by("-created_date")
        # yesterday_notifications_serialized_data = NotificationsSerializer(
        #     yesterday_notifications, many=True
        # )

        # this_week_notifications = Notifications.objects.filter(
        #     notified_user=notified_user,
        #     is_hidden=False,
        #     created_date__date__range=[this_week, (today - datetime.timedelta(2))],
        # ).order_by("-created_date")
        # this_week_notifications_serialized_data = NotificationsSerializer(
        #     this_week_notifications, many=True
        # )

        # past_week_notifications = Notifications.objects.filter(
        #     notified_user=notified_user,
        #     is_hidden=False,
        #     created_date__date__lt=this_week,
        # ).order_by("-created_date")
        # past_week_notifications_serialized_data = NotificationsSerializer(
        #     past_week_notifications, many=True
        # )

        return Response(
            {
                "message": "success",
                "not_seen_count": not_seen_count,
                "notifications": notifications_serialized_data.data,
                # "today": today_notifications_serialized_data.data,
                # "yesterday": yesterday_notifications_serialized_data.data,
                # "this_week": this_week_notifications_serialized_data.data,
                # "past_week": past_week_notifications_serialized_data.data,
            },
            status=status.HTTP_200_OK,
        )


class AllStoreNotifications(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="get all notifications")
    def get(self, request, *args, **kwargs):
        """
        get all stores notifications of given user.
        """
        user_reference = self.kwargs.get("user_reference")
        user_instance = User.objects.get(user_reference=user_reference, deleted= False)
        userid = user_instance.userid

        user_stores = Store.objects.filter(created_by=userid,deleted=False).values_list('store_reference')

        default_limit = 10
        default_offset = 0

        # Retrieve limit and offset from query parameters
        limit = request.query_params.get('limit', default_limit)
        offset = request.query_params.get('offset', default_offset)

        # Convert limit and offset to integers
        limit = int(limit)
        offset = int(offset)

        not_seen_count = Notifications.objects.filter(
            notified_user__in=user_stores,
            is_hidden=False,
            notification_status=Notifications.Notification_Status.NOT_SEEN,
        ).count()

        notifications = Notifications.objects.filter(notified_user__in=user_stores, is_hidden=False).exclude(notification_type=Notifications.Notifications_Type.MESSAGE_RECEIVED).order_by("-created_date")

        if notifications:
            notifications = notifications[offset:offset + limit]
        else:
            notifications = []

        notifications_serialized_data = NotificationsSerializer(
            notifications, many=True
        )

        # all_today_notifications = []
        # all_yesterday_notifications = []
        # all_this_week_notifications = []
        # all_past_week_notifications = []
        #
        # today = datetime.date.today()
        # yesterday = today - datetime.timedelta(1)
        # this_week = today - datetime.timedelta(7)

        # for user_store in user_stores:
        #     not_seen_count = Notifications.objects.filter(
        #         notified_user=user_store.store_reference,
        #         is_hidden=False,
        #         notification_status=Notifications.Notification_Status.NOT_SEEN,
        #     ).count()
        #
        #     today_notifications = Notifications.objects.filter(
        #         notified_user=user_store.store_reference, is_hidden=False, created_date__date=today
        #     ).order_by("-created_date")
        #     today_notifications_serialized_data = NotificationsSerializer(
        #         today_notifications, many=True
        #     )
        #
        #     yesterday_notifications = Notifications.objects.filter(
        #         notified_user=user_store.store_reference, is_hidden=False, created_date__date=yesterday
        #     ).order_by("-created_date")
        #     yesterday_notifications_serialized_data = NotificationsSerializer(
        #         yesterday_notifications, many=True
        #     )
        #
        #     this_week_notifications = Notifications.objects.filter(
        #         notified_user=user_store.store_reference,
        #         is_hidden=False,
        #         created_date__date__range=[this_week, (today - datetime.timedelta(2))],
        #     ).order_by("-created_date")
        #     this_week_notifications_serialized_data = NotificationsSerializer(
        #         this_week_notifications, many=True
        #     )
        #
        #     past_week_notifications = Notifications.objects.filter(
        #         notified_user=user_store.store_reference,
        #         is_hidden=False,
        #         created_date__date__lt=this_week,
        #     ).order_by("-created_date")
        #     past_week_notifications_serialized_data = NotificationsSerializer(
        #         past_week_notifications, many=True
        #     )
        #
        #     all_not_seen_count += not_seen_count
        #     all_today_notifications.extend(today_notifications_serialized_data.data)
        #     all_yesterday_notifications.extend(yesterday_notifications_serialized_data.data)
        #     all_this_week_notifications.extend(this_week_notifications_serialized_data.data)
        #     all_past_week_notifications.extend(past_week_notifications_serialized_data.data)

        return Response(
            {
                "message": "success",
                "not_seen_count": not_seen_count,
                "notifications": notifications_serialized_data.data,
                # "today": all_today_notifications,
                # "yesterday": all_yesterday_notifications,
                # "this_week": all_this_week_notifications,
                # "past_week": all_past_week_notifications,
            },
            status=status.HTTP_200_OK,
        )

        # Use the below code to get notifications of each store in user_stores list separately

        # for user_store in user_stores:
        #     not_seen_count = Notifications.objects.filter(
        #         notified_user=user_store,
        #         is_hidden=False,
        #         notification_status=Notifications.Notification_Status.NOT_SEEN,
        #     ).count()
        #
        #     today = datetime.date.today()
        #     yesterday = today - datetime.timedelta(1)
        #     this_week = yesterday - datetime.timedelta(7)
        #
        #     today_notifications = Notifications.objects.filter(
        #         notified_user=user_store, is_hidden=False, created_date__date=today
        #     ).order_by("-created_date")
        #     today_notifications_serialized_data = NotificationsSerializer(
        #         today_notifications, many=True
        #     )
        #
        #     yesterday_notifications = Notifications.objects.filter(
        #         notified_user=user_store, is_hidden=False, created_date__date=yesterday
        #     ).order_by("-created_date")
        #     yesterday_notifications_serialized_data = NotificationsSerializer(
        #         yesterday_notifications, many=True
        #     )
        #
        #     this_week_notifications = Notifications.objects.filter(
        #         notified_user=user_store,
        #         is_hidden=False,
        #         created_date__date__range=[this_week, (today - datetime.timedelta(2))],
        #     ).order_by("-created_date")
        #     this_week_notifications_serialized_data = NotificationsSerializer(
        #         this_week_notifications, many=True
        #     )
        #
        #     past_week_notifications = Notifications.objects.filter(
        #         notified_user=user_store,
        #         is_hidden=False,
        #         created_date__date__lt=this_week,
        #     ).order_by("-created_date")
        #     past_week_notifications_serialized_data = NotificationsSerializer(
        #         past_week_notifications, many=True
        #     )
        #
        #     all_notifications.append({
        #         "not_seen_count": not_seen_count,
        #         "today": today_notifications_serialized_data.data,
        #         "yesterday": yesterday_notifications_serialized_data.data,
        #         "this_week": this_week_notifications_serialized_data.data,
        #         "past_week": past_week_notifications_serialized_data.data,
        #     })
        # return Response(
        #     {
        #         "message": "success",
        #         "notifications": all_notifications,
        #     },
        #     status=status.HTTP_200_OK,
        # )


class UpdateNotificationStatus(generics.CreateAPIView):
    @swagger_auto_schema (
        operation_summary="update the notification status",
        request_body=openapi.Schema (
            type=openapi.TYPE_OBJECT,
            required=["notification_list", "notified_user", "is_all_notifications"],
            properties={
                "notified_user": openapi.Schema (type=openapi.TYPE_STRING),
                "notification_list": openapi.Schema (
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(type=openapi.TYPE_STRING)),
                "is_all_notifications": openapi.Schema(type=openapi.TYPE_BOOLEAN),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        """
        Update the notification status from SEEN to UNSEEN. input will be 3 parameters
        1.notification list
        2.notified user list
        3.Is all notifications (boolean flag)

        ----> if 'is all notifications' is true then all the notifications of entities(either store or user)
        in notified user list will be marked as SEEN
        ----> if 'notification list' exists then the particular notifications (mentioned in notification_list) of the user
        in notified user list will be marked as SEEN
        """
        # new optimised code
        try:
            notification_list = request.data.get('notification_list', [])
            notified_user = request.data.get('notified_user', [])
            is_all_notifications = request.data.get('is_all_notifications', False)

            query = Notifications.objects.filter(
                notified_user__in=notified_user,
                is_hidden=False
            )

            if is_all_notifications:
                query.update(notification_status=Notifications.Notification_Status.SEEN)
            elif notification_list:
                query.filter(
                    notification_reference__in=notification_list,
                    notified_user__in=notified_user
                ).update(notification_status=Notifications.Notification_Status.SEEN)

            return Response({"message": "success", "data": "updated"}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"message": "error", "error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # New update notification status Api(unoptimised)
        # try:
        #     notification_list = request.data.get('notification_list', [])
        #     notified_user = request.data.get('notified_user', [])
        #     is_all_notifications = request.data.get('is_all_notifications', False)
        #
        #     if not notification_list and is_all_notifications:
        #         # Update all notifications for each user in the list
        #         Notifications.objects.filter(
        #             notified_user__in=notified_user,
        #             is_hidden=False
        #         ).update(notification_status=Notifications.Notification_Status.SEEN)
        #
        #     elif notification_list and not is_all_notifications:
        #         # Update specific notifications for each user in the list
        #         Notifications.objects.filter(
        #             notified_user__in=notified_user,
        #             notification_reference__in=notification_list,
        #             is_hidden=False
        #         ).update(notification_status=Notifications.Notification_Status.SEEN)
        #
        #     return Response({"message": "success", "data": "updated"}, status=status.HTTP_200_OK)
        #
        # except Exception as e:
        #     return Response({"message": "error", "error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # Old update notification status  api
        # try:
        #     notification_list = request.data['notification_list']
        #     notified_user = request.data['notified_user']
        #     Notifications.objects.filter(notification_reference__in=notification_list,
        #                                  notified_user=notified_user,
        #                                  is_hidden=False).update(notification_status=Notifications.Notification_Status.SEEN)
        #     return Response({"message": "success", "data": "updated"}, status=status.HTTP_200_OK)
        # except:
        #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        #

class ClearAllNotifications(generics.UpdateAPIView):
    @swagger_auto_schema (operation_summary="clear all notifications")
    def update(self, request, *args, **kwargs):
        """
        Clear all notifications of a givn user
        """
        notified_user = kwargs['notified_user']
        try:
            Notifications.objects.filter(notified_user=notified_user, is_hidden=False).update(is_hidden=True)
            return Response ({"message": "success", "data": "all cleared"}, status=status.HTTP_200_OK)
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class DisappearNotifications(APIView):
    def get(self, request):
        Notifications.objects.filter(
            Q(is_hidden=False) &
            (Q(notification_type=Notifications.Notifications_Type.DELIVERY_OTP) | Q(notification_type=Notifications.Notifications_Type.RETURN_OTP)),
            Q(notification_hide_date__lt=datetime.date.today()),
        ).update(is_hidden=True)
        data = "removed expired notifications"
        return Response({"message": "success", "data": data}, status=status.HTTP_200_OK)
