# Generated by Django 3.2.13 on 2022-12-15 05:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0031_alter_productimages_options"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="comment",
            name="order_reference",
            field=models.Char<PERSON>ield(blank=True, max_length=15, null=True),
        ),
        migrations.AddField(
            model_name="comment",
            name="user_reference",
            field=models.Char<PERSON>ield(blank=True, max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name="comment",
            name="comment_type",
            field=models.Char<PERSON>ield(
                choices=[
                    ("review", "Review"),
                    ("comment", "Comment"),
                    ("question", "Question"),
                ],
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="comment",
            name="product_version",
            field=models.Char<PERSON>ield(blank=True, max_length=10, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="reply",
            name="clapped_users",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=300, null=True),
        ),
    ]
