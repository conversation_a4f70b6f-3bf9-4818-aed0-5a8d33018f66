from rest_framework import serializers
from .models import Notifications
import datetime
import pytz
from django.utils import timezone
from django.utils.timezone import utc
from ..user_api.models import User
from products.models import ProductImages
from stores.store_api.models import Store
from orders.order_api.models import SubOrder
from datetime import datetime, timedelta


class NotificationsSerializer(serializers.ModelSerializer):
    date = serializers.SerializerMethodField("get_date")
    time = serializers.SerializerMethodField("get_time")
    group = serializers.SerializerMethodField("get_group")
    action_page = serializers.SerializerMethodField("get_action_page")


    class Meta:
        model = Notifications
        fields = [
            "notification_reference",
            "notified_user",
            "notification_type",
            "notification_message",
            "notification_status",
            "notification_about",
            "date",
            "image",
            "time",
            "group",
            "action_page",
        ]
# get group method to get the group of the notification based on the notification time frame into today, yesterday, this week, this month, and later
    def get_group(self, obj):
        now = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        if obj.created_date >= now:
            return "Today"
        elif obj.created_date >= (now - timedelta(days=1)):
            return "Yesterday" 
        elif obj.created_date >= (now - timedelta(days=7)):
            return "This week"
        elif obj.created_date >= (now - timedelta(days=30)):
            return "This month"
        else:
            return "Later"

    def get_action_page(self, obj):
        notification_type = obj.notification_type

        # Reward-related notifications
        if notification_type in [
            Notifications.Notifications_Type.YOU_SIGNED_UP_WITH_SOMEONES_CODE,
            Notifications.Notifications_Type.STORE_VERIFICATION_ONBOARDING_REWARD,
            Notifications.Notifications_Type.STORE_VERIFICATION_PROMOTION_REWARD,
            Notifications.Notifications_Type.NORMAL_STORE_VERIFICATION_REWARD,
        ]:
            return "REWARDS"

        # Invitee-related notifications
        elif notification_type == Notifications.Notifications_Type.SOMEONE_SIGNED_UP_WITH_YOUR_CODE:
            return "MY_INVITEES"

        # Trust center related notifications
        elif notification_type in [
            Notifications.Notifications_Type.GST_VERIFICATION_APPROVED,
            Notifications.Notifications_Type.PAN_VERIFICATION_APPROVED,
            Notifications.Notifications_Type.GST_VERIFICATION_REJECTED,
            Notifications.Notifications_Type.PAN_VERIFICATION_REJECTED,
        ]:
            return "TRUST_CENTER"

        # Default case
        else:
            return None
                
    @staticmethod
    def convert_seconds_to_time_unit(seconds):
        # Define the conversion factors for each time unit
        seconds_in_a_minute = 60
        seconds_in_an_hour = 60 * seconds_in_a_minute
        seconds_in_a_day = 24 * seconds_in_an_hour
        seconds_in_a_week = 7 * seconds_in_a_day
        seconds_in_a_month = 30 * seconds_in_a_day
        seconds_in_year = 12 * seconds_in_a_month
        # Approximate average month length (30.44 days) but taken 30
        # for get a rounded value

        if seconds >= seconds_in_year:
            converted_time = seconds // seconds_in_year
            if converted_time == 1:
                time_unit = "y"
            else:
                time_unit = "y"

        elif seconds >= seconds_in_a_month:
            converted_time = seconds // seconds_in_a_month
            if converted_time == 1:
                time_unit = "mo"
            else:
                time_unit = "mo"

        elif seconds >= seconds_in_a_week:
            converted_time = seconds // seconds_in_a_week
            if converted_time == 1:
                time_unit = "w"
            else:
                time_unit = "w"

        elif seconds >= seconds_in_a_day:
            converted_time = seconds // seconds_in_a_day
            if converted_time == 1:
                time_unit = "d"
            else:
                time_unit = "d"

        elif seconds >= seconds_in_an_hour:
            converted_time = seconds // seconds_in_an_hour
            time_unit = "h"

        elif seconds >= seconds_in_a_minute:
            converted_time = seconds // seconds_in_a_minute
            time_unit = "m"

        else:
            converted_time = seconds
            time_unit = "s"

        return converted_time, time_unit

    def get_time(self, obj):
        now = datetime.utcnow().replace(tzinfo=utc)
        timediff = now - obj.created_date
        second = int(timediff.total_seconds())

        time_amount, time_unit = self.convert_seconds_to_time_unit(second)
        # print(f"{second} seconds is approximately {time_amount} {time_unit}.")
        return f"{time_amount}{time_unit}"

    def get_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")
