from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.fields import Array<PERSON>ield



# Create your models here.
class StoreShippingAccountBalance(models.Model):
    store_shipping_account_balance_id = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        'stores.Store', 
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        db_column="store_reference",
        related_name="store_shipping_balance"
    )
    shipping_balance = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "store_shipping_account_balance"
        db_table = '"order"."store_shipping_account_balance"'

class SwadesicShippingPackageDetails(models.Model):
    swadesic_shipping_package_details_id = models.AutoField(primary_key=True)
    package_number = models.CharField(max_length=50, null=True, blank=True)
    order_reference = models.CharField(max_length=50, null=True, blank=True)  # Swadesic Order ID
    store_reference = models.Char<PERSON><PERSON>(max_length=20, null=True, blank=True)  # Store reference
    user_reference = models.CharField(max_length=20, null=True, blank=True)  # User reference
    
    # Shiprocket-related details
    shiprocket_order_id = models.CharField(max_length=50, null=True, blank=True)  # Shiprocket Order ID
    shipment_id = models.CharField(max_length=50, null=True, blank=True)  # Shiprocket Shipment ID
    awb_number = models.CharField(max_length=50, null=True, blank=True)  # Air Waybill (AWB) number
    courier_name = models.CharField(max_length=100, null=True, blank=True)  # Assigned courier
    tracking_url = models.URLField(null=True, blank=True)  # Shiprocket tracking URL
    selected_courier_details = models.JSONField(default=dict, null=True, blank=True)  # Selected courier details
    label_url = models.URLField(null=True, blank=True)# Shiprocket label URL
    
    # Address details stored as JSON
    pickup_address = models.JSONField(default=dict)  # Store's pickup address
    delivery_address = models.JSONField(default=dict)  # Buyer's delivery address
    
    # Package dimensions & weight
    volumetric_weight = models.FloatField(null=True, blank=True)
    dead_weight = models.FloatField(null=True, blank=True)
    length = models.FloatField(null=True, blank=True)
    breadth = models.FloatField(null=True, blank=True)
    height = models.FloatField(null=True, blank=True)

    # Product details
    package_contents = models.JSONField(default=dict)  
    # Example: {"products": [{"sku": "P001", "name": "Product 1", "quantity": 2, "price": 499}]}

    # Shipping balance check
    shipping_cost = models.FloatField(null=True, blank=True)
    is_shipping_balance_deducted = models.BooleanField(default=False)

    # Tracking timeline
    tracking_milestones = models.JSONField(default=dict)  
    # Example: {"timeline": [{"status": "Out for Pickup", "timestamp": "2025-03-21 10:30"}]}

    # Status Flags       
    current_status = models.CharField(max_length=50, default="Yet to be Updated")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Swadesic Shipping Package Details"
        db_table = '"order"."swadesic_shipping_package_details"'


class StoreShippingBalanceHistory(models.Model):
    class PaymentStatus(models.TextChoices):
        SUCCESS = "SUCCESS", _("Success")
        FAILED = "FAILED", _("Failed")
        PENDING = "PENDING", _("Pending")

    class TransactionTypeChoices(models.TextChoices):
        CREDIT = "CREDIT", _("Credit")
        DEBIT = "DEBIT", _("Debit")

    store_shipping_balance_history_id = models.AutoField(primary_key=True)
    shipping_balance_payment_reference = models.CharField(max_length=100)
    store_reference = models.ForeignKey(
        'stores.Store',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="store_reference",
        db_column="store_reference",
        related_name="store_shipping_balance_history"
    )
    package_number = models.CharField(max_length=50, null=True, blank=True)
    order_reference = models.CharField(max_length=50, null=True, blank=True) 
    suborder_references = ArrayField(models.CharField(max_length=100, blank=True), null=True, blank=True, default=list)
    # Payment details
    txn_token = models.CharField(max_length=50, null=True)
    transaction_id = models.CharField(max_length=100, null=True)
    bank_transaction_id = models.CharField(max_length=100, null=True)
    payment_mode = models.CharField(max_length=50, null=True)
    payment_channel = models.CharField(max_length=30, null=True)
    transaction_date = models.CharField(max_length=50, null=True)
    transaction_type = models.CharField(choices=TransactionTypeChoices.choices, max_length=10)
    payment_status = models.CharField(choices=PaymentStatus.choices, max_length=10)
    payment_amount = models.FloatField(null=True)
    transaction_fee = models.FloatField(null=True, default=0)
    transaction_fee_tax = models.FloatField(null=True, default=0)
    pg_transctionfee_with_tax_perc = models.FloatField(null=True, default=0)
    pg_transctionfee_perc = models.FloatField(null=True, default=0)
    pg_gst_tax_perc = models.FloatField(null=True, default=0)
    razorpay_payment_id = models.CharField(max_length=50, null=True)
    notes = models.TextField(null=True, blank=True)
    is_refund_transaction = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "store_shipping_balance_history"
        db_table = '"order"."store_shipping_balance_history"'



