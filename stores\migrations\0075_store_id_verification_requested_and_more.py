# Generated by Django 4.2.7 on 2024-09-27 19:24

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "stores",
            "0074_alter_deliverysettings_distance_based_max_deliveryfee_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="store",
            name="ID_verification_requested",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[("GST", "GST"), ("PAN", "PAN")], max_length=3
                ),
                blank=True,
                default=list,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="store",
            name="verification_requested_time",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="storerewardshistory",
            name="reward_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("MONTHLY_CREDIT", "Monthly Credit"),
                    ("TRANSFER", "Transfer"),
                    ("REDEEM", "Redeem"),
                    ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                    (
                        "VERIFICATION_PROMOTIONAL_REWARD",
                        "Verification Promotional Reward",
                    ),
                ],
                max_length=40,
                null=True,
            ),
        ),
    ]
