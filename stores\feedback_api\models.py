from django.db import models
from django.utils.translation import gettext_lazy as _
from common.util.support_helper import compress
from django.utils import timezone
import time

class FeedBackFiles(models.Model):
    feedback_file_id = models.AutoField(primary_key=True)
    images = models.ImageField(upload_to="feed_back_image", null=True, blank=True)
    attachments = models.FileField(
        upload_to="feed_back_attachment", null=True, blank=True
    )
    created_date = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    feedback = models.ForeignKey(
        "stores.FeedBack",
        on_delete=models.CASCADE,
        db_column="feedback",
        related_name="feedback_files",
    )
    def save(self, *args, **kwargs):
        if self.images:
            new_image = compress(self.images, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
            self.images = new_image
        super(FeedBackFiles, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "feed back files"
        db_table = '"store"."feedback_files"'


class FeedBack(models.Model):
    class Feedback_Type(models.TextChoices):
        REPORT = "REPORT", _("Report")
        SUGGESTION = "SUGGESTION", _("Suggestion")
        USER_RESPONSE = "USER_RESPONSE", _("User response")
        SWADESIC_RESPONSE = "SWADESIC_RESPONSE", _("Swadesic response")
        USER_REPORT = "USER_REPORT", _("User Report")

    class Status_Type(models.TextChoices):
        CREATED = "CREATED", _("Created")
        REVIEWED = "REVIEWED", _("Reviewed")
        APPROVED = "APPROVED", _("Approved")
        REJECTED = "REJECTED", _("Rejected")
        PLANNED = "PLANNED", _("Planned")
        SNAR = "SNAR", _("SNAR")
        FIXED = "FIXED", _("Fixed")
        ARCHIVE = "ARCHIVE", _("Archive")

    feedback_id = models.AutoField(primary_key=True)
    ticket_reference = models.CharField(max_length=20, unique=True, editable=False, null=True, blank=True)
    feedback_type = models.CharField(
        max_length=30,
        choices=Feedback_Type.choices,
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=100, null=True, blank=True)
    screen_category = models.CharField(max_length=100, null=True, blank=True)
    brief = models.TextField(null=True, blank=True)
    details = models.TextField(null=True, blank=True)
    status = models.CharField(
        max_length=20,
        choices=Status_Type.choices,
        default=Status_Type.CREATED,
        null=True,
        blank=True,
    )
    group = models.CharField(max_length=100, null=True, blank=True)
    priority = models.PositiveIntegerField(null=True, blank=True)
    user_reference = models.CharField(max_length=20, blank=True, null=True)
    upvote_count = models.PositiveIntegerField(default=0)
    upvote_users = models.CharField(max_length=300, null=True, blank=True)
    responses = models.CharField(max_length=100, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    modified_date = models.DateField(auto_now=True, null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.ticket_reference:
            self.ticket_reference = self.generate_ticket_reference()
        super().save(*args, **kwargs)

    @classmethod
    def generate_ticket_reference(cls):
        prefix = 'TKT'
        timestamp = int(time.time() * 1000)  # Unix timestamp in milliseconds
        
        return f"{prefix}{timestamp}"

    class Meta:
        verbose_name_plural = "feed back"
        db_table = '"store"."feed_back"'
