from rest_framework import serializers
from swadesic_admin.models import SubscriptionPlan
from stores.store_api.models import StoreSubscription
from users.user_api.models import UserSubscription


class GetStoreSubscriptionPlanSerializer(serializers.ModelSerializer):
    plan_price_monthly = serializers.SerializerMethodField()
    plan_price_yearly = serializers.SerializerMethodField()
    plan_yearly_discount_string = serializers.SerializerMethodField()
    plan_reference_monthly = serializers.SerializerMethodField()
    plan_reference_yearly = serializers.SerializerMethodField()
    plan_status_string = serializers.SerializerMethodField()
    is_active = serializers.SerializerMethodField()
    razorpay_subscription_id = serializers.SerializerMethodField()
    is_subscription_cancelled = serializers.SerializerMethodField()

    class Meta:
        model = SubscriptionPlan
        fields = [
            'plan_name', 'plan_price_monthly', 'plan_price_yearly',
            'plan_yearly_discount_string', 'plan_description',
            'plan_reference_monthly', 'plan_reference_yearly',
            'plan_details', 'plan_status_string', 'is_active',
            'razorpay_subscription_id', 'is_subscription_cancelled'
        ]
    
    def get_is_subscription_cancelled(self, obj):
        store_reference = self.context.get('store_reference')
        current_subscription = StoreSubscription.objects.filter(
            store_reference=store_reference,
            subscription_status=StoreSubscription.Subscription_Status.ACTIVE,
            subscription_plan_reference__plan_name=obj.plan_name
        ).order_by('-subscription_start_date').first()
        if current_subscription and current_subscription.is_scheduled_for_cancellation:
            return True
        else:
            return False

    def get_razorpay_subscription_id(self, obj):
        store_reference = self.context.get('store_reference')
        current_subscription = StoreSubscription.objects.filter(
            store_reference=store_reference,
            subscription_plan_reference__plan_name=obj.plan_name
        ).order_by('-subscription_start_date').first()
        return current_subscription.razorpay_subscription_id if current_subscription else None

    def get_plan_price_monthly(self, obj):
        monthly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='monthly',
            is_active=True
        ).first()
        return float(monthly_plan.plan_amount) if monthly_plan else 0

    def get_plan_price_yearly(self, obj):
        yearly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='yearly',
            is_active=True
        ).first()
        return float(yearly_plan.plan_amount) if yearly_plan else 0

    def get_plan_yearly_discount_string(self, obj):
        monthly_price = self.get_plan_price_monthly(obj)
        yearly_price = self.get_plan_price_yearly(obj)
        if monthly_price and yearly_price:
            yearly_equivalent = monthly_price * 12
            savings = yearly_equivalent - yearly_price
            if savings > 0:
                return f"Save ₹{int(savings):,}"
        return ""

    def get_plan_reference_monthly(self, obj):
        monthly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='monthly',
            is_active=True
        ).first()
        return monthly_plan.plan_reference if monthly_plan else ""

    def get_plan_reference_yearly(self, obj):
        yearly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='yearly',
            is_active=True
        ).first()
        return yearly_plan.plan_reference if yearly_plan else ""

    def get_plan_status_string(self, obj):
        store_reference = self.context.get('store_reference')
        if not store_reference:
            return ""

        current_subscription = StoreSubscription.objects.filter(
            store_reference=store_reference,
            subscription_plan_reference__plan_name=obj.plan_name
        ).order_by('-subscription_start_date').first()

        if not current_subscription:
            return ""
        if current_subscription.subscription_status == StoreSubscription.Subscription_Status.ACTIVE:
            if current_subscription.is_scheduled_for_cancellation:
                return f"Valid till {current_subscription.scheduled_cancellation_date.strftime('%b %d %Y')}"
            else:
                return f"Auto renews on {current_subscription.subscription_end_date.strftime('%b %d %Y')}" if current_subscription.subscription_end_date else ""
        elif current_subscription.subscription_status == StoreSubscription.Subscription_Status.EXPIRED:
            return f"Expired on {current_subscription.subscription_end_date.strftime('%b %d %Y')}" if current_subscription.subscription_end_date else ""
        return ""

    def get_is_active(self, obj):
        store_reference = self.context.get('store_reference')
        if not store_reference:
            return False

        paid_plan_exists = StoreSubscription.objects.filter(
            store_reference=store_reference,
            subscription_status=StoreSubscription.Subscription_Status.ACTIVE
        ).exists()

        return (obj.plan_name == 'Free') != paid_plan_exists


class GetUserSubscriptionPlanSerializer(serializers.ModelSerializer):
    plan_price_monthly = serializers.SerializerMethodField()
    plan_price_yearly = serializers.SerializerMethodField()
    plan_yearly_discount_string = serializers.SerializerMethodField()
    plan_reference_monthly = serializers.SerializerMethodField()
    plan_reference_yearly = serializers.SerializerMethodField()
    plan_status_string = serializers.SerializerMethodField()
    is_active = serializers.SerializerMethodField()
    razorpay_subscription_id = serializers.SerializerMethodField()
    is_subscription_cancelled = serializers.SerializerMethodField()

    class Meta:
        model = SubscriptionPlan
        fields = [
            'plan_name', 'plan_price_monthly', 'plan_price_yearly',
            'plan_yearly_discount_string', 'plan_description',
            'plan_reference_monthly', 'plan_reference_yearly',
            'plan_details', 'plan_status_string', 'is_active',
            'razorpay_subscription_id', 'is_subscription_cancelled'
        ]

    def get_razorpay_subscription_id(self, obj):
        user_reference = self.context.get('user_reference')
        current_subscription = UserSubscription.objects.filter(
            user_reference=user_reference,
            subscription_plan_reference__plan_name=obj.plan_name
        ).order_by('-subscription_start_date').first()
        return current_subscription.razorpay_subscription_id if current_subscription else None

    def get_is_subscription_cancelled(self, obj):
        user_reference = self.context.get('user_reference')
        current_subscription = UserSubscription.objects.filter(
            user_reference=user_reference,
            subscription_status=UserSubscription.Subscription_Status.ACTIVE,
            subscription_plan_reference__plan_name=obj.plan_name
        ).order_by('-subscription_start_date').first()
        if current_subscription and current_subscription.is_scheduled_for_cancellation:
            return True
        else:               
            return False

    def get_plan_price_monthly(self, obj):
        monthly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='monthly',
            is_active=True
        ).first()
        return float(monthly_plan.plan_amount) if monthly_plan else 0

    def get_plan_price_yearly(self, obj):
        yearly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='yearly',
            is_active=True
        ).first()
        return float(yearly_plan.plan_amount) if yearly_plan else 0

    def get_plan_yearly_discount_string(self, obj):
        monthly_price = self.get_plan_price_monthly(obj)
        yearly_price = self.get_plan_price_yearly(obj)
        if monthly_price and yearly_price:
            yearly_equivalent = monthly_price * 12
            savings = yearly_equivalent - yearly_price
            if savings > 0:
                return f"Save ₹{int(savings):,}"
        return ""

    def get_plan_reference_monthly(self, obj):
        monthly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='monthly',
            is_active=True
        ).first()
        return monthly_plan.plan_reference if monthly_plan else ""

    def get_plan_reference_yearly(self, obj):
        yearly_plan = SubscriptionPlan.objects.filter(
            plan_name=obj.plan_name,
            plan_period='yearly',
            is_active=True
        ).first()
        return yearly_plan.plan_reference if yearly_plan else ""

    def get_plan_status_string(self, obj):
        user_reference = self.context.get('user_reference')
        if not user_reference:
            return ""
        current_subscription = UserSubscription.objects.filter(
            user_reference=user_reference,
            subscription_plan_reference__plan_name=obj.plan_name
        ).order_by('-subscription_start_date').first()

        if not current_subscription:
            return ""

        if current_subscription.subscription_status == UserSubscription.Subscription_Status.ACTIVE:
            if current_subscription.is_scheduled_for_cancellation:
                return f"Valid till {current_subscription.scheduled_cancellation_date.strftime('%b %d %Y')}"
            else:
                return f"Auto renews on {current_subscription.subscription_end_date.strftime('%b %d %Y')}" if current_subscription.subscription_end_date else ""

        elif current_subscription.subscription_status == UserSubscription.Subscription_Status.EXPIRED:
            return f"Expired on {current_subscription.subscription_end_date.strftime('%b %d %Y')}" if current_subscription.subscription_end_date else ""

        return ""

    def get_is_active(self, obj):
        user_reference = self.context.get('user_reference')
        if not user_reference:
            return False
        paid_plan_exists = UserSubscription.objects.filter(
            user_reference=user_reference,
            subscription_status=UserSubscription.Subscription_Status.ACTIVE
        ).exists()
        return (obj.plan_name == 'Free') != paid_plan_exists
