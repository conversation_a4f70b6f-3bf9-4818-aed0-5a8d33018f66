# Generated by Django 4.2.7 on 2024-11-19 15:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0098_userrewards_is_auto_pay_enabled"),
    ]

    operations = [
        migrations.AddField(
            model_name="rewardshistory",
            name="bank_account_name",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="rewardshistory",
            name="bank_account_number",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="rewardshistory",
            name="bank_ifsc_code",
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="rewardshistory",
            name="reward_type",
            field=models.Char<PERSON><PERSON>(
                blank=True,
                choices=[
                    ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                    ("INVITE_CODE_REWARD", "Invite Code Reward"),
                    ("STORE_VERIFICATION", "Store Verification"),
                    ("U2U_TRANSFER", "U2U Transfer"),
                    ("S2U_TRANSFER", "S2U Transfer"),
                    ("RUPEE", "Rupee"),
                ],
                max_length=20,
                null=True,
            ),
        ),
    ]
