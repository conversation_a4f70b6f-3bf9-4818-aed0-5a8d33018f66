# Generated by Django 3.2.13 on 2023-01-04 02:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0029_alter_notifications_notification_type'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='user_reference',
            field=models.CharField(max_length=15, unique=True),
        ),
        migrations.CreateModel(
            name='UserFollow',
            fields=[
                ('user_follow_id', models.AutoField(primary_key=True, serialize=False)),
                ('is_following', models.BooleanField(default=False)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('modified_date', models.DateTimeField(auto_now=True)),
                ('follower_reference', models.ForeignKey(blank=True, db_column='follower_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='follower', to='users.user', to_field='user_reference')),
                ('user_reference', models.ForeignKey(blank=True, db_column='user_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user', to='users.user', to_field='user_reference')),
            ],
            options={
                'verbose_name_plural': 'user follow',
                'db_table': '"user"."user_follow"',
            },
        ),
    ]
