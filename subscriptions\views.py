from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timedel<PERSON>, datetime
from decouple import config
import requests
from requests.auth import HTTPBasicAuth

from stores.store_api.models import Store, StoreSubscription
from users.user_api.models import User, UserSubscription
from swadesic_admin.models import SubscriptionPlan
from .serializers import (
    GetStoreSubscriptionPlanSerializer,
    GetUserSubscriptionPlanSerializer
)
from .utils import fetch_and_process_subscription_payments
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
# Move existing views from swadesic_admin

class GetStoreSubscriptionPlanView(APIView):
    def get(self, request, store_reference):
        try:
            # Get unique plan names (to group monthly/yearly variants)
            unique_plans = SubscriptionPlan.objects.filter(
                plan_type='store',
                is_active=True,
            ).values('plan_name').distinct()

            # Get one plan object for each unique plan name
            plans = [
                SubscriptionPlan.objects.filter(plan_type='store', plan_name=plan['plan_name']).first()
                for plan in unique_plans
            ]

            serializer = GetStoreSubscriptionPlanSerializer(
                plans,
                many=True,
                context={'store_reference': store_reference}
            )

            return Response({"message": "Subscription plans fetched successfully", "data": serializer.data},
                            status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetUserSubscriptionPlanView(APIView):
    def get(self, request, user_reference):
        try:
            unique_plan_names = SubscriptionPlan.objects.filter(
                plan_type='user',
                is_active=True,
            ).values('plan_name').distinct()

            plans = [
                SubscriptionPlan.objects.filter(plan_type='user', plan_name=plan['plan_name']).first()
                for plan in unique_plan_names
            ]

            serializer = GetUserSubscriptionPlanSerializer(
                plans,
                many=True,
                context={'user_reference': user_reference}
            )
            return Response({"message": "Subscription plans fetched successfully", "data": serializer.data},
                            status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CreateSubscriptionView(APIView):
    def post(self, request):
        try:
            # Get required parameters
            entity_reference = request.data.get('entity_reference')
            plan_reference = request.data.get('plan_reference')

            if not entity_reference or not plan_reference:
                return Response({
                    "error": "Both entity_reference and plan_reference are required"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check for existing active subscription
            entity_type = 'store' if entity_reference.startswith('S') else 'user'
            
            if entity_type == 'store':
                existing_subscription = StoreSubscription.objects.filter(
                    store_reference__store_reference=entity_reference,
                    subscription_status=StoreSubscription.Subscription_Status.ACTIVE
                ).first()
            else:
                existing_subscription = UserSubscription.objects.filter(
                    user_reference__user_reference=entity_reference,
                    subscription_status=UserSubscription.Subscription_Status.ACTIVE
                ).first()

            if existing_subscription:
                return Response({
                    "error": "An active subscription already exists for this entity",
                    "subscription_id": existing_subscription.razorpay_subscription_id,
                    "subscription_end_date": existing_subscription.subscription_end_date,
                    "is_custom":True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the subscription plan
            subscription_plan = get_object_or_404(SubscriptionPlan, plan_reference=plan_reference)

            # Determine if this is for a store or user
            entity_type = 'store' if entity_reference.startswith('S') else 'user'

            if entity_type == 'store':
                entity = get_object_or_404(Store, store_reference=entity_reference)
            else:
                entity = get_object_or_404(User, user_reference=entity_reference)

            # Create Basic Auth header
            auth = HTTPBasicAuth(config('RAZORPAY_ID'), config('RAZORPAY_KEY'))

            # Calculate total count based on plan period
            period_to_count = {
                'daily': 365,
                'weekly': 52,
                'monthly': 12,
                'quarterly': 4,
                'yearly': 1
            }

            total_count = period_to_count.get(subscription_plan.plan_period, 12)

            # Prepare subscription data for Razorpay
            subscription_data = {
                "plan_id": subscription_plan.razorpay_plan_id,
                "total_count": total_count,
                "quantity": 1,
                "customer_notify": 1,
                "notes": {
                    "entity_reference": entity_reference,
                    "plan_reference": plan_reference
                }
            }

            # Create subscription in Razorpay
            response = requests.post(
                'https://api.razorpay.com/v1/subscriptions',
                json=subscription_data,
                auth=auth,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                razorpay_subscription = response.json()

                # Create subscription record in database
                if entity_type == 'store':
                    subscription = StoreSubscription.objects.create(
                        store_reference=entity,
                        subscription_plan_reference=subscription_plan,
                        razorpay_subscription_id=razorpay_subscription['id'],
                        subscription_status=StoreSubscription.Subscription_Status.INACTIVE
                    )
                else:
                    subscription = UserSubscription.objects.create(
                        user_reference=entity,
                        subscription_plan_reference=subscription_plan,
                        razorpay_subscription_id=razorpay_subscription['id'],
                        subscription_status=UserSubscription.Subscription_Status.INACTIVE
                    )

                return Response({
                    "message": "Subscription created successfully",
                    "subscription_id": razorpay_subscription['id'],
                    "short_url": razorpay_subscription.get('short_url')
                }, status=status.HTTP_201_CREATED)

            return Response({
                "error": "Failed to create subscription",
                "details": response.json()
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckSubscriptionStatusView(APIView):
    def get(self, request, subscription_id):
        try:
            auth = HTTPBasicAuth(config('RAZORPAY_ID'), config('RAZORPAY_KEY'))

            response = requests.get(
                f'https://api.razorpay.com/v1/subscriptions/{subscription_id}',
                auth=auth
            )

            if response.status_code == 200:
                razorpay_subscription = response.json()

                store_subscription = StoreSubscription.objects.filter(
                    razorpay_subscription_id=subscription_id
                ).first()

                user_subscription = UserSubscription.objects.filter(
                    razorpay_subscription_id=subscription_id
                ).first()

                subscription = store_subscription or user_subscription
                if not subscription:
                    return Response({
                        "error": "Subscription not found in database"
                    }, status=status.HTTP_404_NOT_FOUND)

                # Check if subscription has expired
                if subscription.subscription_end_date and subscription.subscription_end_date < timezone.now():
                    subscription.subscription_status = (
                        StoreSubscription.Subscription_Status.EXPIRED
                        if store_subscription
                        else UserSubscription.Subscription_Status.EXPIRED
                    )

                    # Reset subscription type to FREE
                    if store_subscription:
                        store_subscription.store_reference.subscription_type = 'FREE'
                        store_subscription.store_reference.save()
                    else:
                        user_subscription.user_reference.subscription_type = 'FREE'
                        user_subscription.user_reference.save()

                    subscription.save()

                    return Response({
                        "message": "Subscription has expired",
                        "status": "expired",
                        "subscription_end_date": subscription.subscription_end_date
                    }, status=status.HTTP_200_OK)

                # Handle different Razorpay subscription statuses

                if razorpay_subscription['status'] == 'created':
                    return Response({
                        "message": "Subscription is created",
                        "status": razorpay_subscription['status'],
                    }, status=status.HTTP_200_OK)

                elif razorpay_subscription['status'] == 'completed' and subscription.subscription_plan_reference.plan_period == 'yearly':
                    if subscription.subscription_status != 'ACTIVE':
                        subscription.subscription_status = (
                            StoreSubscription.Subscription_Status.ACTIVE
                            if store_subscription
                            else UserSubscription.Subscription_Status.ACTIVE
                        )

                        # Calculate end date based on plan period only if not set
                        if not subscription.subscription_end_date:
                            period_to_days = {
                                'daily': 1,
                                'weekly': 7,
                                'monthly': 30,
                                'quarterly': 90,
                                'yearly': 365
                            }

                            days = period_to_days.get(
                                subscription.subscription_plan_reference.plan_period,
                                30
                            )

                            subscription.subscription_end_date = timezone.now() + timedelta(days=days)

                            # Update entity subscription type only if not already premium
                            if store_subscription and store_subscription.store_reference.subscription_type != 'PREMIUM':
                                store_subscription.store_reference.subscription_type = 'PREMIUM'
                                store_subscription.store_reference.save()
                            elif user_subscription and user_subscription.user_reference.subscription_type != 'PREMIUM':
                                user_subscription.user_reference.subscription_type = 'PREMIUM'
                                user_subscription.user_reference.save()
                        success = fetch_and_process_subscription_payments(subscription_id)
                        if success:
                            logger.info(f"Subscription payments processed successfully for subscription_id: {subscription_id}")
                        subscription.save()

                    return Response({
                        "message": "Subscription is Completed",
                        "status": "payment_completed" if subscription.subscription_status == StoreSubscription.Subscription_Status.ACTIVE else razorpay_subscription['status'],
                        "subscription_end_date": subscription.subscription_end_date
                    }, status=status.HTTP_200_OK)

                elif razorpay_subscription['status'] == 'active':
                    # Only update if subscription is not already active
                    if subscription.subscription_status != 'ACTIVE':
                        subscription.subscription_status = (
                            StoreSubscription.Subscription_Status.ACTIVE
                            if store_subscription
                            else UserSubscription.Subscription_Status.ACTIVE
                        )

                        # Calculate end date based on plan period only if not set
                        if not subscription.subscription_end_date:
                            period_to_days = {
                                'daily': 1,
                                'weekly': 7,
                                'monthly': 30,
                                'quarterly': 90,
                                'yearly': 365
                            }

                            days = period_to_days.get(
                                subscription.subscription_plan_reference.plan_period,
                                30
                            )

                            subscription.subscription_end_date = timezone.now() + timedelta(days=days)

                            # Update entity subscription type only if not already premium
                            if store_subscription and store_subscription.store_reference.subscription_type != 'PREMIUM':
                                store_subscription.store_reference.subscription_type = 'PREMIUM'
                                store_subscription.store_reference.save()
                            elif user_subscription and user_subscription.user_reference.subscription_type != 'PREMIUM':
                                user_subscription.user_reference.subscription_type = 'PREMIUM'
                                user_subscription.user_reference.save()
                        success = fetch_and_process_subscription_payments(subscription_id)
                        if success:
                            logger.info(f"Subscription payments processed successfully for subscription_id: {subscription_id}")
                        subscription.save()

                    return Response({
                        "message": "Subscription is active",
                        "status": 'payment_completed' if razorpay_subscription['status'] == 'active' else
                        razorpay_subscription['status'],
                        "subscription_end_date": subscription.subscription_end_date
                    }, status=status.HTTP_200_OK)

                elif razorpay_subscription['status'] in ['halted', 'cancelled', 'completed', 'created']:
                    # Handle failed renewal or cancelled subscription
                    subscription.subscription_status = (
                        StoreSubscription.Subscription_Status.INACTIVE
                        if store_subscription
                        else UserSubscription.Subscription_Status.INACTIVE
                    )

                    # Reset subscription type to FREE
                    if store_subscription:
                        store_subscription.store_reference.subscription_type = 'FREE'
                        store_subscription.store_reference.save()
                    else:
                        user_subscription.user_reference.subscription_type = 'FREE'
                        user_subscription.user_reference.save()

                    subscription.save()

                    return Response({
                        "message": f"Subscription is {razorpay_subscription['status']}",
                        "status": razorpay_subscription['status'],
                        "subscription_end_date": subscription.subscription_end_date
                    }, status=status.HTTP_200_OK)

            return Response({
                "error": "Failed to fetch subscription status",
                "details": response.json()
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetSubscriptionInvoicesView(APIView):
    def get(self, request):
        try:
            subscription_id = request.query_params.get('subscription_id')
            # Create Basic Auth header
            auth = HTTPBasicAuth(config('RAZORPAY_ID'), config('RAZORPAY_KEY'))

            # Get invoices from Razorpay
            response = requests.get(
                f'https://api.razorpay.com/v1/invoices',
                params={'subscription_id': subscription_id},
                auth=auth
            )

            if response.status_code == 200:
                razorpay_invoices = response.json()

                # Find the subscription in our database
                store_subscription = StoreSubscription.objects.filter(
                    razorpay_subscription_id=subscription_id
                ).first()

                user_subscription = UserSubscription.objects.filter(
                    razorpay_subscription_id=subscription_id
                ).first()

                subscription = store_subscription or user_subscription
                if not subscription:
                    return Response({
                        "error": "Subscription not found in database"
                    }, status=status.HTTP_404_NOT_FOUND)

                # Process and format the invoice data
                formatted_invoices = []
                for invoice in razorpay_invoices.get('items', []):
                    formatted_invoice = {
                        'invoice_id': invoice.get('id'),
                        'payment_id': invoice.get('payment_id'),
                        'status': invoice.get('status'),
                        'amount': invoice.get('amount') / 100,  # Convert from paise to rupees
                        'amount_paid': invoice.get('amount_paid') / 100,
                        'amount_due': invoice.get('amount_due') / 100,
                        'currency': invoice.get('currency'),
                        'issued_at': datetime.fromtimestamp(invoice.get('issued_at')).isoformat() if invoice.get(
                            'issued_at') else None,
                        'paid_at': datetime.fromtimestamp(invoice.get('paid_at')).isoformat() if invoice.get(
                            'paid_at') else None,
                        'cancelled_at': datetime.fromtimestamp(invoice.get('cancelled_at')).isoformat() if invoice.get(
                            'cancelled_at') else None,
                        'expired_at': datetime.fromtimestamp(invoice.get('expired_at')).isoformat() if invoice.get(
                            'expired_at') else None,
                        'billing_start': datetime.fromtimestamp(
                            invoice.get('billing_start')).isoformat() if invoice.get('billing_start') else None,
                        'billing_end': datetime.fromtimestamp(invoice.get('billing_end')).isoformat() if invoice.get(
                            'billing_end') else None,
                        'short_url': invoice.get('short_url'),
                        'customer_details': {
                            'name': invoice.get('customer_details', {}).get('name'),
                            'email': invoice.get('customer_details', {}).get('email'),
                            'contact': invoice.get('customer_details', {}).get('contact')
                        },
                        'line_items': [{
                            'name': item.get('name'),
                            'amount': item.get('amount') / 100,
                            'currency': item.get('currency'),
                            'description': item.get('description'),
                            'type': item.get('type')
                        } for item in invoice.get('line_items', [])]
                    }
                    formatted_invoices.append(formatted_invoice)

                return Response({
                    "message": "Invoices fetched successfully",
                    "subscription_id": subscription_id,
                    "entity_type": "store" if store_subscription else "user",
                    "entity_reference": store_subscription.store_reference.store_reference if store_subscription
                    else user_subscription.user_reference.user_reference,
                    "total_count": razorpay_invoices.get('count', 0),
                    "invoices": formatted_invoices
                }, status=status.HTTP_200_OK)

            return Response({
                "error": "Failed to fetch invoices",
                "details": response.json()
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Add new view for processing payments
class ProcessSubscriptionPaymentsView(APIView):
    def post(self, request):
        subscription_id = request.data.get('subscription_id')
        
        if not subscription_id:
            return Response({
                "error": "subscription_id is required"
            }, status=status.HTTP_400_BAD_REQUEST)
            
        success = fetch_and_process_subscription_payments(subscription_id)
        
        if success:
            return Response({
                "message": "Subscription payments processed successfully"
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "error": "Failed to process subscription payments"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Add this new class to views.py
class CancelSubscriptionView(APIView):
    def post(self, request):
        try:
            subscription_id = request.data.get('subscription_id')
            cancel_at_cycle_end = request.data.get('cancel_at_cycle_end', False)
            cancellation_reason = request.data.get('cancellation_reason')
            
            if not subscription_id:
                return Response({
                    "error": "subscription_id is required"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if subscription exists and is already cancelled
            store_subscription = StoreSubscription.objects.filter(
                razorpay_subscription_id=subscription_id
            ).first()
            
            user_subscription = UserSubscription.objects.filter(
                razorpay_subscription_id=subscription_id
            ).first()
            
            subscription = store_subscription or user_subscription
            if not subscription:
                return Response({
                    "error": "Subscription not found in database"
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if subscription is already cancelled or inactive
            if subscription.razorpay_subscription_id == subscription_id and subscription.is_scheduled_for_cancellation:
                return Response({
                    "message": "Subscription is already cancelled",
                    "is_custom":True
                }, status=status.HTTP_400_BAD_REQUEST)

            auth = HTTPBasicAuth(config('RAZORPAY_ID'), config('RAZORPAY_KEY'))
            razorpay_cancel_value = 1 if cancel_at_cycle_end else 0
            
            cancel_data = {
                'cancel_at_cycle_end': razorpay_cancel_value
            }
            
            response = requests.post(
                f'https://api.razorpay.com/v1/subscriptions/{subscription_id}/cancel',
                json=cancel_data,
                auth=auth,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                razorpay_subscription = response.json()
                
                store_subscription = StoreSubscription.objects.filter(
                    razorpay_subscription_id=subscription_id
                ).first()
                
                user_subscription = UserSubscription.objects.filter(
                    razorpay_subscription_id=subscription_id
                ).first()
                
                subscription = store_subscription or user_subscription
                if not subscription:
                    return Response({
                        "error": "Subscription not found in database"
                    }, status=status.HTTP_404_NOT_FOUND)
                
                current_time = timezone.now()
                
                if not cancel_at_cycle_end:
                    # Immediate cancellation
                    subscription.subscription_status = (
                        StoreSubscription.Subscription_Status.INACTIVE 
                        if store_subscription 
                        else UserSubscription.Subscription_Status.INACTIVE
                    )
                    subscription.subscription_end_date = current_time
                    subscription.cancelled_at = current_time
                    subscription.is_scheduled_for_cancellation = False
                    
                    # Reset subscription type to FREE
                    if store_subscription:
                        store_subscription.store_reference.subscription_type = 'FREE'
                        store_subscription.store_reference.save()
                    else:
                        user_subscription.user_reference.subscription_type = 'FREE'
                        user_subscription.user_reference.save()
                
                else:
                    # Schedule cancellation at cycle end
                    subscription_end_date_timestamp = razorpay_subscription.get('current_end')
                    if subscription_end_date_timestamp:
                        end_date = datetime.fromtimestamp(
                            subscription_end_date_timestamp, 
                            tz=timezone.get_current_timezone()
                        )
                        subscription.subscription_end_date = end_date
                        subscription.is_scheduled_for_cancellation = True
                        subscription.scheduled_cancellation_date = end_date
                        
                        if end_date > current_time:
                            subscription.subscription_status = (
                                StoreSubscription.Subscription_Status.ACTIVE
                                if store_subscription
                                else UserSubscription.Subscription_Status.ACTIVE
                            )
                        else:
                            subscription.subscription_status = (
                                StoreSubscription.Subscription_Status.INACTIVE
                                if store_subscription
                                else UserSubscription.Subscription_Status.INACTIVE
                            )
                            # Reset subscription type to FREE
                            if store_subscription:
                                store_subscription.store_reference.subscription_type = 'FREE'
                                store_subscription.store_reference.save()
                            else:
                                user_subscription.user_reference.subscription_type = 'FREE'
                                user_subscription.user_reference.save()
                # Update common fields
                subscription.auto_renew = False
                subscription.cancellation_reason = cancellation_reason
                subscription.save()
                
                return Response({
                    "message": "Subscription cancelled successfully",
                    "status": razorpay_subscription['status'],
                    "cancelled_at_cycle_end": cancel_at_cycle_end,
                    "current_status": subscription.subscription_status,
                    "subscription_end_date": subscription.subscription_end_date,
                    "is_scheduled_for_cancellation": subscription.is_scheduled_for_cancellation,
                    "scheduled_cancellation_date": subscription.scheduled_cancellation_date,
                    "cancellation_reason": subscription.cancellation_reason,
                    "cancelled_at": subscription.cancelled_at,
                    "note": "Subscription will remain active until the current billing cycle ends" if cancel_at_cycle_end else "Subscription cancelled immediately"
                }, status=status.HTTP_200_OK)
            
            return Response({
                "error": "Failed to cancel subscription",
                "details": response.json()
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            return Response({
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
