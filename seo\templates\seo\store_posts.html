{% extends 'seo/base.html' %}

{% block content %}
<div class="store-header">
    {% if store.icon %}
    <img src="{{ store.icon.url }}" alt="{{ store.store_name }} icon" class="store-icon">
    {% endif %}
    <div class="store-info">
        <h1>{{ store.store_name }} - Posts</h1>
        <p>{{ store.store_desc }}</p>
        <p>Handle: @{{ store.storehandle }}</p>
    </div>
</div>

{% if posts %}
<div class="post-grid">
    {% for post in posts %}
    <div class="post-card">
        {% if post.post_images.exists %}
        <img src="{{ post.post_images.first.post_image.url }}" alt="{{ post.post_text|truncatechars:300 }}" class="post-image">
        {% endif %}
        <h3></h3>
        <p>{{ post.post_text|truncatechars:300 }}</p>
        <p class="timestamp">{{ post.created_date|date:"F j, Y" }}</p>
    </div>
    {% endfor %}
</div>
{% else %}
<p>No posts available.</p>
{% endif %}
{% endblock %}
