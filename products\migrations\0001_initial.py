# Generated by Django 3.2.13 on 2022-07-26 14:05

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                ("categoryid", models.AutoField(primary_key=True, serialize=False)),
                ("category_name", models.Char<PERSON>ield(max_length=100, unique=True)),
                ("active", models.BooleanField(default=True)),
                ("created_by", models.CharField(max_length=100)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_by", models.Char<PERSON>ield(max_length=100)),
                ("modified_date", models.DateField(auto_now=True)),
            ],
            options={
                "db_table": '"product"."category"',
            },
        ),
        migrations.CreateModel(
            name="Product",
            fields=[
                ("productid", models.AutoField(primary_key=True, serialize=False)),
                ("product_name", models.Char<PERSON>ield(max_length=200)),
                ("brand_name", models.Char<PERSON>ield(default=True, max_length=50)),
                ("active", models.BooleanField(default=True)),
                ("product_description", models.TextField()),
                ("promotion_link", models.CharField(max_length=255)),
                ("hashtags", models.CharField(blank=True, max_length=255, null=True)),
                ("in_stock", models.PositiveIntegerField()),
                ("mrp_price", models.PositiveIntegerField()),
                ("selling_price", models.PositiveIntegerField()),
                ("receive", models.PositiveIntegerField(blank=True, null=True)),
                ("hide", models.BooleanField(default=False)),
                ("deleted", models.BooleanField(default=False)),
                ("rating", models.FloatField(blank=True, null=True)),
                ("count_of_ratings", models.IntegerField(default=0)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": '"product"."product"',
            },
        ),
    ]
