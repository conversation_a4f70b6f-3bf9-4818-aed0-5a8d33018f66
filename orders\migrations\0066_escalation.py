# Generated by Django 3.2.13 on 2022-12-29 08:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0065_orderpaymentdetails_suborder_deleted_condition"),
    ]

    operations = [
        migrations.CreateModel(
            name="Escalation",
            fields=[
                ("escalation_id", models.AutoField(primary_key=True, serialize=False)),
                ("order_reference", models.<PERSON>r<PERSON><PERSON>(max_length=20)),
                ("store_reference", models.<PERSON>r<PERSON>ield(max_length=20)),
                ("user_reference", models.Char<PERSON>ield(max_length=20)),
                (
                    "suborder_reference",
                    models.Char<PERSON>ield(blank=True, max_length=20, null=True),
                ),
                (
                    "package_reference",
                    models.Char<PERSON>ield(blank=True, max_length=20, null=True),
                ),
                ("order_status", models.Char<PERSON>ield(max_length=20)),
                (
                    "escalation_status",
                    models.<PERSON>r<PERSON><PERSON>(
                        choices=[("CREATED", "Created")],
                        default="CREATED",
                        max_length=20,
                    ),
                ),
                (
                    "escalation_type",
                    models.<PERSON>r<PERSON>ield(blank=True, max_length=20, null=True),
                ),
                (
                    "escalation_note",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "escalation_description",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "escalation",
                "db_table": '"order"."escalation"',
            },
        ),
    ]
