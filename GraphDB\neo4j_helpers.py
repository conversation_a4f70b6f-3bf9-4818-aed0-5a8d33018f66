from neomodel import db
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def get_batched_entity_follow_status(visitor_ref, entity_refs):
    """
    Get follow status between a visitor and multiple entities in batch.

    Args:
        visitor_ref: Reference of the visitor entity
        entity_refs: List of entity references to check follow status for

    Returns:
        Dictionary mapping entity references to their follow status details
    """
    if not entity_refs:
        return {}

    query = """
    UNWIND $refs AS ref
    MATCH (visitor:Neo4jEntity {reference: $visitor_ref})
    MATCH (entity:Neo4jEntity {reference: ref})

    WITH visitor, entity, ref,
        EXISTS((visitor)-[:FOLLOWS]->(entity)) AS following,
        EXISTS((entity)-[:FOLLOWS]->(visitor)) AS followed_by,
        CASE
            WHEN entity:Neo4jStore THEN 'STORE'
            WHEN entity:Neo4jUser THEN 'USER'
            ELSE 'UNKNOWN'
        END AS entity_type

    RETURN ref AS entity_ref, following, followed_by, entity_type
    """

    try:
        result, _ = db.cypher_query(query, {
            "visitor_ref": visitor_ref,
            "refs": entity_refs
        })

        return {
            row[0]: {
                "following": row[1],
                "followed_by": row[2],
                "entity_type": row[3],
                "follow_status": get_formatted_follow_status(row[1], row[2], row[3])
            } for row in result
        }
    except Exception as e:
        logger.error(f"Error in get_batched_entity_follow_status: {e}")
        return {}

def get_batched_entity_metrics(entity_refs):
    """
    Get follower and following counts for multiple entities in batch.

    Args:
        entity_refs: List of entity references to get metrics for

    Returns:
        Dictionary mapping entity references to their metrics
    """
    if not entity_refs:
        return {}

    query = """
    UNWIND $refs AS ref
    MATCH (entity:Neo4jEntity {reference: ref})

    WITH entity, ref,
        SIZE([p = (entity)<-[:FOLLOWS]-(follower:Neo4jEntity) WHERE follower.is_deleted = false | p]) AS follower_count,
        SIZE([p = (entity)-[:FOLLOWS]->(following:Neo4jEntity) WHERE following.is_deleted = false | p]) AS following_count

    RETURN ref AS entity_ref, follower_count, following_count
    """

    try:
        result, _ = db.cypher_query(query, {
            "refs": entity_refs
        })

        return {
            row[0]: {
                "follower_count": row[1],
                "following_count": row[2]
            } for row in result
        }
    except Exception as e:
        logger.error(f"Error in get_batched_entity_metrics: {e}")
        return {}

def get_formatted_follow_status(following, followed_by, entity_type):
    """
    Format follow status based on relationship and entity type.

    Args:
        following: Whether visitor follows entity
        followed_by: Whether entity follows visitor
        entity_type: Type of entity (STORE or USER)

    Returns:
        Formatted follow status string
    """
    if entity_type == "STORE":
        if following and followed_by:
            return "Support back"
        elif following:
            return "Supporting"
        elif followed_by:
            return "Support back"
        else:
            return "Support"
    else:  # USER or other
        if following and followed_by:
            return "Follow back"
        elif following:
            return "Following"
        elif followed_by:
            return "Follow back"
        else:
            return "Follow"
