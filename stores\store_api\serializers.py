from rest_framework import serializers
from .models import Store, Storelink, Category, Cities, BusinessTypes, StoreConfig
from ..store_settings_api.models import TrustCenter, DeliverySettings, DeliveryLocations, RefundAndWarranty
from users.user_api.models import UserStore, User
from orders.order_api.models import SubOrder, Order
from GraphDB.models import Neo4jEntity
import logging
from content.models import Posts
from products.models import Product
from django.core.exceptions import ObjectDoesNotExist
from django.core.cache import cache
from django.db.models import Prefetch, Exists, OuterRef, Count, Q, Value, Case, When, IntegerField, BooleanField, CharField, Subquery
from django.db.models.functions import Coalesce
from neomodel import db as neo4j_db
from GraphDB.queries import dbqueries
from django.db.models.functions import Upper


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class StoreLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = Storelink
        fields = "__all__"


class StoreContactSerializer(serializers.ModelSerializer):
    phone_number = serializers.SerializerMethodField("get_phone_number")
    email = serializers.SerializerMethodField("get_store_email")
    location = serializers.CharField(source="city")
    class Meta:
        model = TrustCenter
        fields = ["location", "state", "phone_number", "email"]

    @staticmethod
    def get_phone_number(obj):
        phone_number = obj.phonenumber
        if phone_number:
            phone_number_list = phone_number.split('|')
        else:
            phone_number_list = None
        return phone_number_list

    @staticmethod
    def get_store_email(obj):
        email = obj.emailid
        if email:
            email_list = email.split('|')
        else:
            email_list = None
        return email_list


class StoreActivationSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Store
        fields = [
            "is_active",
            "open_for_order"
        ]


class AddStoreSerializer(serializers.ModelSerializer):
    class Meta:
        model = Store
        fields = [
            "storeid",
            "store_name",
            "store_desc",
            "is_active",
            "deleted",
            "business_description",
            "created_by",
            "modified_by",
            "storehandle",
            "gst_number",
            "gst_business_name",
            "pan_number",
            "pan_name",
            "category_name",
            "is_test_store",
            "verification_type",
            "is_auto_withdrawal_enabled"
        ]


def convert_delivery_locations_to_pincode(delivery_locations):
    queryset = DeliveryLocations.objects.all()
    all_states = set(queryset.values_list(Upper('state'), flat=True).distinct())
    all_cities = set(queryset.values_list(Upper('city'), flat=True).distinct())

    list_of_locations = delivery_locations.split("|")
    lst = []

    for elem in list_of_locations:
        elem_upper = elem.upper()
        if elem_upper in all_states:
            pincode = list(queryset.filter(state__iexact=elem).values_list("pincode", flat=True))
            lst.extend(pincode)
        elif elem_upper in all_cities:
            pincode = list(queryset.filter(city__iexact=elem).values_list("pincode", flat=True))
            lst.extend(pincode)
        else:
            lst.append(elem)

    return lst

def get_deliverability(store_reference, user_pincode):
    cache_key = f'deliverability_{store_reference}_{user_pincode}'
    deliverability = cache.get(cache_key)

    if deliverability is None or deliverability is False:
        store_delivery_settings = DeliverySettings.objects.filter(
            store_reference=store_reference, product_reference__isnull=True, is_deleted=False
        ).last()
        if store_delivery_settings:
            if store_delivery_settings.fulfillment_options == 'IN_STORE_PICKUP':
                deliverability = False
            elif store_delivery_settings and store_delivery_settings.delivery_locations:
                list_of_pincode = convert_delivery_locations_to_pincode(store_delivery_settings.delivery_locations)
                deliverability = user_pincode in list_of_pincode
            else:
                deliverability = False
        else:
            deliverability = False

        cache.set(cache_key, deliverability, 300)  # Cache for 1 hour

    return deliverability


class GetStoreSerializer(serializers.ModelSerializer):
    storelinks = StoreLinkSerializer(many=True)
    store_details = StoreContactSerializer(many=True)
    supports = serializers.SerializerMethodField("get_store_supporters_count")
    sales = serializers.SerializerMethodField("get_store_sales_count")
    deliverability = serializers.SerializerMethodField("get_deliverability")
    swadeshi_owned = serializers.SerializerMethodField("get_swadeshi_label")
    config_receive_orders = serializers.SerializerMethodField("get_config_receive_orders")
    has_products = serializers.SerializerMethodField("get_has_products")
    has_posts = serializers.SerializerMethodField("get_has_posts")
    class Meta:
        model = Store
        fields = [
            "storeid",
            "store_reference",
            "store_name",
            "store_desc",
            "is_active",
            "business_description",
            "created_by",
            "modified_by",
            "icon",
            "cover_image",
            "store_signature",
            "storehandle",
            "category_name",
            "gst_number",
            "gst_business_name",
            "is_gst_verified",
            "pan_number",
            "pan_name",
            "is_pan_verified",
            "invite_code",
            "deliverability",
            "supports",
            "sales",
            "store_details",
            "storelinks",
            "swadeshi_owned",
            "deleted",
            "config_receive_orders",
            "is_test_store",
            "verification_type",
            "is_verification_completed",
            "has_products",
            "has_posts",
            "is_auto_withdrawal_enabled",
            "subscription_type",
            "js_messaging_token",
            "new_messaging_token",
            "new_messaging_user_id",
            "store_ai_reference",
            "store_ai_messaging_token",
            "store_ai_messaging_user_id",
            "store_valuation",
            "store_level",
            "analytics_view_count",
            "store_review_count",
            "store_avg_rating",
            "store_product_review_count",
            "store_product_avg_rating"
        ]
    
    def get_has_products(self, obj):
        products = Product.objects.filter(store_reference=obj, deleted=False)
        return products.exists()
    def get_has_posts(self, obj):
        posts = Posts.objects.filter(store_reference=obj, is_deleted=False)
        return posts.exists()
    
    def get_config_receive_orders(self,obj):
        store_reference = obj.store_reference
        try:
            store_config_instance = StoreConfig.objects.get(store_reference=store_reference)
            is_ordering_enabled = store_config_instance.enable_orders
        except StoreConfig.DoesNotExist:
            # Handle the case where TrustCenter does not exist for the given store_reference
            is_ordering_enabled = False
        return is_ordering_enabled

    @staticmethod
    def setup_eager_loading(queryset, user_pincode=None):
        """ Perform necessary eager loading of data. """
        queryset = queryset.prefetch_related('storelinks', 'store_details')

        return queryset

    def get_swadeshi_label(self,obj):
        store_reference = obj.store_reference
        try:
            trust_center = TrustCenter.objects.get(store_reference=store_reference)
            swadeshi_label = trust_center.swadeshi_owned
        except TrustCenter.DoesNotExist:
            # Handle the case where TrustCenter does not exist for the given store_reference
            swadeshi_label = None
        return swadeshi_label


    def get_deliverability(self, obj):
        deliverability = False
        if self.context.get("user_pincode"):
            user_pincode = self.context.get("user_pincode")
            store_delivery_settings = DeliverySettings.objects.filter(
                store_reference=obj.store_reference, product_reference__isnull=True, is_deleted=False).last()
            if store_delivery_settings:
                delivery_locations = store_delivery_settings.delivery_locations
                if store_delivery_settings.fulfillment_options == 'IN_STORE_PICKUP':
                    return deliverability
                if delivery_locations:
                    list_of_pincode = convert_delivery_locations_to_pincode(delivery_locations)
                    if user_pincode in list_of_pincode:
                        deliverability = True

        return deliverability

    def get_store_supporters_count(self, obj):
        # Retrieve the store reference
        entity_reference = obj.store_reference

        '''
        Right now We are using both the supporter count logics from graph db and Postgres db 
        because there are some stores present in Postgres db but not cloned to graphdb 
        '''
        #TODO Need to change this method to work completely on graph db in production

        # Try to get the entity node from Neo4j
        try:
            entity_node = Neo4jEntity.nodes.get(reference=entity_reference)
        except Neo4jEntity.DoesNotExist:
            # If the entity doesn't exist in Neo4j, fall back to counting supporters in PostgreSQL
            store_supporters = obj.supportsss.filter(is_following=True).count()
            return store_supporters

        # If the entity exists in Neo4j, return its follower count
        return entity_node.get_follower_count()

    def get_store_sales_count(self, obj):
        store_id = obj.storeid
        order_instance_list = list(Order.new_objects.filter(storeid=store_id).values_list("order_number", flat=True))
        count = SubOrder.new_objects.filter(order_number__in=order_instance_list, suborder_status=SubOrder.Suborder_Status.ORDER_DELIVERED).count()
        return count


class NewGetstoreSerializer(serializers.ModelSerializer):
    storelinks = StoreLinkSerializer(many=True)
    store_details = StoreContactSerializer(many=True)
    supports = serializers.IntegerField()
    sales = serializers.IntegerField()
    deliverability = serializers.SerializerMethodField()
    swadeshi_owned = serializers.CharField()
    config_receive_orders = serializers.BooleanField()
    has_products = serializers.BooleanField()
    has_posts = serializers.BooleanField()

    class Meta:
        model = Store
        fields = [
            "storeid", "store_reference", "store_name", "store_desc", "is_active",
            "business_description", "icon", "cover_image", "store_signature", "storehandle",
            "category_name", "gst_number", "gst_business_name", "is_gst_verified",
            "pan_number", "pan_name", "is_pan_verified", "deliverability",
            "supports", "sales", "store_details", "storelinks", "swadeshi_owned",
            "deleted", "config_receive_orders", "is_test_store", "verification_type",
            "is_verification_completed", "has_products", "has_posts",
            "is_auto_withdrawal_enabled", "subscription_type", "store_valuation","store_level","analytics_view_count",
            "store_review_count", "store_avg_rating", "store_product_review_count", "store_product_avg_rating"
        ]

    def get_deliverability(self, obj):
        user_pincode = self.context.get('user_pincode')
        if user_pincode:
            return get_deliverability(obj.store_reference, user_pincode)
        return False

    @staticmethod
    def follower_count_generator(data):
        for item in data:
            yield item['reference'], item['follower_count']

    @staticmethod
    def setup_eager_loading(queryset):
        from django.db.models import Subquery
        
        # Prefetch related objects
        queryset = queryset.prefetch_related(
            'storelinks',
            'store_details',
            'store_configurations',
        )

        # Get all store references
        store_references = list(queryset.values_list('store_reference', flat=True))

        # Get follower counts for all store references
        follower_data = dbqueries.get_follower_counts(references=store_references)
        follower_counts = dict(NewGetstoreSerializer.follower_count_generator(follower_data))

        # Create a list of When conditions for the Case expression
        whens = [
            When(store_reference=ref, then=Value(count))
            for ref, count in follower_counts.items()
        ]

        # Annotate the queryset with a single Case expression
        queryset = queryset.annotate(
            sales=Count('store__suborderitems',
                              filter=Q(store__suborderitems__suborder_status=SubOrder.Suborder_Status.ORDER_DELIVERED)),
            has_products=Exists(Product.objects.filter(store_reference=OuterRef('store_reference'), deleted=False)),
            has_posts=Exists(Posts.objects.filter(store_reference=OuterRef('store_reference'), is_deleted=False)),
            supports=Coalesce(
                Case(
                    *whens,
                    default=Value(0),
                    output_field=IntegerField()
                ),
                Value(0)
            ),
            swadeshi_owned=Coalesce(
                Subquery(
                    TrustCenter.objects.filter(store_reference=OuterRef('store_reference'))
                    .values('swadeshi_owned')[:1]
                ),
                Value(''),
                output_field=CharField()
            ),
            config_receive_orders=Coalesce(
                Subquery(
                    StoreConfig.objects.filter(store_reference=OuterRef('store_reference'))
                    .values('enable_orders')[:1]
                ),
                Value(False),
                output_field=BooleanField()
            )
        )

        return queryset

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['sales'] = getattr(instance, 'sales', 0)
        representation['has_products'] = getattr(instance, 'has_products', False)
        representation['has_posts'] = getattr(instance, 'has_posts', False)
        representation['supports'] = getattr(instance, 'supports', 0)
        representation['config_receive_orders'] = getattr(instance, 'config_receive_orders', False)
        representation['swadeshi_owned'] = getattr(instance, 'swadeshi_owned', '')

        return representation



class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ["categoryid", "category_name"]


class GetStoreFollowersSerializer(serializers.ModelSerializer):

    user_name = serializers.CharField(source="userid.user_name")
    icon = serializers.ImageField(source="userid.icon")
    userid = serializers.CharField(source="userid.userid")

    class Meta:
        model = UserStore
        fields = ["userid", "user_name", "icon"]


class StoreDashboardSerializer(serializers.ModelSerializer):

    waiting_for_confirmation = serializers.SerializerMethodField("get_waiting_for_confirmation")
    confirmed_not_shipped = serializers.SerializerMethodField("get_confirmed_not_shipped")
    delivered = serializers.SerializerMethodField("get_delivered")
    shipping_in_progress = serializers.SerializerMethodField("get_shipping_in_progress")
    cancelled = serializers.SerializerMethodField("get_cancelled")
    return_requested = serializers.SerializerMethodField("get_return_requested")
    return_to_pickup = serializers.SerializerMethodField("get_return_to_pickup")
    returned = serializers.SerializerMethodField("get_returned")
    refunded = serializers.SerializerMethodField("get_refunded")
    refund_hold = serializers.SerializerMethodField("get_refund_hold")
    order_alert_message = serializers.SerializerMethodField("get_order_alert_message")
    warranty_and_return = serializers.SerializerMethodField("get_warranty_and_return")
    delivery_settings = serializers.SerializerMethodField("get_delivery_settings")

    class Meta:
        model = Store
        fields = [
            "store_reference",
            "trustcenter_detail",
            "warranty_and_return",
            "delivery_settings",
            "add_products",
            "open_for_order",
            "is_active",
            "gst_number",
            "waiting_for_confirmation",
            "confirmed_not_shipped",
            "delivered",
            "shipping_in_progress",
            "cancelled",
            "return_requested",
            "return_to_pickup",
            "returned",
            "refunded",
            "refund_hold",
            "order_alert_message",
            "subscription_type",
            "store_valuation",
            "store_level",
            "analytics_view_count"
        ]

    def get_warranty_and_return(self, obj):
        return RefundAndWarranty.objects.filter(store_reference=obj.store_reference, is_deleted=False, product_reference__isnull=True).exists()
    
    def get_delivery_settings(self, obj):
        return DeliverySettings.objects.filter(store_reference=obj.store_reference, is_deleted=False, product_reference__isnull=True).exists()

    def get_waiting_for_confirmation(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION).count()

    def get_confirmed_not_shipped(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.ORDER_CONFIRMED).count()

    def get_delivered(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.ORDER_DELIVERED).count()

    def get_shipping_in_progress(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS).count()

    def get_cancelled(self, obj):
        return SubOrder.objects.filter(
            store_reference=obj.store_reference,
            suborder_status__in=[
                SubOrder.Suborder_Status.ORDER_CANCELLED,
                SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
                SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
                SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED
            ]
        ).count()

    def get_return_requested(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.RETURN_REQUESTED).count()

    def get_return_to_pickup(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.RETURN_IN_PROGRESS).count()

    def get_returned(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.RETURNED_TO_SELLER).count()

    def get_refunded(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.REFUNDED).count()

    def get_refund_hold(self, obj):
        return SubOrder.objects.filter(store_reference=obj.store_reference, suborder_status=SubOrder.Suborder_Status.REFUND_HOLD).count()

    def get_order_alert_message(self, obj): 
        waiting_for_confirmation = self.get_waiting_for_confirmation(obj)
        if waiting_for_confirmation == 0:
            return None
        else:
            return f"You have {waiting_for_confirmation} order{'s' if waiting_for_confirmation > 1 else ''} waiting for confirmation"

class CitiesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cities
        fields = '__all__'


class BusinessTypesSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessTypes
        fields = '__all__'


class StoreFollowerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Store
        fields = ["store_reference","storehandle", "icon"]


class StoreVerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Store
        fields = [
            'storehandle',
            'ID_verification_requested',
            'verification_type', 
            'is_verification_completed', 
            'first_verified_date', 
            'is_pan_verified', 
            'pan_number', 
            'pan_name', 
            'pan_verified_time', 
            'is_gst_verified', 
            'gst_number', 
            'gst_business_name', 
            'gst_verified_time', 
            'verification_requested_time'
        ]











