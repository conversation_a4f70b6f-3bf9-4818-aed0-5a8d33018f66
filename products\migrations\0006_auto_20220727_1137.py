# Generated by Django 3.2.13 on 2022-07-27 06:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0001_initial"),
        ("products", "0005_auto_20220727_1137"),
    ]

    operations = [
        migrations.AlterField(
            model_name="comment",
            name="userid",
            field=models.ForeignKey(
                db_column="userid",
                on_delete=django.db.models.deletion.CASCADE,
                to="users.user",
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="created_by",
            field=models.ForeignKey(
                db_column="created_by",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_person",
                to="users.user",
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="modified_by",
            field=models.ForeignKey(
                db_column="modified_by",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="modified_person",
                to="users.user",
            ),
        ),
        migrations.AlterField(
            model_name="reply",
            name="userid",
            field=models.ForeignKey(
                db_column="userid",
                on_delete=django.db.models.deletion.CASCADE,
                to="users.user",
            ),
        ),
        migrations.AlterField(
            model_name="reviewimages",
            name="created_by",
            field=models.ForeignKey(
                db_column="created_by",
                on_delete=django.db.models.deletion.CASCADE,
                to="users.user",
            ),
        ),
        migrations.DeleteModel(
            name="UserProduct",
        ),
    ]
