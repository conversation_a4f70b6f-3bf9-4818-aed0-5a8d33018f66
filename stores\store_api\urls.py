from django.urls import path
from .views import (
    GetRecentlyVisitedStores,
    GetFollowingStores,
    GetAllStoresOfOneUser,
    StoreListAV,
    StoreDetailsAV,
    StoreLogo,
    StoreCoverImage,
    CategoryListAV,
    CategoryDetailsAV,
    StoreLinkList,
    StoreLinkDetail,
    StoreHandleAvailabilityAV,
    GetStoreProductsSellerSide,
    GetStoreProductBuyerSide,
    FollowUnfollowStore,
    GetStoreFollowers,
    CheckStoreFollowedOrNot,
    VisitStoreAV,
    CheckProductDeliverability,
    OpenOrCloseStore,
    StoreDashboard,
    ActivateOrDeactivateStore,
    DeleteStore,
    GettingReferenceFromHandle,
    CitiesAV,
    CitiesDetailsAV,
    BusinessTypesAV,
    BusinessTypesDetailsAV,
    BulkCreateCategory,
    StoreSignature,
    SendStoreCreationOtp,
    VerifyStoreCreationOtp,
    GetStoreMilestones
)


urlpatterns = [
    path(
        "getfollowingstores/<str:user_reference>/",
        GetFollowingStores.as_view(),
        name="get-following-stores",
    ),
    path(
        "getrecentstores/<str:user_reference>/",
        GetRecentlyVisitedStores.as_view(),
        name="get-recently-visited-stores",
    ),
    # STORE ON BOARDING-SELLER
    path("send_store_creation_otp/", SendStoreCreationOtp.as_view(), name="send-store-creation-otp"),
    path("verify_store_creation_otp/", VerifyStoreCreationOtp.as_view(), name="verify-store-creation-otp"),
    path(
        "get-userstores-list/<int:userid>/",
        GetAllStoresOfOneUser.as_view(),
        name="user-stores-list",
    ),
    path("storelist/", StoreListAV.as_view(), name="store-list"),
    path(
        "storedetails/<str:store_reference>/",
        StoreDetailsAV.as_view(),
        name="store-details",
    ),
    path("addstorelogo/<str:store_reference>/", StoreLogo.as_view(), name="store-logo"),
    path("addstorecoverimage/<str:store_reference>/", StoreCoverImage.as_view(), name="store-cover-image"),
    path("addstoresignature/<str:store_reference>/", StoreSignature.as_view(), name="store-signature"),

    path("categorylist/", CategoryListAV.as_view(), name="category-list"),
    path(
        "open_or_close_store/<str:store_reference>/",
        OpenOrCloseStore.as_view(),
        name="open-store",
    ),
    path(
        "get_store_dashboard/<str:store_reference>/",
        StoreDashboard.as_view(),
        name="get-store-dashboard",
    ),
    path(
        "categorydetails/<int:category_id>/",
        CategoryDetailsAV.as_view(),
        name="category-details",
    ),
    path("storelink/", StoreLinkList.as_view(), name="store-link"),
    path(
        "storelink/<int:store_link_id>/",
        StoreLinkDetail.as_view(),
        name="store-link-detail",
    ),
    path(
        "storehandleavailability/",
        StoreHandleAvailabilityAV.as_view(),
        name="store-handle-availability",
    ),
    path(
        "getting_reference_from_handle/",
        GettingReferenceFromHandle.as_view(),
        name="getting-reference-from-handle",
    ),
    path(
        "getstoreproducts-seller/<str:store_reference>/",
        GetStoreProductsSellerSide.as_view(),
        name="get-store-products-seller",
    ),
    #  STORE PAGE
    path("visitstores/", VisitStoreAV.as_view(), name="visit-stores"),
    path(
        "getstoreproducts-buyer/store/<str:store_reference>/",
        GetStoreProductBuyerSide.as_view(),
        name="get-store-products-buyer",
    ),
    path(
        "getstoreproducts-buyer/store/<str:store_reference>/<str:v1>/",
        GetStoreProductBuyerSide.as_view(),
        name="get-store-products-buyer",
    ),
    path(
        "follow-or-unfollow-store/", FollowUnfollowStore.as_view(), name="follow-store"
    ),
    path(
        "check-store-followed-or-not/follower/<str:supporter_reference>/store/<str:store_reference>/",
        CheckStoreFollowedOrNot.as_view(),
        name="get-follow-or-not",
    ),
    path(
        "getstorefollowers/<str:store_reference>/",
        GetStoreFollowers.as_view(),
        name="get-store-followers",
    ),
    path(
        "get_store_milestones/<str:store_reference>/",
        GetStoreMilestones.as_view(),
        name="get-store-milestones",
    ),
    path(
        "activate_or_deactivate_store/<str:store_reference>/",
        ActivateOrDeactivateStore.as_view(),
        name="activate-or-deactivate-store",
    ),
    path(
        "delete_store/<str:store_reference>/",
        DeleteStore.as_view(),
        name="delete-store",
    ),
    path("city_list/", CitiesAV.as_view(), name="city-list"),
    path("city_details/<int:pk>/", CitiesDetailsAV.as_view(), name="city-details"),

    path("business_type_list/", BusinessTypesAV.as_view(), name="business-type-list"),
    path("business_type_details/<int:pk>/", BusinessTypesDetailsAV.as_view(), name="business-type-details"),
    path("bulk_create_category/", BulkCreateCategory.as_view(), name="bulk-create-category"),


    # ORDER PAGE
    path(
        "check_product_deliverability/",
        CheckProductDeliverability.as_view(),
        name="check-product-deliverability",
    )
]
