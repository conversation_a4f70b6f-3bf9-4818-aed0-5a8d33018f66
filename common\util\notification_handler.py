from orders.order_api.models import Order, SubOrder, RefundedAmount
from users.notification_api.models import Notifications
from users.user_api.models import User, UserDeviceDetails
from stores.store_api.models import Store
from GraphDB.models import Neo4jEntity
from .serializers import NotificationSerializer
import logging
import datetime
import pytz
import os
from decouple import config
import requests
import json
import google.auth
import google.auth.transport.requests
from google.oauth2 import service_account
from users.notification_api.serializers import NotificationsSerializer


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class NotificationHandler:
    def __init__(
        self,
        notified_user,
        notification_type,
        notification_about,
        image=None,
        product_id=None,
        store_pincode=None,
        otp=None,
        package_number=None,
        package_name=None,
        refund_id=None,
        comment_type=None,
        commented_by=None,
        comments=None,
        content_type=None,
        notified_from=None,
        points_count=None,
        payout_amount=None,
        old_state_name=None,
        notes=None,
    ):
        self.notified_user = notified_user
        self.notification_type = notification_type
        self.notification_about = notification_about
        self.image = image
        self.product_id = product_id
        self.store_pincode = store_pincode
        self.otp = otp
        self.package_number = package_number
        self.package_name = package_name
        self.refund_id = refund_id
        self.comment_type = comment_type
        self.commented_by = commented_by
        self.comment_content = comments
        self.content_type = content_type
        self.notified_from = notified_from
        self.points_count = points_count
        self.payout_amount = payout_amount
        self.old_state_name = old_state_name
        self.notes = notes

    @staticmethod
    def get_date(date):

        # Example string representing a date and time
        date_string = date

        # Define the format of the input string
        # This format string should match the structure of your date string
        format_string = "%d/%m/%Y %H:%M:%S"

        # Use datetime.strptime() to convert the string to a datetime object
        date_time_obj = datetime.datetime.strptime(date_string, format_string)

        local_time = date_time_obj.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d-%m-%Y %H:%M:%S")

    @staticmethod
    def action_new_order(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        order_value = order_instance.total_order_amount
        user_name = order_instance.user_reference.user_name
        notification_message = (
            f"Congratulations🎉You got a new order worth ₹{order_value} from @{user_name}"
        )
        return notification_message

    @staticmethod
    def action_order_confirmed(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        notification_message = f"Congratulations! @{store_handle} has just confirmed your order."
        return notification_message

    @staticmethod
    def action_order_updated(notification_handler):
        sub_order_number = notification_handler.notification_about
        sub_order_instance = SubOrder.objects.get(sub_order_number=sub_order_number)
        notification_message = (
            f"Estimated delivery date of your confirmed order has been changed to {sub_order_instance.estimated_delivery_date}."
        )
        return notification_message
    
    @staticmethod
    def action_package_shipped(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        notification_message = f"@{store_handle} is shipping your package. Track your package and the products in it. "
        return notification_message

    @staticmethod
    def action_shipping_package_updated(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        packname = notification_handler.package_name
        notification_message = (
            f"The package {packname} from @{store_handle} has been updated. Check the latest details."
        )
        return notification_message

    @staticmethod
    def action_delivery_otp(notification_handler):
        otp = notification_handler.otp
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        pack_name = notification_handler.package_name
        notification_message = f"Please share this OTP {otp} to receive the package {pack_name} from @{store_handle}"
        return notification_message

    def action_package_delivered(self, notification_handler):
        order_number = notification_handler.notification_about
        package_number = notification_handler.package_number

        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle

        suborder_instance = SubOrder.objects.filter(
            order_number=order_number, package_number=package_number
        ).first()
        pack_name = suborder_instance.display_package_number
        package_delivered_date = suborder_instance.delivered_date

        notification_message = f"The Package {pack_name} from @{store_handle} delivered to you at {package_delivered_date}"
        return notification_message

    @staticmethod
    def action_delivery_failed(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        user_name = order_instance.user_reference.user_name
        notification_message = f"⚠️Attention: @{user_name} just mentioned that the order you marked delivered was" \
                               f" not received. Please speak to your customer and sort it out. "
        return notification_message

    @staticmethod
    def action_seller_cancelled_order(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        notification_message = (f"Your order with @{store_handle} has been cancelled. Swadesic will process your refund "
                                f"as soon as possible. Tap to view the cancel reason.")
        return notification_message

    @staticmethod
    def action_buyer_cancelled_order(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        user_name = order_instance.user_reference.user_name
        notification_message = f"Sorry, your customer @{user_name} has cancelled this sub order {order_number} "
        return notification_message

    @staticmethod
    def action_return_requested(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        user_name = order_instance.user_reference.user_name
        notification_message = f"Your customer @{user_name} just initiated a return within their return window. Please check and process"
        return notification_message

    @staticmethod
    def action_return_accepted(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        notification_message = (
            f"@{store_handle} has accepted return for this product.They will share details for returning the product. Please reach out if there are delays."
        )
        return notification_message


    @staticmethod
    def action_return_request_cancelled(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        user_handle = order_instance.user_reference.user_name
        notification_message = (
            f"Return request for this product has been cancelled by @{user_handle}."
        )
        return notification_message

    @staticmethod
    def action_return_received(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        notification_message = (
            f"@{store_handle} has received your return. Your money will be refunded automatically unless they"
            f" holds it due to an issue."
        )
        return notification_message

    @staticmethod
    def action_refund_initiated(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        notification_message = (
            f"Your refund for an order with @{store_handle} has been initiated"
        )
        return notification_message

    @staticmethod
    def action_refund_hold(notification_handler):
        order_number = notification_handler.notification_about
        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle
        notification_message = f"Your refund for the return with @{store_handle} is onhold. Tap to view details."
        return notification_message

    def action_refund_released(self, notification_handler):

        # Getting required data to create notification message from notification_handler
        order_number = notification_handler.notification_about
        refund_id = notification_handler.refund_id

        order_instance = Order.objects.get(order_number=order_number)
        store_handle = order_instance.store_reference.storehandle

        refund_instance = RefundedAmount.objects.get(refund_id=refund_id)
        refunded_amount = refund_instance.refunded_amount
        refunded_date = refund_instance.refunded_date

        # Changing the datetime to IST
        local_refunded_date = self.get_date(refunded_date)

        notification_message = f"Swadesic and @{store_handle} value your business. A refund of ₹{refunded_amount} for" \
                               f"your order has been credited to you at {local_refunded_date}"
        return notification_message

    @staticmethod
    def action_commented_on_product(notification_handler):

        handle = notification_handler.commented_by
        comment_content = notification_handler.comment_content
        comment_type_input = notification_handler.comment_type
        comment_type_text_options = {
            "comment": "commented",
            "question": "questioned",
            "review": "reviewed"
        }
        comment_type_output = comment_type_text_options.get(comment_type_input)
        notification_message = f"@{handle} {comment_type_output} your product: {comment_content}"
        return notification_message

    @staticmethod
    def action_someone_followed_entity(notification_handler):
        visitor_reference = notification_handler.notification_about
        entity_reference = notification_handler.notified_user

        visitor = Neo4jEntity.nodes.get(reference=visitor_reference)
        supporter_name = visitor.handle
        notification_message = f"@{supporter_name} started supporting your store" if entity_reference.startswith('S') else f"@{supporter_name} started following you"
        return notification_message

    @staticmethod
    def action_new_store_created(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        store_handle = store_instance.storehandle

        notification_message = f"Your nearby store @{store_handle} has opened their store for public"
        return notification_message

    @staticmethod
    def action_content_liked(notification_handler):
        liker_name = notification_handler.notified_from
        content_type = notification_handler.content_type
        notification_message = f"@{liker_name} liked your {content_type}"
        return notification_message

    @staticmethod
    def action_message_received(notification_handler):
        unread_count = notification_handler.notes
        notification_message = f"view {unread_count} unread messages"
        return notification_message

    @staticmethod
    def action_someone_commented_on_content(notification_handler):
        commented_by = notification_handler.notified_from
        content_type = notification_handler.content_type
        notification_message = f"@{commented_by} commented on your {content_type}"
        return notification_message

    @staticmethod
    def someone_reposted_content(notification_handler):
        reposted_by = notification_handler.notified_from
        content_type = notification_handler.content_type
        notification_message = f"@{reposted_by} reposted your {content_type}"
        return notification_message

    @staticmethod
    def someone_signed_up_with_your_code(notification_handler):
        username = notification_handler.notification_about
        points_count = notification_handler.points_count
        notification_message = (f"@{username} just signed up with your Invite code. ₹{points_count} has added to your Infinity balance")
        return notification_message


    @staticmethod
    def you_signed_up_with_someones_code(notification_handler):
        username = notification_handler.notification_about
        points_count = notification_handler.points_count
        notification_message = (f"Welcome to Swadesic! You received ₹{points_count} into your Infinity balance for using an Invite code")
        return notification_message

    @staticmethod
    def action_payout_otp_sent(notification_handler):
        otp = notification_handler.otp
        store_reference = notification_handler.notification_about,
        store_instance = Store.objects.get(store_reference=store_reference[0], deleted=False)
        amount = notification_handler.payout_amount
        notification_message = f"Your OTP for withdrawing amount ₹{amount} from your account {store_instance.storehandle} is {otp}. This code is valid for the next 2 minutes. Do not share it with anyone."
        return notification_message

    @staticmethod
    def action_store_state_changed(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        old_state_name = notification_handler.old_state_name
        new_state_name = store_instance.store_details.last().state
        notification_message = f"Delivery locations of products selected for the state {old_state_name} has been cleared as you have changed your state, please add new delivery locations for your new state {new_state_name} now"
        return notification_message

    @staticmethod
    def action_store_onboarding_reward(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        reward_amount = notification_handler.points_count
        referred_user = store_instance.created_by.display_name

        if notification_handler.notified_user.startswith("U"):
            notification_message = f"@{referred_user} has created and verified a store. You've received ₹{reward_amount} infinity points as referral reward!"
        else:
            notification_message = f"Congratulations! Your store {store_instance.storehandle} has been verified. You've received ₹{reward_amount} infinity points as a reward."

        return notification_message

    @staticmethod
    def action_store_verification_promotional_reward(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        reward_amount = notification_handler.points_count
        notification_message = f"Congratulations! Your store {store_instance.storehandle} has been verified. You've received ₹{reward_amount} Flash points as a promotional reward."
        return notification_message
    
    @staticmethod
    def action_normal_store_verification_reward(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        reward_amount = notification_handler.points_count
        notification_message = f"Congratulations! Your store {store_instance.storehandle} has been verified. You've received ₹{reward_amount} Flash points as a reward."
        return notification_message

    @staticmethod
    def action_product_removed_from_shipping(notification_handler):
        suborder_number = notification_handler.notes
        suborder_instance = SubOrder.objects.get(suborder_number=suborder_number, is_deleted=False)
        notification_message = f"A product has been removed from existing package by your seller and changed to {suborder_instance.suborder_status} status "
        return notification_message

    @staticmethod
    def action_product_updated_from_shipping(notification_handler):
        suborder_number = notification_handler.notes
        suborder_instance = SubOrder.objects.get(suborder_number=suborder_number, is_deleted=False)
        notification_message = f"A product has been updated from existing package by your seller and changed to {suborder_instance.suborder_status} status "
        return notification_message

    @staticmethod
    def action_store_verification_approved(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        notification_message = f"Your store @{store_instance.storehandle} has been verified, you can now open your store for orders & start receiving them"
        return notification_message

    @staticmethod
    def action_pan_verification_rejected(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        notification_message = f"Your store @{store_instance.storehandle}'s PAN verification is rejected due to a reason. Please check Trust center ID verification section and reverify."
        return notification_message

    @staticmethod
    def action_gst_verification_rejected(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        notification_message = f"Your store @{store_instance.storehandle}'s GST verification is rejected due to a reason. Please check Trust center ID verification section and reverify."
        return notification_message

    @staticmethod
    def action_gst_verification_approved(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        notification_message = f"Your store @{store_instance.storehandle}'s GST verification is approved"
        return notification_message

    @staticmethod
    def action_pan_verification_approved(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        notification_message = f"Your store @{store_instance.storehandle}'s PAN verification is approved"
        return notification_message

    @staticmethod
    def action_payout_success(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        notification_message = f"Your withdrawal request for ₹{notification_handler.payout_amount} from @{store_instance.storehandle} has been successful"
        return notification_message
    
    @staticmethod
    def action_payout_failure(notification_handler):
        store_reference = notification_handler.notification_about
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
        notification_message = f"Your withdrawal request for ₹{notification_handler.payout_amount} from @{store_instance.storehandle} has failed"
        return notification_message

    @staticmethod
    def send_silent_fcm_notification(registration_ids, data):

        def get_access_token():
            request = google.auth.transport.requests.Request()
            credentials.refresh(request)
            return credentials.token

        service_account_file = config('SERVICE_JSON_FILE')
        credentials = service_account.Credentials.from_service_account_file(service_account_file, scopes=["https://www.googleapis.com/auth/cloud-platform"])

        access_token = get_access_token()

        url = f"https://fcm.googleapis.com/v1/projects/{config('FIREBASE_PROJECT_ID')}/messages:send"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        payload = {
            "message": {
                "token": registration_ids,
                "data": {
                    **data
                }
            }
        }

        response = requests.post(url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            logger.info("Silent FCM Notification sent successfully")
        else:
            logger.error(f"Error sending Silent FCM Notification: {response.text}")

    @staticmethod
    def send_fcm_notification(registration_ids, data):
        image_field_value = data.get("image", "")
        base_url = config('BASE_URL')
        image_serializable = f"{base_url}{str(image_field_value)}" if image_field_value else ""
        
        def get_access_token():
            request = google.auth.transport.requests.Request()
            credentials.refresh(request)
            return credentials.token

        service_account_file = config('SERVICE_JSON_FILE')
        credentials = service_account.Credentials.from_service_account_file(service_account_file, scopes=["https://www.googleapis.com/auth/cloud-platform"])

        access_token = get_access_token()

        url = f"https://fcm.googleapis.com/v1/projects/{config('FIREBASE_PROJECT_ID')}/messages:send"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        payload = {
            "message": {
                "token": registration_ids,
                "notification": {
                    "title": data.get("title", ""),
                    "body": data.get("notification_message", ""),
                    "image": image_serializable,
                },
                "data": {
                    **data
                }
            }
        }
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            logger.info("FCM Notification sent successfully")
        else:
            logger.error(f"Error sending FCM Notification: {response.text}")

    def create_notification(self, notification_handler):
        # Define a dictionary to map notification types to action methods
        notification_actions = {
            Notifications.Notifications_Type.ORDER_CONFIRMED: self.action_order_confirmed,
            Notifications.Notifications_Type.NEW_ORDER: self.action_new_order,
            Notifications.Notifications_Type.ORDER_SHIPPED: self.action_package_shipped,
            Notifications.Notifications_Type.SHIPPING_PACAKGE_UPDATED: self.action_shipping_package_updated,
            Notifications.Notifications_Type.DELIVERY_OTP: self.action_delivery_otp,
            Notifications.Notifications_Type.PACKAGE_DELIVERED: self.action_package_delivered,
            Notifications.Notifications_Type.DELIVERY_FAILED: self.action_delivery_failed,
            Notifications.Notifications_Type.SELLER_CANCELLED_ORDER: self.action_seller_cancelled_order,
            Notifications.Notifications_Type.BUYER_CANCELLED_ORDER: self.action_buyer_cancelled_order,
            Notifications.Notifications_Type.RETURN_REQUESTED: self.action_return_requested,
            Notifications.Notifications_Type.RETURN_ACCEPTED: self.action_return_accepted,
            Notifications.Notifications_Type.RETURN_RECEIVED: self.action_return_received,
            Notifications.Notifications_Type.REFUND_INITIATED: self.action_refund_initiated,
            Notifications.Notifications_Type.REFUND_HOLD: self.action_refund_hold,
            Notifications.Notifications_Type.REFUND_RELEASED: self.action_refund_released,
            Notifications.Notifications_Type.COMMENTED: self.action_commented_on_product,
            Notifications.Notifications_Type.SOMEONE_FOLLOWED_ENTITY: self.action_someone_followed_entity,
            # Notifications.Notifications_Type.SOMEONE_FOLLOWED_STORE: self.action_someone_followed_store,
            # Notifications.Notifications_Type.SOMEONE_FOLLOWED_USER: self.action_someone_followed_user,

            Notifications.Notifications_Type.STORE_CREATED: self.action_new_store_created,
            Notifications.Notifications_Type.CONTENT_LIKED: self.action_content_liked,
            Notifications.Notifications_Type.SOMEONE_COMMENTED_ON_CONTENT: self.action_someone_commented_on_content,
            Notifications.Notifications_Type.SOMEONE_REPOSTED_CONTENT: self.someone_reposted_content,
            Notifications.Notifications_Type.SOMEONE_SIGNED_UP_WITH_YOUR_CODE: self.someone_signed_up_with_your_code,
            Notifications.Notifications_Type.YOU_SIGNED_UP_WITH_SOMEONES_CODE: self.you_signed_up_with_someones_code,
            Notifications.Notifications_Type.ORDER_UPDATE: self.action_order_updated,
            Notifications.Notifications_Type.PAYOUT_OTP: self.action_payout_otp_sent,
            Notifications.Notifications_Type.STORE_VERIFICATION_ONBOARDING_REWARD: self.action_store_onboarding_reward,
            Notifications.Notifications_Type.PRODUCT_UPDATED_FROM_SHIPPING_GROUP: self.action_product_updated_from_shipping,
            Notifications.Notifications_Type.PRODUCT_REMOVED_FROM_SHIPPING_GROUP: self.action_product_removed_from_shipping,
            Notifications.Notifications_Type.STORE_VERIFICATION_APPROVED: self.action_store_verification_approved,
            Notifications.Notifications_Type.STORE_VERIFICATION_PROMOTION_REWARD: self.action_store_verification_promotional_reward,
            Notifications.Notifications_Type.PAN_VERIFICATION_REJECTED: self.action_pan_verification_rejected,
            Notifications.Notifications_Type.GST_VERIFICATION_REJECTED: self.action_gst_verification_rejected,
            Notifications.Notifications_Type.GST_VERIFICATION_APPROVED: self.action_gst_verification_approved,
            Notifications.Notifications_Type.PAN_VERIFICATION_APPROVED: self.action_pan_verification_approved,
            Notifications.Notifications_Type.NORMAL_STORE_VERIFICATION_REWARD: self.action_normal_store_verification_reward,
            Notifications.Notifications_Type.PAYOUT_SUCCESS: self.action_payout_success,
            Notifications.Notifications_Type.PAYOUT_FAILED: self.action_payout_failure,
            Notifications.Notifications_Type.MESSAGE_RECEIVED: self.action_message_received,

            # Add more notification types and action methods as needed
        }

        # Get the action method based on the notification type
        notification_type = notification_handler.notification_type
        action_method = notification_actions.get(notification_type)
        #TODO Check if every action method is being called in the above line if yes optimize the flow

        if action_method is not None:
            # Call the action method
            notification_message = action_method(notification_handler)

            # Create the notification
            context_data = {
                "notified_user": notification_handler.notified_user,
                "notification_type": notification_handler.notification_type,
                "notification_message": notification_message,
                "notification_about": notification_handler.notification_about,
                "image": notification_handler.image if notification_handler.image else None,
            }
            serializer = NotificationSerializer(data=context_data)
            if serializer.is_valid():
                serializer.save()
                notification_instance = serializer.instance
                fcm_serializer = NotificationsSerializer(notification_instance)
                
                # Retrieve FCM registration tokens for the notified user
                user_device_tokens = UserDeviceDetails.objects.filter(
                    user_reference=notification_handler.notified_user)

                if notification_handler.notification_type == Notifications.Notifications_Type.MESSAGE_RECEIVED:
                    handle = Neo4jEntity.nodes.get(reference=notification_handler.notification_about).handle
                else:
                    handle = None

                # Prepare FCM notification data
                fcm_data = {
                    "title": get_notification_title(notification_type=notification_type, handle=handle),
                    **fcm_serializer.data
                }
                excluded_push_notification_types = [
                    Notifications.Notifications_Type.SHIPPING_PACAKGE_UPDATED,
                    Notifications.Notifications_Type.STORE_CREATED
                ]
                for device_token in user_device_tokens:
                    registration_ids = device_token.fcm_token
                    if notification_type in excluded_push_notification_types:
                        self.send_silent_fcm_notification(registration_ids, fcm_data)
                    else:
                        self.send_fcm_notification(registration_ids, fcm_data)
            else:
                print(serializer.errors)
                logger.info("something went wrong, notification couldn't be created")


def get_notification_title(notification_type, handle=None):
    # Define a dictionary that maps notification types to titles
    notification_titles = {
        Notifications.Notifications_Type.ORDER_CONFIRMED: "Your order is confirmed",
        Notifications.Notifications_Type.NEW_ORDER: "You got a new order",
        Notifications.Notifications_Type.ORDER_SHIPPED: "Hurray! Your order is shipped",
        Notifications.Notifications_Type.SHIPPING_PACAKGE_UPDATED: "Your shipping package details updated",
        Notifications.Notifications_Type.DELIVERY_OTP: "Delivery OTP generated",
        Notifications.Notifications_Type.PACKAGE_DELIVERED: "Your order has been delivered successfully",
        Notifications.Notifications_Type.DELIVERY_FAILED: "Delivery Failure: Attention Required!!",
        Notifications.Notifications_Type.SELLER_CANCELLED_ORDER: "Sorry! Seller cancelled your order",
        Notifications.Notifications_Type.BUYER_CANCELLED_ORDER: "Order cancelled successfully",
        Notifications.Notifications_Type.RETURN_REQUESTED: "Return requested",
        Notifications.Notifications_Type.RETURN_ACCEPTED: "Return accepted",
        Notifications.Notifications_Type.RETURN_RECEIVED: "Return received",
        Notifications.Notifications_Type.REFUND_INITIATED: "Your refund has been initiated",
        Notifications.Notifications_Type.REFUND_HOLD: "Your refund is on hold",
        Notifications.Notifications_Type.REFUND_RELEASED: "Your refund is released",
        Notifications.Notifications_Type.COMMENTED: "New comment on your product",
        Notifications.Notifications_Type.SOMEONE_FOLLOWED_ENTITY: "You got a new follower",

        # Notifications.Notifications_Type.SOMEONE_FOLLOWED_STORE: "Your Store just got a new Supporter!",
        # Notifications.Notifications_Type.SOMEONE_FOLLOWED_USER: "You have a new Follower!",
        Notifications.Notifications_Type.STORE_CREATED: "New store created nearby!",
        Notifications.Notifications_Type.CONTENT_LIKED: "You've got a new like",
        Notifications.Notifications_Type.SOMEONE_COMMENTED_ON_CONTENT: "You've got a new comment",
        Notifications.Notifications_Type.SOMEONE_REPOSTED_CONTENT: "You've got a repost",
        Notifications.Notifications_Type.SOMEONE_SIGNED_UP_WITH_YOUR_CODE: "New Invitee Joined",
        Notifications.Notifications_Type.YOU_SIGNED_UP_WITH_SOMEONES_CODE: "Received Infinity points from Swadesic",
        Notifications.Notifications_Type.ORDER_UPDATE: "Order update!",
        Notifications.Notifications_Type.STORE_VERIFICATION_ONBOARDING_REWARD: "Store Verification Reward!",
        Notifications.Notifications_Type.PRODUCT_UPDATED_FROM_SHIPPING_GROUP: "Product updated from shipping group",
        Notifications.Notifications_Type.PRODUCT_REMOVED_FROM_SHIPPING_GROUP: "Product removed from shipping group",
        Notifications.Notifications_Type.PAYOUT_SUCCESS: "Payout Success!",
        Notifications.Notifications_Type.PAYOUT_FAILED: "Payout Failed!",
        Notifications.Notifications_Type.MESSAGE_RECEIVED: f"{handle} messaged you." if handle is not None else "A Swadeshi messaged you!!",
    # Add more notification types as needed
    }
    # Fetch the title based on the notification type or use a default if not found
    return notification_titles.get(notification_type)
