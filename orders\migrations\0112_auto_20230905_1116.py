# Generated by Django 3.2.13 on 2023-09-05 05:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0111_auto_20230817_2250'),
    ]

    operations = [
        migrations.AlterField(
            model_name='refundedamount',
            name='refund_status',
            field=models.CharField(blank=True, choices=[('UNPROCESSED', 'Unprocessed'), ('CANCELLED', 'Cancelled'), ('REFUNDED', 'Refunded'), ('PENDING', 'Pending'), ('HOLD', 'Hold'), ('REFUND_INITIATED', 'Refund initiated'), ('REFUND_INITIATE_FAILED', 'Initiate failed'), ('REFUND_FAILED', 'Refund failed')], max_length=40, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='suborder',
            name='suborder_status',
            field=models.Char<PERSON>ield(choices=[('ORDER_INITIATED', 'Order initiated'), ('PAYMENT_INITIATED', 'Payment initiated'), ('PAYMENT_SUCCESS', 'Payment success'), ('WAITING_FOR_CONFIRMATION', 'waiting for confirmation'), ('PAYMENT_PENDING', 'Payment pending'), ('PAYMENT_FAILED', 'Payment failed'), ('ORDER_CONFIRMED', 'Order confirmed'), ('DELIVERY_IN_PROGRESS', 'Delivery in progress'), ('ORDER_DELIVERED', 'Order delivered'), ('ORDER_CANCELLED', 'Order cancelled'), ('ORDER_CANCELLED_BY_BUYER', 'Order cancelled by buyer'), ('ORDER_CANCELLED_BY_SELLER', 'Order cancelled by seller'), ('DELIVERY_FAILED', 'Delivery failed'), ('RETURN_REQUESTED', 'Return requested'), ('RETURN_CONFIRMED', 'Return confirmed'), ('RETURN_IN_PROGRESS', 'Return in progress'), ('RETURNED_TO_SELLER', 'Return to seller'), ('RETURN_FAILED', 'Return failed'), ('REFUND_HOLD', 'Refund hold'), ('REFUNDED', 'Refunded'), ('ORDER_AUTO_CANCELLED', 'Order auto cancelled')], default='ORDER_INITIATED', max_length=50),
        ),
    ]
