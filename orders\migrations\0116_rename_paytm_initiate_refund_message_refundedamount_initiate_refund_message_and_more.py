# Generated by Django 4.2.7 on 2024-08-03 11:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0115_order_payment_channel_orderlifecycle_payment_channel"),
    ]

    operations = [
        migrations.RenameField(
            model_name="refundedamount",
            old_name="paytm_initiate_refund_message",
            new_name="initiate_refund_message",
        ),
        migrations.RenameField(
            model_name="refundedamount",
            old_name="paytm_initiate_refund_status",
            new_name="initiate_refund_status",
        ),
        migrations.AddField(
            model_name="refundedamount",
            name="razorpay_refund_id",
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
        migrations.AddField(
            model_name="refundedamount",
            name="refund_transaction_id",
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
    ]
