import graphene
from .mutations import DeletePostMutation, LikePostMutation
from .types import PostsType, PostImagesType, PostLikesType
from .models import Posts, PostImages, PostLikes
from users.user_api.models import User
from stores.store_api.models import Store
from .feed_api.models import FeedPost


class Mutation(graphene.ObjectType):
    delete_post = DeletePostMutation.Field()
    like_post = LikePostMutation.Field()


class Query(graphene.ObjectType):
    all_posts = graphene.List(
        PostsType,
        limit=graphene.Int(),
        offset=graphene.Int(),
    )
    posts = graphene.List(PostsType, post_references=graphene.List(graphene.String, required=True))

    user_and_store_posts = graphene.List(
        PostsType,
        creator_reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int(),
    )

    feed_posts = graphene.List(
        PostsType,
        reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int(),
    )

    def resolve_all_posts(self, info, limit=None, offset=None):
        # Your custom logic for pagination goes here
        posts_query = Posts.objects.filter(is_deleted=False).order_by('-created_date')

        if offset is not None and offset >= 0:
            posts_query = posts_query[offset:]

        if limit is not None and limit >= 0:
            posts_query = posts_query[:limit]

        return posts_query

    def resolve_posts(self, info, post_references):
        try:
            return Posts.objects.filter(post_reference__in=post_references, is_deleted=False)
        except Posts.DoesNotExist:
            raise Exception("Post with the given reference does not exist.")

    def resolve_user_and_store_posts(self, info, creator_reference, limit=None, offset=None):
        # Check if the provided reference is a user or store
        try:
            user_or_store = User.objects.get(user_reference=creator_reference)
        except User.DoesNotExist:
            try:
                user_or_store = Store.objects.get(store_reference=creator_reference)
            except Store.DoesNotExist:
                raise Exception("User or Store with the given reference does not exist.")

        # Get posts based on the provided reference
        if isinstance(user_or_store, User):
            posts = Posts.objects.filter(user_reference=user_or_store, is_deleted=False).order_by('-created_date')
        elif isinstance(user_or_store, Store):
            posts = Posts.objects.filter(store_reference=user_or_store, is_deleted=False).order_by('-created_date')
        else:
            raise Exception("Invalid user_or_store type.")

        if offset is not None and offset >= 0:
            posts = posts[offset:]

        if limit is not None and limit >= 0:
            posts = posts[:limit]

        # Check like status for each post based on the visitor_reference
        return posts

    def resolve_feed_posts(self, info, reference, limit=None, offset=None):
        try:
            feed_post_entries = FeedPost.objects.filter(reference=reference).values_list("post_reference")
        except FeedPost.DoesNotExist:
            raise Exception("No feedPosts for the given reference.")

        posts = Posts.objects.filter(post_reference__in=feed_post_entries, is_deleted=False).order_by('-created_date')

        if offset is not None and offset >= 0:
            posts = posts[offset:]

        if limit is not None and limit >= 0:
            posts = posts[:limit]

        return posts

schema = graphene.Schema(query=Query, mutation=Mutation)
