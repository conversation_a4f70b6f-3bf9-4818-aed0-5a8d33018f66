# Generated by Django 3.2.13 on 2022-07-26 17:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0003_deliverylocations_deliverysettings_documents_trustcenter"),
        ("products", "0003_auto_20220726_2214"),
    ]

    operations = [
        migrations.CreateModel(
            name="Comment",
            fields=[
                ("commentid", models.AutoField(primary_key=True, serialize=False)),
                ("comments", models.TextField()),
                ("comment_type", models.<PERSON>r<PERSON>ield(max_length=100)),
                ("claps", models.IntegerField(default=0)),
                (
                    "clapped_users",
                    models.CharField(blank=True, max_length=100000, null=True),
                ),
                ("review", models.FloatField(default=0)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now_add=True)),
                ("report_comment", models.BooleanField(default=False)),
                (
                    "productid",
                    models.ForeignKey(
                        db_column="productid",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                    ),
                ),
                (
                    "userid",
                    models.ForeignKey(
                        db_column="userid",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "comments",
                "db_table": '"product"."comment"',
            },
        ),
        migrations.CreateModel(
            name="ReviewImages",
            fields=[
                (
                    "review_image_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("review_image", models.ImageField(upload_to="review_images")),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now_add=True)),
                (
                    "commentid",
                    models.ForeignKey(
                        db_column="commentid",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comment_review_image",
                        to="products.comment",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        db_column="created_by",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "review images",
                "db_table": '"product"."review_images"',
            },
        ),
        migrations.CreateModel(
            name="Reply",
            fields=[
                ("replyid", models.AutoField(primary_key=True, serialize=False)),
                ("reply", models.TextField()),
                ("claps", models.IntegerField(default=0)),
                (
                    "clapped_users",
                    models.CharField(blank=True, max_length=100000, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now_add=True)),
                ("report_reply", models.BooleanField(default=False)),
                (
                    "commentid",
                    models.ForeignKey(
                        db_column="commentid",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="products.comment",
                    ),
                ),
                (
                    "userid",
                    models.ForeignKey(
                        db_column="userid",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "replies",
                "db_table": '"product"."reply"',
            },
        ),
    ]
