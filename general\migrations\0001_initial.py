# Generated by Django 3.2.13 on 2023-03-10 05:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('banner_id', models.AutoField(primary_key=True, serialize=False)),
                ('banner_image', models.ImageField(upload_to='banner_images')),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('modified_date', models.DateField(auto_now=True)),
                ('note', models.CharField(blank=True, max_length=300, null=True)),
            ],
            options={
                'verbose_name_plural': 'banners',
                'db_table': '"public"."banner"',
            },
        ),
    ]
