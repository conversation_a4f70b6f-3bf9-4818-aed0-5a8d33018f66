from graphene_django import DjangoObjectType
import graphene
import datetime
import json
from datetime import datetime, timedelta
import pytz
import logging
from .models import *
from content.models import Comments, CommentImages, Posts, PostImages
from products.models import Product,ProductImages
from .queries import dbqueries
from stores.store_settings_api.models import Trust<PERSON>enter, DeliverySettings
from stores.store_api.models import Store
from stores.store_api.serializers import GetStoreSerializer
from users.user_api.models import User
from products.api.serializers import GetProductSerializer, GetProductListSerializer
from users.invite_api.models import RewardsHistory
from general.views import BuyStatus<PERSON>hecker

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class Neo4jEntityType(graphene.ObjectType):
    reference = graphene.String(source='reference') #u023324324 or s234234 or ur2343242
    entity_type = graphene.String()
    handle = graphene.String()
    name = graphene.String(source='name')
    icon = graphene.String(source="icon")
    phone_number = graphene.String(source="phonenumber")
    pincode = graphene.String(source="pincode")
    city = graphene.String(source="city")
    is_deleted = graphene.Boolean(source="is_deleted")
    created_date = graphene.String()
    category_name = graphene.String(source='category_name')
    subscription_type = graphene.String()

    followers_or_supporters_count = graphene.Int()
    following_or_supporting_count = graphene.Int()
    follow_status = graphene.String(visitor_reference=graphene.String())
    entity_town = graphene.String()

    reward_subtext = graphene.String(invite_code=graphene.String())


    def resolve_subscription_type(self, info):
        if isinstance(self, Neo4jUser):
            return User.objects.get(user_reference=self.reference).subscription_type
        elif isinstance(self, Neo4jStore):
            return Store.objects.get(store_reference=self.reference).subscription_type
        else:
            return None

    def resolve_reward_subtext(self, info, invite_code):
        try:
            reward = RewardsHistory.objects.filter(
                invite_code=invite_code,
                receiver_reference=self.reference,
                reward_type=RewardsHistory.RewardTypeChoices.ONBOARDING_REFERRAL
            ).first()

            if reward:
                amount = reward.received_value
                if reward.reward_status == RewardsHistory.RewardStatusChoices.SUCCESS:
                    if reward.transaction_type == RewardsHistory.TransactionTypeChoices.CREDIT:
                        transaction_type = "credited"
                    elif reward.transaction_type == RewardsHistory.TransactionTypeChoices.DEBIT:
                        transaction_type = "debited"
                    else:
                        transaction_type = "processed"
                else:
                    transaction_type = "pending"

                return f"₹{amount} {transaction_type}"
            else:
                return ""
        except Exception as e:
            logger.error(f"Error in resolve_reward_subtext: {str(e)}")
            return ""


    def resolve_created_date(self, info):
        date = self.created_date
        if isinstance(date, datetime):
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d:%m:%Y %H:%M:%S")
        else:
            return datetime.strptime(date, "%Y-%m-%d %H:%M:%S").strftime("%d:%m:%Y %H:%M:%S") if date else None

    def resolve_entity_town(self,info):
        return "NELLORE"

    def resolve_handle(self, info):
        reference = self.reference
        if reference.startswith('U'):
            user_instance = User.objects.get(user_reference=reference)
            if self.handle != user_instance.user_name:
                self.handle = user_instance.user_name
                self.save()
            return user_instance.user_name if user_instance.user_name else user_instance.display_name
        if reference.startswith('S'):
            store_instance = Store.objects.get(store_reference=reference)
            return store_instance.storehandle

    def resolve_entity_type(self,info):
        entity_type = "UNREGISTERED"
        if isinstance(self, Neo4jUser):
            entity_type = "USER"
        elif isinstance(self, Neo4jStore):
            entity_type = "STORE"
        elif isinstance(self, Neo4jUnrUser):
            entity_type = "UNREGISTERED"
        else:
            pass
        return entity_type

    def resolve_follow_status(self, info, visitor_reference):
        return self.follow_status(visitor_reference)

    def resolve_followers_or_supporters_count(self, info):
        return self.get_follower_count()

    def resolve_following_or_supporting_count(self, info):
        return self.get_following_count()

    # def resolve_user_reference(self, info):
    #     if isinstance(self, Neo4jUser):
    #         return self.reference
    #
    # def resolve_store_reference(self, info):
    #     if isinstance(self, Neo4jStore):
    #         return self.reference

    class Meta:
        model = Neo4jEntity #Neo4j user, Neo4j Store, Neo4j Entity


def get_content_reference(self):
    if isinstance(self, Posts):
        return self.post_reference
    if isinstance(self, Product):
        return self.product_reference
    if isinstance(self, Comments):
        return self.comment_reference

def get_content_creator(self):
    if isinstance(self, Posts):
        post_node = Neo4jPost.nodes.get(reference=self.post_reference)
        return post_node.get_post_creator()
    if isinstance(self, Product):
        product = Neo4jProduct.nodes.get(reference=self.product_reference)
        return product.get_product_creator()
    if isinstance(self, Comments):
        comment = Neo4jComment.nodes.get(reference=self.comment_reference)
        return comment.get_comment_creator()


class ContentHeaderType(graphene.ObjectType):
    handle = graphene.String()
    reference = graphene.String()


class ContentType(graphene.ObjectType):
    content_type = graphene.String()
    content_category = graphene.String( visitor_reference=graphene.String())
    content_headers = graphene.List(ContentHeaderType, visitor_reference=graphene.String())
    content_header_text = graphene.String(visitor_reference=graphene.String())
    like_count = graphene.Int(source='like_count')
    comment_count = graphene.Int(source='comment_count')
    repost_count = graphene.Int(source='repost_count')
    repost_plus_count = graphene.Int(source='repost_plus_count')
    save_count = graphene.Int(source='save_count')
    share_count = graphene.Int(source='share_count')
    created_date = graphene.DateTime(source='created_date')

    like_status = graphene.Boolean(visitor_reference=graphene.String())
    repost_status = graphene.Boolean(visitor_reference=graphene.String())
    save_status = graphene.Boolean(visitor_reference=graphene.String())

    analytics_view_count = graphene.Int(source='analytics_view_count')
    tagged_users_count = graphene.Int()
    tagged_stores_count = graphene.Int()
    tagged_products_count = graphene.Int()

    def resolve_tagged_users_count(self, info):
        content_reference = get_content_reference(self)
        tagged_count = dbqueries.get_tagged_objects_count(content_reference=content_reference, item_type='USER')
        return tagged_count
    
    def resolve_tagged_stores_count(self, info):
        content_reference = get_content_reference(self)
        tagged_count = dbqueries.get_tagged_objects_count(content_reference=content_reference, item_type='STORE')
        return tagged_count
    
    def resolve_tagged_products_count(self, info):
        content_reference = get_content_reference(self)
        tagged_count = dbqueries.get_tagged_objects_count(content_reference=content_reference, item_type='PRODUCT')
        return tagged_count

    def resolve_content_type(self,info):
        if isinstance(self, Posts):
            return "POST"
        if isinstance(self, Product):
            return "PRODUCT"
        if isinstance(self, Comments):
            return "COMMENT"

    def resolve_content_category(self, info, visitor_reference):
        try:
            entity = Neo4jEntity.nodes.get(reference=visitor_reference)
            content = Neo4jContent.nodes.get(reference=get_content_reference(self))
            content_creator = get_content_creator(self)
            entity_followers = entity.get_following()
            if entity.reposts.is_connected(content):
                results = 'REPOST'
            else:
                if entity.follows.is_connected(content_creator):
                    results = 'POST'
                elif any(follower.reposts.is_connected(content) for follower in entity_followers) and content.created_date.replace(tzinfo=None) > (datetime.now() - timedelta(days=30)):
                    results = 'REPOST'
                else:
                    results = 'POST'
            return results
        except Neo4jEntity.DoesNotExist or Neo4jContent.DoesNotExist:
            raise Exception("Entity or content with the given reference does not exist.")

    def resolve_content_headers(self, info, visitor_reference):
        result = dbqueries.get_content_headers(content_reference=get_content_reference(self), visitor_reference=visitor_reference)
        headers = [{'reference': res[1], 'handle': res[0]} for res in result]
        return headers

    def resolve_content_header_text(self, info, visitor_reference):
        result = dbqueries.get_content_headers(content_reference=get_content_reference(self),
                                               visitor_reference=visitor_reference)
        headers = [{'reference': res[1], 'handle': res[0]} for res in result]
        if not headers:
            return None
        
        if len(headers) == 1:
            return f"reposted by @{headers[0]['handle']}"
        elif len(headers) == 2:
            return f"reposted by @{headers[0]['handle']} and @{headers[1]['handle']}"
        else:
            others_count = len(headers) - 1
            return f"reposted by @{headers[0]['handle']} and {others_count} others"

    def resolve_like_status(self, info, visitor_reference=None):
        try:
            entity = Neo4jEntity.nodes.get(reference=visitor_reference)
            content = Neo4jContent.nodes.get(reference=get_content_reference(self))

            return content.liked_by.is_connected(entity)
        except Neo4jEntity.DoesNotExist:
            raise Exception("Entity with the given reference does not exist.")

    def resolve_repost_status(self, info, visitor_reference=None):
        try:
            entity = Neo4jEntity.nodes.get(reference=visitor_reference)
            content = Neo4jContent.nodes.get(reference=get_content_reference(self))

            return entity.reposts.is_connected(content)
        except Neo4jEntity.DoesNotExist:
            raise Exception("Entity with the given reference does not exist.")

    def resolve_save_status(self, info, visitor_reference=None):
        try:
            entity = Neo4jEntity.nodes.get(reference=visitor_reference)
            content = Neo4jContent.nodes.get(reference=get_content_reference(self))

            return entity.saves.is_connected(content)
        except Neo4jEntity.DoesNotExist:
            raise Exception("Entity with the given reference does not exist.")


class Neo4jUserType(graphene.ObjectType):

    class Meta:
        model = Neo4jUser


class Neo4jStoreType(Neo4jEntityType):
    sales_count = graphene.Int()

    def resolve_sales_count(self, info):
        store_instance = Store.objects.get(store_reference=self.reference)
        serializer = GetStoreSerializer
        count = GetStoreSerializer.get_store_sales_count(self=serializer, obj=store_instance)
        return count

    class Meta:
        model = Neo4jStore


class MediaItemType(graphene.ObjectType):
    media_id = graphene.String()
    media_type = graphene.String()
    media_path = graphene.String()
    order = graphene.Int()


class Neo4jPostType(ContentType):
    post_reference = graphene.String(source='post_reference')
    created_by = graphene.Field(Neo4jEntityType)
    post_text = graphene.String(source='post_text')
    post_images = graphene.List(MediaItemType)
    is_deleted = graphene.Boolean(source='is_deleted')

    def resolve_created_by(self,info):
        post_node = Neo4jPost.nodes.get(reference=self.post_reference)
        return post_node.get_post_creator()

    def resolve_post_images(self, info):
        post_images = PostImages.objects.filter(post_reference=self.post_reference)
        if post_images:
            return [
                MediaItemType(
                    media_id=image.post_image_id,
                    media_path=image.post_image,
                    order=image.reorder
                ) for image in post_images
            ]
        else:
            return []
    class Meta:
        model = Posts


class Neo4jFeedType(graphene.ObjectType):
    feed_id = graphene.String(source='feed_id')
    posts = graphene.List(Neo4jPostType, limit=graphene.Int(), offset=graphene.Int())

    def resolve_posts(self, info, limit, offset):
        return self.get_posts_in_feed(limit, offset)

    class Meta:
        model = Neo4jFeed


class Neo4jContactType(graphene.ObjectType):
    reference = graphene.String()
    entity_type = graphene.String()
    handle = graphene.String()
    name = graphene.String()
    icon = graphene.String()
    phonenumber = graphene.String()
    follow_status = graphene.String()

    def resolve_reference(self, info):
        return self['reference']

    def resolve_entity_type(self, info):
        return self['entity_type']

    def resolve_handle(self, info):
        return self['handle']

    def resolve_name(self, info):
        return self['name']

    def resolve_icon(self, info):
        return self['icon']

    def resolve_phonenumber(self, info):
        return self['phonenumber']

    def resolve_follow_status(self, info):
        return self['follow_status']


class CommentHeaderType(graphene.ObjectType):
    handle = graphene.String()
    reference = graphene.String()


class Neo4jCommentType(ContentType):
    reference = graphene.String(source='comment_reference')
    comment_text = graphene.String(source='comment_text')

    level = graphene.Int(source='level')
    comment_type = graphene.String(source='comment_type')
    rating_count = graphene.String(source='rating_count')
    created_by = graphene.Field(Neo4jEntityType)

    event_reference = graphene.String(source='event_reference')
    main_parent_id = graphene.String(source='main_parent_id')
    parent_comment_id = graphene.String(source='parent_comment_id')
    parent_handle = graphene.String()
    is_deleted = graphene.Boolean(source='is_deleted')

    comment_header_text = graphene.String()
    comment_headers = graphene.List(CommentHeaderType)
    comment_images = graphene.List(MediaItemType)

    def resolve_parent_handle(self, info):
        return dbqueries.get_content_creator_handle(self.comment_reference)

    def resolve_comment_headers(self,info):
        result = dbqueries.get_comment_headers(self.comment_reference)
        headers = [{'reference': res[1], 'handle': res[0]} for res in result]
        return headers

    def resolve_created_by(self,info):
        try:
            comment = Neo4jComment.nodes.get(reference=self.comment_reference)
            return comment.get_comment_creator()
        except Neo4jContent.DoesNotExist:
            raise Exception("Comment with the given reference does not exist.")

    def resolve_comment_header_text(self,info):
        return "Replied to"

    # def resolve_comment_type(self, info):
    #     try:
    #         comment = Neo4jContent.nodes.get(reference=self.comment_reference)
    #         return comment.comment_type if comment.comment_type else ""
    #     except Neo4jContent.DoesNotExist:
    #         raise Exception("Comment with the given reference does not exist.")

    # def resolve_like_status(self, info, visitor_reference=None):
    #     try:
    #         entity = Neo4jEntity.nodes.get(reference=visitor_reference)
    #         comment = Neo4jContent.nodes.get(reference=self.comment_reference)
    #
    #         return comment.liked_by.is_connected(entity)
    #     except Neo4jEntity.DoesNotExist:
    #         raise Exception("Entity with the given reference does not exist.")
    #
    # def resolve_repost_status(self, info, visitor_reference=None):
    #     try:
    #         entity = Neo4jEntity.nodes.get(reference=visitor_reference)
    #         comment = Neo4jContent.nodes.get(reference=self.comment_reference)
    #
    #         return entity.reposts.is_connected(comment)
    #     except Neo4jEntity.DoesNotExist:
    #         raise Exception("Entity with the given reference does not exist.")
    #
    # def resolve_save_status(self, info, visitor_reference=None):
    #     try:
    #         entity = Neo4jEntity.nodes.get(reference=visitor_reference)
    #         comment = Neo4jContent.nodes.get(reference=self.comment_reference)
    #
    #         return entity.saves.is_connected(comment)
    #     except Neo4jEntity.DoesNotExist:
    #         raise Exception("Entity with the given reference does not exist.")

    def resolve_comment_images(self, info):
        comment_images = CommentImages.objects.filter(comment_reference=self.comment_reference)
        if comment_images:
            return [
                MediaItemType(
                media_id=image.comment_image_id,
                media_path=image.comment_image,
                order=image.reorder
                ) for image in comment_images
            ]
        else:
            return []

    class Meta:
        model = Comments


class RefundResponsibilityItem(graphene.ObjectType):
    item_heading = graphene.String()
    item_text = graphene.String()
    item_subtext = graphene.String()


class Neo4jProductType(ContentType):
    productid = graphene.Int(source='productid')
    product_reference = graphene.String(source='product_reference')
    product_name = graphene.String(source='product_name')
    brand_name = graphene.String(source='brand_name')
    product_category = graphene.String(source='product_category')
    store_reference = graphene.String()
    store_id = graphene.Int(source='storeid')

    created_by = graphene.Field(Neo4jEntityType)

    active = graphene.String(source='active')
    product_description = graphene.String(source='product_description')
    promotion_link = graphene.String(source='promotion_link')
    hashtags = graphene.String(source='hashtags')
    in_stock = graphene.Int(source='in_stock')
    mrp_price = graphene.Int(source='mrp_price')
    selling_price = graphene.Int(source='selling_price')
    is_deleted = graphene.Boolean(source='deleted')

    modified_date = graphene.String()

    swadeshi_made = graphene.String(source='swadeshi_made')
    swadeshi_brand = graphene.String(source='swadeshi_brand')
    swadeshi_owned = graphene.String()
    targeted_gender = graphene.String(source='targeted_gender')

    orders_count = graphene.Int(source='orders_count')  # successfully delivered (-1 if returned after delivery)
    returns_count = graphene.Int(source='returns_count')  # returned after delivery (+1 if returned after delivery)
    cancels_count = graphene.Int(source='cancels_count')  # cancelled before delivery

    product_images = graphene.List(MediaItemType)
    config_receive_orders = graphene.Boolean()

    rating = graphene.Float(source='rating')
    count_of_ratings = graphene.Int(source='count_of_ratings')
    product_version = graphene.String(source='product_version')

    location = graphene.String()
    return_period = graphene.String()
    return_cost_on = graphene.String()
    return_conditions = graphene.List(graphene.String)
    return_pick_up = graphene.String()
    delivery_by = graphene.String()
    delivery_partner = graphene.String()
    delivery_fee = graphene.String()
    delivery_settings_type = graphene.String()
    logistic_partner_name = graphene.String()
    deliverability = graphene.Boolean(user_pincode=graphene.String())
    refund_responsibility = graphene.List(RefundResponsibilityItem)

    is_buy_enabled = graphene.Boolean(user_pincode=graphene.String())
    product_status_message = graphene.String(user_pincode=graphene.String())

    fulfillment_options = graphene.String()
    disclaimer_message = graphene.String()

    def resolve_disclaimer_message(self, info):
        if self.store_reference.store_supporters_count < 30 or self.store_reference.store_order_count < 5:
            return "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        else:
            return ""

    def resolve_fulfillment_options(self, info):
        product_delvery_settings = DeliverySettings.objects.filter(product_reference=self.product_reference,
                                                                   is_deleted=False).last()
        if product_delvery_settings:
            return product_delvery_settings.fulfillment_options if product_delvery_settings else ""
        else:
            store_delvery_settings = DeliverySettings.objects.filter(store_reference=self.store_reference,
                                                                     is_deleted=False).last()
            return store_delvery_settings.fulfillment_options if store_delvery_settings else ""

    def resolve_is_buy_enabled(self, info, user_pincode):
        checker = BuyStatusChecker(store=self.store_reference, product=self, user_pincode=user_pincode)
        return checker.is_buy_enabled()

    def resolve_product_status_message(self, info, user_pincode):
        checker = BuyStatusChecker(store=self.store_reference, product=self, user_pincode=user_pincode)
        return checker.get_status_message()

    # def resolve_is_buy_enabled(self, info, user_pincode):
    #     serializer = GetProductSerializer(context={"user_pincode": user_pincode})

    #     app_config_order_enabled = get_config().enable_app_orders
    #     store_config_orders_enabled = self.store_reference.store_configurations.get().enable_orders
    #     is_store_verified = self.store_reference.is_verification_completed
    #     is_store_active = self.store_reference.is_active
    #     is_store_open = self.store_reference.open_for_order
    #     deliverability = serializer.get_deliverability(obj=self)

    #     return all([app_config_order_enabled, store_config_orders_enabled, is_store_verified, is_store_active, is_store_open, deliverability])
    # def resolve_product_status_message(self, info, user_pincode):
    #     serializer = GetProductSerializer(context={"user_pincode": user_pincode})
        
    #     app_config_order_enabled = get_config().enable_app_orders
    #     store_config_orders_enabled = self.store_reference.store_configurations.get().enable_orders
    #     is_store_verified = self.store_reference.is_verification_completed
    #     is_store_active = self.store_reference.is_active
    #     is_store_open = self.store_reference.open_for_order
    #     deliverability = serializer.get_deliverability(obj=self)
    #     if not app_config_order_enabled:
    #         return "Ordering is currently disabled"
    #     elif not store_config_orders_enabled:
    #         return "Store is not accepting orders"
    #     elif not is_store_verified:
    #         return "Store is not verified yet"
    #     elif not is_store_active:
    #         return "Store is temporarily not active"
    #     elif not is_store_open:
    #         return "Store is currently closed for orders"
    #     elif not deliverability:
    #         return "Not deliverable to your pincode"
    #     else:
    #         return None
            
    def resolve_refund_responsibility(self, info):
        serializer = GetProductListSerializer()
        refund_responsibility = serializer.get_refund_responsibility(obj=self)
        # Convert the list to a JSON-serializable format
        return [RefundResponsibilityItem(
            item_heading=item['item_heading'],
            item_text=item['item_text'],
            item_subtext=item['item_subtext']
        ) for item in refund_responsibility]

    def resolve_deliverability(self, info, user_pincode):
        # Create a mock object to simulate the context for the get_deliverability method
        mock_obj = self

        # Set the context manually
        serializer = GetProductSerializer(context={"user_pincode": user_pincode})

        deliverability = serializer.get_deliverability(obj=mock_obj)
        return deliverability

    def resolve_logistic_partner_name(self, info):
        serializer = GetProductSerializer
        name = serializer.get_logistic_partner_name(self=serializer, obj=self)
        return name

    def resolve_delivery_settings_type(self, info):
        serializer = GetProductSerializer
        delivery_settings_type = serializer.get_delivery_settings_type(self=serializer, obj=self)
        return delivery_settings_type

    def resolve_delivery_fee(self, info):
        serializer = GetProductSerializer
        delivery_fee = serializer.get_delivery_fee(self=serializer, obj=self)
        return delivery_fee

    def resolve_delivery_partner(self, info):
        serializer = GetProductSerializer
        delivery_partner = serializer.get_delivery_partner(self=serializer, obj=self)
        return delivery_partner

    def resolve_delivery_by(self, info):
        serializer = GetProductSerializer
        delivery_by = serializer.get_delivery_by(self=serializer, obj=self)
        return delivery_by

    def resolve_return_pick_up(self, info):
        serializer = GetProductSerializer
        return_pickup = serializer.get_return_pick_up(self=serializer, obj=self)
        return return_pickup

    def resolve_return_conditions(self, info):
        serializer = GetProductSerializer
        return_conditions = serializer.get_return_conditions(self=serializer, obj=self)
        return return_conditions

    def resolve_return_cost_on(self, info):
        serializer = GetProductSerializer
        return_cost_on = serializer.get_return_cost_on(self=serializer, obj=self)
        return return_cost_on

    def resolve_return_period(self, info):
        serializer = GetProductSerializer
        return_period = serializer.get_return_period(self=serializer, obj=self)
        return return_period

    def resolve_location(self, info):
        serializer = GetProductSerializer
        location = serializer.get_location(self=serializer,obj=self)
        return location

    def resolve_config_receive_orders(self,info):
        return self.store_reference.store_configurations.get().enable_orders

    def resolve_store_reference(self, info):
        return self.store_reference.store_reference

    def resolve_swadeshi_owned(self, info):
        return self.store_reference.store_details.get().swadeshi_owned

    def resolve_created_by(self, info):
        try:
            product = Neo4jProduct.nodes.get(reference=self.product_reference)
            return product.get_product_creator()
        except Neo4jProduct.DoesNotExist:
            raise Exception("Product with the given reference does not exist.")

    def resolve_product_images(self, info):
        product_images = ProductImages.objects.filter(product_reference=self.product_reference, is_deleted=False)
        return [
            MediaItemType(
                media_id=str(image.productimageid),
                media_path=image.product_image.url,
                order=image.reorder
            ) for image in product_images
        ]

    def resolve_modified_date(self, info):
        date = self.modified_date
        if isinstance(date, datetime):
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d:%m:%Y %H:%M:%S")
        else:
            return datetime.strptime(date, "%Y-%m-%d %H:%M:%S").strftime("%d:%m:%Y %H:%M:%S") if date else None

    class Meta:
        model = Product


class GetParentContent(graphene.ObjectType):
    parent_post = graphene.Field(Neo4jPostType)
    parent_product = graphene.Field(Neo4jProductType)
    parent_comment = graphene.Field(Neo4jCommentType)


######Types for previews######
# we are using Neo4jpost type itself for post previews
class EntityPreviewType(graphene.ObjectType):
    reference = graphene.String(source='reference')
    entity_type = graphene.String()
    handle = graphene.String(source='handle')
    name = graphene.String(source='name')
    icon = graphene.String(source='icon')

    def resolve_entity_type(self, info):
        entity_type = "UNREGISTERED"
        if isinstance(self, Neo4jUser):
            entity_type = "USER"
        elif isinstance(self, Neo4jStore):
            entity_type = "STORE"
        elif isinstance(self, Neo4jUnrUser):
            entity_type = "UNREGISTERED"
        else:
            pass
        return entity_type

    class Meta:
        model = Neo4jEntity #Neo4j user, Neo4j Store, Neo4j Entity


class ProductPreviewType(graphene.ObjectType):
    productid = graphene.Int(source='productid')
    product_reference = graphene.String(source='product_reference')
    product_name = graphene.String(source='product_name')
    brand_name = graphene.String(source='brand_name')
    product_images = graphene.List(MediaItemType)

    created_by = graphene.Field(Neo4jEntityType)

    def resolve_created_by(self, info):
        try:
            product = Neo4jProduct.nodes.get(reference=self.product_reference)
            return product.get_product_creator()
        except Neo4jProduct.DoesNotExist:
            raise Exception("Product with the given reference does not exist.")

    def resolve_product_images(self, info):
        product_images = ProductImages.objects.filter(product_reference=self.product_reference, is_deleted=False)
        return [
            MediaItemType(
                media_id=str(image.productimageid),
                media_path=image.product_image.url,
                order=image.reorder
            ) for image in product_images
        ]

    class Meta:
        model = Product


class OrderPreviewType(graphene.ObjectType):
    order_number = graphene.String()
    suborder_number = graphene.String(required=False)

    user_reference = graphene.String()
    user_icon = graphene.String()
    user_name = graphene.String()

    store_reference = graphene.String()
    store_icon = graphene.String()
    store_name = graphene.String()

    product_preview_image = graphene.String()


