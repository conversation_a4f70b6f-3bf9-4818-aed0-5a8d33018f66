import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from decouple import config
from users.user_api.models import User
from users.user_api.utils import (
    generate_otp,
    send_otp_via_email
)
from .serializers import (
    EmailUserCheckSerializer,
    EmailSignInSerializer,
    EmailVerifySerializer,
    EmailResendOtpSerializer
)
from .utils import email_authentication_flow, mask_phonenumber
from ..user_api.utils import mask_email

# Get an instance of a logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class CheckUserInfoByEmail(APIView):
    """
    Check if a user exists with the given email and return basic info
    """
    permission_classes = [AllowAny]
    serializer_class = EmailUserCheckSerializer

    def get(self, request, email, *args, **kwargs):
        user = User.objects.filter(email=email, deleted=False).first()

        return Response(
            data={
                'user_exists': True if (user and user.is_email_verified) else False,
                'phonenumber': mask_phonenumber(user.phonenumber) if (user and user.phonenumber and user.is_phonenumber_verified) else None,
                'email': mask_email(user.email) if (user and user.email and user.is_email_verified) else None,
            }, status=200)


class EmailSignIn(APIView):
    """
    Initiate sign-in process with email (either OTP or Google Auth)
    """
    permission_classes = [AllowAny]
    serializer_class = EmailSignInSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data.get("email")
        google_access_token = serializer.validated_data.get("access_token")

        response = email_authentication_flow(
            action='initiate',
            email=email,
            google_access_token=google_access_token
        )
        return response


class VerifyEmailSignIn(APIView):
    """
    Verify email sign-in with OTP or Google Auth
    """
    permission_classes = [AllowAny]
    serializer_class = EmailVerifySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data.get("email")
        email_otp = serializer.validated_data.get("email_otp")
        google_access_token = serializer.validated_data.get("access_token")

        logger.info(f"Received data: email={email}, email_otp={email_otp}, access_token={'present' if google_access_token else 'not present'}")

        response = email_authentication_flow(
            action='validate',
            email=email,
            email_otp=email_otp,
            google_access_token=google_access_token
        )

        logger.info(f"Email Authentication Flow response: {response.data}")
        return response


class ResendEmailOtp(APIView):
    """
    Resend OTP to the user's email
    """
    permission_classes = [AllowAny]
    serializer_class = EmailResendOtpSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data.get("email")

        config_send_otp = config("SEND_OTP").lower() == 'true'  # Get the value of SEND_OTP from .env

        if config_send_otp:
            email_otp = generate_otp(identifier=email)
            send_otp_via_email(email_id=email, otp=email_otp)

        return Response({"message": "OTP sent successfully"}, status=200)
