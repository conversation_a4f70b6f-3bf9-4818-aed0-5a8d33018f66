# Generated by Django 3.2.13 on 2022-07-27 06:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0001_initial"),
        ("products", "0006_auto_20220727_1137"),
        ("stores", "0004_auto_20220727_1137"),
    ]

    operations = [
        migrations.AlterField(
            model_name="store",
            name="created_by",
            field=models.ForeignKey(
                db_column="created_by",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created",
                to="users.user",
            ),
        ),
        migrations.AlterField(
            model_name="store",
            name="modified_by",
            field=models.ForeignKey(
                db_column="modified_by",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="modified",
                to="users.user",
            ),
        ),
        migrations.DeleteModel(
            name="User",
        ),
        migrations.DeleteModel(
            name="UserStore",
        ),
        migrations.AddField(
            model_name="refundandwarranty",
            name="return_delivery_address_id",
            field=models.ForeignKey(
                db_column="return_delivery_address_id",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="stores.address",
            ),
        ),
        migrations.AddField(
            model_name="refundandwarranty",
            name="storeid",
            field=models.ForeignKey(
                db_column="storeid",
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.store",
            ),
        ),
    ]
