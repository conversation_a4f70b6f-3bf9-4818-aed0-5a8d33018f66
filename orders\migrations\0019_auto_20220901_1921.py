# Generated by Django 3.2.13 on 2022-09-01 13:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0018_auto_20220901_0714"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderlifecycle",
            name="cancelled_date",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="confirmation_date",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="delivered_date",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="delivery_person_contact",
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="delivery_person_name",
            field=models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="estimated_delivery_date",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="estimated_return_date",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="return_initiate_date",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="return_person_contact",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="return_person_name",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="return_tracking_link",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="returned_date",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="orderlifecycle",
            name="tracking_link",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
