# Generated by Django 3.2.13 on 2022-12-16 11:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0061_auto_20221215_1338"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderRating",
            fields=[
                (
                    "order_rating_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("order_id", models.CharField(blank=True, max_length=12, null=True)),
                (
                    "order_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "order_status",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "review_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("BUYER", "buyer review"),
                            ("SELLER", "seller review"),
                        ],
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "user_reference",
                    models.CharField(blank=True, max_length=12, null=True),
                ),
                ("order_rate", models.PositiveIntegerField(default=0)),
                ("easiness_level", models.PositiveIntegerField(default=0)),
                ("improvement_note", models.TextField()),
                ("like_note", models.TextField()),
                ("problems_facing", models.TextField()),
                ("experience_level", models.PositiveIntegerField(default=0)),
                ("experience_note", models.TextField()),
            ],
            options={
                "verbose_name_plural": "order rating",
                "db_table": '"order"."order_rating"',
            },
        ),
    ]
