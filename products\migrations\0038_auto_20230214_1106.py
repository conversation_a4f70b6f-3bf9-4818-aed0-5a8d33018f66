# Generated by Django 3.2.13 on 2023-02-14 05:36

from django.db import migrations


def add_user_reference_data_comment_table(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in user_reference column, where
    # userid of User model is same as Userid in comment table.

    Reply = apps.get_model("products", "Reply")
    User = apps.get_model("users", "User")
    for item in Reply.objects.all():
        user_id = item.userid.userid
        if User.objects.filter(userid=user_id).exists():
            user_instance = User.objects.get(userid=user_id)
            item.reference = user_instance.user_reference
        else:
            item.reference = None
        item.save(update_fields=["reference"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    Reply = apps.get_model("products", "Reply")
    for item in Reply.objects.all():
        item.reference = None
        item.save(update_fields=["reference"])

class Migration(migrations.Migration):

    dependencies = [
        ('products', '0037_auto_20230214_1104'),
    ]

    operations = [
        migrations.RunPython(add_user_reference_data_comment_table, reverse_func)
    ]
