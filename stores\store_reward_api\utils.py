from .models import *


def get_store_lifetime_infinity_savings(store_reference):
    all_infinity_credit_txns = StoreRewardsHistory.objects.filter(
        store_reference=store_reference,
        transaction_type='CREDIT',
        reward_category='INFINITY'
    ).values_list("reward_value", flat=True)
    # Convert reward values to floats
    list_of_all_infinity_credit_txns = [int(value) for value in all_infinity_credit_txns]

    # Calculate the total lifetime infinity balance
    total_lifetime_infinity_balance = sum(list_of_all_infinity_credit_txns)
    return total_lifetime_infinity_balance

def get_store_inprocess_infinity_points(store_reference):
    in_process_infinity_points = 10000
    # todo add real calculation logic in upcoming iterations
    return in_process_infinity_points


def get_store_awaiting_infinity_points(store_reference):
    awaiting_infinity_points = 10000
    # todo add real calculation logic in upcoming iterations
    return awaiting_infinity_points

