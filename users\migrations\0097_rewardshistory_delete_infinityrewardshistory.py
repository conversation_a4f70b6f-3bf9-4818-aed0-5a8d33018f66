# Generated by Django 4.2.7 on 2024-11-19 10:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0096_usersubscription"),
    ]

    operations = [
        migrations.CreateModel(
            name="RewardsHistory",
            fields=[
                ("reward_id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "reward_reference",
                    models.Char<PERSON>ield(blank=True, max_length=10, null=True, unique=True),
                ),
                (
                    "event_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("sent_value", models.CharField(blank=True, max_length=15, null=True)),
                (
                    "received_value",
                    models.Char<PERSON>ield(blank=True, max_length=15, null=True),
                ),
                (
                    "reward_ratio",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                (
                    "reward_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                            ("INVITE_CODE_REWARD", "Invite Code Reward"),
                            ("STORE_VERIFICATION", "Store Verification"),
                            ("U2U_TRANSFER", "U2U Transfer"),
                            ("S2U_TRANSFER", "S2U Transfer"),
                            ("AFFILIATE_REWARD", "Affiliate Reward"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "reward_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("SUCCESS", "Success"),
                            ("PENDING", "Pending"),
                            ("FAILED", "Failed"),
                            ("INVALID", "Invalid"),
                        ],
                        default="SUCCESS",
                        max_length=10,
                        null=True,
                    ),
                ),
                ("invite_code", models.CharField(blank=True, max_length=15, null=True)),
                (
                    "transaction_type",
                    models.CharField(
                        blank=True,
                        choices=[("CREDIT", "Credit"), ("DEBIT", "Debit")],
                        max_length=7,
                        null=True,
                    ),
                ),
                (
                    "sender_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "receiver_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "bank_transaction_id",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "bank_reference_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("bank_transaction_date", models.DateTimeField(blank=True, null=True)),
                (
                    "bank_transaction_status",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "rewards histories",
                "db_table": '"user"."rewards_history"',
            },
        ),
        migrations.DeleteModel(
            name="InfinityRewardsHistory",
        ),
    ]
