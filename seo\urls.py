from django.urls import path
from django.contrib.sitemaps.views import sitemap
from django.views.generic.base import TemplateView
from . import views
from .sitemaps import StoreSitemap, ProductSitemap, PostSitemap, StorySitemap
from .feeds import LatestPostsFeed, LatestStoriesFeed
from django.conf import settings

app_name = 'seo'

sitemaps = {
    'stores': StoreSitemap(),
    'products': ProductSitemap(),
    'posts': PostSitemap(),
    'stories': StorySitemap(),
}

urlpatterns = [
    # Existing URL patterns
    path('store/<str:store_reference>/', views.StoreHomeView.as_view(), name='store_home'),
    path('store/<str:store_reference>/products/', views.StoreProductsView.as_view(), name='store_products'),
    path('store/<str:store_reference>/posts/', views.StorePostsView.as_view(), name='store_posts'),
    path('product/<str:product_reference>/', views.ProductDetailView.as_view(), name='product_detail'),
    path('post/<str:post_reference>/', views.PostDetailView.as_view(), name='post_detail'),
    path('story/<str:story_reference>/', views.StoryDetailView.as_view(), name='story_detail'),

    # SEO-related URLs
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}, name='django.contrib.sitemaps.views.sitemap'),
    path('robots.txt', TemplateView.as_view(template_name='robots.txt', content_type='text/plain'), name='robots_txt'),
    path('feeds/posts/atom/', LatestPostsFeed(), name='posts_feed'),
    path('feeds/stories/atom/', LatestStoriesFeed(), name='stories_feed'),
]
