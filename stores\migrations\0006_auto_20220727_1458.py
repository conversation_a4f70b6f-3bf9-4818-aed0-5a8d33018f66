# Generated by Django 3.2.13 on 2022-07-27 09:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0005_auto_20220727_1137"),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                ("categoryid", models.AutoField(primary_key=True, serialize=False)),
                ("category_name", models.CharField(max_length=100, unique=True)),
                ("active", models.BooleanField(default=True)),
                ("created_by", models.CharField(max_length=100)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_by", models.Char<PERSON><PERSON>(max_length=100)),
                ("modified_date", models.DateField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "categories",
                "db_table": '"store"."category"',
            },
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="store",
            name="categoryid",
            field=models.ForeignKey(
                db_column="categoryid",
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.category",
            ),
        ),
    ]
