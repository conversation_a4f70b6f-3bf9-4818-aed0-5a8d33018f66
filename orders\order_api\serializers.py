import datetime

from rest_framework import serializers
from .models import (
    Order,
    SubOrder,
    OrderLifeCycle,
    ShippingHistory,
    OrderRating,
    RefundedAmount,
    LogisticPartner,
    OrderOtp,
)
from ..payment_api.models import SubOrderPaymentDetails
from ..payout_api.models import OrderPayout
from ..cart_api.models import CartItem
from django.db.models import Q
from django.db.models import Prefetch
import pytz
from users.user_api.models import User, UserAddress
from stores.store_settings_api.models import TrustCenter, RefundAndWarranty
from products.models import Product
from GraphDB.models import Neo4jEntity, Neo4jUser, Neo4jStore
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class OrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = "__all__"


class OrderItemsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubOrder
        fields = "__all__"


class StoreDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubOrder
        fields = [
            "storeid",
            "store_name",
            "store_image",
            "store_category",
            "store_description",
            "trust_score",
            "seller_level",
        ]


class RefundDetailsSerializer(serializers.ModelSerializer):
    refund_initiate_date = serializers.SerializerMethodField("get_refund_initiate_date")
    refunded_date = serializers.SerializerMethodField("get_refunded_date")
    class Meta:
        model = RefundedAmount
        fields = [
            "refunded_amount",
            "refund_status",
            "refund_hold_reason",
            "refund_initiate_date",
            "refunded_date",
        ]

    def get_refund_initiate_date(self, obj):
        if obj.refund_initiated_datetime:
            date = obj.refund_initiated_datetime
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d/%m/%Y %H:%M:%S")
        return None

    def get_refunded_date(self,obj):
        if obj.refunded_datetime:
            date = obj.refunded_datetime
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d/%m/%Y %H:%M:%S")
        return None


class OrderItemSellerSerializer(serializers.ModelSerializer):
    refund_details = RefundDetailsSerializer(many=True)
    product_latest_version = serializers.SerializerMethodField(
        "get_product_latest_version"
    )
    store_details = serializers.SerializerMethodField("get_store_details")
    payout_details = serializers.SerializerMethodField("get_payout_details")
    suborder_fee_details = serializers.SerializerMethodField("get_suborder_fee_details")
    return_days = serializers.SerializerMethodField("get_return_days")
    return_initiate_date = serializers.SerializerMethodField("get_return_initiate_date")

    class Meta:
        model = SubOrder
        fields = [
            "suborderid",
            "suborder_number",
            "package_number",
            "display_package_number",
            "product_reference",
            "productid",
            "product_name",
            "product_description",
            "product_brand",
            "product_image",
            "product_quantity",
            "mrp_price",
            "selling_price",
            "product_version",
            "product_latest_version",
            "suborder_status",
            "secondary_suborder_status",
            "cancelled_date",
            "cancelled_by",
            "cancellation_reason",
            "return_and_warranty_description",
            "confirmation_date",
            "delivered_date",
            "estimated_delivery_date",
            "self_delivery_by_store",
            "delivery_person_name",
            "delivery_person_contact",
            "delivery_by_logistic_partner",
            "delivery_by_swadesic",
            "logistic_partner",
            "tracking_number",
            "tracking_link",
            "additional_notes",
            "return_reason",
            "return_initiate_date",
            "estimated_pickup_date",
            "return_package_number",
            "display_return_package_number",
            "self_return_by_store",
            "return_person_name",
            "return_person_contact",
            "return_by_logistic_partner",
            "return_pickup_logistic_partner",
            "return_tracking_number",
            "return_estimated_pickup_date",
            "return_estimated_delivery_date",
            "return_tracking_link",
            "additional_return_notes",
            "returned_date",
            "return_confirmation_date",
            "return_days",
            "payout_details",
            "suborder_fee_details",
            "store_details",
            "refund_details",
            "return_conditions_json"
        ]

    def get_return_initiate_date(self, obj):
        if obj.return_initiate_date:
            date = obj.return_initiate_date
            if isinstance(date, str):
                try:
                    date = datetime.datetime.strptime(date, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    try:
                        date = datetime.datetime.strptime(date, "%Y-%m-%d %H:%M:%S.%f%z")
                    except ValueError:
                        return None
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d/%m/%Y %H:%M:%S")
        return None

    def get_return_days(self, obj):
        refund_warranty_id = obj.refund_warranty_id
        return_period = 0

        refund_warranty_instance = RefundAndWarranty.objects.get(
            refundandwarrantyid=refund_warranty_id
        )

        if refund_warranty_instance.return_type:
            return_period = refund_warranty_instance.return_period
        return return_period

    def get_suborder_fee_details(self, obj):
        if SubOrderPaymentDetails.objects.filter(
            suborder_number=obj.suborder_number
        ).exists():
            order_payment_details_instance = SubOrderPaymentDetails.objects.get(
                suborder_number=obj.suborder_number
            )
            out_data = {
                "product_price": order_payment_details_instance.product_amount,
                "product_level_delivery_fee": order_payment_details_instance.product_delivery_fee,
                "store_level_delivery_fee": order_payment_details_instance.store_delivery_fee,
            }
        else:
            out_data = None
        return out_data

    def get_payout_details(self, obj):
        if OrderPayout.objects.filter(suborder_number=obj.suborder_number).exists():
            payout_instance = OrderPayout.objects.get(
                suborder_number=obj.suborder_number
            )
            #payoutbug
            out_data = {
                "payout_status": payout_instance.payout_status,
                "payout_release_date": payout_instance.payout_release_date,
                "payout_amount": payout_instance.payout_amount,
            }
        else:
            out_data = None
        return out_data

    def get_product_latest_version(self, obj):
        product_instance = Product.objects.get(product_reference=obj.product_reference)
        product_version = product_instance.product_version
        return product_version

    def get_store_details(self, obj):
        store_reference = obj.store_reference
        instance = TrustCenter.objects.get(store_reference=store_reference)
        phone_number = instance.phonenumber.split("|")
        email_id = instance.emailid.split("|")
        contact_type = instance.primarycontacttype
        context = {
            "store_reference": obj.store_reference,
            "phone_number": phone_number,
            "email_id": email_id,
            "primary_contact_type": contact_type,
        }
        return context

    # def get_product_details(self,obj):
    #     context = {
    #         "product_reference":obj.product_reference,
    #         "productid": obj.productid,
    #         "product_name": obj.product_name,
    #         "product_description": obj.product_description,
    #         "product_brand": obj.product_brand,
    #         "product_image": obj.product_image.url,
    #         "product_quantity": obj.product_quantity,
    #         "mrp_price": obj.mrp_price,
    #         "selling_price": obj.selling_price,
    #         "product_version": obj.product_version,
    #     }
    #     return context
    # def get_order_return(self, obj):
    #     context = {"return_initiate_date": obj.return_initiate_date,
    #                "return_reason": obj.return_reason,
    #                "estimated_pickup_date": obj.estimated_pickup_date,
    #                "return_person_name": obj.return_person_name,
    #                "return_person_contact": obj.return_person_contact,
    #                "return_tracking_link": obj.return_tracking_link,
    #                "returned_date": obj.returned_date
    #                }
    #     return context
    #
    # def get_order_cancel(self, obj):
    #     context = {"cancelled_date": obj.cancelled_date,
    #                "cancelled_by": obj.cancelled_by,
    #                "cancellation_reason": obj.cancellation_reason
    #                }
    #     return context
    #
    # def get_order_delivery(self, obj):
    #     context = {"estimated_delivery_date": obj.estimated_delivery_date,
    #                "self_delivery_by_store": obj.self_delivery_by_store,
    #                "delivery_person_name": obj.delivery_person_name,
    #                "delivery_person_contact": obj.delivery_person_contact,
    #                "delivery_by_logistic_partner": obj.delivery_by_logistic_partner,
    #                "logistic_partner": obj.logistic_partner,
    #                "tracking_number": obj.tracking_number,
    #                "tracking_link": obj.tracking_link,
    #                "additional_notes": obj.additional_notes,
    #                "delivered_date": obj.delivered_date,
    #                }
    #     return context


class OrderItemBuyerSerializer(serializers.ModelSerializer):
    product_latest_version = serializers.SerializerMethodField(
        "get_product_latest_version"
    )
    store_details = serializers.SerializerMethodField("get_store_details")
    suborder_fee_details = serializers.SerializerMethodField("get_suborder_fee_details")
    return_days = serializers.SerializerMethodField("get_return_days")
    refund_details = RefundDetailsSerializer(many=True)
    return_initiate_date = serializers.SerializerMethodField("get_return_initiate_date")

    class Meta:
        model = SubOrder
        fields = [
            "suborderid",
            "suborder_number",
            "package_number",
            "display_package_number",
            "product_reference",
            "productid",
            "product_name",
            "product_description",
            "product_brand",
            "product_image",
            "product_quantity",
            "mrp_price",
            "selling_price",
            "product_version",
            "product_latest_version",
            "suborder_status",
            "secondary_suborder_status",
            "cancelled_date",
            "cancelled_by",
            "cancellation_reason",
            "return_and_warranty_description",
            "confirmation_date",
            "delivered_date",
            "estimated_delivery_date",
            "self_delivery_by_store",
            "delivery_person_name",
            "delivery_person_contact",
            "delivery_by_logistic_partner",
            "delivery_by_swadesic",
            "logistic_partner",
            "tracking_number",
            "tracking_link",
            "additional_notes",
            "return_reason",
            "return_initiate_date",
            "estimated_pickup_date",
            "return_package_number",
            "display_return_package_number",
            "self_return_by_store",
            "return_person_name",
            "return_person_contact",
            "return_by_logistic_partner",
            "return_pickup_logistic_partner",
            "return_tracking_number",
            "return_tracking_link",
            "return_estimated_pickup_date",
            "return_estimated_delivery_date",
            "additional_return_notes",
            "returned_date",
            "return_confirmation_date",
            "return_days",
            "suborder_fee_details",
            "store_details",
            "refund_details",
            "return_conditions_json"
        ]
    def get_return_initiate_date(self, obj):
        if obj.return_initiate_date:
            date = obj.return_initiate_date
            if isinstance(date, str):
                try:
                    date = datetime.datetime.strptime(date, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    try:
                        date = datetime.datetime.strptime(date, "%Y-%m-%d %H:%M:%S.%f%z")
                    except ValueError:
                        return None
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d/%m/%Y %H:%M:%S")
        return None



    def get_return_days(self, obj):
        refund_warranty_id = obj.refund_warranty_id
        return_period = 0

        refund_warranty_instance = RefundAndWarranty.objects.get(
            refundandwarrantyid=refund_warranty_id
        )

        if refund_warranty_instance.return_type:
            return_period = refund_warranty_instance.return_period

        return return_period

    def get_suborder_fee_details(self, obj):
        if SubOrderPaymentDetails.objects.filter(
            suborder_number=obj.suborder_number
        ).exists():
            order_payment_details_instance = SubOrderPaymentDetails.objects.get(
                suborder_number=obj.suborder_number
            )

            if SubOrderPaymentDetails.objects.filter(
                    order_number=obj.order_number,
                    suborder_number__isnull=True).exists():
                storedf_payment_details_instance = SubOrderPaymentDetails.objects.get(
                    order_number=obj.order_number, suborder_number__isnull=True
                )
                product_level_delivery_fee = storedf_payment_details_instance.product_delivery_fee

            out_data = {
                "product_price": order_payment_details_instance.product_amount,
                "product_level_delivery_fee": order_payment_details_instance.product_delivery_fee,
                "store_level_delivery_fee": order_payment_details_instance.store_delivery_fee,
            }
        else:
            out_data = None
        return out_data

    def get_product_latest_version(self, obj):
        product_instance = Product.objects.get(product_reference=obj.product_reference)
        product_version = product_instance.product_version
        return product_version

    def get_store_details(self, obj):
        store_reference = obj.store_reference
        instance = TrustCenter.objects.get(store_reference=store_reference)
        phone_number = instance.phonenumber.split("|")
        email_id = instance.emailid.split("|")
        contact_type = instance.primarycontacttype
        context = {
            "store_reference": obj.store_reference,
            "phone_number": phone_number,
            "email_id": email_id,
            "primary_contact_type": contact_type,
        }
        return context

    # def get_product_details(self,obj):
    #     context = {
    #         "product_reference":obj.product_reference,
    #         "productid": obj.productid,
    #         "product_name": obj.product_name,
    #         "product_description": obj.product_description,
    #         "product_brand": obj.product_brand,
    #         "product_image": obj.product_image.url,
    #         "product_quantity": obj.product_quantity,
    #         "mrp_price": obj.mrp_price,
    #         "selling_price": obj.selling_price,
    #         "product_version": obj.product_version,
    #     }
    #     return context
    # def get_order_return(self, obj):
    #     context = {"return_initiate_date": obj.return_initiate_date,
    #                "return_reason": obj.return_reason,
    #                "estimated_pickup_date": obj.estimated_pickup_date,
    #                "return_person_name": obj.return_person_name,
    #                "return_person_contact": obj.return_person_contact,
    #                "return_tracking_link": obj.return_tracking_link,
    #                "returned_date": obj.returned_date
    #                }
    #     return context
    #
    # def get_order_cancel(self, obj):
    #     context = {"cancelled_date": obj.cancelled_date,
    #                "cancelled_by": obj.cancelled_by,
    #                "cancellation_reason": obj.cancellation_reason
    #                }
    #     return context
    #
    # def get_order_delivery(self, obj):
    #     context = {"estimated_delivery_date": obj.estimated_delivery_date,
    #                "self_delivery_by_store": obj.self_delivery_by_store,
    #                "delivery_person_name": obj.delivery_person_name,
    #                "delivery_person_contact": obj.delivery_person_contact,
    #                "delivery_by_logistic_partner": obj.delivery_by_logistic_partner,
    #                "logistic_partner": obj.logistic_partner,
    #                "tracking_number": obj.tracking_number,
    #                "tracking_link": obj.tracking_link,
    #                "tracking_link": obj.tracking_link,
    #                "additional_notes": obj.additional_notes,
    #                "delivered_date": obj.delivered_date,
    #                }
    #     return context


class GetUserOrderItemsSerializer(serializers.ModelSerializer):
    order_items = serializers.SerializerMethodField("get_order_items_details")
    store_details = serializers.SerializerMethodField("get_store_details")
    ordered_date = serializers.SerializerMethodField("get_ordered_date")
    total_amount = serializers.SerializerMethodField("get_total_amount")
    subscription_type = serializers.CharField(source="user_reference.subscription_type")



    class Meta:
        model = Order
        fields = [
            "order_number",
            "order_request_number",
            "store_reference",
            "total_amount",
            "store_details",
            "order_status",
            "ordered_date",
            "order_items",
            "subscription_type"
        ]

    def get_total_amount(self, obj):
        return obj.total_order_amount

    # def get_user_details(self, obj):
    #     user = User.objects.get(user_reference=obj.user_reference)
    #     f_name = user.first_name
    #     l_name = user.last_name
    #     if f_name and l_name:
    #         full_name = " ".join ([f_name, l_name])
    #     elif f_name and l_name is None:
    #         full_name = f_name
    #     else:
    #         full_name = user.user_name
    #     buyer_contact_number = str(user.phonenumber)
    #     context = {
    #         "buyer_name": full_name,
    #         "buyer_contact": buyer_contact_number
    #     }
    #     return context

    def get_store_details(self, obj):
        suborder_items = SubOrder.objects.filter(order_number=obj.order_number).first()
        serializer = StoreDetailsSerializer(suborder_items)
        return serializer.data

    def get_order_items_details(self, obj):
        suborder_items = SubOrder.objects.filter(
            Q(order_number=obj.order_number)
            & (
                ~Q(suborder_status=SubOrder.Suborder_Status.PAYMENT_INITIATED)
                | Q(suborder_status=SubOrder.Suborder_Status.ORDER_INITIATED)
            )
        )
        serializer = OrderItemBuyerSerializer(suborder_items, many=True)
        return serializer.data

    def get_ordered_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")


class AllOrderItemsSellerSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubOrder
        fields = ["suborder_number", "suborder_status", "product_quantity"]

    # def get_payout_status(self, obj):
    #     payouts = obj.suborder_item.prefetch_related(
    #         Prefetch(
    #             "suborder_item",
    #             queryset=OrderPayout.objects.filter(store_reference=obj.store_reference),
    #             to_attr="related_payouts"
    #         )
    #     )
    #     payout_data = []
    #     for suborder in payouts:
    #         payout_data.extend (
    #             [
    #                 {"suborder_number": suborder.suborder_number, "payout_status": payout.payout_status}
    #                 for payout in suborder.related_payouts
    #             ]
    #         )
    #     return payout_data

    # @staticmethod
    # def get_payout_status(obj):
    #     payout_instance = getattr(obj, "payout_instance", None)
    #     if payout_instance:
    #         return payout_instance[0].payout_status
    #     return None
    #
    # def to_representation(self, instance):
    #     queryset = OrderPayout.objects.filter(suborder_number=instance.suborder_number)
    #     prefetch = Prefetch('suborder_item', queryset=queryset, to_attr='payout_instance')
    #     instance = SubOrder.objects.prefetch_related(prefetch).get(suborder_number=instance.suborder_number)
    #     return super().to_representation(instance)


class AllOrderSellerSerializer(serializers.ModelSerializer):

    orderitems = AllOrderItemsSellerSerializer(many=True)
    store_reference = serializers.CharField(source="store_reference.store_reference")
    store_icon = serializers.ImageField(source="store_reference.icon")
    store_name = serializers.CharField(source="store_reference.store_name")
    user_reference = serializers.CharField(source="user_reference.user_reference")
    user_icon = serializers.ImageField(source="user_reference.icon")
    customer_name = serializers.SerializerMethodField("get_customer_name")
    ordered_date = serializers.SerializerMethodField("get_ordered_date")
    total_amount = serializers.SerializerMethodField("get_total_amount")
    subscription_type = serializers.CharField(source="user_reference.subscription_type")  # Correct mapping


    class Meta:
        model = Order
        fields = [
            "order_number",
            "order_request_number",
            "store_reference",
            "store_icon",
            "store_name",
            "user_reference",
            "user_icon",
            "customer_name",
            "ordered_date",
            "total_amount",
            "orderitems",
            "subscription_type",
        ]

    def get_total_amount(self, obj):
        return obj.total_order_amount

    @staticmethod
    def get_customer_name(obj):
        return obj.user_reference.full_name() if obj.user_reference else None

    @staticmethod
    def get_ordered_date(obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    @staticmethod
    def setup_eager_loading(queryset):
        """Perform necessary eager loading of data."""
        queryset = queryset.prefetch_related("suborderitems")
        queryset = queryset.select_related("user_reference", "store_reference")
        return queryset


class GetstoreOrderItemsSerializer(serializers.ModelSerializer):

    orderitems = OrderItemSellerSerializer(many=True)
    customer_contact = serializers.CharField(source="user_reference.phonenumber")
    ordered_date = serializers.SerializerMethodField("get_ordered_date")
    user_icon = serializers.ImageField(source="user_reference.icon")
    customer_name = serializers.SerializerMethodField("get_customer_name")
    total_amount = serializers.SerializerMethodField("get_total_amount")
    subscription_type = serializers.CharField(source="user_reference.subscription_type")


    class Meta:
        model = Order
        fields = [
            "userid",
            "user_reference",
            "user_icon",
            "customer_name",
            "customer_contact",
            "order_number",
            "order_request_number",
            "total_amount",
            "ordered_date",
            "orderitems",
            "subscription_type"
        ]

    def get_total_amount(self, obj):
        return obj.total_order_amount

    @staticmethod
    def setup_eager_loading(queryset):
        """Perform necessary eager loading of data."""
        queryset = queryset.prefetch_related("orderitems")
        return queryset

    def get_customer_name(self, obj):
        return obj.user_reference.full_name() if obj.user_reference else None

    def get_ordered_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")


class GetCustomerDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = ["userid", "user_reference", "order_number"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        logger.info("Entered to_representation")
        user_id = instance.userid
        order_number = instance.order_number
        order_instance = Order.objects.get(order_number=order_number)
        user_billing_address_instance = UserAddress.objects.get(
            address_type="BILLING",
            useraddressid=order_instance.billing_address_id.useraddressid,
            userid=user_id,
        )
        if UserAddress.objects.filter(
            address_type="DELIVERY",
            useraddressid=order_instance.billing_address_id.useraddressid,
        ).exists():
            logger.info("separate billing and delivery address exists")
            user_delivery_address_instance = UserAddress.objects.get(
                address_type="DELIVERY",
                useraddressid=order_instance.billing_address_id.useraddressid,
            )
        else:
            logger.info("billing address taken as delivery address")
            user_delivery_address_instance = user_billing_address_instance
        user_instance = User.objects.get(userid=user_id)
        representation["user_name"] = user_instance.user_name
        if user_instance.icon:
            representation["icon"] = user_instance.icon.url
        else:
            representation["icon"] = None
        if self.context.get("store_reference"):
            try:
                neo4j_user = Neo4jEntity.nodes.get(reference=user_instance.user_reference)
                neo4j_store = Neo4jEntity.nodes.get(reference=self.context.get("store_reference"))
                if neo4j_store.is_following(neo4j_user):
                    x = "Following"
                elif neo4j_user.is_following(neo4j_store):
                    x = "Follow back"
                else:
                    x = "Follow"
                representation["follow_status"] = x
            except Exception as e:
                logger.error("Error while fetching GDB nodes for follow status")
                representation["follow_status"] = "Follow"
        representation["location"] = user_instance.user_location
        representation["seller_note"] = instance.seller_note
        representation["delivery_note"] = instance.delivery_note
        representation["delivered_person_name"] = user_delivery_address_instance.name
        representation["delivered_address"] = user_delivery_address_instance.address
        representation["delivered_city"] = user_delivery_address_instance.city
        representation["delivered_pincode"] = user_delivery_address_instance.pincode
        representation["delivered_state"] = user_delivery_address_instance.state
        representation["delivered_contact_number"] = order_instance.order_phone_number
        representation["billing_person_name"] = user_billing_address_instance.name
        representation["billing_address"] = user_billing_address_instance.address
        representation["billing_city"] = user_billing_address_instance.city
        representation["billing_pincode"] = user_billing_address_instance.pincode
        representation["billing_state"] = user_billing_address_instance.state
        logger.info("Exited to_representation")
        return representation


class GetProductDetailsOfOrderSerializer(serializers.ModelSerializer):
    status = serializers.CharField(source="suborder_status")
    secondary_status = serializers.CharField(source="secondary_suborder_status")
    product_price = serializers.CharField(source="selling_price")
    return_days = serializers.SerializerMethodField("get_return_days")

    class Meta:
        model = SubOrder
        fields = [
            "product_reference",
            "product_name",
            "product_image",
            "product_brand",
            "product_quantity",
            "product_version",
            "return_days",
            "product_price",
            "status",
            "secondary_status"
        ]

    def get_return_days(self, obj):
        refund_warranty_id = obj.refund_warranty_id
        return_period = 0

        refund_warranty_instance = RefundAndWarranty.objects.get(
            refundandwarrantyid=refund_warranty_id
        )

        if refund_warranty_instance.return_type:
            return_period = refund_warranty_instance.return_period

        return return_period


class GetOrderDetailsSerializer(serializers.ModelSerializer):

    products_details = serializers.SerializerMethodField("get_products_details")

    class Meta:
        model = SubOrderPaymentDetails
        fields = ["products_details"]

    def get_products_details(self, obj):
        suborder_number = obj.suborder_number.suborder_number
        # if SubOrder.new_objects.filter(suborder_number=suborder_number).exists():
        suborder_instance = SubOrder.objects.get(suborder_number=suborder_number)
        serializer = GetProductDetailsOfOrderSerializer(suborder_instance)
        serializer_copy = serializer.data
        serializer_copy["total_product_price"] = obj.product_amount
        serializer_copy["product_delivery_fee"] = obj.product_delivery_fee
        if obj.product_delivery_fee == 0:
            serializer_copy["store_level_fee"] = obj.store_delivery_fee
        else:
            serializer_copy["store_level_fee"] = 0
        return serializer_copy

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return representation["products_details"]


class OrderLifeCycleSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderLifeCycle
        fields = "__all__"


class DeliveryTrackingSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubOrder
        fields = [
            "self_delivery_by_store",
            "package_number",
            "delivery_person_name",
            "delivery_person_contact",
            "delivery_by_logistic_partner",
            "delivery_by_swadesic",
            "display_package_number",
            "logistic_partner",
            "tracking_number",
            "tracking_link",
            "additional_notes",
        ]


class ReturnTrackingSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubOrder
        fields = [
            "return_package_number",
            "display_return_package_number",
            "self_return_by_store",
            "return_person_name",
            "return_person_contact",
            "return_by_logistic_partner",
            "return_pickup_logistic_partner",
            "delivery_by_swadesic",
            "return_tracking_number",
            "return_estimated_pickup_date",
            "return_estimated_delivery_date",
            "return_tracking_link",
            "additional_return_notes",
            "return_conditions_json"
        ]


class ShippingUpdatesSerializer(serializers.ModelSerializer):
    date = serializers.SerializerMethodField("get_date")

    class Meta:
        model = ShippingHistory
        fields = [
            "shipping_update_id",
            "title",
            "description",
            "shipping_reference",
            "date",
            "order_number",
            "is_editable",
        ]

    def get_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    def get_title(self, obj):
        words = obj.title.split('_')
        formatted_title = ' '.join(word.capitalize() for word in words)
        return formatted_title

class OrderRatingSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderRating
        fields = [
            "order_reference",
            "review_type",
            "user_reference",
            "order_rate",
            "easiness_level",
            "improvement_note",
            "like_note",
            "problems_facing",
            "experience_level",
            "experience_note",
        ]


class SubOrderHistorySerializer(serializers.ModelSerializer):
    shipping_update_id = serializers.IntegerField(default=0)
    title = serializers.SerializerMethodField("get_title")
    description = serializers.SerializerMethodField("get_description")
    date = serializers.SerializerMethodField("get_date")
    order_numbers = serializers.SerializerMethodField("get_order_numbers")
    shipping_references = serializers.SerializerMethodField("get_shipping_references")
    is_editable = serializers.BooleanField(default=False)

    class Meta:
        fields = [
            "shipping_update_id",
            "title",
            "description",
            "shipping_references",
            "date",
            "order_numbers",
            "is_editable",
        ]
        model = OrderLifeCycle

    def get_title(self, obj):
        # Split the string by underscore
        words = obj.suborder_status.split('_')
        
        # Capitalize each word and join them with spaces
        formatted_title = ' '.join(word.capitalize() for word in words)
        
        return formatted_title

    def get_shipping_references(self, obj):
        return obj.package_number

    def get_order_numbers(self, obj):
        return obj.order_number

    def get_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    def get_description(self, obj):
        if obj.suborder_status == "ORDER_ESCALATED":
            return "Buyer has created an escalation for this order."
        else:
            return None


class Refunddetailsserializer(serializers.ModelSerializer):
    class Meta:
        model = RefundedAmount
        fields = [
            "suborder_status",
            "secondary_suborder_status",
            "return_cost_on",
            "is_shipped",
            "df_settings",
            "df_type",
            "product_price",
            "product_df",
            "df_reversal",
            "refunded_amount",
            "transaction_fee",
        ]


class LogisticPartnerSerializer(serializers.ModelSerializer):
    class Meta:
        model = LogisticPartner
        fields = "__all__"


class OrderOtpSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderOtp
        fields = "__all__"
