# Generated by Django 3.2.13 on 2022-07-31 04:31

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("orders", "0002_delete_order"),
    ]

    operations = [
        migrations.CreateModel(
            name="CartItem",
            fields=[
                ("cartitemid", models.AutoField(primary_key=True, serialize=False)),
                ("userid", models.CharField(max_length=5000)),
                ("productid", models.CharField(max_length=5000)),
                ("storeid", models.CharField(max_length=5000)),
                ("product_quantity", models.IntegerField(default=0)),
                ("cart_product_status", models.Char<PERSON>ield(max_length=500)),
                ("created_by", models.CharField(blank=True, max_length=500, null=True)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "modified_by",
                    models.Char<PERSON><PERSON>(blank=True, max_length=500, null=True),
                ),
                ("modified_date", models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "cart items",
                "db_table": '"order"."cart_item"',
            },
        ),
        migrations.CreateModel(
            name="Order",
            fields=[
                ("orderid", models.AutoField(primary_key=True, serialize=False)),
                ("order_number", models.CharField(max_length=50)),
                ("userid", models.CharField(blank=True, max_length=5000, null=True)),
                ("storeid", models.CharField(blank=True, max_length=5000, null=True)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now=True)),
                ("date", models.DateField(default=datetime.date.today)),
                ("order_status", models.CharField(default="initiated", max_length=200)),
                ("is_deleted", models.BooleanField(default=False)),
                (
                    "cancelled_by",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "cancellation_reason",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "delivery_note",
                    models.CharField(blank=True, max_length=5000, null=True),
                ),
                ("order_request_number", models.CharField(max_length=500, null=True)),
                ("store_total", models.IntegerField(default=0, null=True)),
                ("delivery_fee", models.IntegerField(default=0, null=True)),
                ("total_amount", models.IntegerField(default=0, null=True)),
            ],
            options={
                "verbose_name_plural": "order",
                "db_table": '"order"."order"',
            },
        ),
        migrations.CreateModel(
            name="SubOrder",
            fields=[
                ("suborderid", models.AutoField(primary_key=True, serialize=False)),
                ("order_number", models.CharField(max_length=200)),
                ("suborder_number", models.CharField(max_length=50)),
                (
                    "package_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("storeid", models.CharField(blank=True, max_length=5000, null=True)),
                ("store_name", models.CharField(blank=True, max_length=500, null=True)),
                (
                    "store_category",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "store_description",
                    models.CharField(blank=True, max_length=5000, null=True),
                ),
                (
                    "trust_score",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "seller_level",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "return_and_warranty_description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("productid", models.CharField(blank=True, max_length=5000, null=True)),
                (
                    "product_name",
                    models.CharField(blank=True, max_length=5000, null=True),
                ),
                (
                    "product_description",
                    models.CharField(blank=True, max_length=5000, null=True),
                ),
                (
                    "product_brand",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "product_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="product_image_cart"
                    ),
                ),
                ("product_quantity", models.PositiveIntegerField()),
                ("mrp_price", models.PositiveIntegerField()),
                ("selling_price", models.PositiveIntegerField()),
                (
                    "suborder_status",
                    models.CharField(default="initiated", max_length=200),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                (
                    "cancelled_by",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "cancellation_reason",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "seller_note",
                    models.CharField(blank=True, max_length=5000, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now=True)),
                (
                    "orderid",
                    models.ForeignKey(
                        db_column="orderid",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="orderitems",
                        to="orders.order",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "sub orders",
                "db_table": '"order"."sub_order"',
            },
        ),
        migrations.CreateModel(
            name="OrderLifeCycle",
            fields=[
                (
                    "orderlifecycleid",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "created_by",
                    models.CharField(blank=True, max_length=5000, null=True),
                ),
                (
                    "suborder_status",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("order_updated_date", models.DateTimeField(auto_now_add=True)),
                ("suborder_number", models.CharField(max_length=100, null=True)),
                ("order_number", models.CharField(max_length=100, null=True)),
                (
                    "orderid",
                    models.ForeignKey(
                        db_column="orderid",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="orders.order",
                    ),
                ),
                (
                    "suborderid",
                    models.ForeignKey(
                        db_column="suborderid",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="orders.suborder",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "order life cycle ",
                "db_table": '"order"."order_life_cycle"',
            },
        ),
    ]
