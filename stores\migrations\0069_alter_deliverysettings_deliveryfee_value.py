# Generated by Django 4.2.7 on 2024-09-05 06:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0068_store_gst_verified_time_store_pan_verified_time_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="deliverysettings",
            name="deliveryfee_value",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.RunSQL(
            sql="UPDATE store.delivery_settings SET deliveryfee_value = 0 WHERE deliveryfee_value IS NULL;",
            reverse_sql="UPDATE store.delivery_settings SET deliveryfee_value = NULL WHERE deliveryfee_value = 0;",
        ),
    ]
