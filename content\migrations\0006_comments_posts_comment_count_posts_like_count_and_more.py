# Generated by Django 4.2.7 on 2024-03-12 07:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("content", "0005_alter_postimages_post_image_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Comments",
            fields=[
                ("comment_id", models.AutoField(primary_key=True, serialize=False)),
                ("comment_reference", models.Char<PERSON>ield(max_length=20, unique=True)),
                ("comment_text", models.TextField(blank=True, null=True)),
                ("is_deleted", models.BooleanField(default=False)),
                (
                    "main_parent_id",
                    models.CharField(blank=True, max_length=25, null=True),
                ),
                (
                    "parent_comment_id",
                    models.CharField(blank=True, max_length=25, null=True),
                ),
                ("level", models.IntegerField()),
                ("like_count", models.IntegerField(blank=True, null=True)),
                ("comment_count", models.IntegerField(blank=True, null=True)),
                ("repost_count", models.IntegerField(blank=True, null=True)),
                ("repost_plus_count", models.IntegerField(blank=True, null=True)),
                ("save_count", models.IntegerField(blank=True, null=True)),
                ("share_count", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "verbose_name_plural": "comments",
                "db_table": '"content"."comments"',
            },
        ),
        migrations.AddField(
            model_name="posts",
            name="comment_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="posts",
            name="like_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="posts",
            name="quote_parent_id",
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
        migrations.AddField(
            model_name="posts",
            name="repost_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="posts",
            name="repost_plus_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="posts",
            name="save_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="posts",
            name="share_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="CommentImages",
            fields=[
                (
                    "comment_image_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "comment_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="comment_images"
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("reorder", models.IntegerField(blank=True, null=True)),
                (
                    "comment_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="comment_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comment_images",
                        to="content.comments",
                        to_field="comment_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "comment images",
                "db_table": '"content"."comment_images"',
            },
        ),
    ]
