from django.urls import path
from .views import (
    LeanStoreProductsView,
    LeanProductFullDetailsView,
    LeanProductPartialDetailsView,
    LeanPaginatedStoreProductsView,
    LeanPaginatedStorePartialProductsView,
    LeanStoreProductsNewView,
    LeanFeedAPIView,
    LeanAllFeedAPIView,
    StoreProductsFullNew,
    GetUserOrStorePosts,
    GetRecommendedProducts,
    GetStoreComments
)
from .search_api import LeanSearchView, LeanSearchViewNew

app_name = 'lean_apis'

urlpatterns = [
    path('store/<str:store_reference>/products/',LeanStoreProductsView.as_view(),name='lean-store-products'),
    path('store/<str:store_reference>/products/new/',LeanStoreProductsNewView.as_view(),name='lean-store-products'),
    path('store/<str:store_reference>/products_full/',StoreProductsFullNew.as_view(),name='lean-store-products-paginated'),
    path('store/<str:store_reference>/products_partial/',LeanPaginatedStorePartialProductsView.as_view(),name='lean-store-products-paginated'),
    path('products/full_details/',LeanProductFullDetailsView.as_view(),name='lean-product-full-details'),
    path('products/partial_details/',LeanProductPartialDetailsView.as_view(),name='lean-product-partial-details'),
    path('search/',LeanSearchViewNew.as_view(),name='lean-search'),
    path('feed/',LeanFeedAPIView.as_view(),name='lean-feed'),
    path('all_feed/',LeanAllFeedAPIView.as_view(),name='lean-all-feed'),
    path('get_user_or_store_posts/', GetUserOrStorePosts.as_view(), name='get_user_or_store_posts'),
    path('recommended_products/', GetRecommendedProducts.as_view(), name='recommended-products'),
    path('get_store_comments/', GetStoreComments.as_view(), name='get-store-comments')
]