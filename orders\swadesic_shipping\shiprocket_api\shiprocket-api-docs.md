# Ship Rocket API Documentation

## 1. Check Courier Serviceability

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/courier/serviceability/?pickup_postcode=524004&delivery_postcode=500032&cod=0&weight=2' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response:

```json
{
    "company_auto_shipment_insurance_setting": false,
    "covid_zones": {
        "delivery_zone": null,
        "pickup_zone": null
    },
    "currency": "INR",
    "data": {
        "available_courier_companies": [
            {
                "air_max_weight": "0.00",
                "assured_amount": 0,
                "base_courier_id": null,
                "base_weight": "",
                "blocked": 0,
                "call_before_delivery": "Not Available",
                "charge_weight": 2,
                "city": "Hyderabad",
                "cod": 1,
                "cod_charges": 0,
                "cod_multiplier": 0,
                "cost": "",
                "courier_company_id": 32,
                "courier_name": "Amazon Shipping Surface 2kg",
                "courier_type": "0",
                "coverage_charges": 0,
                "cutoff_time": "09:00",
                "delivery_boy_contact": "Not Available",
                "delivery_performance": 3,
                "description": "",
                "edd": "",
                "entry_tax": 0,
                "estimated_delivery_days": "5",
                "etd": "Mar 25, 2025",
                "etd_hours": 113,
                "freight_charge": 162.92,
                "id": *********,
                "is_custom_rate": 0,
                "is_hyperlocal": false,
                "is_international": 0,
                "is_rto_address_available": false,
                "is_surface": true,
                "local_region": 0,
                "metro": 1,
                "min_weight": 2,
                "mode": 0,
                "new_edd": 1,
                "odablock": false,
                "other_charges": 0,
                "others": "{\"auto_pickup\":1,\"cancel_real_time\":true,\"is_cancellation_courier\":1,\"is_custom_courier\":1,\"is_edd_courier\":1,\"is_notify_cancel_courier\":1,\"is_webhook_courier\":1,\"min_breadth\":1,\"min_length\":1,\"wec\":1}",
                "pickup_availability": "0",
                "pickup_performance": 3.8,
                "pickup_priority": "",
                "pickup_supress_hours": 0,
                "pod_available": "On Request",
                "postcode": "500032",
                "qc_courier": 0,
                "rank": "",
                "rate": 162.92,
                "rating": 2.8,
                "realtime_tracking": "Real Time",
                "region": 2,
                "rto_charges": 162.92,
                "rto_performance": 1,
                "seconds_left_for_pickup": -28718,
                "secure_shipment_disabled": false,
                "ship_type": 1,
                "state": "TELANGANA",
                "suppress_date": "Mar 21, 2025",
                "suppress_text": "",
                "suppression_dates": {
                    "action_on": "2025-02-05 17:23:34",
                    "blocked_fm": "",
                    "blocked_lm": ""
                },
                "surface_max_weight": "4.00",
                "tracking_performance": 5,
                "volumetric_max_weight": null,
                "weight_cases": 4.6,
                "zone": "z_d"
            },
            {
                "air_max_weight": "0.00",
                "assured_amount": 0,
                "base_courier_id": null,
                "base_weight": "",
                "blocked": 0,
                "call_before_delivery": "Not Available",
                "charge_weight": 2,
                "city": "Hyderabad",
                "cod": 1,
                "cod_charges": 0,
                "cod_multiplier": 0,
                "cost": "",
                "courier_company_id": 24,
                "courier_name": "Xpressbees Surface 2kg",
                "courier_type": "0",
                "coverage_charges": 0,
                "cutoff_time": "11:00",
                "delivery_boy_contact": "Not Available",
                "delivery_performance": 4.8,
                "description": "",
                "edd": "",
                "entry_tax": 0,
                "estimated_delivery_days": "6",
                "etd": "Mar 26, 2025",
                "etd_hours": 131,
                "freight_charge": 160,
                "id": *********,
                "is_custom_rate": 0,
                "is_hyperlocal": false,
                "is_international": 0,
                "is_rto_address_available": true,
                "is_surface": true,
                "local_region": 0,
                "metro": 1,
                "min_weight": 2,
                "mode": 0,
                "new_edd": 0,
                "odablock": false,
                "other_charges": 0,
                "others": "{\"allow_postcode_auto_sync\":1,\"cancel_real_time\":true,\"courier_available_for_payment_change\":1,\"fbs_amazon_Standard\":1,\"international_enabled\":1,\"is_cancellation_courier\":1,\"is_custom_courier\":0,\"is_edd_courier\":1,\"is_eway_bill_courier\":1,\"is_manual_courier\":1,\"is_manual_prefecth_courier\":1,\"is_notify_cancel_courier\":1,\"is_webhook_courier\":1,\"min_breadth\":1,\"min_length\":1,\"prefetch_awb\":1,\"prefetch_enabled\":0,\"qr_pickrr_enable\":1,\"wec\":1}",
                "pickup_availability": "0",
                "pickup_performance": 4.7,
                "pickup_priority": "",
                "pickup_supress_hours": 0,
                "pod_available": "Instant",
                "postcode": "500032",
                "qc_courier": 0,
                "rank": "",
                "rate": 160,
                "rating": 2.8,
                "realtime_tracking": "Real Time",
                "region": 2,
                "rto_charges": 128,
                "rto_performance": 1,
                "seconds_left_for_pickup": -21518,
                "secure_shipment_disabled": false,
                "ship_type": 1,
                "state": "TELANGANA",
                "suppress_date": "Mar 21, 2025",
                "suppress_text": "",
                "suppression_dates": {
                    "action_on": "2025-03-18 18:16:23",
                    "blocked_fm": "",
                    "blocked_lm": ""
                },
                "surface_max_weight": "4.00",
                "tracking_performance": 4.2,
                "volumetric_max_weight": null,
                "weight_cases": 4.6,
                "zone": "z_d"
            }
        ],
        "child_courier_id": null,
        "is_recommendation_enabled": 1,
        "promise_recommended_courier_company_id": null,
        "recommendation_advance_rule": 0,
        "recommendation_level": "default",
        "recommended_by": {
            "id": 6,
            "title": "Recommendation By Shiprocket"
        },
        "recommended_courier_company_id": 32,
        "shiprocket_recommended_courier_id": 32
    },
    "dg_courier": 0,
    "eligible_for_insurance": 0,
    "insurace_opted_at_order_creation": false,
    "is_allow_templatized_pricing": true,
    "is_latlong": 0,
    "is_old_zone_opted": false,
    "is_zone_from_mongo": false,
    "label_generate_type": 0,
    "on_new_zone": 0,
    "seller_address": [],
    "status": 200,
    "user_insurance_manadatory": false
}
```

courier name
courier company id 
pickup time
delivery cost
estimated_delivery_days
cutoff_time
pickup_availability
etd
realtime_tracking
pickup_performance, rto_performance, tracking_performance
delivery boy contact
rto_charges
call_before_delivery


## 2. Create Order API

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/orders/create/adhoc' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data-raw '{
  "order_id": "224-447",
  "order_date": "2025-03-20 11:11",
  "pickup_location": "Home-1",
  "channel_id": "",
  "comment": "Reseller: M/s Goku",
  "billing_customer_name": "Repto",
  "billing_last_name": "",
  "billing_address": "235, new indiramma colony,",
  "billing_address_2": "Water tank",
  "billing_city": "Nellore",
  "billing_pincode": "524004",
  "billing_state": "Andhra Pradesh",
  "billing_country": "India",
  "billing_email": "<EMAIL>",
  "billing_phone": "**********",
  "shipping_is_billing": true,
  "shipping_customer_name": "",
  "shipping_last_name": "",
  "shipping_address": "",
  "shipping_address_2": "",
  "shipping_city": "",
  "shipping_pincode": "",
  "shipping_country": "",
  "shipping_state": "",
  "shipping_email": "",
  "shipping_phone": "",
  "order_items": [
    {
      "name": "Kunai",
      "sku": "chakra123",
      "units": 10,
      "selling_price": "900",
      "discount": "",
      "tax": "",
      "hsn": 441122
    }
  ],
  "payment_method": "Prepaid",
  "shipping_charges": 0,
  "giftwrap_charges": 0,
  "transaction_charges": 0,
  "total_discount": 0,
  "sub_total": 9000,
  "length": 10,
  "breadth": 15,
  "height": 20,
  "weight": 2.5
}'
```

### Response:

```json
{
    "order_id": *********,
    "channel_order_id": "224-447",
    "shipment_id": *********,
    "status": "NEW",
    "status_code": 1,
    "onboarding_completed_now": 0,
    "awb_code": "",
    "courier_company_id": "",
    "courier_name": "",
    "new_channel": false,
    "packaging_box_error": ""
}
```

## 3. Assign AWB for the Shipment

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/courier/assign/awb' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
  "shipment_id": "*********",
  "courier_id": "32"
}'
```

### Response:

```json
{
    "awb_assign_status": 1,
    "response": {
        "data": {
            "courier_company_id": 32,
            "awb_code": "348452859457",
            "cod": 0,
            "order_id": *********,
            "shipment_id": *********,
            "awb_code_status": 1,
            "assigned_date_time": {
                "date": "2025-03-20 17:33:02.000000",
                "timezone_type": 3,
                "timezone": "Asia/Kolkata"
            },
            "applied_weight": 2.5,
            "company_id": 3429769,
            "courier_name": "Amazon Shipping Surface 2kg",
            "child_courier_name": null,
            "freight_charges": 126.96,
            "pickup_scheduled_date": "2025-03-21 11:00:00",
            "routing_code": "",
            "rto_routing_code": null,
            "invoice_no": "Retail00002",
            "transporter_id": "",
            "transporter_name": "",
            "shipped_by": {
                "shipper_company_name": "Repto",
                "shipper_address_1": "235, new indiramma colony,",
                "shipper_address_2": "Water tank",
                "shipper_city": "Nellore",
                "shipper_state": "Andhra Pradesh",
                "shipper_country": "India",
                "shipper_postcode": "524004",
                "shipper_first_mile_activated": 0,
                "shipper_phone": "**********",
                "lat": "",
                "long": "",
                "shipper_email": "<EMAIL>",
                "extra_info": {
                    "source": 1
                },
                "rto_company_name": "Repto",
                "rto_address_1": "235, new indiramma colony,",
                "rto_address_2": "Water tank",
                "rto_city": "Nellore",
                "rto_state": "Andhra Pradesh",
                "rto_country": "India",
                "rto_postcode": "524004",
                "rto_phone": "**********",
                "rto_email": "<EMAIL>"
            }
        }
    },
    "no_pickup_popup": 0,
    "quick_pick": 0
}
```

## 4. Schedule Pickup

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/courier/generate/pickup' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "shipment_id": [*********],
    "pickup_date": ["2025-03-21"]
}'
```

### Response:

```json
{
  "pickup_status": 1,
  "response": {
    "pickup_scheduled_date": "2021-12-10 12:39:54",
    "pickup_token_number": "Reference No: 194_BIGFOOT 1966840_11122021",
    "status": 3,
    "others": "{\"tier_id\":5,\"etd_zone\":\"z_e\",\"etd_hours\":\"{\\\"assign_to_pick\\\":6.****************,\\\"pick_to_ship\\\":22.***************,\\\"ship_to_deliver\\\":151.**************,\\\"etd_zone\\\":\\\"z_e\\\",\\\"pick_to_ship_table\\\":\\\"dev_etd_pickup_to_ship\\\",\\\"ship_to_deliver_table\\\":\\\"dev_etd_ship_to_deliver\\\"}\",\"actual_etd\":\"2021-12-18 00:36:03\",\"routing_code\":\"S2\\/S-69\\/1B\\/016\",\"addition_in_etd\":[\"deduction_of_6_and_half_hours\"],\"shipment_metadata\":{\"type\":\"ship\",\"device\":\"WebKit\",\"platform\":\"desktop\",\"client_ip\":\"*************\",\"created_at\":\"2021-12-10 12:36:03\",\"request_type\":\"web\"},\"templatized_pricing\":0,\"selected_courier_type\":\"Best in price\",\"recommended_courier_data\":{\"etd\":\"Dec 19, 2021\",\"price\":153,\"rating\":3.6,\"courier_id\":54},\"recommendation_advance_rule\":null,\"dynamic_weight\":\"1.00\"}",
    "pickup_generated_date": {
      "date": "2021-12-10 12:39:54.034695",
      "timezone_type": 3,
      "timezone": "Asia/Kolkata"
    },
    "data": "Pickup is confirmed by Xpressbees 1kg For AWB :- ***************"
  }
}
```

## 5. Generate Manifest

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/manifests/generate' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "shipment_id": [*********]
}'
```

### Response:

```json
{
    "status": 1,
    "manifest_url": "https://s3-ap-south-1.amazonaws.com/kr-shipmultichannel-mum/3429769/manifest/MANIFEST-0002.pdf",
    "error": []
}
```

## 6. Print Manifest

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/manifests/print' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "order_ids": [*********]
}'
```

### Response:

```json
{
    "manifest_url": "https://s3-ap-south-1.amazonaws.com/kr-shipmultichannel-mum/manifest/c_3429769/print_manifests/6953563_amazon-shipping-surface-2kg__Vy16n1742477366.pdf"
}
```

## 7. Generate Label

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/courier/generate/label' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "shipment_id": [*********]
}'
```

### Response:

```json
{
    "label_created": 1,
    "label_url": "https://kr-shipmultichannel-mum.s3.ap-south-1.amazonaws.com/5641520/labels/712410731final_labels_amazon.pdf",
    "response": "Label has been created and uploaded successfully!",
    "not_created": []
}
```

## 8. Generate Invoice

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/orders/print/invoice' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "ids": [*********]
}'
```

### Response:

```json
{
    "is_invoice_created": true,
    "invoice_url": "https://s3-ap-south-1.amazonaws.com/kr-shipmultichannel-mum/3429769/invoices/Retail0000351acda34-f502-485f-9643-c4767ace5714.pdf",
    "not_created": [],
    "irn_no": ""
}
```

## 9. Get Tracking API

### a. Through AWB

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/courier/track/awb/348453552036' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response:

```json
{
    "tracking_data": {
        "track_status": 1,
        "shipment_status": 3,
        "shipment_track": [
            {
                "id": *********,
                "awb_code": "348453552036",
                "courier_company_id": 32,
                "shipment_id": *********,
                "order_id": *********,
                "pickup_date": "",
                "delivered_date": "",
                "weight": "2.00",
                "packages": 1,
                "current_status": "Pickup Generated",
                "delivered_to": "Nellore",
                "destination": "Nellore",
                "consignee_name": "Repto",
                "origin": "Nellore",
                "courier_agent_details": null,
                "courier_name": "Amazon Shipping Surface 2kg",
                "edd": "2025-03-22 23:59:00",
                "pod": "Not Available",
                "pod_status": "",
                "rto_delivered_date": "NA",
                "return_awb_code": "",
                "updated_time_stamp": "2025-03-20 18:39:48"
            }
        ],
        "shipment_track_activities": [
            {
                "date": "2025-03-20 18:39:48",
                "status": "ReadyForReceive",
                "activity": "ReadyForReceive",
                "location": "NA",
                "sr-status": "NA",
                "sr-status-label": "NA"
            }
        ],
        "track_url": "https://shiprocket.co/tracking/348453552036",
        "etd": "2025-03-22 23:59:00",
        "qc_response": "",
        "is_return": false
    }
}
```

### b. Through Order_id

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/courier/track?order_id=224-448' \
--header 'Authorization: Bearer <token>'
```

### Response:

```json
[
    {
        "tracking_data": {
            "track_status": 1,
            "shipment_status": 3,
            "shipment_track": [
                {
                    "id": *********,
                    "awb_code": "348453552036",
                    "courier_company_id": 32,
                    "shipment_id": *********,
                    "order_id": *********,
                    "pickup_date": "",
                    "delivered_date": "",
                    "weight": "2.00",
                    "packages": 1,
                    "current_status": "Pickup Generated",
                    "delivered_to": "Nellore",
                    "destination": "Nellore",
                    "consignee_name": "Repto",
                    "origin": "Nellore",
                    "courier_agent_details": null,
                    "courier_name": "Amazon Shipping Surface 2kg",
                    "edd": "2025-03-22 23:59:00",
                    "pod": "Not Available",
                    "pod_status": "",
                    "rto_delivered_date": "NA",
                    "return_awb_code": "",
                    "updated_time_stamp": "2025-03-20 18:39:48"
                }
            ],
            "shipment_track_activities": [
                {
                    "date": "2025-03-20 18:39:48",
                    "status": "ReadyForReceive",
                    "activity": "ReadyForReceive",
                    "location": "NA",
                    "sr-status": "NA",
                    "sr-status-label": "NA"
                }
            ],
            "track_url": "https://shiprocket.co/tracking/348453552036",
            "etd": "2025-03-22 23:59:00",
            "qc_response": "",
            "is_return": false
        }
    }
]
```

### c. Through Shipment_id

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/courier/track/shipment/*********' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response:

```json
{
    "tracking_data": {
        "track_status": 1,
        "shipment_status": 3,
        "shipment_track": [
            {
                "id": *********,
                "awb_code": "348453552036",
                "courier_company_id": 32,
                "shipment_id": *********,
                "order_id": *********,
                "pickup_date": "",
                "delivered_date": "",
                "weight": "2.00",
                "packages": 1,
                "current_status": "Pickup Generated",
                "delivered_to": "Nellore",
                "destination": "Nellore",
                "consignee_name": "Repto",
                "origin": "Nellore",
                "courier_agent_details": null,
                "courier_name": "Amazon Shipping Surface 2kg",
                "edd": "2025-03-22 23:59:00",
                "pod": "Not Available",
                "pod_status": "",
                "rto_delivered_date": "NA",
                "return_awb_code": "",
                "updated_time_stamp": "2025-03-20 18:39:48"
            }
        ],
        "shipment_track_activities": [
            {
                "date": "2025-03-20 18:39:48",
                "status": "ReadyForReceive",
                "activity": "ReadyForReceive",
                "location": "NA",
                "sr-status": "NA",
                "sr-status-label": "NA"
            }
        ],
        "track_url": "https://shiprocket.co/tracking/348453552036",
        "etd": "2025-03-22 23:59:00",
        "qc_response": "",
        "is_return": false
    }
}
```

## 10. Add Pickup Location

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/settings/company/addpickup' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data-raw '{
    "pickup_location": "Home-3",
    "name": "Ragnar",
    "email": "<EMAIL>",
    "phone": "8790167084",
    "address": "1436, new indiramma colony",
    "address_2": "Golgamudi road",
    "city": "Nellore",
    "state":"Andhra Rradesh",
    "country": "India",
    "pin_code": "524004"
}'
```

### Response:

```json
{
    "success": true,
    "address": {
        "company_id": 3429769,
        "pickup_code": "Home-3",
        "updated_at": "2025-03-20T13:49:57.000000Z",
        "created_at": "2025-03-20T13:49:57.000000Z",
        "id": 7437846,
        "address": "1436, new indiramma colony",
        "address_2": "Golgamudi road",
        "address_type": null,
        "city": "Nellore",
        "state": "Andhra Rradesh",
        "country": "India",
        "gstin": null,
        "pin_code": "524004",
        "phone": "8790167084",
        "email": "<EMAIL>",
        "name": "Ragnar",
        "alternate_phone": null,
        "lat": null,
        "long": null,
        "status": 1,
        "rto_address_id": 7437846,
        "extra_info": "{\"source\":3}",
        "phone_verified": 0
    },
    "pickup_id": 7437846,
    "company_name": "Amma's Ghee and oils",
    "full_name": "API"
}
```


## 11. Cancel a Shipment

```bash
curl --location 'https://apiv2.shiprocket.in/v1/external/orders/cancel/shipment/awbs' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer {{token}}' \
--data '{
"awbs": ["19041211125783"]
}'
```

### Response:

```json
{
  "message": "Bulk Shipment cancellation is in progress. Please wait for some time."
}
```