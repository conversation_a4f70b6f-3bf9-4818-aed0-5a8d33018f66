from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site
from django.conf import settings


class Command(BaseCommand):
    help = 'Update the site domain for SEO sitemaps'

    def add_arguments(self, parser):
        parser.add_argument(
            '--domain',
            type=str,
            help='Domain name to set (e.g., swadesic.com)',
        )

    def handle(self, *args, **options):
        domain = options.get('domain') or settings.SITE_DOMAIN
        
        if not domain or domain == 'swadesic.com':
            self.stdout.write(
                self.style.WARNING(
                    "⚠️  Using default domain 'swadesic.com'. "
                    "Set SITE_DOMAIN in your environment or use --domain flag."
                )
            )
            domain = input("Enter your actual domain (e.g., yourdomain.com): ").strip()
            
            if not domain:
                self.stdout.write(self.style.ERROR("❌ No domain provided. Exiting."))
                return

        try:
            # Update or create the site
            site, created = Site.objects.update_or_create(
                id=settings.SITE_ID,
                defaults={
                    'domain': domain,
                    'name': 'Swadesic'
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Created new site with domain: {domain}")
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Updated site domain to: {domain}")
                )
            
            # Verify the change
            current_site = Site.objects.get(id=settings.SITE_ID)
            self.stdout.write(f"📋 Current site configuration:")
            self.stdout.write(f"   ID: {current_site.id}")
            self.stdout.write(f"   Domain: {current_site.domain}")
            self.stdout.write(f"   Name: {current_site.name}")
            
            # Test sitemap URL
            sitemap_url = f"https://{domain}/seo/sitemap.xml"
            self.stdout.write(f"\n🔗 Your sitemap URL: {sitemap_url}")
            self.stdout.write(f"🔗 Your robots.txt URL: https://{domain}/seo/robots.txt")
            
            self.stdout.write(
                self.style.SUCCESS(
                    "\n🎉 Site domain updated successfully! "
                    "Your sitemap should now show the correct domain."
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Error updating site domain: {e}")
            )
