from django.urls import path
from .views import (
    ProductlistAV,
    ProductDetailsAV,
    UpdateProductStock,
    ProductimagelistAV,
    ProductimageDetailsAV,
    UpdateProductImageOrder,
    VisitProductAV,
    CheckProductSavedOrNot,
    SaveProductAV,
    HideProducts,
    GetHiddenProducts,
    UnHideProducts,
    UpdateInventory,
    ProductHistory,
    WishlistProducts,
    RecommendedProducts,
    ProductSlugCodeAvailability,
    GetProductReferenceFromSlug,
    ProductVariantsView
)


urlpatterns = [
    #  product management page
    path("productlist/", ProductlistAV.as_view(), name="product-list"),
    path(
        "productdetails/<str:product_reference>/",
        ProductDetailsAV.as_view(),
        name="product-details",
    ),
    path(
        "productdetails/<str:product_reference>/<str:user_pincode>/",
        ProductDetailsAV.as_view(),
        name="product-details",
    ),
    path(
        "update_product_stock/<str:product_reference>/",
        UpdateProductStock.as_view(),
        name="update-product-stock",
    ),
    path(
        "productdetails/<str:product_reference>/<str:product_version>/",
        ProductDetailsAV.as_view(),
        name="product-details",
    ),
    path(
        "product_history/<str:product_reference>/",
        ProductHistory.as_view(),
        name="product-history",
    ),
    path(
        "productimages/<str:product_reference>/",
        ProductimagelistAV.as_view(),
        name="product-image-list",
    ),
    path(
        "productimages/<str:product_reference>/<str:product_version>/",
        ProductimagelistAV.as_view(),
        name="product-image-list",
    ),
    path(
        "productimagedetails/<int:product_image_id>/",
        ProductimageDetailsAV.as_view(),
        name="product-image-details",
    ),
    path(
        "updateproductimageorder/",
        UpdateProductImageOrder.as_view(),
        name="update-product-image-order",
    ),
    path("hideproducts/", HideProducts.as_view(), name="hide-products"),
    path(
        "gethiddenproducts/<str:store_reference>/",
        GetHiddenProducts.as_view(),
        name="get-hidden-products",
    ),
    path("unhideproducts/", UnHideProducts.as_view(), name="un-hide-products"),
    #  store page-buyer
    path("visitproduct/", VisitProductAV.as_view(), name="visit-product"),
    path(
        "check_product_saved_or_not/",
        CheckProductSavedOrNot.as_view(),
        name="check-product-saved-or-not",
    ),
    path("saveproduct/", SaveProductAV.as_view(), name="save-product"),
    path("wishlist_product/", WishlistProducts.as_view(), name="wishlist-product"),
    path("update_inventory/", UpdateInventory.as_view(), name="update-inventory"),
    path("recommended_products/", RecommendedProducts.as_view(), name="recommended-products"),
    path("product_slug_code_availability/", ProductSlugCodeAvailability.as_view(), name="product-slug-code-availability"),
    path("get_product_reference_from_slug/", GetProductReferenceFromSlug.as_view(), name="get-product-reference-from-slug"),

    path("productvariants/", ProductVariantsView.as_view(), name="product-variants"),
]
