from django.forms.models import model_to_dict
import boto3
from decouple import config
import base64
import json
import datetime
from itertools import groupby
from users.user_api.models import User, UserProduct
from users.notification_api.models import Notifications
from django.db import models
from django.core.files import File
from io import BytesIO
import os
from PIL import Image, ImageOps
import numpy as np
import re
import requests
import logging
from django.core.cache import cache


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def calculate_size(image, quality: int):
    im_io = BytesIO()
    image.save(im_io, format="JPEG", quality=quality)
    k = im_io.tell()
    # returns the size in KB in numbers
    return k / 1024


def check_range(value, range_min, range_max):
    if value < range_min:
        return -1
    elif value >= range_min and value <= range_max:
        return 0
    else:
        return 1


def compress(
    image_path,
    min_image_length=1000,
    desired_min_size_in_KB=100,
    desired_max_size_in_KB=130,
):
    # image path
    image = Image.open(image_path)

    # downsize the image to minimum desired image length
    w, h = image.size
    initial_size = calculate_size(image, quality=100)

    divident = np.array(image.size).min() / min_image_length
    nw, nh = int(w / divident), int(h / divident)
    image = image.resize((nw, nh))

    # check & decrease quality if not in range
    if (
        check_range(
            calculate_size(image, quality=100),
            desired_min_size_in_KB,
            desired_max_size_in_KB,
        )
        != 0
    ):

        for quality in reversed(range(50, 100, 10)):
            if (
                check_range(
                    calculate_size(image, quality=quality),
                    desired_min_size_in_KB,
                    desired_max_size_in_KB,
                )
                < 0
            ):
                quality = quality + 5
                break
            elif (
                check_range(
                    calculate_size(image, quality=quality),
                    desired_min_size_in_KB,
                    desired_max_size_in_KB,
                )
                > 0
            ):
                continue

    else:
        quality = 100

    im_io = BytesIO()
    image.save(im_io, format="JPEG", quality=quality)
    new_image = File(im_io, name=image_path.name)
    return new_image


class DeleteManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)


# AWS sms publishing
client = boto3.client(
    "sns",
    region_name=config("AWS_S3_REGION_NAME"),
    aws_access_key_id=config("AWS_ACCESS_KEY"),
    aws_secret_access_key=config("AWS_SECRET_KEY"),
)


class GenerateNotifications:
    @staticmethod
    def create_notifications(
        notified_user,
        notification_type,
        notification_about,
        image=None,
        product_id=None,
        store_pincode=None,
        otp=None,
    ):

        if notification_type == Notifications.Notifications_Type.ONBOARDING:
            notification_message = "your friend has joined using your invite code, you can follow him".format(
                notification_about
            )
        elif notification_type == Notifications.Notifications_Type.ORDER_CONFIRMED:
            notification_message = "Hurray🥳 @storehandle has just confirmed your order. Tap to view details"
        elif notification_type == Notifications.Notifications_Type.ORDER_SHIPPED:
            notification_message = (
                "your order has been shipped, details has been updated"
            )
        elif notification_type == Notifications.Notifications_Type.NEW_ORDER:
            notification_message = "your store has new order"
        elif notification_type == Notifications.Notifications_Type.ORDER_CANCELLED:
            notification_message = "your store's order has cancelled"
        elif notification_type == Notifications.Notifications_Type.RETURN_REQUESTED:
            notification_message = "Return requested for product from your store"
        elif notification_type == Notifications.Notifications_Type.DELIVERY_OTP:
            notification_message = "Secure delivery code is " + otp
        elif notification_type == Notifications.Notifications_Type.RETURN_OTP:
            notification_message = "Secure return pickup code is: " + otp
        if notified_user:
            Notifications.objects.create(
                notified_user=notified_user,
                notification_type=notification_type,
                notification_message=notification_message,
                notification_status=Notifications.Notification_Status.NOT_SEEN,
                notification_about=notification_about,
                image=image,
            )
        else:
            if (
                notification_type
                == Notifications.Notifications_Type.PRODUCT_VERSION_CHANGED
            ):
                interested_users = UserProduct.objects.filter(
                    productid=product_id
                ).values_list("userid", flat=True)

                notification_message = "Product you interested has been updated"
                for users in interested_users:
                    user = User.objects.get(userid=users)
                    user_reference = user.user_reference
                    Notifications.objects.create(
                        notified_user=user_reference,
                        notification_type=notification_type,
                        notification_message=notification_message,
                        notification_status=Notifications.Notification_Status.NOT_SEEN,
                        notification_about=notification_about,
                        image=image,
                    )
            elif notification_type == Notifications.Notifications_Type.STORE_CREATED:
                users_with_same_pincode = User.objects.filter(
                    pincode=store_pincode
                ).values_list("user_reference", flat=True)

                notification_message = "Your nearby store @storehandle has opened their store for public"
                for user in users_with_same_pincode:
                    Notifications.objects.create(
                        notified_user=user,
                        notification_type=notification_type,
                        notification_message=notification_message,
                        notification_status=Notifications.Notification_Status.NOT_SEEN,
                        notification_about=notification_about,
                        image=image,
                    )


                # for user in users_with_same_pincode:
                #     notification_handler = NotificationHandler(
                #     notified_user=user,
                #     notification_type=Notifications.Notifications_Type.STORE_CREATED,
                #     notification_message=notification_message,
                #     notification_status=Notifications.Notification_Status.NOT_SEEN,
                #     notification_handler=notification_about,
                #     image=image,
                # )
                # notification_handler.create_notification(notification_handler)
# #
# class GenerateNotifications:
#     NOTIFICATION_MESSAGES = {
#         Notifications.Notifications_Type.ONBOARDING: "Your friend has joined using your invite code, you can follow them.",
#         Notifications.Notifications_Type.ORDER_CONFIRMED: "Your order has been confirmed, details have been updated.",
#         Notifications.Notifications_Type.ORDER_SHIPPED: "Your order has been shipped, details have been updated.",
#         Notifications.Notifications_Type.NEW_ORDER: "Your store has a new order.",
#         Notifications.Notifications_Type.ORDER_CANCELLED: "Your store's order has been cancelled.",
#         Notifications.Notifications_Type.RETURN_REQUESTED: "Return requested for a product from your store.",
#         Notifications.Notifications_Type.DELIVERY_OTP: "Secure delivery code is {}",
#         Notifications.Notifications_Type.RETURN_OTP: "Secure return pickup code is: {}",
#         Notifications.Notifications_Type.PRODUCT_VERSION_CHANGED: "Product you are interested in has been updated.",
#         Notifications.Notifications_Type.STORE_CREATED: "New store created near you."
#     }
#
#     @staticmethod
#     def create_notifications(notified_user, notification_type, notification_about, image=None, product_id=None, store_pincode=None, otp=None):
#         message_template = GenerateNotifications.NOTIFICATION_MESSAGES.get(notification_type, "Default message")
#         notification_message = message_template.format(otp) if otp else message_template
#
#         if notified_user:
#             Notifications.objects.create(
#                 notified_user=notified_user,
#                 notification_type=notification_type,
#                 notification_message=notification_message,
#                 notification_status=Notifications.Notification_Status.NOT_SEEN,
#                 notification_about=notification_about,
#                 image=image
#             )
#         else:
#             if notification_type == Notifications.Notifications_Type.PRODUCT_VERSION_CHANGED:
#                 interested_users = UserProduct.objects.filter(productid=product_id).values_list('userid', flat=True)
#                 for users in interested_users:
#                     user = User.objects.get(userid=users)
#                     user_reference = user.user_reference
#                     Notifications.objects.create(
#                         notified_user=user_reference,
#                         notification_type=notification_type,
#                         notification_message=notification_message,
#                         notification_status=Notifications.Notification_Status.NOT_SEEN,
#                         notification_about=notification_about,
#                         image=image
#                     )
#             elif notification_type == Notifications.Notifications_Type.STORE_CREATED:
#                 users_with_same_pincode = User.objects.filter(pincode=store_pincode).values_list('user_reference', flat=True)
#                 for user in users_with_same_pincode:
#                     Notifications.objects.create(
#                         notified_user=user,
#                         notification_type=notification_type,
#                         notification_message=notification_message,
#                         notification_status=Notifications.Notification_Status.NOT_SEEN,
#                         notification_about=notification_about,
#                         image=image
#                     )



def all_equal_or_not(iterable):
    g = groupby(iterable)
    return next(g, True) and not next(g, False)
#
# def all_equal_or_not(iterable):
#     return len(set(iterable)) == 1



# generate key for login otp generation
class generateKey:
    @staticmethod
    def returnValue(phone):
        generated_key = (
            str(phone) + str(datetime.date.today()) + "Some Random Secret Key"
        )
        return generated_key


# generate key for otp validation on order delivery to customer
class DeliveryGenerateKey:
    @staticmethod
    def returnValue(user_reference, package_number, otp):
        generated_key = str(user_reference) + str(package_number) + str(otp)
        return generated_key


class ModelDiffMixin(object):
    """
    A model mixin that tracks model fields' values and provide some useful api
    to know what fields have been changed.
    """

    def __init__(self, *args, **kwargs):
        super(ModelDiffMixin, self).__init__(*args, **kwargs)
        self.__initial = self._dict

    @property
    def diff(self):
        d1 = self.__initial
        d2 = self._dict
        diffs = [(k, (v, d2[k])) for k, v in d1.items() if v != d2[k]]
        return dict(diffs)

    @property
    def has_changed(self):
        return bool(self.diff)

    @property
    def changed_fields(self):
        return self.diff.keys()

    def get_field_diff(self, field_name):
        """
        Returns a diff for field if it's changed and None otherwise.
        """
        return self.diff.get(field_name, None)

    def save(self, *args, **kwargs):
        """
        Saves model and set initial state.
        """
        super(ModelDiffMixin, self).save(*args, **kwargs)
        self.__initial = self._dict

    @property
    def _dict(self):
        return model_to_dict(self, fields=[field.name for field in self._meta.fields])

# def get_recommended_users_on_location(self, reference):
#     if reference.startswith('U'):
#         target_entity = User.objects.get(user_reference=reference, deleted=False)
#         target_city = target_entity.user_location
#
#     else:
#         target_entity = TrustCenter.objects.get(store_reference=reference, deleted=False)
#         target_city = target_entity.city
#
#     # Extract geographical information of the target user
#     target_pincode = target_entity.pincode
#     # target_state = target_user.state
#
#     # Calculate geographical distances and prioritize recommendations
#     users = User.objects.exclude(userid=target_user.userid, deleted=True)
#
#     users = users.annotate(
#         pincode_distance=ExpressionWrapper(
#             F('pincode') == target_pincode, output_field=FloatField()
#         ),
#         city_distance=ExpressionWrapper(
#             F('user_location') == target_city, output_field=FloatField()
#         ),
#         total_distance=F('pincode_distance') + F('city_distance')
#     ).order_by('-total_distance')
#     recommended_users = [{"user": user, "distance": user.total_distance} for user in users]
#
#     return recommended_users

def extract_input_arguments_with_values(graphql_query):
    # Regular expression pattern to match input arguments and their values
    arg_pattern = re.compile(r'\b(\w+)\s*:\s*("[^"]*"|\d+)\s*(?=[,)])')

    # Extract input arguments with their values
    input_arguments_with_values = {}
    for match in arg_pattern.findall(graphql_query):
        input_arguments_with_values[match[0]] = match[1].strip('"')

    return input_arguments_with_values


# utils.py

def encrypt_data(data):
    # Convert data to JSON string
    json_data = json.dumps(data)
    # Convert JSON string to bytes
    bytes_data = json_data.encode('utf-8')
    # Encrypt bytes data using Base32 encoding
    encrypted_data = base64.b32encode(bytes_data).decode('utf-8')
    return encrypted_data

def decrypt_data(encrypted_data):
    # Decode Base32 encoded data to bytes
    bytes_data = base64.b32decode(encrypted_data.encode('utf-8'))
    # Decode bytes data to JSON string
    json_data = bytes_data.decode('utf-8')
    # Convert JSON string to Python dictionary
    decrypted_data = json.loads(json_data)
    return decrypted_data


def get_or_create_auth_token(user_xmpp_jid, user_xmpp_password):
    """
    Helper method to get cached auth token or create new one
    Returns tuple of (success: bool, data: dict, error: str)
    """
    # Check cache first
    auth_data = cache.get(f"messaging_auth_{user_xmpp_jid}")
    if auth_data:
        return True, auth_data, None

    # Make login request to messaging server
    try:
        response = requests.post(
            f"{config('MESSAGING_SERVER_URL')}/api/v1/login",
            json={
                "username": user_xmpp_jid,
                "password": user_xmpp_password
            },
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            data = response.json()
            cache_data = {
                'auth_token': data['data']['authToken'],
                'user_id': data['data']['userId']
            }
            cache.set(f"messaging_auth_{user_xmpp_jid}", cache_data, timeout=86400)  # Cache for 24 hours
            return True, cache_data, None
        else:
            return False, None, response.text
    except Exception as e:
        logger.error(f"Error in getting auth token: {str(e)}")
        return False, None, str(e)


def get_unread_conversations_count(instance):
    """
    Get the count of unread conversations for a user from Swadesic Messaging Server
    
    Args:
        instance: User model instance containing user_reference
        
    Returns:
        int: Number of unread conversations, 0 if there's an error
    """
    try:
        # Get authentication token
        token = instance.js_messaging_token
        if token:
            # Get chat list with unread count
            chat_list_response = requests.get(
                f"{config('JS_MESSAGING_SERVER_URL')}/api/get_chat_list",
                headers={
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                },
                params={
                    'entity_reference': instance.user_reference if isinstance(instance, User) else instance.store_reference
                }
            )

            if chat_list_response.status_code == 200:
                # Assuming the response contains a list of chats with unread counts
                chats = chat_list_response.json()
                return sum(chat.get('unread_count', 0) for chat in chats)
            else:
                logger.info(f"Failed to get chat list: {chat_list_response.text}")
        else:
            logger.info(f"Token not found for user {instance.user_reference}")
    except Exception as e:
        logger.info(f"Error getting unread conversations count: {str(e)}")
    return 0

def delete_user_from_messaging_server(instance):
    try:
        # Get authentication token
        token = instance.js_messaging_token
        if token:
            # Delete user from messaging server
            delete_user_response = requests.post(
                f"{config('JS_MESSAGING_SERVER_URL')}/api/delete_account",
                headers={
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                },
                json={
                    'user_reference': instance.user_reference if isinstance(instance, User) else instance.store_reference
                }
            )


            if delete_user_response.status_code == 200:
                return True
            else:
                logger.info(f"Failed to delete user from messaging server: {delete_user_response.text}")
        else:
            logger.info(f"Token not found for user {instance.user_reference}")
    except Exception as e:
        logger.info(f"Error deleting user from messaging server: {str(e)}")
    return False