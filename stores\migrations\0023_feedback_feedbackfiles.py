# Generated by Django 3.2.13 on 2022-12-28 05:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0022_store_business_description"),
    ]

    operations = [
        migrations.CreateModel(
            name="FeedBack",
            fields=[
                ("feedback_id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "screen_category",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("brief", models.CharField(blank=True, max_length=100, null=True)),
                ("details", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("REVIEWED", "Reviewed"),
                            ("APPROVED", "Approved"),
                            ("REJECTED", "Rejected"),
                            ("PLANNED", "Planned"),
                            ("SNAR", "SNAR"),
                            ("FIXED", "Fixed"),
                        ],
                        max_length=10,
                        null=True,
                    ),
                ),
                ("group", models.CharField(blank=True, max_length=100, null=True)),
                ("priority", models.PositiveIntegerField(blank=True, null=True)),
                ("created_date", models.DateTimeField(auto_now_add=True, null=True)),
                ("modified_date", models.DateField(auto_now=True, null=True)),
            ],
            options={
                "verbose_name_plural": "feed back",
                "db_table": '"store"."feed_back"',
            },
        ),
        migrations.CreateModel(
            name="FeedBackFiles",
            fields=[
                (
                    "feedback_file_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "screen_short",
                    models.ImageField(
                        blank=True, null=True, upload_to="feed_back_image"
                    ),
                ),
                (
                    "attachments",
                    models.FileField(
                        blank=True, null=True, upload_to="feed_back_attachment"
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True, null=True)),
                (
                    "feedback",
                    models.ForeignKey(
                        db_column="feedback",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedback_files",
                        to="stores.feedback",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "feed back files",
                "db_table": '"store"."feedback_files"',
            },
        ),
    ]
