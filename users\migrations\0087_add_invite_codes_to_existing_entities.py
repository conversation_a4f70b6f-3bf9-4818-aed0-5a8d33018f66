from django.db import migrations, models
import random
import string
from itertools import cycle


def generate_referral_code(apps, name, invite_type='USER'):
    """
    Generate a unique referral code.
    invite_type : USER or STORE
    """
    User = apps.get_model('users', 'User')
    Store = apps.get_model('stores', 'Store')

    prefix = "SWC" if invite_type.upper() == 'USER' else "SWS"
    name_part = name[:4].upper()
    alphabet = string.ascii_uppercase
    alphabet_cycle = cycle(alphabet)

    def generate_code(name_part):
        for _ in range(256):  # Limit the attempts
            random_part = ''.join(random.choices(string.hexdigits.upper(), k=2))
            referral_code = f"{prefix}{name_part}{random_part}"

            if not User.objects.filter(invite_code=referral_code).exists() and \
                    not Store.objects.filter(invite_code=referral_code).exists():
                return referral_code

        return None

    for i in range(4, 0, -1):
        code = generate_code(name_part[:i].ljust(4, 'X'))  # Padding with 'X' if less than 4
        if code:
            return code

    raise Exception("Unable to generate unique referral code after exhausting all possibilities.")


def add_invite_codes(apps, schema_editor):
    User = apps.get_model('users', 'User')
    Store = apps.get_model('stores', 'Store')

    # Assign invite codes to existing users
    for user in User.objects.filter(user_name__isnull=False):
        if not user.invite_code:
            user.invite_code = generate_referral_code(apps, user.user_name, 'USER')
            user.save()

    # Assign invite codes to existing stores
    for store in Store.objects.filter(store_name__isnull=False):
        if not store.invite_code:
            store.invite_code = generate_referral_code(apps, store.store_name, 'STORE')
            store.save()


def remove_invite_codes(apps, schema_editor):
    User = apps.get_model('users', 'User')
    Store = apps.get_model('stores', 'Store')

    # Remove invite codes for all users and stores
    User.objects.all().update(invite_code=None)
    Store.objects.all().update(invite_code=None)


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0086_alter_notifications_notification_type"),
    ]

    operations = [
        migrations.RunPython(add_invite_codes, reverse_code=remove_invite_codes),
    ]
