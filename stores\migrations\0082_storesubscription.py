# Generated by Django 4.2.7 on 2024-11-14 22:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("swadesic_admin", "0001_initial"),
        ("stores", "0081_store_subscription_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="StoreSubscription",
            fields=[
                (
                    "store_subscription_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("subscription_start_date", models.DateTimeField(auto_now_add=True)),
                ("subscription_end_date", models.DateTimeField(blank=True, null=True)),
                (
                    "subscription_status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("INACTIVE", "Inactive"),
                            ("EXPIRED", "Expired"),
                        ],
                        default="INACTIVE",
                        max_length=100,
                    ),
                ),
                (
                    "razorpay_payment_id",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "razorpay_subscription_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("auto_renew", models.BooleanField(default=False)),
                (
                    "store_reference",
                    models.ForeignKey(
                        db_column="store_reference",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="store_subscriptions",
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
                (
                    "subscription_plan_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="subscription_plan_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="store_subscriptions",
                        to="swadesic_admin.subscriptionplan",
                        to_field="plan_reference",
                    ),
                ),
            ],
            options={
                "db_table": '"store"."store_subscription"',
            },
        ),
    ]
