# Generated by Django 4.2.7 on 2024-08-21 08:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0124_merge_20240813_2357"),
    ]

    operations = [
        migrations.CreateModel(
            name="SubOrderPaymentDetails",
            fields=[
                (
                    "order_payment_details_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "order_request_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "product_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "product_unit_price",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                (
                    "product_quantity",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("product_amount", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "product_delivery_fee",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                (
                    "store_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "store_delivery_fee",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                (
                    "payment_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("PAYMENT_INITIATED", "Payment initiated"),
                            ("PAYMENT_SUCCESS", "Payment success"),
                            ("REFUND_INITIATED", "Refund initiated"),
                            ("REFUND_INITIATE_FAILED", "Refund initiate failed"),
                            ("REFUND_PENDING", "Refund pending"),
                            ("REFUND_SUCCESS", "Refund success"),
                            ("REFUND_FAILED", "Refund failed"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("refund_id", models.CharField(blank=True, max_length=255, null=True)),
                ("refund_amount", models.PositiveIntegerField(blank=True, null=True)),
                ("is_suborder_deleted", models.BooleanField(default=False)),
                (
                    "suborder_deleted_condition",
                    models.CharField(
                        blank=True,
                        choices=[("CANCELLED", "Cancelled"), ("RETURNED", "Returned")],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "order_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="order_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.order",
                        to_field="order_number",
                    ),
                ),
                (
                    "suborder_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="suborder_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.suborder",
                        to_field="suborder_number",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "suborder payment details",
                "db_table": '"order"."suborder_payment_details"',
            },
        ),
        migrations.RenameField(
            model_name="order",
            old_name="delivery_fee",
            new_name="total_delivery_fee",
        ),
        migrations.RenameField(
            model_name="order",
            old_name="total_amount",
            new_name="total_order_amount",
        ),
        migrations.RenameField(
            model_name="order",
            old_name="store_total",
            new_name="total_product_amount",
        ),
        migrations.AddField(
            model_name="order",
            name="expected_swadesic_fee",
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AddField(
            model_name="order",
            name="store_delivery_fee",
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AddField(
            model_name="order",
            name="swadesic_fee",
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AddField(
            model_name="order",
            name="swadesic_fee_status",
            field=models.CharField(
                max_length=25,
                blank=True,
                choices=[
                    ("GIVEN", "Given"),
                    ("TAKEN", "Taken"),
                    ("UNPROCESSED", "Unprocessed"),
                ],
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="orderconfiguration",
            name="min_cart_value_for_commission_fee",
            field=models.IntegerField(blank=True, default=500, null=True),
        ),
        migrations.DeleteModel(
            name="OrderPaymentDetails",
        ),
    ]
