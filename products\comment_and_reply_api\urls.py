from django.urls import path
from .views import (
    AddCommentAV,
    GetCommentAV,
    AddReplyAV,
    UpdateCommentsAndReply,
    AddAndRemoveClaps,
    DeleteComment,
    DeleteReply,
    AddAndDeleteReviewImage,
    CheckReviewAccess,
)


urlpatterns = [
    #  comment and reply
    path("addcomment/", AddCommentAV.as_view(), name="add-comment"),
    path("check_review_access/", CheckReviewAccess.as_view(), name="check-review-access"),
    path(
        "getcomment/product/<str:product_reference>/user/<str:reference>/",
        GetCommentAV.as_view(),
        name="get-comment",
    ),
    path("addreply/", AddReplyAV.as_view(), name="add-reply"),
    path(
        "updatecommentsandreply/<int:pk>/",
        UpdateCommentsAndReply.as_view(),
        name="update-comments-and-reply",
    ),
    path(
        "addandremoveclaps/", AddAndRemoveClaps.as_view(), name="add-and-remove-claps"
    ),
    path(
        "deletecomment/<int:comment_id>/",
        DeleteComment.as_view(),
        name="delete-comment",
    ),
    path("deletereply/<int:reply_id>/", DeleteReply.as_view(), name="delete-reply"),
    path(
        "add_and_delete_review_image/<int:comment_id>/",
        AddAndDeleteReviewImage.as_view(),
        name="add-and-delete-review-image",
    ),
]
