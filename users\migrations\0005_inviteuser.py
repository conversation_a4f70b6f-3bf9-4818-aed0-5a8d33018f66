# Generated by Django 3.2.13 on 2022-09-02 04:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0004_alter_user_user_reference"),
    ]

    operations = [
        migrations.CreateModel(
            name="InviteUser",
            fields=[
                ("invite_id", models.AutoField(primary_key=True, serialize=False)),
                ("invite_code", models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ("invite_type", models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ("invited_user_role", models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ("invited_user", models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                (
                    "number_of_coupon",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                (
                    "coupon_expiry_date",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "remaining_coupon",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                (
                    "phone_number",
                    models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=15, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "invite users",
                "db_table": '"user"."invite_user"',
            },
        ),
    ]
