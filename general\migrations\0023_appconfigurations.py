# Generated by Django 4.2.7 on 2024-09-29 13:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("general", "0022_request_user_reference"),
    ]

    operations = [
        migrations.CreateModel(
            name="AppConfigurations",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "swadesic_default_invite_code",
                    models.<PERSON>r<PERSON><PERSON>(default="SWADESHIFIRST", max_length=20),
                ),
                ("real_store_creation_limit", models.IntegerField(default=5)),
                ("test_store_creation_limit", models.IntegerField(default=1)),
                ("invite_reward_onboarding", models.IntegerField(default=51)),
                ("invite_reward_store_verification", models.IntegerField(default=51)),
                ("flash_promotion_period", models.IntegerField(default=12)),
                ("monthly_flash_point_credit", models.Integer<PERSON>ield(default=500)),
                ("is_promotion_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ("promotion_start_time", models.DateTimeField(blank=True, null=True)),
                ("promotion_end_time", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name_plural": "App configurations",
                "db_table": '"public"."app_configurations"',
            },
        ),
    ]
