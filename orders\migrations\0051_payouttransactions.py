# Generated by Django 3.2.13 on 2022-12-08 10:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0018_auto_20221111_1736"),
        ("orders", "0050_shippinghistory"),
    ]

    operations = [
        migrations.CreateModel(
            name="PayoutTransactions",
            fields=[
                (
                    "payout_transaction_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("order_value", models.PositiveIntegerField(blank=True, null=True)),
                ("order_date", models.CharField(blank=True, max_length=100, null=True)),
                ("payout_release_date", models.DateField(blank=True, null=True)),
                ("transaction_status", models.CharField(max_length=100)),
                (
                    "transaction_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("CREDITED", "Credited"),
                            ("WITH_DRAWL", "With drawl"),
                        ],
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "order_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="order_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.order",
                        to_field="order_number",
                    ),
                ),
                (
                    "store_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="store_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
                (
                    "suborder_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="suborder_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.suborder",
                        to_field="suborder_number",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "payout transactions",
                "db_table": '"order"."payout_transactions"',
            },
        ),
    ]
