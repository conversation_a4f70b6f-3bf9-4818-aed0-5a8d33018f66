# Generated by Django 4.2.7 on 2025-03-21 18:29

from django.db import migrations, models
import django.db.models.deletion


def populate_store_shipping_balance(apps, schema_editor):
    """
    Populate shipping balance for all stores.
    Initial balance set to 0.00 for all stores.
    """
    Store = apps.get_model('stores', 'Store')
    StoreShippingAccountBalance = apps.get_model('orders', 'StoreShippingAccountBalance')
    
    # Get all stores
    stores = Store.objects.all()
    
    # Create shipping balance entries
    balance_entries = [
        StoreShippingAccountBalance(
            store_reference=store,
            shipping_balance=0.00
        )
        for store in stores
    ]
    
    # Bulk create all entries
    if balance_entries:
        StoreShippingAccountBalance.objects.bulk_create(balance_entries)


class Migration(migrations.Migration):

    dependencies = [
        (
            "stores",
            "0093_rename_deliverymethod_whitelabel_deliverysettings_deliverymethod_swadesic_and_more",
        ),
        ("orders", "0145_payouttransactions_bank_reference_number_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SwadesicShippingPackageDetails",
            fields=[
                (
                    "swadesic_shipping_package_details_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "package_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("order_reference", models.CharField(max_length=50, unique=True)),
                (
                    "store_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "user_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "shiprocket_order_id",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("shipment_id", models.CharField(blank=True, max_length=50, null=True)),
                ("awb_number", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "courier_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("tracking_url", models.URLField(blank=True, null=True)),
                ("pickup_address", models.JSONField(default=dict)),
                ("delivery_address", models.JSONField(default=dict)),
                ("volumetric_weight", models.FloatField(blank=True, null=True)),
                ("dead_weight", models.FloatField(blank=True, null=True)),
                ("length", models.FloatField(blank=True, null=True)),
                ("breadth", models.FloatField(blank=True, null=True)),
                ("height", models.FloatField(blank=True, null=True)),
                ("package_contents", models.JSONField(default=dict)),
                ("shipping_cost", models.FloatField(blank=True, null=True)),
                ("is_shipping_balance_deducted", models.BooleanField(default=False)),
                ("tracking_milestones", models.JSONField(default=dict)),
                (
                    "current_status",
                    models.CharField(default="Yet to be Updated", max_length=50),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Swadesic Shipping Package Details",
                "db_table": '"order"."swadesic_shipping_package_details"',
            },
        ),
        migrations.CreateModel(
            name="StoreShippingBalanceHistory",
            fields=[
                (
                    "store_shipping_balance_history_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "shipping_balance_payment_reference",
                    models.CharField(max_length=100, unique=True),
                ),
                (
                    "package_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "order_reference",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("txn_token", models.CharField(max_length=50, null=True)),
                ("transaction_id", models.CharField(max_length=100, null=True)),
                ("bank_transaction_id", models.CharField(max_length=100, null=True)),
                ("payment_mode", models.CharField(max_length=50, null=True)),
                ("payment_channel", models.CharField(max_length=30, null=True)),
                ("transaction_date", models.CharField(max_length=50, null=True)),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[("CREDIT", "Credit"), ("DEBIT", "Debit")],
                        max_length=10,
                    ),
                ),
                (
                    "payment_status",
                    models.CharField(
                        choices=[
                            ("SUCCESS", "Success"),
                            ("FAILED", "Failed"),
                            ("PENDING", "Pending"),
                        ],
                        max_length=10,
                    ),
                ),
                ("transaction_fee", models.FloatField(default=0, null=True)),
                ("transaction_fee_tax", models.FloatField(default=0, null=True)),
                (
                    "pg_transctionfee_with_tax_perc",
                    models.FloatField(default=0, null=True),
                ),
                ("pg_transctionfee_perc", models.FloatField(default=0, null=True)),
                ("pg_gst_tax_perc", models.FloatField(default=0, null=True)),
                ("razorpay_payment_id", models.CharField(max_length=50, null=True)),
                (
                    "store_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="store_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="store_shipping_balance_history",
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "store_shipping_balance_history",
                "db_table": '"order"."store_shipping_balance_history"',
            },
        ),
        migrations.CreateModel(
            name="StoreShippingAccountBalance",
            fields=[
                (
                    "store_shipping_account_balance_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "shipping_balance",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "store_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="store_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="store_shipping_balance",
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "store_shipping_account_balance",
                "db_table": '"order"."store_shipping_account_balance"',
            },
        ),
        migrations.RunPython(
            populate_store_shipping_balance,
            reverse_code=migrations.RunPython.noop
        ),
    ]
