from django.db import models
from django.core.validators import RegexValidator


class FaqCategory(models.Model):
    """
    Model for FAQ categories (e.g., General, Pricing, Dashboard, API)
    """
    id = models.AutoField(primary_key=True)
    category_key = models.CharField(
        max_length=100,
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^[a-z0-9_]+$',
                message='Category key must contain only lowercase letters, numbers, and underscores'
            )
        ],
        help_text='Unique key for the category (used in deep links)'
    )
    name = models.CharField(
        max_length=200,
        help_text='Display name for the category'
    )
    order = models.PositiveIntegerField(
        default=0,
        help_text='Order in which categories should be displayed'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Whether this category is active and should be displayed'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = '"public"."faq_category"'
        verbose_name = 'FAQ Category'
        verbose_name_plural = 'FAQ Categories'

    def __str__(self):
        return self.name

    @property
    def active_items_count(self):
        """Returns count of active FAQ items in this category"""
        return self.items.filter(is_active=True).count()

    def get_active_items(self):
        """Returns queryset of active FAQ items ordered by their order field"""
        return self.items.filter(is_active=True).order_by('order')


class FaqItem(models.Model):
    """
    Model for individual FAQ questions and answers
    """
    id = models.AutoField(primary_key=True)
    item_key = models.CharField(
        max_length=100,
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^[a-z0-9_]+$',
                message='Item key must contain only lowercase letters, numbers, and underscores'
            )
        ],
        help_text='Unique key for the FAQ item (used in deep links)'
    )
    category = models.ForeignKey(
        FaqCategory,
        on_delete=models.CASCADE,
        related_name='items',
        help_text='Category this FAQ item belongs to'
    )
    question = models.TextField(
        help_text='The FAQ question text'
    )
    answer = models.TextField(
        help_text='The FAQ answer text (can contain HTML for formatting)'
    )
    order = models.PositiveIntegerField(
        default=0,
        help_text='Order in which items should be displayed within the category'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Whether this FAQ item is active and should be displayed'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = '"public"."faq_item"'
        ordering = ['category__order', 'order', 'question']
        verbose_name = 'FAQ Item'
        verbose_name_plural = 'FAQ Items'

    def save(self, *args, **kwargs):
        # Auto-generate order if not provided
        if self.order == 0:
            last_item = FaqItem.objects.filter(category=self.category).order_by('-order').first()
            self.order = (last_item.order + 1) if last_item else 1
        super().save(*args, **kwargs)

    def clean(self):
        """Custom validation for the model"""
        from django.core.exceptions import ValidationError

        # Ensure category exists when validating
        if self.category_id:
            # Check for duplicate order within category (additional validation)
            existing = FaqItem.objects.filter(
                category=self.category,
                order=self.order
            ).exclude(pk=self.pk)

            if existing.exists():
                raise ValidationError({
                    'order': f'An FAQ item with order {self.order} already exists in this category.'
                })


class FaqItemImages(models.Model):
    """
    Model for FAQ item images
    """
    id = models.AutoField(primary_key=True)
    item = models.ForeignKey(
        FaqItem,
        on_delete=models.CASCADE,
        related_name='images',
        help_text='FAQ item this image belongs to'
    )
    image = models.ImageField(
        upload_to='faq_images',
        help_text='The FAQ item image'
    )
    order = models.PositiveIntegerField(
        default=0,
        help_text='Order in which images should be displayed within the item'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = '"public"."faq_item_images"'
        ordering = ['order']
        verbose_name = 'FAQ Item Image'
        verbose_name_plural = 'FAQ Item Images'

    def save(self, *args, **kwargs):
        # Auto-generate order if not provided
        if self.order == 0:
            last_image = FaqItemImages.objects.filter(item=self.item).order_by('-order').first()
            self.order = (last_image.order + 1) if last_image else 1
        super().save(*args, **kwargs)
