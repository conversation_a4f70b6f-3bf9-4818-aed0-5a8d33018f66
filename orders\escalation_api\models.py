from django.db import models
from django.utils.translation import gettext_lazy as _


class Escalation(models.Model):
    class Escalation_Status(models.TextChoices):
        CREATED = "CREATED", _("Created")

    escalation_id = models.AutoField(primary_key=True)
    order_reference = models.CharField(max_length=20, null=True, blank=True)
    store_reference = models.CharField(max_length=20, null=True, blank=True)
    user_reference = models.CharField(max_length=20, null=True, blank=True)
    suborder_reference = models.CharField(max_length=500, null=True, blank=True)
    package_reference = models.CharField(max_length=20, null=True, blank=True)
    order_status = models.CharField(max_length=30, null=True, blank=True)
    escalation_status = models.CharField(
        max_length=20,
        choices=Escalation_Status.choices,
        default=Escalation_Status.CREATED,
    )
    escalation_type = models.Char<PERSON>ield(max_length=500, null=True, blank=True)
    escalation_remark = models.Text<PERSON>ield(null=True, blank=True)
    escalation_description = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "escalation"
        db_table = '"order"."escalation"'
