# Generated by Django 3.2.13 on 2023-06-05 23:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0093_auto_20230606_0413'),
    ]

    operations = [
        migrations.AlterField(
            model_name='orderpayout',
            name='payout_amount',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='orderpayout',
            name='tds_calculated',
            field=models.FloatField(default=0),
        ),
        migrations.AlterField(
            model_name='orderpayout',
            name='transaction_fee_calculated',
            field=models.FloatField(default=0),
        ),
        migrations.AlterField(
            model_name='payoutbalance',
            name='current_balance',
            field=models.FloatField(default=0),
        ),
        migrations.AlterField(
            model_name='payoutbalance',
            name='life_time_balance',
            field=models.FloatField(default=0),
        ),
        migrations.AlterField(
            model_name='payoutbalance',
            name='missed_revenue',
            field=models.FloatField(default=0),
        ),
    ]
