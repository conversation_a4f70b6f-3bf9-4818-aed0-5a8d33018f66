from django.db import models
from django.utils.translation import gettext_lazy as _
import random
import string

class InviteUser(models.Model):

    class Invite_Type_Choices(models.TextChoices):
        NON_MEMBER = "NON_MEMBER", _("Non member")
        MEMBER = "MEMBER", _("Member")
        SELLER = "SELLER", _("Seller")
        CORPORATE_SELLER = "CORPORATE_SELLER", _("Corporate seller")
        CORPORATE_MEMBER = "CORPORATE_MEMBER", _("Corporate member")
        MEMBER_TO_SELLER = "MEMBER_TO_SELLER", _("Member to seller")

    class Invite_Status(models.TextChoices):
        CREATED = "CREATED", _("Created")
        USED = "USED", _("Used")
        CLOSED = "CLOSED", _("Closed")
        EXPIRED = "EXPIRED", _("Expired")
        DISABLED = "DISABLED", _("Disabled")

    invite_id = models.AutoField(primary_key=True)
    invite_code = models.CharField(max_length=100, unique=True)
    invite_type = models.CharField(max_length=100, choices=Invite_Type_Choices.choices)
    invited_user_role = models.CharField(max_length=100)
    invited_user = models.ForeignKey(
        "users.User", on_delete=models.CASCADE, db_column="invited_user"
    )
    number_of_invites = models.PositiveIntegerField(null=True, blank=True, default=0)
    invite_expiry_date = models.CharField(max_length=100, null=True, blank=True)
    remaining_invites = models.PositiveIntegerField(null=True, blank=True, default=0)
    phone_number = models.CharField(max_length=15, null=True, blank=True)
    invitee_name = models.CharField(max_length=25, null=True, blank=True)
    event_name = models.CharField(max_length=500, null=True, blank=True)
    event_note = models.CharField(max_length=500, null=True, blank=True)
    invite_status = models.CharField(max_length=200, choices=Invite_Status.choices, default=Invite_Status.CREATED)
    is_deleted = models.BooleanField(default=False)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "invite users"
        db_table = '"user"."invite_user"'


class InviteActivity(models.Model):
    invite_activity_id = models.AutoField(primary_key=True)
    user_id = models.ForeignKey(
        "users.User", on_delete=models.CASCADE, db_column="user_id"
    )
    invite_id = models.ForeignKey(
        "users.InviteUser", null=True, on_delete=models.CASCADE, db_column="invite_id"
    )
    invite_code = models.CharField(max_length=50, null=True, blank=True)
    invite_type = models.CharField(max_length=50, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "invite activities"
        db_table = '"user"."invite_activity"'


class InviteConfiguration(models.Model):
    configuration_id = models.AutoField(primary_key=True)
    invite_type = models.CharField(max_length=100)
    max_invite_limit = models.PositiveIntegerField()

    class Meta:
        verbose_name_plural = "invite configurations"
        db_table = '"user"."invite_configuration"'

# ################################## // New Invite Flow //  ######################################### #


class UserRewards(models.Model):
    user_point_id = models.AutoField(primary_key=True)
    user_reference = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="user_reference",
        db_column="user_reference",
        related_name="user_points"
    )
    infinity_points = models.PositiveIntegerField(default=0)
    user_affiliate_balance = models.PositiveIntegerField(default=0)
    is_auto_pay_enabled = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "user points"
        db_table = '"user"."user_points"'


class RewardsHistory(models.Model):
    class RewardTypeChoices(models.TextChoices):
        ONBOARDING_REFERRAL = "ONBOARDING_REFERRAL", _("Onboarding Referral")
        INVITE_CODE_REWARD = "INVITE_CODE_REWARD", _("Invite Code Reward")
        STORE_VERIFICATION = "STORE_VERIFICATION", _("Store Verification")
        U2U_TRANSFER = "U2U_TRANSFER", _("U2U Transfer")
        S2U_TRANSFER = "S2U_TRANSFER", _("S2U Transfer")
        RUPEE = "RUPEE", _("Rupee")

    class RewardStatusChoices(models.TextChoices):
        SUCCESS = "SUCCESS", _("Success")
        PENDING = "PENDING", _("Pending")
        FAILED = "FAILED", _("Failed")
        INVALID = "INVALID", _("Invalid")

    class TransactionTypeChoices(models.TextChoices):
        CREDIT = "CREDIT", _("Credit")
        DEBIT = "DEBIT", _("Debit")

    reward_id = models.AutoField(primary_key=True)
    reward_reference = models.CharField(max_length=10, unique=True, blank=True, null=True)  # New field
    event_reference = models.CharField(max_length=20, blank=True, null=True)
    sent_value = models.CharField(max_length=15, blank=True,null=True)
    received_value = models.CharField(max_length=15, blank=True,null=True)
    reward_ratio = models.CharField(max_length=10, blank=True, null=True)
    reward_type = models.CharField(max_length=20, blank=True, null=True, choices=RewardTypeChoices.choices)
    reward_status = models.CharField(max_length=10, blank=True,null=True, default=RewardStatusChoices.SUCCESS, choices=RewardStatusChoices.choices)
    invite_code = models.CharField(max_length=15, blank=True, null=True)
    transaction_type = models.CharField(max_length=7, blank=True, null=True, choices=TransactionTypeChoices.choices)
    sender_reference = models.CharField(max_length=20, blank=True, null=True)
    receiver_reference = models.CharField(max_length=20, blank=True, null=True)
    created_date = models.DateTimeField(auto_now_add=True)

    bank_transaction_id = models.CharField(max_length=50, blank=True, null=True)
    bank_reference_number = models.CharField(max_length=50, blank=True, null=True)
    bank_transaction_date = models.DateTimeField(blank=True, null=True)
    bank_transaction_status = models.CharField(max_length=20, blank=True, null=True)

    bank_account_number = models.CharField(max_length=50, blank=True, null=True)
    bank_account_name = models.CharField(max_length=50, blank=True, null=True)
    bank_ifsc_code = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        verbose_name_plural = "rewards histories"
        db_table = '"user"."rewards_history"'

    def generate_reward_reference(self):
        while True:
            reference = 'RW' + ''.join(random.choices(string.digits, k=8))
            if not RewardsHistory.objects.filter(reward_reference=reference).exists():
                return reference

    def save(self, *args, **kwargs):
        if not self.reward_reference:
            self.reward_reference = self.generate_reward_reference()
        super(RewardsHistory, self).save(*args, **kwargs)


