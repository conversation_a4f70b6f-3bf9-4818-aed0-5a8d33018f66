import datetime
import pandas as pd, numpy as np, json
import os
import base64
import time
import math
import json
from decimal import Decimal

from jinja2 import Template
from xhtml2pdf import pisa
from io import BytesIO
import inflect
from rest_framework import generics, mixins
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from django.template import Template, Context
from .serializers import (
    GetUserOrderItemsSerializer,
    GetstoreOrderItemsSerializer,
    AllOrderSellerSerializer,
    OrderItemsSerializer,
    GetCustomerDetailsSerializer,
    GetOrderDetailsSerializer,
    OrderLifeCycleSerializer,
    DeliveryTrackingSerializer,
    ReturnTrackingSerializer,
    ShippingUpdatesSerializer,
    OrderRatingSerializer,
    SubOrderHistorySerializer,
    LogisticPartnerSerializer,
)
import string
import random
from .models import (
    Order,
    SubOrder,
    OrderLifeCycle,
    ShippingHistory,
    RefundedAmount,
    OrderConfiguration,
    LogisticPartner,
    OrderOtp
)
from ..payout_api.models import OrderPayout, PayoutTransactions
from ..payment_api.models import SubOrderPaymentDetails
from ..helpers import (
    initiate_refund,
    get_order_request_number#,
    # update_payout_after_cancel_or_return,
)
from orders.helpers import RefundCalculations, OrderPayoutCalculations
from shared_utils.payout_utils import update_payout_after_cancel_or_return
from django.db.models import Q
from django.db.models import Sum
from itertools import groupby
from itertools import chain
from operator import itemgetter
from common.util.support_helper import GenerateNotifications
from common.util.notification_handler import NotificationHandler
from stores.store_api.models import Store
from stores.store_api.serializers import StoreFollowerSerializer
from stores.store_settings_api.models import DeliveryLocations, DeliverySettings
from stores.store_api.views import StoreDetailsAV
from stores.store_settings_api.views import GetTrustCenterAV
from stores.store_settings_api.models import (
    TrustCenter,
    RefundAndWarranty,
    DeliverySettings,
)
from users.user_api.models import User, UserAddress
from users.user_api.serializers import UserFollowerListSerializer
from users.notification_api.models import Notifications
from products.models import Product, ProductImages, ProductVersion
from products.comment_and_reply_api.models import Comment
from products.api.serializers import ProductVersionSerializer, GetProductListSerializer
from ..cart_api.models import CartItem
from ..helpers import (
    input_formatter_cart_to_fee_calculation_wrapper,
    input_formatter_order_to_fee_calculation_wrapper,
    fee_calculation_wrapper,
    update_refund_amount_old,
)
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging
import pytz
from django_pandas.io import read_frame
from django.db import transaction
from ..payment_api.utils import get_dummy_txn_token
from decouple import config
import requests
from requests.auth import HTTPBasicAuth
from ..helpers import input_formatter_cart_to_fee_calculation_wrapper, fee_calculation_wrapper
from shared_utils.order_utils import UpdateOrderStatus_single, calculate_swadesic_fee
from assets.invoice_generator_script import InvoiceGenerator
from django.core.files.storage import default_storage


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

SKIP_RAZORPAY_API = config("SKIP_RAZORPAY_API", cast=bool)
RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")
TEST_RAZORPAY_ID = config("TEST_RAZORPAY_ID")
TEST_RAZORPAY_KEY = config("TEST_RAZORPAY_KEY")


def get_refund_warranty(storeid, product):
    if RefundAndWarranty.objects.filter(
        product_reference=product, storeid=storeid, is_deleted=False
    ).exists():
        refund_warranty = RefundAndWarranty.objects.filter(
            product_reference=product, storeid=storeid, is_deleted=False
        ).last()
    else:
        refund_warranty = RefundAndWarranty.objects.filter(
            product_reference__isnull=True, storeid=storeid, is_deleted=False
        ).last()
    return refund_warranty


def calculate_refund_warranty_info(storeid, product, flag=None):
    refund_warranty = get_refund_warranty(storeid, product)
    if flag == "message":
        if (
            refund_warranty.return_type == False
            and refund_warranty.replacement_type == True
        ):
            message = (
                "replacement accepted in "
                + str(refund_warranty.return_period)
                + " days from delivery."
            )
        elif (
            refund_warranty.replacement_type == False
            and refund_warranty.return_type == True
        ):
            message = (
                "return accepted in "
                + str(refund_warranty.return_period)
                + " days from delivery."
            )
        elif (
            refund_warranty.replacement_type == False
            and refund_warranty.return_type == False
        ):
            message = "no returns accepted"
        return message
    elif flag == "refund_warranty_version":
        version = refund_warranty.refund_warranty_version
        return version

    elif flag == "refund_warranty_id":
        refund_warranty_id = refund_warranty.refundandwarrantyid
        return refund_warranty_id

    elif flag == "refund_warranty_settings":
        if refund_warranty.product_reference:
            refund_settings_type = "product_settings"
        else:
            refund_settings_type = "store_settings"
        return refund_settings_type


def create_suborder_number(order_reference):
    count = SubOrder.objects.filter(order_number=order_reference).count() + 1
    digit = str(count).zfill(2)
    my_code = (order_reference, "-", digit)
    return "".join(my_code)


def get_delivery_settings_of_product(product_reference, store_reference):
    if DeliverySettings.objects.filter(
        product_reference=product_reference,
        store_reference=store_reference,
        is_deleted=False,
    ).exists():
        delivery_settings_instance = DeliverySettings.objects.filter(
            product_reference=product_reference,
            store_reference=store_reference,
            is_deleted=False,
        ).last()
    else:
        delivery_settings_instance = DeliverySettings.objects.filter(
            store_reference=store_reference,
            product_reference__isnull=True,
            is_deleted=False,
        ).last()
    return delivery_settings_instance


def create_suborder_payment_detail(
    calculated_fee_response,
    store_id,
    product_reference,
    order_request_number,
    sub_order_reference,
    order_reference,
):
    logger.info("Entered order payment details api")
    sub_order_instance = SubOrder.objects.get(suborder_number=sub_order_reference)

    for store in calculated_fee_response:
        if store["storeid"] == store_id:

            # update order table
            store_order_instance = Order.objects.get(order_number=order_reference)
            store_order_instance.total_product_amount = store["total_product_amount"]
            store_order_instance.total_delivery_fee = store["total_delivery_fee"]
            store_order_instance.store_delivery_fee = store["store_delivery_fee"]
            store_order_instance.total_order_amount = store['total_order_amount']
            calculated_swadesic_fee = calculate_swadesic_fee(store["total_order_amount"])
            store_order_instance.swadesic_fee = calculated_swadesic_fee
            store_order_instance.expected_swadesic_fee = calculated_swadesic_fee
            store_order_instance.save()

            # OrderPayout/commission record entry
            if not (store_order_instance.order_payout_items.filter(suborder_number__isnull = True, order_type=OrderPayout.OrderType.COMMISSION).exists()):
                OrderPayout.objects.create(
                    order_number=store_order_instance,
                    order_amount=calculated_swadesic_fee,
                    order_type=OrderPayout.OrderType.COMMISSION,
                    expected_payout_amount=-calculated_swadesic_fee,
                    payout_amount=0,  # updated when total confirmed order amount > commission threshold of configuration
                    store_reference=store_order_instance.store_reference,
                    user_reference=store_order_instance.user_reference,
                    order_date=store_order_instance.created_date,
                    product_amount=0,
                    payout_status=OrderPayout.Payout_Status.WAITING_FOR_CONFIRMATION
                )

            # create a SubOrderPaymentDetails when Store delivery fee is present
            # TODO: Unify PDF & SDF into a delivery fee
            if (store["store_delivery_fee"] > 0) and not (SubOrderPaymentDetails.objects.filter(order_number = store_order_instance, suborder_number__isnull = True).exists()):
                SubOrderPaymentDetails.objects.create(
                    store_delivery_fee=0, #store["store_delivery_fee"],
                    order_number=store_order_instance,
                    order_request_number=order_request_number,
                    product_unit_price=0,
                    product_quantity=0,
                    product_amount=0,
                    product_delivery_fee=store["store_delivery_fee"],
                    store_reference=store["store_reference"],
                )


            # Create SubOrderPaymentDetails for products
            for product in store["product_split"]:
                if product["product_reference"] == product_reference:
                    SubOrderPaymentDetails.objects.create(
                        suborder_number=sub_order_instance,
                        order_number=store_order_instance,
                        order_request_number=order_request_number,
                        product_reference=product_reference,
                        product_unit_price = product['product_price'],
                        product_quantity=product["product_quantity"],
                        product_amount=product["product_price"] * product["product_quantity"],
                        product_delivery_fee=product["product_delivery_fee"],
                        store_reference=store["store_reference"],
                        store_delivery_fee=0,
                        delivery_fee_scope = product["delivery_fee_scope"]
                    )

    logger.info("Exited order payment details api")


def calculate_delivery_info(storeid, product, flag=None):
    store_instance = Store.objects.get(storeid=storeid, deleted=False)
    store_reference = store_instance.store_reference
    delivery_settings_instance = get_delivery_settings_of_product(
        product, store_reference
    )

    if flag == "estimated_delivery_date":
        delivery_time = delivery_settings_instance.time_to_deliver
        estimated_delivery_date = datetime.date.today() + datetime.timedelta(
            delivery_time + 1
        )
        estimated_delivery_date_str = estimated_delivery_date.strftime("%d-%m-%Y")
        return estimated_delivery_date_str

    elif flag == "delivery_settings_version":
        delivery_settings_version = delivery_settings_instance.delivery_settings_version
        return delivery_settings_version

    elif flag == "delivery_settings_id":
        delivery_settings_id = delivery_settings_instance.deliverysettingid
        return delivery_settings_id

    elif flag == "delivery_settings_type":
        if delivery_settings_instance.product_reference:
            delivery_settings_type = "product_settings"
        else:
            delivery_settings_type = "store_settings"
        return delivery_settings_type


class CreateAndDeleteOrder(
    mixins.CreateModelMixin, mixins.DestroyModelMixin, generics.GenericAPIView
):
    @swagger_auto_schema(
        operation_summary="create order",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "user_id",
                "cart_items",
                "billing_address_id",
                "phone_number",
                "delivery_note",
                "seller_note",
            ],
            properties={
                "userid": openapi.Schema(type=openapi.TYPE_INTEGER),
                "billing_address_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                "phone_number": openapi.Schema(type=openapi.TYPE_STRING),
                "delivery_note": openapi.Schema(type=openapi.TYPE_STRING),
                "seller_note": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(
                        type=openapi.TYPE_OBJECT,
                        required=["storeid", "note"],
                        properties={
                            "storeid": openapi.Schema(type=openapi.TYPE_INTEGER),
                            "note": openapi.Schema(type=openapi.TYPE_STRING),
                        },
                    ),
                ),
                "cart_items": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(type=openapi.TYPE_INTEGER),
                ),
            },
        ),
    )
    def get_deliverability(self, product_reference, store_reference, delivery_pincode):
        deliverability = False

        if DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
        ).exists():
            delivery_settings_instance = DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
            ).last()
        else:
            delivery_settings_instance = DeliverySettings.objects.filter(
                store_reference=store_reference, product_reference__isnull=True, is_deleted=False
            ).last()
        delivery_locations = delivery_settings_instance.delivery_locations
        if delivery_settings_instance.fulfillment_options == 'IN_STORE_PICKUP':
            return deliverability

        if delivery_locations:
            list_of_pincode = self.convert_delivery_locations_to_pincode(delivery_locations)
            if delivery_pincode in list_of_pincode:
                deliverability = True
        return deliverability

    @staticmethod
    def convert_delivery_locations_to_pincode(delivery_locations):
        queryset = DeliveryLocations.objects.all()
        '''Use python django-pandas package to filter the pincodes easily
        Read the querysets into a dataframe using read_frame method
        '''

        df_all_delivery_locations = read_frame(queryset)

        '''
        Convert the values in 'city', 'state' columns in dataframe and input 'delivery_locations' string 
        as lower case to avoid comparison issues
        '''

        df_all_delivery_locations['city'] = df_all_delivery_locations['city'].str.lower()
        df_all_delivery_locations['state'] = df_all_delivery_locations['state'].str.lower()

        list_of_locations = delivery_locations.lower().split("|")

        '''
        Filter the DataFrame (df_all_delivery_locations) to get pincodes based on specified 
        locations in either the 'state' or 'city' columns. The resulting pincodes are then converted to lists (tolist())
        '''

        state_pincodes = df_all_delivery_locations[df_all_delivery_locations.state.isin(list_of_locations)][
            'pincode'].tolist()
        city_pincodes = df_all_delivery_locations[df_all_delivery_locations.city.isin(list_of_locations)][
            'pincode'].tolist()
        input_pincodes = [x for x in list_of_locations if str(x).isnumeric()]

        ''' uncomment below line to test the lengths of each list'''

        # print([len(x) for  x  in [state_pincodes, city_pincodes, input_pincodes]])

        lst = list(set(input_pincodes + state_pincodes + city_pincodes))
        return lst

    def post(self, request, *args, **kwargs):
        """
        create an Order(order_number) first, and save all the order details(store and product details) in the OrderItem
        which will refer to the created Order.
        """
        logger.info("Entered order create api")
        userid = request.data["userid"]
        items = request.data["cart_items"]
        billing_address = UserAddress.objects.get(
            useraddressid=request.data["billing_address_id"]
        )
        delivery_pincode = billing_address.pincode
        order_phone_number = request.data["phone_number"]

        # from app settings table (OrderConfiguration) transaction fee has taken.
        order_configuration = OrderConfiguration.objects.all().first()
        transaction_fee_percentage = order_configuration.transaction_fee

        #  Calculate the fee for this order using existing fee calculation api
        final_input = input_formatter_cart_to_fee_calculation_wrapper(userid, items)
        result = fee_calculation_wrapper(final_input)
        input_for_payment_details = result[3]

        cart_item = CartItem.new_objects.filter(
            userid=userid, cartitemid__in=items
        )  # get the cart item objects
        cart_items = CartItem.new_objects.filter(
            userid=userid, cartitemid__in=items
        ).values("storeid", "product_reference", "product_quantity")
        input_data = list(cart_items)

        final_lst = {}
        for d in input_data:
            final_lst.setdefault(d["storeid"], []).append(
                [d["product_reference"], d["product_quantity"]]
            )
        final_lst = [{"storeid": k, "product_details": v} for k, v in final_lst.items()]
        print("final_lst", final_lst)
        order_request_number = get_order_request_number()
        cart_item.update(order_request_number=order_request_number)
        output = {}

        #     if not store_instance.open_for_order:
        #         closed_store.append(store_instance.storeid)
        # if closed_store:
        #     return Response(
        #         {"message": "success", "closed_store": closed_store},
        #         status=status.HTTP_200_OK,
        #     )

        closed_store = []
        deleted_store = []
        deleted_product = []
        non_deliverable_products = []

        for elem in final_lst:
            store_id = elem["storeid"]
            store_instance = Store.objects.get(storeid=store_id)
            store_reference = store_instance.store_reference
            if store_instance.deleted:
                deleted_store.append(store_instance.storeid)
            elif not store_instance.open_for_order:
                closed_store.append(store_instance.storeid)
            else:
                for product_reference in elem["product_details"]:
                    product_instance = Product.objects.get(product_reference=product_reference[0])
                    if product_instance.deleted:
                        deleted_product.append(product_reference[0])
                    elif not self.get_deliverability(product_reference[0], store_reference, delivery_pincode):
                        non_deliverable_products.append(product_reference[0])

        response_data = {"message": "success",
                         "closed_store": closed_store,
                         "deleted_store": deleted_store,
                         "deleted_product": deleted_product,
                         "non_deliverable_products": non_deliverable_products
                         }

        if closed_store or deleted_store or deleted_product or non_deliverable_products:
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            for elem in final_lst:
                store_id = elem["storeid"]
                new_order = Order.objects.create(
                    userid=userid,
                    storeid=store_id,
                    order_request_number=order_request_number,
                    billing_address_id=billing_address,
                    order_phone_number=order_phone_number,
                )

                if "delivery_note" in request.data:
                    new_order.delivery_note = request.data["delivery_note"]
                    new_order.save(update_fields=["delivery_note"])
                if "seller_note" in request.data:
                    seller_note_list = request.data["seller_note"]
                    for seller_note in seller_note_list:
                        if seller_note["storeid"] == store_id:
                            new_order.seller_note = seller_note["note"]
                            new_order.save(update_fields=["seller_note"])

                order_reference = new_order.order_number
                output.setdefault(store_id, order_reference)
                store = Store.objects.get(storeid=store_id)
                trust_center = TrustCenter.objects.get(storeid=store_id)

                for product in elem["product_details"]:
                    product_instance = Product.objects.get(product_reference=product[0])
                    product_image_instance = ProductImages.objects.filter(
                        product_reference=product_instance
                    ).first()
                    if product_image_instance:
                        image = product_image_instance.product_image
                    else:
                        image = None
                    sub_order = SubOrder.objects.create(
                        storeid=store.storeid,
                        store_name=store.store_name,
                        store_image=store.icon,
                        store_category=store.category_name,
                        store_description=store.store_desc,
                        trust_score=trust_center.trustscore,
                        seller_level=trust_center.sellerlevel,
                        return_and_warranty_description=calculate_refund_warranty_info(
                            elem["storeid"], product[0], flag="message"
                        ),
                        productid=product_instance.productid,
                        estimated_delivery_date=calculate_delivery_info(
                            elem["storeid"], product[0], flag="estimated_delivery_date"
                        ),
                        product_reference=product_instance.product_reference,
                        product_image=image,
                        product_name=product_instance.product_name,
                        product_description=product_instance.product_description,
                        product_brand=product_instance.brand_name,
                        product_quantity=product[1],
                        mrp_price=product_instance.mrp_price,
                        selling_price=product_instance.selling_price,
                        order_number=order_reference,
                        suborder_number=create_suborder_number(order_reference),
                        orderid=new_order,
                        transaction_fee=transaction_fee_percentage,
                        delivery_settings_version=calculate_delivery_info(
                            elem["storeid"],
                            product[0],
                            flag="delivery_settings_version",
                        ),
                        delivery_settings_type=calculate_delivery_info(
                            elem["storeid"], product[0], flag="delivery_settings_type"
                        ),
                        delivery_settings_id=calculate_delivery_info(
                            elem["storeid"], product[0], flag="delivery_settings_id"
                        ),
                        refund_warranty_version=calculate_refund_warranty_info(
                            elem["storeid"], product[0], flag="refund_warranty_version"
                        ),
                        refund_warranty_settings_type=calculate_refund_warranty_info(
                            elem["storeid"], product[0], flag="refund_warranty_settings"
                        ),
                        refund_warranty_id=calculate_refund_warranty_info(
                            elem["storeid"], product[0], flag="refund_warranty_id"
                        ),
                    )
                    product_reference = product_instance.product_reference
                    sub_order_reference = sub_order.suborder_number
                    # create an entry for order payment details every time suborder created
                    create_order_payment_detail(
                        input_for_payment_details,
                        store_id,
                        product_reference,
                        order_request_number,
                        sub_order_reference,
                        order_reference,
                    )

            output = [{"store_id": k, "order_reference": v} for k, v in output.items()]
            logger.info("Exited order create api")
            return Response(
                {
                    "message": "success",
                    "order_request_number": order_request_number,
                    "order_references": output,
                },
                status=status.HTTP_200_OK,
            )

    @swagger_auto_schema(operation_summary="Delete order")
    def delete(self, request, *args, **kwargs):

        """
        when deleting an order make sure to delete the sub orders also. here instead of hard delete we are using
        soft delete, there for updating the is_delete field to True to mark that order and suborders as deleted.
        """
        order_number = self.kwargs["ordernumber"]
        cancellation_reason = request.data["cancellation_reason"]
        cancelled_by = request.data["cancelled_by"]

        order_instance = Order.new_objects.filter(
            order_number=order_number
        )  # filter the order corresponding to the order_number
        order_instance.update(
            is_deleted=True,
            order_status=Order.Order_Status.ORDER_CANCELLED,
            cancellation_reason=cancellation_reason,
            cancelled_by=cancelled_by,
        )

        order_items = SubOrder.new_objects.filter(
            order_number=order_number
        )  # filter all the suborder corresponding to
        # the order and mark is_deleted as True.
        for order in order_items:
            order.suborder_status = SubOrder.Suborder_Status.ORDER_CANCELLED
            order.is_deleted = True
            order.cancellation_reason = cancellation_reason
            order.cancelled_by = cancelled_by
            order.save(
                update_fields=[
                    "suborder_status",
                    "is_deleted",
                    "cancellation_reason",
                    "cancelled_by",
                ]
            )
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class DeleteSubOrders(mixins.DestroyModelMixin, generics.GenericAPIView):
    """
    single order contains suborders, for deleting suborders use this api. it will take suborder_number in url
    and for that suborder make is_deleted field as true to keep it as deleted, then if all the suborder of
    that order has changed to delete, then mark the order also as deleted.
    """

    @swagger_auto_schema(operation_summary="Delete sub order")
    def delete(self, request, *args, **kwargs):
        suborder_number = self.kwargs["subordernumber"]
        cancellation_reason = request.data["cancellation_reason"]
        cancelled_by = request.data["cancelled_by"]
        order_number = suborder_number.split("-")[
            0
        ]  # split suborder number by '-' and get the 0th index value,
        # that is order_number

        order_items = SubOrder.new_objects.filter(
            suborder_number=suborder_number
        )  # filter the order items
        for order in order_items:
            order.suborder_status = SubOrder.Suborder_Status.ORDER_CANCELLED
            order.is_deleted = True
            order.cancellation_reason = cancellation_reason
            order.cancelled_by = cancelled_by
            order.save()
        order = SubOrder.new_objects.filter(order_number=order_number).values_list(
            "is_deleted", flat=True
        )  # get list of
        # is_deleted values for the order

        if all(
            order
        ):  # if true, that means all the suborders in that order is set as deleted.
            orders = Order.objects.filter(
                order_number=order_number
            )  # then get that order
            orders.update(
                is_deleted=True,
                order_status=Order.Order_Status.ORDER_CANCELLED,
                cancellation_reason=cancellation_reason,
                cancelled_by=cancelled_by,
            )  # update that order as deleted
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetOrderDetailsByUser(mixins.ListModelMixin, generics.GenericAPIView):
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(operation_summary="Get order details of a user")
    def get(self, request, order_number=None, *args, **kwargs):
        userid = self.kwargs["userid"]
        if order_number:
            try:
                order = Order.objects.filter(
                    Q(userid=userid)
                    & Q(order_number=order_number)
                    & ~Q(order_status=Order.Order_Status.ORDER_INITIATED)
                    & ~Q(order_status=Order.Order_Status.PAYMENT_INITIATED)
                ).order_by("-created_date")
            except:
                return Response(
                    {"message": "error"}, status=status.HTTP_400_BAD_REQUEST
                )
            serializer = GetUserOrderItemsSerializer(order, many=True)
        else:
            try:
                order = Order.objects.filter(
                    Q(userid=userid)
                    & ~Q(order_status=Order.Order_Status.ORDER_INITIATED)
                    & ~Q(order_status=Order.Order_Status.PAYMENT_INITIATED)
                ).order_by("-created_date")
            except:
                return Response(
                    {"message": "error"}, status=status.HTTP_400_BAD_REQUEST
                )
            # serializer = GetUserOrderItemsSerializer(order, many=True)
            order_items_eager_loading = AllOrderSellerSerializer.setup_eager_loading(
                order
            )
            serializer = AllOrderSellerSerializer(order_items_eager_loading, many=True)

        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class GetOrderDetailsByStore(mixins.ListModelMixin, generics.GenericAPIView):

    parser_classes = [MultiPartParser, FormParser]
    serializer_class = GetstoreOrderItemsSerializer

    @swagger_auto_schema(operation_summary="Get order details of a Store")
    def get(self, request, *args, **kwargs):

        # start = time.time()
        # logger.info("started %s", start)
        store_id = self.kwargs["storeid"]
        logger.info("store_id assigned: %s", store_id)
        order_number = self.kwargs.get("order_number")
        logger.info("order_number assigned: %s", order_number)
        status_list = [
            SubOrder.Suborder_Status.PAYMENT_INITIATED,
            SubOrder.Suborder_Status.PAYMENT_FAILED,
            SubOrder.Suborder_Status.PAYMENT_PENDING,
            SubOrder.Suborder_Status.ORDER_INITIATED,
        ]
        # try:
        if order_number:
            order_items = (
                Order.objects.filter(
                    Q(storeid=store_id)
                    & Q(order_number=order_number)
                    & ~Q(order_status__in=status_list)
                )
                .select_related("user_reference")
                .order_by("-created_date")
            )
            logger.info("order_items assigned for order_number: %s", order_items)
            order_items_eager_loading = (
                GetstoreOrderItemsSerializer.setup_eager_loading(order_items)
            )  # Set up eager loading to avoid N+1 selects
            serializer = GetstoreOrderItemsSerializer(
                order_items_eager_loading, many=True
            ).data
            logger.info("serializer assigned for order_number: %s", serializer)

        else:
            order_items = Order.objects.filter(
                Q(storeid=store_id) & ~Q(order_status__in=status_list)
            ).order_by("-created_date")
            logger.info("order_items assigned without order_number: %s", order_items)
            order_items_eager_loading = AllOrderSellerSerializer.setup_eager_loading(
                order_items
            )
            serializer = AllOrderSellerSerializer(
                order_items_eager_loading, many=True
            ).data
            logger.info("serializer assigned without order_number: %s", serializer)

        # except:
        #     return Response({"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # serializer = GetstoreOrderItemsSerializer(order_items, many=True)
        # order_items_eager_loading = GetstoreOrderItemsSerializer.setup_eager_loading(order_items)  # Set up eager loading to avoid N+1 selects
        # serializer = GetstoreOrderItemsSerializer(order_items_eager_loading, many=True).data

        # end = time.time()
        # diff = end - start
        # logger.info("ended %s", end)
        # logger.info("time taken in terminal %s", diff)
        return Response(
            {"message": "success", "data": serializer}, status=status.HTTP_200_OK
        )


class CreateDisplayPackageNumber(generics.CreateAPIView):
    """
    Generates a new display package number for an order based on the specified packaging type.

    This API view is responsible for creating a new display package number for an order, either for a delivery or a return package. It checks the existing display package numbers for the order and generates a new unique package name.

    Args:
        order_number (str): The order number for which to generate the display package number.
        packaging_type (str): The type of packaging, either "DELIVERY" or "RETURN".

    Returns:
        A JSON response containing the generated display package number.
    """
    def post(self, request, *args, **kwargs):
        order_number = request.data["order_number"]
        packaging_type = request.data["packaging_type"]

        suborder_count = SubOrder.objects.filter(order_number=order_number).count()
        list_of_numbers = list(range(1, suborder_count + 1))
        list_of_package_names = ["PKG" + str(i) for i in list_of_numbers]

        if packaging_type == "DELIVERY":
            existing_package_names = list(
                SubOrder.objects.values_list(
                    "display_package_number", flat=True
                ).filter(order_number=order_number)
            )
            new_package_name = list(
                set(list_of_package_names).difference(set(existing_package_names))
            )[0]
            return Response(
                {"message": "success", "display_package_number": new_package_name}
            )

        elif packaging_type == "RETURN":
            existing_return_package_names = list(
                SubOrder.objects.values_list(
                    "display_return_package_number", flat=True
                ).filter(order_number=order_number)
            )
            new_package_name = list(
                set(list_of_package_names).difference(
                    set(existing_return_package_names)
                )
            )[0]
            return Response(
                {
                    "message": "success",
                    "display_return_package_number": new_package_name,
                }
            )


def create_package_number(packaging_type=None):
    now = datetime.datetime.now()
    code = now.strftime("%y%m%d%H%M%S")
    N = 3
    random_string = "".join(random.choices(string.ascii_uppercase, k=N))
    if packaging_type == "RETURN":
        my_code = ("R", code, random_string)
    elif packaging_type == "DELIVERY":
        my_code = ("P", code, random_string)
    return "".join(my_code)


class OrderPackaging(APIView):
    @swagger_auto_schema(
        operation_summary="order packaging",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["sub_order_reference"],
            properties={
                "sub_order_reference": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        sub_order_reference = request.data["sub_order_reference"]
        instance = SubOrder.new_objects.filter(suborder_number__in=sub_order_reference)
        created_package_number = create_package_number()
        instance.update(package_number=created_package_number)
        serializer = OrderItemsSerializer(instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


def update_order_package(value):
    suborder_numbers = value["suborder_number"]
    package_numbers = value["package_number"]
    display_package_number = value["display_package_number"]
    suborder = SubOrder.new_objects.filter(suborder_number=suborder_numbers)
    if package_numbers.startswith("P"):
        suborder.update(
            package_number=package_numbers,
            display_package_number=display_package_number,
            suborder_status=SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS,
        )
    elif package_numbers.startswith("R"):
        suborder.update(
            return_package_number=package_numbers,
            display_return_package_number=display_package_number,
            suborder_status=SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
        )

# TODO : update reset status notification to buyer


def remove_order_package(value):
    suborder_numbers = value["suborder_number"]
    package_numbers = value["package_number"]
    suborder = SubOrder.new_objects.filter(suborder_number=suborder_numbers)
    if package_numbers.startswith("P"):
        suborder.update(
            package_number=None,
            display_package_number=None,
            suborder_status=SubOrder.Suborder_Status.ORDER_CONFIRMED,
        )
        suborder_instance = suborder[0]

        if suborder_instance.self_delivery_by_store:
            suborder_instance.self_delivery_by_store = False
            suborder_instance.delivery_person_name = None
            suborder_instance.delivery_person_contact = None
            suborder_instance.additional_notes = None

            suborder_instance.save(
                update_fields=[
                    "self_delivery_by_store",
                    "delivery_person_name",
                    "delivery_person_contact",
                    "additional_notes",
                ]
            )

        elif suborder_instance.delivery_by_logistic_partner:
            suborder_instance.delivery_by_logistic_partner = False
            suborder_instance.logistic_partner = None
            suborder_instance.tracking_number = None
            suborder_instance.tracking_link = None
            suborder_instance.additional_notes = None

            suborder_instance.save(
                update_fields=[
                    "delivery_by_logistic_partner",
                    "logistic_partner",
                    "tracking_number",
                    "tracking_link",
                    "additional_notes",
                ]
            )

    elif package_numbers.startswith("R"):
        suborder.update(
            return_package_number=None,
            display_return_package_number=None,
            suborder_status=SubOrder.Suborder_Status.RETURN_CONFIRMED,
        )
        suborder_instance = suborder[0]

        if suborder_instance.self_return_by_store:
            suborder_instance.self_return_by_store = False
            suborder_instance.return_person_name = None
            suborder_instance.return_person_contact = None
            suborder_instance.additional_return_notes = None

            suborder_instance.save(
                update_fields=[
                    "self_return_by_store",
                    "return_person_name",
                    "return_person_contact",
                    "additional_return_notes",
                ]
            )

        elif suborder_instance.return_by_logistic_partner:
            suborder_instance.return_by_logistic_partner = False
            suborder_instance.return_pickup_logistic_partner = None
            suborder_instance.return_tracking_number = None
            suborder_instance.return_tracking_link = None
            suborder_instance.additional_return_notes = None
            suborder_instance.return_estimated_delivery_date = None

            suborder_instance.save(
                update_fields=[
                    "return_by_logistic_partner",
                    "return_pickup_logistic_partner",
                    "return_tracking_number",
                    "return_tracking_link",
                    "additional_return_notes",
                    "return_estimated_delivery_date"
                ]
            )


class UpdateAndDeleteOrderPackage(APIView):
    def post(self, request, *args, **kwargs):

        suborder_number = request.data["data"]
        order_number = suborder_number[0]["suborder_number"].split("-")[0]
        suborder_instance = SubOrder.objects.filter(
            suborder_number=suborder_number[0]["suborder_number"]).first()
        order_instance = Order.objects.get(order_number=order_number)
        if request.data["flag"] == "update":
            list(map(update_order_package, suborder_number))
            notification_handler = NotificationHandler(
                notified_user=order_instance.user_reference.user_reference,
                notification_about=suborder_instance.order_number,
                notes=suborder_instance.suborder_number,
                notification_type=Notifications.Notifications_Type.PRODUCT_UPDATED_FROM_SHIPPING_GROUP,
                image=suborder_instance.product_image
            )
            notification_handler.create_notification(notification_handler)
        elif request.data["flag"] == "delete":
            list(map(remove_order_package, suborder_number))
            if suborder_number[0]["package_number"].startswith("P"):
                title = "Order status reset to Confirmed "
                shipping_history_description = "Seller reset the Order to Confirmed Status"
            if suborder_number[0]["package_number"].startswith("R"):
                title = "Order status reset to Return requested"
                shipping_history_description = "Seller reset the Order status to Return requested"

            ShippingHistory.objects.create(
                title=title,
                description=shipping_history_description,
                order_number=order_instance,
                shipping_reference=suborder_number[0]["package_number"],
                is_editable=False,
            )
            # create a notification for the user to let him know that the product has been removed from the cart
            notification_handler = NotificationHandler(
                notified_user=order_instance.user_reference.user_reference,
                notification_about=suborder_instance.order_number,
                notes=suborder_instance.suborder_number,
                notification_type=Notifications.Notifications_Type.PRODUCT_REMOVED_FROM_SHIPPING_GROUP,
                image=suborder_instance.product_image
            )
            notification_handler.create_notification(notification_handler)

        return Response({"message": "success"}, status=status.HTTP_200_OK)


class UpdateOrderStatus(APIView):
    # def post_old_changed(self, request, *args, **kwargs):
    #     """for any updates of an order uses this api. Only the input varies."""
    #     suborder_lists = request.data["suborder_list"]
    #
    #     for suborder_list in suborder_lists:
    #         suborder_number = suborder_list["suborder_number"]
    #         suborder_instance = SubOrder.objects.get(suborder_number=suborder_number)
    #         suborder_instance.suborder_status = suborder_list["status"]
    #         suborder_instance.save(update_fields=["suborder_status"])
    #
    #     # To get the order_number
    #     suborder_number = suborder_lists[0]["suborder_number"]
    #
    #     # from suborder number split and take order_number
    #     order_number = suborder_number.split("-")[0]
    #
    #     if suborder_lists[0]["status"] == SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS:
    #         created_package_number = create_package_number(packaging_type="DELIVERY")
    #         suborder_lists = [
    #             dict(item, **{"package_number": created_package_number})
    #             for item in suborder_lists
    #         ]
    #         list(map(update_order_status, suborder_lists))
    #         (
    #             deliverymethod_self,
    #             logistics,
    #             name,
    #             phone,
    #             partner,
    #             tracking_link,
    #         ) = get_default_delivery_details(created_package_number)
    #
    #         suborder = SubOrder.new_objects.get(
    #             suborder_number=suborder_number, package_number=created_package_number
    #         )
    #       missing? save
    #         return Response(
    #             {
    #                 "message": "success",
    #                 "self_delivery_by_store": deliverymethod_self,
    #                 "delivery_by_logistic_partner": logistics,
    #                 "delivery_person_name": name,
    #                 "delivery_person_contact": phone,
    #                 "logistic_partner": partner,
    #                 "tracking_link": tracking_link,
    #                 "display_package_number": suborder.display_package_number,
    #                 "package_number": created_package_number,
    #             },
    #             status=status.HTTP_200_OK,
    #         )
    #
    #     elif suborder_lists[0]["status"] == "RETURN_IN_PROGRESS":
    #         created_return_package_number = create_package_number(
    #             packaging_type="RETURN"
    #         )
    #         suborder_lists = [
    #             dict(item, **{"return_package_number": created_return_package_number})
    #             for item in suborder_lists
    #         ]
    #         list(map(update_order_status, suborder_lists))
    #         return Response(
    #             {
    #                 "message": "success",
    #                 "display_return_package_number": None,
    #                 "return_package_number": created_return_package_number,
    #             },
    #             status=status.HTTP_200_OK,
    #         )
        # elif suborder_lists[0]["status"] == "ORDER_DELIVERED":
        #     suborder = SubOrder.new_objects.get(suborder_number=suborder_number)

        #     if not OrderOtp.objects.filter(
        #             package_number=suborder.package_number,
        #             user_reference=suborder.user_reference,
        #             otp_status=OrderOtp.Status.USED
        #     ).exists():
        #         return Response({"message": "Delivery OTP is not Verified"}, status=status.HTTP_200_OK)

        #     list(map(update_order_status, suborder_lists))

        #     order = Order.new_objects.filter(order_number=order_number).first()
        #     ordered_user = order.user_reference.user_reference
        #     notification_handler = NotificationHandler(
        #         notified_user=ordered_user,
        #         notification_type=Notifications.Notifications_Type.PACKAGE_DELIVERED,
        #         notification_about=order_number,
        #         image=suborder.store_image,
        #         package_number=suborder.package_number,
        #     )
        #     notification_handler.create_notification(notification_handler)
        #     return Response({"message": "success"}, status=status.HTTP_200_OK)
    #     else:
    #         list(map(update_order_status, suborder_lists))
    #         return Response({"message": "success"}, status=status.HTTP_200_OK)

    @swagger_auto_schema(operation_summary="update order status.")
    def post(self, request, *args, **kwargs):
        """for any updates of an order uses this api. Only the input varies."""
        suborder_dicts = request.data["suborder_list"]

        # Grouping the suborder_dicts
        grouped_suborder_dicts = {}
        for suborder in suborder_dicts:
            status = suborder["status"]
            if status not in grouped_suborder_dicts:
                grouped_suborder_dicts[status] = []
            grouped_suborder_dicts[status].append(suborder)

        # Based on statuses we change our suborder processing
        for status, suborder_list in grouped_suborder_dicts.items():
            self.suborder_list = suborder_list
            if status == "DELIVERY_IN_PROGRESS":
                suborder = SubOrder.new_objects.filter(suborder_number=self.suborder_list[0]['suborder_number']).first()
                if not suborder.package_number:
                    package_number = create_package_number(packaging_type="DELIVERY")
                    self.update_order_statuses(package_number=package_number)
                    return Response({"message": "success", "package_number": package_number},status=200)
                else:
                    self.update_order_statuses()
                    return Response({"message": "success"},status=200)
            elif status == "ORDER_DELIVERED":
                first_suborder = SubOrder.new_objects.get(suborder_number=self.suborder_list[0]['suborder_number'])
                if not OrderOtp.objects.filter(
                    package_number=first_suborder.package_number,
                    user_reference=first_suborder.user_reference,
                    otp_status=OrderOtp.Status.USED).exists() and not first_suborder.logistic_partner:
                    return Response({"message": "Delivery OTP is not Verified"}, status=200)
                self.update_order_statuses()
                self.suborder = first_suborder
                self.notify(notified_entity='USER',
                            notification_type=Notifications.Notifications_Type.PACKAGE_DELIVERED,
                            package_number=first_suborder.package_number)

            elif status == "RETURN_IN_PROGRESS":
                return_package_number = create_package_number(packaging_type="RETURN")
                self.update_order_statuses(return_package_number=return_package_number)
                return Response({"message": "success",
                                 "display_return_package_number": None,
                                 "return_package_number": return_package_number},status=200)

            else:
                self.update_order_statuses()

        return Response({"message": "success"}, status=200)

    def update_order_statuses(self, **kwargs):
        for suborder in self.suborder_list:
            update_order_status_single = UpdateOrderStatus_single(**suborder, **kwargs)
            update_order_status_single.update_order_status()

    def notify(self, notified_entity, notification_type, **kwargs):  # TODO: need to verify if this notification is happening everytime

        if notified_entity == 'USER':
            notified_entity_reference = self.suborder.user_reference
        elif notified_entity == 'STORE':
            notified_entity_reference = self.suborder.store_reference
        else:
            notified_entity_reference = ""
        notification_handler = NotificationHandler(
            notified_user=notified_entity_reference,
            notification_type=notification_type,
            notification_about=self.suborder.order_number,
            image=self.suborder.product_image,
            **kwargs
        )
        notification_handler.create_notification(notification_handler)


def get_default_delivery_details(package_number):
    instance = SubOrder.new_objects.filter(package_number=package_number).first()
    product_reference = instance.product_reference
    store_reference = instance.store_reference
    delivery_setting_instance = get_delivery_settings_of_product(
        product_reference, store_reference
    )
    deliverymethod_self = delivery_setting_instance.deliverymethod_self
    deliverymethod_logistics = delivery_setting_instance.deliverymethod_logistics
    delivery_personal_name = delivery_setting_instance.delivery_personal_name
    delivery_personal_phone = delivery_setting_instance.delivery_personal_phone
    default_logistic_partner = delivery_setting_instance.default_logistic_partner
    delivery_tracking_link = delivery_setting_instance.delivery_tracking_link
    return (
        deliverymethod_self,
        deliverymethod_logistics,
        delivery_personal_name,
        delivery_personal_phone,
        default_logistic_partner,
        delivery_tracking_link,
    )


class UpdateDeliveryTrackingDetails(generics.RetrieveUpdateAPIView):
    serializer_class = OrderItemsSerializer
    lookup_field = "package_number"

    @swagger_auto_schema(
        operation_summary="update delivery tracking details",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "delivery_person_name",
                "estimated_delivery_date",
                "delivery_person_contact",
                "tracking_link",
            ],
            properties={
                "delivery_person_name": openapi.Schema(type=openapi.TYPE_STRING),
                "estimated_delivery_date": openapi.Schema(type=openapi.TYPE_STRING),
                "delivery_person_contact": openapi.Schema(type=openapi.TYPE_STRING),
                "tracking_link": openapi.Schema(type=openapi.TYPE_STRING),
                "display_package_number": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def partial_update(self, request, package_number, toNotify=None, *args, **kwargs, ):

        partial = kwargs.pop("partial", True)
        instances = self.get_queryset(package_number)
        one_suborder_instance = instances.first()
        changed_fields = []
        delivery_person_name = one_suborder_instance.delivery_person_name
        delivery_person_contact = one_suborder_instance.delivery_person_contact
        logistic_partner = one_suborder_instance.logistic_partner
        tracking_link = one_suborder_instance.tracking_link
        display_package_number = one_suborder_instance.display_package_number
        estimated_delivery_date = one_suborder_instance.estimated_delivery_date
        if "delivery_person_name" in request.data:
            if delivery_person_name != request.data["delivery_person_name"]:
                changed_fields.append("delivery_person_name")
        if "delivery_person_contact" in request.data:
            if delivery_person_contact != request.data["delivery_person_contact"]:
                changed_fields.append("delivery_person_contact")
        if "logistic_partner" in request.data:
            if logistic_partner != request.data["logistic_partner"]:
                changed_fields.append("logistic_partner")
        if "tracking_link" in request.data:
            if tracking_link != request.data["tracking_link"]:
                changed_fields.append("tracking_link")
        if "display_package_number" in request.data:
            if display_package_number != request.data["display_package_number"]:
                changed_fields.append("display_package_number")
        if "estimated_delivery_date" in request.data:
            if estimated_delivery_date != request.data["estimated_delivery_date"]:
                changed_fields.append("estimated_delivery_date")

        logger.info(f"changed_fields:{changed_fields}")

        if changed_fields:
            if not all([delivery_person_name,delivery_person_contact,display_package_number]):
                #This means that this api is being called for the first time for an update
                # for that we do not need to send a notification
                toNotify = False
            else:
                toNotify = True
            if ShippingHistory.objects.filter(
                shipping_reference=package_number
            ).exists():

                title = "Shipping updated"
                shipping_history_description = ",".join(changed_fields) + " got updated"
            else:
                shipping_history_description = "Shipping started"
                title = "Shipping started"

            order_number = Order.new_objects.get(
                order_number=one_suborder_instance.order_number
            )
            ShippingHistory.objects.create(
                title=title,
                description=shipping_history_description,
                order_number=order_number,
                shipping_reference=one_suborder_instance.package_number,
                is_editable=False,
            )
            try:
                # Get data for sending a shipping update notification
                ordered_user = order_number.user_reference.user_reference
                package_name = request.data["display_package_number"] if request.data.get("display_package_number") else display_package_number
                image = one_suborder_instance.store_image

                notification_handler = NotificationHandler(
                    notified_user=ordered_user,
                    notification_type=Notifications.Notifications_Type.SHIPPING_PACAKGE_UPDATED,
                    notification_about=order_number.order_number,
                    image=image,
                    package_name=package_name,
                )
                if toNotify:
                    notification_handler.create_notification(notification_handler)
            except:
                logger.info("shipping update notification failed")

        for instance in instances:
            serializer = self.get_serializer(
                instance, data=request.data, partial=partial
            )
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            if OrderLifeCycle.objects.filter(
                package_number=package_number,
                suborder_status=SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS,
            ).exists():
                order_lifecycle = OrderLifeCycle.objects.filter(
                    package_number=package_number,
                    suborder_status=SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS,
                )
                for order in order_lifecycle:
                    serializer = OrderLifeCycleSerializer(
                        order, data=request.data, partial=partial
                    )
                    serializer.is_valid(raise_exception=True)
                    self.perform_update(serializer)
        return Response({"message": "success"}, status=status.HTTP_200_OK)

    def get(self, request, package_number, *args, **kwargs):
        instance = self.get_queryset(package_number).first()
        serializer = DeliveryTrackingSerializer(instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def get_queryset(self, package_number):
        return SubOrder.objects.filter(package_number=package_number)


class UpdateReturnTrackingDetails(generics.UpdateAPIView):
    serializer_class = OrderItemsSerializer
    lookup_field = "return_package_number"

    def get_queryset(self, return_package_number):
        return SubOrder.objects.filter(return_package_number=return_package_number)

    def partial_update(self, request, return_package_number, *args, **kwargs):
        partial = kwargs.pop("partial", True)
        instances = self.get_queryset(return_package_number)
        changed_fields = []
        one_suborder_instance = instances.first()
        return_person_name = one_suborder_instance.return_person_name
        return_person_contact = one_suborder_instance.return_person_contact
        return_pickup_logistic_partner = (
            one_suborder_instance.return_pickup_logistic_partner
        )
        return_tracking_link = one_suborder_instance.return_tracking_link
        if "return_person_name" in request.data:
            if return_person_name != request.data["return_person_name"]:
                changed_fields.append("delivery_person_name")
        if "return_person_contact" in request.data:
            if return_person_contact != request.data["return_person_contact"]:
                changed_fields.append("delivery_person_contact")
        if "return_pickup_logistic_partner" in request.data:
            if (
                return_pickup_logistic_partner
                != request.data["return_pickup_logistic_partner"]
            ):
                changed_fields.append("logistic_partner")
        if "return_tracking_link" in request.data:
            if return_tracking_link != request.data["return_tracking_link"]:
                changed_fields.append("tracking_link")

        logger.info(changed_fields)

        if changed_fields:
            if ShippingHistory.objects.filter(
                shipping_reference=return_package_number
            ).exists():
                title = "Return updated"
                shipping_history_description = ",".join(changed_fields) + " got updated"
            else:
                shipping_history_description = "Return started"
                title = "Return started"
            order_number = Order.new_objects.get(
                order_number=one_suborder_instance.order_number
            )
            ShippingHistory.objects.create(
                title=title,
                description=shipping_history_description,
                order_number=order_number,
                shipping_reference=one_suborder_instance.return_package_number,
                is_editable=False,
            )

        for instance in instances:
            serializer = self.get_serializer(
                instance, data=request.data, partial=partial
            )
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            if OrderLifeCycle.objects.filter(
                return_package_number=return_package_number,
                suborder_status="RETURN_IN_PROGRESS",
            ).exists():
                order_lifecycle = OrderLifeCycle.objects.filter(
                    return_package_number=return_package_number,
                    suborder_status="RETURN_IN_PROGRESS",
                )
                for order in order_lifecycle:
                    serializer = OrderLifeCycleSerializer(
                        order, data=request.data, partial=partial
                    )
                    serializer.is_valid(raise_exception=True)
                    self.perform_update(serializer)
        return Response({"message": "success"}, status=status.HTTP_200_OK)

    def get(self, request, return_package_number, *args, **kwargs):
        instance = self.get_queryset(return_package_number).first()
        serializer = ReturnTrackingSerializer(instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class GetCustomerDetails(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="customer details of single order")
    def get(self, request, *args, **kwargs):
        """Get customer details of single order in seller side."""
        user_id = kwargs["user_id"]
        store_reference = request.query_params.get('store_reference')
        user_instance = User.objects.get(userid=user_id,deleted=False)
        user_reference = user_instance.user_reference
        order_number = kwargs["order_number"]
        order_instance = Order.objects.get(user_reference=user_reference, order_number=order_number)
        serializer = GetCustomerDetailsSerializer(order_instance, context={"store_reference":store_reference})

        # user_instance = User.objects.get(userid=user_id)
        # user_follower_reference_list = user_instance.following.filter(is_following=True).values_list('user_follower', flat=True)
        # store_follower_reference_list = user_instance.following.filter(is_following=True).values_list('store_follower',flat=True)
        #
        # userfollowers = User.objects.filter(user_reference__in=user_follower_reference_list)
        # storefollowers = Store.objects.filter(store_reference__in=store_follower_reference_list)
        #
        # userfollowers_serializer = UserFollowerListSerializer(userfollowers, many=True)
        # storefollowers_serializer = StoreFollowerSerializer(storefollowers, many=True)
        #
        # user_following_reference_list = user_instance.userfollower.filter(is_following=True).values_list('user_reference', flat=True)
        # store_following_reference_list = user_instance.usersupporter.filter(is_following=True).values_list('store_reference', flat=True)
        #
        # user_following = User.objects.filter(user_reference__in=user_following_reference_list)
        # store_following = Store.objects.filter(store_reference__in=store_following_reference_list)
        #
        # userfollowing_serializer = UserFollowerListSerializer(user_following, many=True)
        # storefollowing_serializer = StoreFollowerSerializer(store_following,many=True)

        # followers = User.objects.filter(user_reference__in=follower_reference_list)
        # followers_serializer = UserFollowerListSerializer(followers, many=True)
        #
        # following_reference_list = user_instance.follower.filter(
        #     is_following=True
        # ).values_list("user_reference", flat=True)
        # following = User.objects.filter(user_reference__in=following_reference_list)
        # following_serializer = UserFollowerListSerializer(following, many=True)
        return Response(

            {
                "message": "success",
                "data": serializer.data,
                # "followers": {"users": userfollowers_serializer.data,
                #               "stores": storefollowers_serializer.data},
                # "following": {"users": userfollowing_serializer.data,
                #               "stores": storefollowing_serializer.data}
            },
            status=status.HTTP_200_OK,
        )


def key_func(k):
    return k["product_delivery_fee"]


class OrderFeesDetails(generics.RetrieveAPIView):
    @staticmethod
    def output_formatter(text, subtext, value):
        return {
            "order_breakup_item_text": text,
            "order_breakup_item_subtext": subtext,
            "order_breakup_item_value": value,
        }

    @staticmethod
    def add_rupee_sign(num):
        num_to_string = str(num)
        result = "\u20B9" "{}".format(num_to_string)
        return result

    def get(self, request, is_seller=None, *args, **kwargs):
        """Get order details of single order in seller side."""
        logger.info("Entered get order details of an order seller side")
        order_number = kwargs["order_number"]

        suborder_count = SubOrder.objects.filter(order_number=order_number).count()

        # get all the currently available payment details of an order from order payment details table, if a product of
        # order has cancelled, then to avoid that product we are using the flag is_suborder_deleted

        order_payment_instance = SubOrderPaymentDetails.objects.filter(
            order_number=order_number, suborder_number__isnull=False
        )
        serializer = GetOrderDetailsSerializer(order_payment_instance, many=True)

        serializer_copy = serializer.data  # list

        # group the serialized data; products with store level fee are grouped together, others grouped separately
        serializer_copy = sorted(serializer_copy, key=key_func)
        grouped_dict = {}  # product details of active suborders
        count = 1
        for key, value in groupby(serializer_copy, key_func):
            key = "group" + str(count)
            grouped_dict[key] = list(value)
            count += 1

        # total product cost and breakups of that particular order
        list_of_order_amounts = list(
            SubOrderPaymentDetails.objects.values_list("product_amount", flat=True).filter(
                order_number=order_number
            ).exclude(suborder_number__isnull=True)
        )
        total_product_cost_breakup = " + ".join(
            map(self.add_rupee_sign, list_of_order_amounts)
        )
        total_product_cost = sum(list_of_order_amounts)
        total_product_cost_str = self.add_rupee_sign(total_product_cost)

        list_of_order_df = list(
            SubOrderPaymentDetails.objects.values_list("product_delivery_fee", flat=True).filter(
                order_number=order_number
            ).exclude(suborder_number__isnull=True)
        )
        # to calculate total shipping fees and shipping fees breakups, have to look into some more settings.

        # if list_of_order_df has 0 , it indicates that order has products with store level delivery fee.
        # for all the products with store level there is a common shipping fee only.
        # so replace the first occurrence of zero (delivery fee is zero for products which store level delivery fee.)
        # with store order df

        list_of_store_df_amount = [0]
        if 0 in list_of_order_df:
            list_of_store_df_amount = list(
                SubOrderPaymentDetails.objects.values_list("product_delivery_fee", flat=True).filter(
                    order_number=order_number, suborder_number__isnull=True
                )
            )

        shipping_fee_breakup = " + ".join(
            map(self.add_rupee_sign, list_of_order_df)
        ) + " + " + f"({self.add_rupee_sign(sum(list_of_store_df_amount))}))"

        shipping_fee = sum(list_of_order_df) + sum(list_of_store_df_amount)
        shipping_fee_str = self.add_rupee_sign(shipping_fee)

        grand_total = total_product_cost + shipping_fee
        grand_total_str = self.add_rupee_sign(grand_total)

        deleted_order_payment_instance = SubOrderPaymentDetails.objects.filter(
            order_number=order_number, is_suborder_deleted=True
        )

        deleted_suborders = SubOrder.objects.filter(order_number=order_number, is_deleted=True)

        deleted_order_payout_list = list(
            OrderPayout.objects.filter(
                suborder_number__in=deleted_suborders
            ).values_list("transaction_fee_calculated", flat=True)
        )
        refund_cost_breakup = " + ".join(
            map(self.add_rupee_sign, deleted_order_payout_list)
        )
        refund_cost = sum(deleted_order_payout_list)
        refund_cost = round(refund_cost, 2)
        refund_cost_str = self.add_rupee_sign(refund_cost)

        deleted_suborder_count = deleted_order_payment_instance.count()
        deleted_serializer = GetOrderDetailsSerializer(
            deleted_order_payment_instance.exclude(suborder_number=None), many=True
        )
        deleted_serializer_copy = deleted_serializer.data

        grand_total_list = []
        product_total_obj = self.output_formatter(
            "Product total", total_product_cost_breakup, total_product_cost_str
        )
        df_obj = self.output_formatter(
            "Delivery fees", shipping_fee_breakup, shipping_fee_str
        )
        promotions_obj = self.output_formatter("Promotions", None, "₹0")
        grand_total_obj = self.output_formatter("Grand total", None, grand_total_str)

        grand_total_list.append(product_total_obj)
        grand_total_list.append(df_obj)
        grand_total_list.append(promotions_obj)
        grand_total_list.append(grand_total_obj)

        if is_seller:
            seller_received_list = []
            store_amount_received_list = []
            # total refunded amount of this order.
            refunded_amount_str = "₹0.0"
            refunded_amount_breakup = ""
            if RefundedAmount.objects.filter(order_reference=order_number).exists():
                refunded_amount_list = list(
                    RefundedAmount.objects.filter(
                        order_reference=order_number
                    ).values_list("refunded_amount", flat=True)
                )

                refunded_amount_breakup = " + ".join(
                    map(self.add_rupee_sign, refunded_amount_list)
                )
                refunded_amount = sum(refunded_amount_list)
                refunded_amount = round(refunded_amount, 2)
                refunded_amount_str = self.add_rupee_sign(refunded_amount)

            order_tds_list = list(
                OrderPayout.objects.filter(order_number=order_number).values_list(
                    "tds_calculated", flat=True
                )
            )
            tds_breakup = " + ".join(map(self.add_rupee_sign, order_tds_list))
            tds = sum(order_tds_list)
            tds = round(tds, 2)
            tds_str = self.add_rupee_sign(tds)

            order_payout_instance = OrderPayout.objects.filter(
                order_number=order_number
            ).first()
            tf_percentage = order_payout_instance.order_number.pg_transctionfee_with_tax_perc
            tf_text = "Transaction fee({}%)".format(tf_percentage)

            tf_list = list(
                OrderPayout.objects.filter(order_number=order_number).values_list(
                    "transaction_fee_calculated", flat=True
                )
            )
            tf_breakup = " + ".join(map(self.add_rupee_sign, tf_list))
            tf = sum(tf_list)
            tf = round(tf, 2)
            tf_str = self.add_rupee_sign(tf)

            if suborder_count != deleted_suborder_count:
                swadesic_fee = order_payout_instance.expected_swadesic_fee
                swadesic_fee_str = self.add_rupee_sign(swadesic_fee)

                swadesic_fee_discount = order_payout_instance.promotional_value
                swadesic_fee_discount_str = self.add_rupee_sign(swadesic_fee_discount)

                commission_fee_offer_obj = self.output_formatter(
                    "Early bird offer on Swadesic fee(98% off)",
                    None,
                    swadesic_fee_discount_str,
                )

            else:
                swadesic_fee_str = "₹0"

            total_receivable_list = list(
                OrderPayout.objects.filter(order_number=order_number).values_list(
                    "payout_amount", flat=True
                )
            )
            total_receivable_breakup = " + ".join(
                map(self.add_rupee_sign, total_receivable_list)
            )
            total_receivable = (sum(total_receivable_list))
            total_receivable = round(total_receivable, 2)
            total_receivable_str = self.add_rupee_sign(total_receivable)

            total_expected_receivable_list = list(
                OrderPayout.objects.filter(order_number=order_number).values_list(
                    "expected_payout_amount", flat=True
                )
            )
            total_expected_receivable_breakup = " + ".join(
                map(self.add_rupee_sign, total_expected_receivable_list)
            )
            total_expected_receivable = sum(total_expected_receivable_list)
            total_expected_receivable = round(total_expected_receivable, 2)
            total_expected_receivable_str = self.add_rupee_sign(
                total_expected_receivable
            )

            store_received_list = PayoutTransactions.objects.filter(
                order_number=order_number,
                transaction_type=PayoutTransactions.Transaction_Type.CREDITED,
            ).values_list("payout_amount", flat=True)
            store_received_breakup = " + ".join(
                map(self.add_rupee_sign, store_received_list)
            )
            store_received = sum(store_received_list)
            store_received = round(store_received, 2)
            store_received_str = self.add_rupee_sign(store_received)

            commission_released = OrderPayout.objects.filter(
                order_number=order_number,
                order_type='COMMISSION',
                payout_status='RELEASED'
            ).exists()

            # commission calculation
            # fetch the expected swadesic fee from orderpayout table and create similar breakup as above.
            expected_swadesic_fee_list = list(
                OrderPayout.objects.filter(order_number=order_number, order_type='COMMISSION').values_list(
                    "expected_payout_amount", flat=True
                )
            )
            expected_swadesic_fee_breakup = " + ".join(
                map(self.add_rupee_sign, expected_swadesic_fee_list)
            )
            expected_swadesic_fee = sum(expected_swadesic_fee_list)
            expected_swadesic_fee = round(expected_swadesic_fee, 2)
            expected_swadesic_fee_str = self.add_rupee_sign(expected_swadesic_fee)

            # Flash points calculation
            flash_point_deduction_list = list(
                OrderPayout.objects.filter(order_number=order_number, order_type='COMMISSION').values_list(
                    "flash_points", flat=True
                )
            )
            flash_point_deduction_breakup = " + ".join(
                map(self.add_rupee_sign, flash_point_deduction_list)
            )
            flash_point_deduction = sum(flash_point_deduction_list)
            flash_point_deduction = round(flash_point_deduction, 2)
            flash_point_deduction_str = self.add_rupee_sign(flash_point_deduction)

            # ### response objects ### #

            if not commission_released:
                expected_swadesic_fee_obj = self.output_formatter(
                    "Expected Swadesic Fee (per order) ",
                    expected_swadesic_fee_breakup,
                    "{}".format(expected_swadesic_fee_str),
                )

                flash_point_deduction_obj = self.output_formatter(
                    "With Flash Points",
                    flash_point_deduction_breakup,
                    "{}".format(flash_point_deduction_str),
                )
            else:
                expected_swadesic_fee_obj = self.output_formatter(
                    "Swadesic Fee (per order) ",
                    expected_swadesic_fee_breakup,
                    "{}".format(expected_swadesic_fee_str),
                )

                flash_point_deduction_obj = self.output_formatter(
                    "Flash Points redeemed",
                    flash_point_deduction_breakup,
                    "{}".format(flash_point_deduction_str),
                )

            cancelled_or_returned_refund_obj = self.output_formatter(
                "Cancelled or return refund",
                refunded_amount_breakup,
                "-{}".format(refunded_amount_str),
            )
            tds_obj = self.output_formatter(
                "TDS(1%)", tds_breakup, "-{}".format(tds_str)
            )
            tf_obj = self.output_formatter(tf_text, tf_breakup, "-{}".format(tf_str))
            commission_fee_obj = self.output_formatter(
                "Swadesic fee per order", None, "-{}".format(swadesic_fee_str)
            )
            total_expected_receivable_obj = self.output_formatter(
                "Expected settlement",
                total_expected_receivable_breakup,
                total_expected_receivable_str,
            )
            total_receivable_obj = self.output_formatter(
                "Total settlement amount", total_receivable_breakup, total_receivable_str
            )

            store_amount_received_obj = self.output_formatter(
                "Amount received till now", store_received_breakup, store_received_str
            )

            store_refund_cost_obj = self.output_formatter(
                "Refund cost till now", "-{}".format(refund_cost_breakup), "-{}".format(refund_cost_str)
            )

            seller_received_list.append(grand_total_obj)
            seller_received_list.append(cancelled_or_returned_refund_obj)
            seller_received_list.append(tds_obj)
            seller_received_list.append(tf_obj)
            seller_received_list.append(commission_fee_obj)
            # seller_received_list.append(expected_swadesic_fee_obj)
            seller_received_list.append(flash_point_deduction_obj)

            # if suborder_count != deleted_suborder_count:
            #       seller_received_list.append(commission_fee_offer_obj)
            seller_received_list.append(total_expected_receivable_obj)
            seller_received_list.append(total_receivable_obj)

            store_amount_received_list.append(store_amount_received_obj)
            store_amount_received_list.append(store_refund_cost_obj)

            logger.info("Exited get order details of an order seller side")
            return Response(
                {
                    "message": "success",
                    "grand_total": grand_total_list,
                    "total_amount_to_be_received": seller_received_list,
                    "amount_received_till_now": store_amount_received_list,
                    "product_details": grouped_dict,
                    "cancelled_or_returned_order": deleted_serializer_copy,
                },
                status=status.HTTP_200_OK,
            )

        logger.info("Exited get order details of an order seller side")
        return Response(
            {
                "message": "success",
                "grand_total": grand_total_list,
                "product_details": grouped_dict,
                "cancelled_or_returned_order": deleted_serializer_copy,
            },
            status=status.HTTP_200_OK,
        )


class AddShippingUpdates(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Add shipping history update",
        request_body=ShippingUpdatesSerializer,
    )
    def post(self, request, *args, **kwargs):
        """Add shipping updates, this same api used for updating shipping details of
        both delivery and return.
        For delivery shipping_reference will be package_number and for return,
        shipping_reference will be return_package_number."""

        serializer = ShippingUpdatesSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()

            notification_about = request.data["order_number"]
            package_number = request.data["shipping_reference"]
            order_instance = Order.objects.get(order_number=notification_about)
            notified_to = order_instance.user_reference.user_reference
            suborder_instance = SubOrder.objects.filter(
                order_number=notification_about, package_number=package_number
            ).first()
            image = suborder_instance.store_image
            package_name = suborder_instance.display_package_number

            notification_handler = NotificationHandler(
                notified_user=notified_to,
                notification_type=Notifications.Notifications_Type.SHIPPING_PACAKGE_UPDATED,
                notification_about=notification_about,
                image=image,
                package_name=package_name,
            )
            notification_handler.create_notification(notification_handler)

            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class GetShippingUpdates(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="get shipping history updates")
    def get(self, request, *args, **kwargs):
        """
        Get all the shipping history according to a delivery shipping or return shipping
        by passing shipping reference, ie, package_number or return_package_number
        """
        shipping_reference = kwargs["shipping_reference"]
        order_number = ShippingHistory.objects.filter(
            shipping_reference=shipping_reference
        ).values_list("order_number", flat=True).first()
        shipping_updates_instance = ShippingHistory.objects.filter(
            order_number=order_number
        ).order_by("created_date")
        serializer = ShippingUpdatesSerializer(shipping_updates_instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class EditDeleteShippingUpdates(
    mixins.UpdateModelMixin, mixins.DestroyModelMixin, generics.GenericAPIView
):
    queryset = ShippingHistory.objects.all()
    serializer_class = ShippingUpdatesSerializer

    @swagger_auto_schema(
        operation_summary="Edit a shipping history update",
        request_body=ShippingUpdatesSerializer,
    )
    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(operation_summary="Delete a shipping history update")
    def delete(self, *args, **kwargs):
        """Delete a shipping update by passing shipping_update_id"""
        instance = self.get_object()
        instance.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class AddOrderRating(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Add order rating",
        request_body=ShippingUpdatesSerializer,
    )
    def post(self, request, *args, **kwargs):
        serializer = OrderRatingSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class GetSubOrderHistory(generics.RetrieveAPIView):
    def get(self, request, *args, **kwargs):
        suborder_number = kwargs["suborder_number"]
        order_number = suborder_number.split("-")[0]
        order_instance = Order.objects.get(order_number = order_number)
        suborder_history = OrderLifeCycle.objects.filter(
            (Q(suborder_number=suborder_number) |
            Q(suborder_number__contains=suborder_number))
            & ~Q(suborder_status="ORDER_INITIATED")
            & ~Q(suborder_status="PAYMENT_INITIATED")
        )
        shipping_history = ShippingHistory.objects.filter(
                order_number=order_instance
            ).order_by("created_date")
        suborder_instance = SubOrder.objects.get(suborder_number=suborder_number)
        package_number = suborder_instance.package_number
        return_package_number = suborder_instance.return_package_number

        suborder_history_serializer = SubOrderHistorySerializer(
            suborder_history, many=True
        )
        shipping_history_serializer = ShippingUpdatesSerializer(
                shipping_history, many=True
            )

        if package_number and return_package_number:

            order_number = ShippingHistory.objects.filter(
                shipping_reference=package_number
            ).values_list("order_number", flat=True).first()

            delivery_shipping_updates_instance = ShippingHistory.objects.filter(
                order_number=order_number
            ).order_by("created_date")
            delivery_shipping_history_serializer = ShippingUpdatesSerializer(
                delivery_shipping_updates_instance, many=True
            )
            return_shipping_updates_instance = ShippingHistory.objects.filter(
                order_number=order_number
            ).order_by("created_date")
            return_shipping_history_serializer = ShippingUpdatesSerializer(
                return_shipping_updates_instance, many=True
            )
            combined_history_list = list(
                chain(
                    suborder_history_serializer.data,
                    delivery_shipping_history_serializer.data,
                    return_shipping_history_serializer.data,
                )
            )
        elif package_number:
            order_number = ShippingHistory.objects.filter(
                shipping_reference=package_number
            ).values_list("order_number", flat=True).first()
            delivery_shipping_updates_instance = ShippingHistory.objects.filter(
                order_number=order_number
            ).order_by("created_date")
            delivery_shipping_history_serializer = ShippingUpdatesSerializer(
                delivery_shipping_updates_instance, many=True
            )
            combined_history_list = list(
                chain(
                    suborder_history_serializer.data,
                    delivery_shipping_history_serializer.data,
                )
            )
        else:
            combined_history_list = list(
                chain(
                    suborder_history_serializer.data,
                    shipping_history_serializer.data,
                )
            )
        sorted_history_list = sorted(combined_history_list, key=itemgetter("date"))
        return Response(
            {"message": "success", "data": sorted_history_list},
            status=status.HTTP_200_OK,
        )

    # def get(self, request, *args, **kwargs):
    #     suborder_number = kwargs["suborder_number"]
    #     suborder_history = OrderLifeCycle.objects.filter(
    #         Q(suborder_number=suborder_number)
    #         & ~Q(suborder_status="ORDER_INITIATED")
    #         & ~Q(suborder_status="PAYMENT_INITIATED")
    #     )
    #
    #     suborder_instance = SubOrder.objects.get(suborder_number=suborder_number)
    #     package_number = suborder_instance.package_number
    #     return_package_number = suborder_instance.return_package_number
    #
    #     suborder_history_serializer = SubOrderHistorySerializer(
    #         suborder_history, many=True
    #     )
    #
    #     if package_number and return_package_number:
    #         delivery_shipping_updates_instance = ShippingHistory.objects.filter(
    #             shipping_reference=package_number
    #         ).order_by("created_date")
    #         delivery_shipping_history_serializer = ShippingUpdatesSerializer(
    #             delivery_shipping_updates_instance, many=True
    #         )
    #
    #         return_shipping_updates_instance = ShippingHistory.objects.filter(
    #             shipping_reference=package_number
    #         ).order_by("created_date")
    #         return_shipping_history_serializer = ShippingUpdatesSerializer(
    #             return_shipping_updates_instance, many=True
    #         )
    #         combined_history_list = list(
    #             chain(
    #                 suborder_history_serializer.data,
    #                 delivery_shipping_history_serializer.data,
    #                 return_shipping_history_serializer.data,
    #             )
    #         )
    #     elif package_number:
    #         delivery_shipping_updates_instance = ShippingHistory.objects.filter(
    #            shipping_reference=package_number
    #         ).order_by("created_date")
    #         delivery_shipping_history_serializer = ShippingUpdatesSerializer(
    #             delivery_shipping_updates_instance, many=True
    #         )
    #         combined_history_list = list(
    #             chain(
    #                 suborder_history_serializer.data,
    #                 delivery_shipping_history_serializer.data,
    #             )
    #         )
    #     else:
    #         combined_history_list = list(suborder_history_serializer.data),
    #     sorted_history_list = sorted(combined_history_list, key=itemgetter("date"))
    #     return Response(
    #         {"message": "success", "data": sorted_history_list},
    #         status=status.HTTP_200_OK,
    #     )


class CheckPossibleRefund(generics.RetrieveAPIView):
    @staticmethod
    def output_formatter(text, subtext, value):
        return {
            "order_breakup_item_text": text,
            "order_breakup_item_subtext": subtext,
            "order_breakup_item_value": value,
        }

    @staticmethod
    def add_rupee_sign(num):
        num_to_string = str(num)
        result = "\u20B9" "{}".format(num_to_string)
        return result

    @staticmethod
    def convert_string(input_string):
        # Replace underscores with spaces
        converted_string = input_string.replace("_", " ")

        # Capitalize the first letter
        converted_string = converted_string.capitalize()

        return converted_string

    @staticmethod
    def get_date(date):
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%Y-%m-%d %H:%M:%S")

    def post(self, request, is_seller=None, *args, **kwargs):

        order_number = request.data["order_number"]
        suborder_number_list = request.data["suborder_numbers"]

        order_instance = Order.new_objects.get(order_number=order_number)
        grant_total = order_instance.total_order_amount
        order_delivery_fee = order_instance.total_delivery_fee
        # order_amount = order_instance.store_total
        # order_delivery_fee_after_promotion = order_delivery_fee
        # grant_total_after_promotion = grant_total

        suborder_status_list = [
            SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
            SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
            SubOrder.Suborder_Status.RETURN_REQUESTED,
            SubOrder.Suborder_Status.RETURN_CONFIRMED,
            SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
            SubOrder.Suborder_Status.RETURNED_TO_SELLER,
            SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED,
        ]
        is_cancelled_suborders = SubOrder.objects.filter(
            suborder_number=suborder_number_list[0],
            suborder_status__in=suborder_status_list,
        ).exists()

        order_payout_instance = OrderPayout.objects.filter(
            order_number=order_number
        ).first()
        tf_percentage = order_payout_instance.transaction_fee_percentage
        tf_text = "Transaction fee({}%)".format(tf_percentage)

        if is_cancelled_suborders:

            cancelled_products_details = []

            product_total_list = list(
                RefundedAmount.objects.filter(
                    suborder_reference__in=suborder_number_list
                ).values_list("product_price", flat=True)
            )
            product_total = sum(product_total_list)
            product_total_breakup = "+".join(
                map(self.add_rupee_sign, product_total_list)
            )
            product_total_str = self.add_rupee_sign(product_total)

            delivery_fee_list = list(
                RefundedAmount.objects.filter(
                    suborder_reference__in=suborder_number_list
                ).values_list("product_df", flat=True)
            )
            delivery_fee_total = sum(delivery_fee_list)
            delivery_fee_total_breakup = "+".join(
                map(self.add_rupee_sign, delivery_fee_list)
            )
            delivery_fee_total_str = self.add_rupee_sign(delivery_fee_total)

            delivery_fee_refund_list = list(
                RefundedAmount.objects.filter(
                    suborder_reference__in=suborder_number_list
                ).values_list("df_reversal", flat=True)
            )
            delivery_fee_refund_total = sum(delivery_fee_refund_list)
            delivery_fee_refund_total_breakup = "+".join(
                map(self.add_rupee_sign, delivery_fee_refund_list)
            )
            delivery_fee_refund_total_str = self.add_rupee_sign(
                delivery_fee_refund_total
            )

            transaction_fee_list = list(
                RefundedAmount.objects.filter(
                    suborder_reference__in=suborder_number_list
                ).values_list("transaction_fee", flat=True)
            )
            transaction_fee_total = sum(transaction_fee_list)
            transaction_fee_total_str = self.add_rupee_sign(transaction_fee_total)

            total_refund_amount_list = list(
                RefundedAmount.objects.filter(
                    suborder_reference__in=suborder_number_list
                ).values_list("refunded_amount", flat=True)
            )
            total_refund_amount = sum(total_refund_amount_list)
            total_refund_amount_str = self.add_rupee_sign(total_refund_amount)

            amount_paid = product_total + delivery_fee_total
            amount_paid_str = self.add_rupee_sign(amount_paid)

            refunded_items = RefundedAmount.objects.filter(
                suborder_reference__in=suborder_number_list
            )
            for item in refunded_items:
                suborder_number = item.suborder_reference.suborder_number
                suborder_status = item.suborder_status
                return_cost_on = item.return_cost_on
                is_shipped = item.is_shipped
                df_settings = item.df_settings
                df_type = item.df_type
                product_price = item.product_price
                product_df = item.product_df
                df_reversal = item.df_reversal
                transaction_fee = item.transaction_fee
                refunded_amount = item.refunded_amount
                refund_requested_date = self.get_date(item.created_date)
                if item.refunded_date:
                    refunded_date = self.get_date(item.refunded_date)
                else:
                    refunded_date = item.refunded_date
                refund_status = item.refund_status

                context = {
                    "suborder_number": suborder_number,
                    "suborder_status": suborder_status,
                    "return_cost_on": return_cost_on,
                    "is_shipped": is_shipped,
                    "df_settings": df_settings,
                    "df_type": df_type,
                    "product_price": product_price,
                    "product_df": product_df,
                    "df_reversal": df_reversal,
                    "transaction_fee": transaction_fee,
                    "refunded_amount": refunded_amount,
                    "requested_date": refund_requested_date,
                    "refunded_date": refunded_date,
                    "refund_status": refund_status,
                }
                cancelled_products_details.append(context)

        # if suborders in the list is not already cancelled or returned one.
        else:
            (
                refund_amount,
                total_cancelled_product_value,
                total_cancelled_product_df,
                total_reverse_df,
                transaction_fees_list,
                details,
            ) = calculate_refund_amount(suborder_number_list, order_number, flag=2)

            product_total_list = total_cancelled_product_value
            product_total = sum(product_total_list)
            product_total_breakup = "+".join(
                map(self.add_rupee_sign, product_total_list)
            )
            product_total_str = self.add_rupee_sign(product_total)

            delivery_fee_list = total_cancelled_product_df
            delivery_fee_total_breakup = "+".join(
                map(self.add_rupee_sign, delivery_fee_list)
            )
            delivery_fee_total = sum(delivery_fee_list)
            delivery_fee_total_str = self.add_rupee_sign(delivery_fee_total)

            delivery_fee_refund_list = total_reverse_df
            delivery_fee_refund_total = sum(delivery_fee_refund_list)
            delivery_fee_refund_total_breakup = "+".join(
                map(self.add_rupee_sign, delivery_fee_refund_list)
            )
            delivery_fee_refund_total_str = self.add_rupee_sign(
                delivery_fee_refund_total
            )

            transaction_fee_list = transaction_fees_list
            transaction_fee_total = sum(transaction_fee_list)
            transaction_fee_total_str = self.add_rupee_sign(transaction_fee_total)

            amount_paid = product_total + delivery_fee_total
            amount_paid_str = self.add_rupee_sign(amount_paid)

            total_refund_amount = refund_amount
            total_refund_amount_str = self.add_rupee_sign(total_refund_amount)
            cancelled_products_details = details

        product_total_obj = self.output_formatter(
            "Product total", product_total_breakup, product_total_str
        )
        delivery_fee_obj = self.output_formatter(
            "Delivery fee", delivery_fee_total_breakup, delivery_fee_total_str
        )
        amount_paid_obj = self.output_formatter(
            "Amount paid for selected products", None, amount_paid_str
        )

        product_total_refund_obj = self.output_formatter(
            "Product total refund", product_total_breakup, product_total_str
        )
        delivery_fee_refund_obj = self.output_formatter(
            "Delivery fee refund",
            delivery_fee_refund_total_breakup,
            delivery_fee_refund_total_str,
        )
        promotion_discount_adjustment_obj = self.output_formatter(
            "Promotion discount adjustment", None, "₹0"
        )
        transaction_fee_obj = self.output_formatter(
            tf_text, None, "-{}".format(transaction_fee_total_str)
        )
        total_refund_amount_obj = self.output_formatter(
            "Total refund amount", None, total_refund_amount_str
        )

        product_details_list = [product_total_obj, delivery_fee_obj, amount_paid_obj]

        refund_details_list = [
            product_total_refund_obj,
            delivery_fee_refund_obj,
            promotion_discount_adjustment_obj,
            transaction_fee_obj,
            total_refund_amount_obj,
        ]

        if is_seller:

            # total refunded amount of this order.
            refunded_amount = 0.0
            refunded_amount_breakup = ""
            if RefundedAmount.objects.filter(
                suborder_reference__in=suborder_number_list
            ).exists():
                refunded_amount_list = list(
                    RefundedAmount.objects.filter(
                        suborder_reference__in=suborder_number_list
                    ).values_list("refunded_amount", flat=True)
                )

                refunded_amount_breakup = " + ".join(map(str, refunded_amount_list))
                refunded_amount = sum(refunded_amount_list)
                refunded_amount = round(refunded_amount, 2)

                refund_instance = RefundedAmount.objects.filter(
                    suborder_reference__in=suborder_number_list
                ).first()
                is_shipped = refund_instance.is_shipped

                if is_shipped:
                    refund_type = "Partial refund"
                else:
                    refund_type = "Full refund"

            order_tds_list = list(
                OrderPayout.objects.filter(
                    suborder_number__in=suborder_number_list
                ).values_list("tds_calculated", flat=True)
            )
            tds_breakup = " + ".join(map(self.add_rupee_sign, order_tds_list))
            tds = sum(order_tds_list)
            tds = round(tds, 2)
            tds_str = self.add_rupee_sign(tds)

            order_payout_instance = OrderPayout.objects.get(
                suborder_number=suborder_number_list[0]
            )
            tf_percentage = order_payout_instance.transaction_fee_percentage
            tf_text = "Transaction fee({}%)".format(tf_percentage)

            tf_list = list(
                OrderPayout.objects.filter(
                    suborder_number__in=suborder_number_list
                ).values_list("transaction_fee_calculated", flat=True)
            )
            tf_breakup = " + ".join(map(self.add_rupee_sign, tf_list))
            tf = sum(tf_list)
            tf = round(tf, 2)
            tf_str = self.add_rupee_sign(tf)

            total_receivable_list = list(
                OrderPayout.objects.filter(
                    suborder_number__in=suborder_number_list
                ).values_list("payout_amount", flat=True)
            )
            total_receivable_breakup = " + ".join(
                map(self.add_rupee_sign, total_receivable_list)
            )
            total_receivable = sum(total_receivable_list)
            total_receivable = round(total_receivable, 2)
            total_receivable_str = self.add_rupee_sign(total_receivable)

            df_seller_received = delivery_fee_total - delivery_fee_refund_total
            df_seller_received_str = self.add_rupee_sign(df_seller_received)

            store_amount_received = PayoutTransactions.objects.filter(
                suborder_number__in=suborder_number_list,
                transaction_type=PayoutTransactions.Transaction_Type.CREDITED,
            ).aggregate(payout_amount=Sum("payout_amount"))["payout_amount"]
            grand_total_obj = self.output_formatter("Grand total", None, amount_paid)
            cancelled_or_returned_refund_obj = self.output_formatter(
                "Cancelled or return refund",
                refunded_amount_breakup,
                -abs(refunded_amount),
            )

            refund_type_obj = self.output_formatter("Refund type", None, refund_type)
            df_seller_received_obj = self.output_formatter(
                "Delivery fee to be received", None, df_seller_received_str
            )
            tds_obj = self.output_formatter(
                "TDS(1%)", tds_breakup, "-{}".format(tds_str)
            )
            tf_obj = self.output_formatter(tf_text, tf_breakup, "-{}".format(tf_str))
            total_receivable_obj = self.output_formatter(
                "Settlement amount", total_receivable_breakup, total_receivable_str
            )

            seller_received_list = [
                refund_type_obj,
                df_seller_received_obj,
                tds_obj,
                tf_obj,
                total_receivable_obj,
            ]

            suborder_cancel_status = [
                SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
                SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
                SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED,
            ]
            suborder_return_status = [
                SubOrder.Suborder_Status.RETURN_REQUESTED,
                SubOrder.Suborder_Status.RETURN_CONFIRMED,
                SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
                SubOrder.Suborder_Status.RETURNED_TO_SELLER,
            ]
            request_type = None
            requested_by = None
            if SubOrder.objects.filter(
                suborder_number=suborder_number_list[0],
                suborder_status__in=suborder_cancel_status,
            ).exists():
                suborder_instance = SubOrder.objects.get(
                    suborder_number=suborder_number_list[0],
                    suborder_status__in=suborder_cancel_status,
                )
                request_type = "Cancel"
                if suborder_instance.cancelled_by == "buyer":
                    requested_by = "Customer"
                elif (
                    suborder_instance.cancelled_by == "seller"
                    or suborder_instance.cancelled_by == "auto cancelled"
                ):
                    requested_by = "Store"

            elif SubOrder.objects.filter(
                suborder_number=suborder_number_list[0],
                suborder_status__in=suborder_return_status,
            ).exists():
                request_type = "Return"
                requested_by = "Customer"

            refund_instance = RefundedAmount.objects.get(
                suborder_reference=suborder_number_list[0]
            )
            status_before_reqeust = self.convert_string(refund_instance.suborder_status)
            df_type = refund_instance.df_type
            if refund_instance.df_settings == "product_settings":
                df_settings = "Store"
            elif refund_instance.df_settings == "store_settings":
                df_settings = "Product"
            else:
                df_settings = ""
            delivery_fee_type = "{}-{}".format(df_settings, df_type)

            return_cost_on = None
            if refund_instance.return_cost_on == "customer":
                return_cost_on = "Customer"
            elif refund_instance.return_cost_on == "seller":
                return_cost_on = "Store"
            fee_responsibility = "{} pays".format(return_cost_on)

            request_type_obj = self.output_formatter("Request type", None, request_type)
            requested_by_obj = self.output_formatter("Requested by", None, requested_by)
            status_before_reqeust_obj = self.output_formatter(
                "Status before request", None, status_before_reqeust
            )

            delivery_fee_for_the_product_obj = self.output_formatter(
                "Delivery fee for the product", None, delivery_fee_total
            )
            delivery_fee_type_obj = self.output_formatter(
                "Delivery fee type", None, delivery_fee_type
            )

            fee_responsibility_obj = self.output_formatter(
                "Fee responsibility", None, fee_responsibility
            )
            seller_cancel_obj = self.output_formatter(
                "Seller cancel waiting order", None, "Seller pays"
            )
            auto_cancel_obj = self.output_formatter(
                "Auto-cancelled orders", None, "Seller pays"
            )

            request_and_suborder_status = [
                request_type_obj,
                requested_by_obj,
                status_before_reqeust_obj,
            ]
            product_and_payment = [
                product_total_obj,
                delivery_fee_for_the_product_obj,
                delivery_fee_type_obj,
            ]
            store_refund_policy = [
                fee_responsibility_obj,
                seller_cancel_obj,
                auto_cancel_obj,
            ]

            more_details_list = [
                {"request_and_suborder_status": request_and_suborder_status},
                {"product_and_payment": product_and_payment},
                {"store_refund_policy": store_refund_policy},
            ]

            return Response(
                {
                    "message": "success",
                    "product_details": product_details_list,
                    "refund_details": refund_details_list,
                    "refund_cost_on_store": seller_received_list,
                    "more_details": more_details_list,
                    "cancelled_products_details": cancelled_products_details,
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "message": "success",
                "product_details": product_details_list,
                "refund_details": refund_details_list,
                "cancelled_products_details": cancelled_products_details,
            },
            status=status.HTTP_200_OK,
        )

################### New Check Possible refund ################

class CheckPossibleRefundNew(APIView):
    def post(self, request, is_seller=None, *args, **kwargs):
        """
    This post method is used to check what happens if the suborders are cancelled or returned, without making any entry in database and return a refund details as below example response. It will refer UpdateOrderStatus_single method for the process.

    Example response:
    {
        "message": "success",
        "product_details": [
            {
                "order_breakup_item_text": "Product total",
                "order_breakup_item_subtext": "₹25.0",
                "order_breakup_item_value": "₹25.0"
            },
            {
                "order_breakup_item_text": "Delivery fee",
                "order_breakup_item_subtext": "₹0.0",
                "order_breakup_item_value": "₹0.0"
            },
            {
                "order_breakup_item_text": "Amount paid for selected products",
                "order_breakup_item_subtext": null,
                "order_breakup_item_value": "₹25.0"
            }
        ],
        "refund_details": [
            {
                "order_breakup_item_text": "Product total refund",
                "order_breakup_item_subtext": "₹25.0",
                "order_breakup_item_value": "₹25.0"
            },
            {
                "order_breakup_item_text": "Delivery fee refund",
                "order_breakup_item_subtext": "₹0.0",
                "order_breakup_item_value": "₹0.0"
            },
            {
                "order_breakup_item_text": "Promotion discount adjustment",
                "order_breakup_item_subtext": null,
                "order_breakup_item_value": "₹0"
            },
            {
                "order_breakup_item_text": "Transaction fee(2%)",
                "order_breakup_item_subtext": null,
                "order_breakup_item_value": "-₹0.0"
            },
            {
                "order_breakup_item_text": "Total refund amount",
                "order_breakup_item_subtext": null,
                "order_breakup_item_value": "₹25.0"
            }
        ],
        "cancelled_products_details": [
            {
                "suborder_number": "O2408281318090001-01",
                "suborder_status": "WAITING_FOR_CONFIRMATION",
                "return_cost_on": "seller",
                "is_shipped": false,
                "df_settings": "store_settings",
                "df_type": "per product",
                "product_price": 25,
                "product_df": 0,
                "df_reversal": 0,
                "transaction_fee": 0,
                "refunded_amount": 25,
                "requested_date": "2024-08-28 13:22:00",
                "refunded_date": "2024-08-28 13:22:01",
                "refund_status": "REFUNDED"
            }
        ]
    }
    """
        # Extract order number and suborder numbers from request data

        order_number = request.data.get("order_number")
        refund_requested_suborder_number_list = request.data.get("suborder_numbers")

        # Validate input data
        if not order_number or not refund_requested_suborder_number_list:
            return Response({"message": "Invalid input data"}, status=status.HTTP_400_BAD_REQUEST)

        # Get order instance
        try:
            order_instance = Order.new_objects.get(order_number=order_number)
        except Order.DoesNotExist:
            return Response({"message": "Order not found"}, status=status.HTTP_404_NOT_FOUND)

        self.transaction_fee_deduction_perc = order_instance.pg_transctionfee_with_tax_perc
        # Get suborder payment details
        suborder_payment_details = order_instance.suborder_payment_detail_items
        # Convert suborder payment details to a DataFrame

        suborder_payment_df = pd.DataFrame(list(suborder_payment_details.values(
            'order_number', 'suborder_number', 'product_amount', 'product_delivery_fee',
             'refund_amount', 'is_suborder_deleted',
             'delivery_fee_scope'
        )))
        suborder_payment_df['suborder_number'] = suborder_payment_df['suborder_number'].fillna('')

        suborder_payment_df = suborder_payment_df.fillna('')
        suborder_payment_df['is_requested_refund'] = suborder_payment_df['suborder_number'].isin(
            refund_requested_suborder_number_list) # which of the suborders are requested to check for refund


        # get return_cost_on_seller from RefundWarranty table for every suborder number in suborder_payment_df using SubOrder table refund_warranty_id, make sure you rename to refund_cost_on
        refund_warranty_ids = SubOrder.objects.filter(
            suborder_number__in=suborder_payment_df['suborder_number']).values('suborder_number', 'refund_warranty_id')
        refund_warranty_ids = pd.DataFrame(refund_warranty_ids)
        refund_warranty_df = pd.DataFrame(
            list(
                RefundAndWarranty.objects.filter(refundandwarrantyid__in=refund_warranty_ids.refund_warranty_id).values(
                    'refundandwarrantyid', 'return_cost_on_seller'
                )))
        refund_warranty_df = pd.merge(refund_warranty_ids, refund_warranty_df, how='right',
                                      left_on='refund_warranty_id', right_on='refundandwarrantyid')
        refund_warranty_df = refund_warranty_df.rename(columns={'return_cost_on_seller': 'refund_cost_on'}) # by default refund cost is on seller/STORE and refund_cost_on = True reflects STORE

        # suborder_payment_df['suborder_number'] = suborder_payment_df['suborder_number'].astype(str)
        # refund_warranty_df['suborder_number'] = refund_warranty_df['suborder_number'].astype(str)

        # merge the refund_warranty_df with suborder_payment_df on suborder_number
        suborder_payment_df = pd.merge(suborder_payment_df, refund_warranty_df, left_on='suborder_number',
                                       right_on='suborder_number', how='left')

        suborder_payment_df = suborder_payment_df.drop(['refundandwarrantyid', 'refund_warranty_id'], axis=1)

        # Get suborder statuses and shipping status in a single query
        suborder_info = SubOrder.objects.filter(suborder_number__in=suborder_payment_df['suborder_number']
                                                ).values('suborder_number', 'suborder_status', 'package_number', 'cancelled_by')

        # Convert to DataFrame and add is_shipped column
        suborder_info_df = pd.DataFrame(suborder_info)
        suborder_info_df['is_shipped'] = suborder_info_df['package_number'].notna()

        # Merge with suborder_payment_df
        suborder_payment_df = pd.merge(
            suborder_payment_df,
            suborder_info_df[['suborder_number', 'suborder_status', 'is_shipped', 'cancelled_by']],
            on='suborder_number',
            how='left'
        )

        # Get previous suborder status from OrderLifeCycle table
        # Filter out empty suborder numbers for the lifecycle query
        valid_suborder_numbers = suborder_payment_df[suborder_payment_df['suborder_number'] != '']['suborder_number'].unique()

        if len(valid_suborder_numbers) > 0:
            # Query OrderLifeCycle to get previous status for each suborder
            lifecycle_data = OrderLifeCycle.objects.filter(
                suborder_number__in=valid_suborder_numbers
            ).values(
                'suborder_number', 'suborder_status', 'order_updated_date'
            ).order_by('suborder_number', '-order_updated_date')

            # Convert to DataFrame for easier manipulation
            lifecycle_df = pd.DataFrame(lifecycle_data)

            if not lifecycle_df.empty:
                # Sort by suborder_number and order_updated_date (ascending to get oldest first)
                lifecycle_df = lifecycle_df.sort_values(['suborder_number', 'order_updated_date'], ascending=[True, True])

                # Define cancellation statuses
                cancellation_statuses = [
                    'ORDER_CANCELLED',
                    'ORDER_CANCELLED_BY_BUYER',
                    'ORDER_CANCELLED_BY_SELLER',
                    'ORDER_AUTO_CANCELLED',
                    'CANCELLED_IN_TRANSIT'
                ]

                # Function to get status before cancellation for each suborder
                def get_status_before_cancellation(group):
                    # Sort by date ascending (oldest first)
                    group = group.sort_values('order_updated_date', ascending=True)

                    # Find the first cancellation status
                    cancellation_mask = group['suborder_status'].isin(cancellation_statuses)
                    cancellation_indices = group[cancellation_mask].index

                    if len(cancellation_indices) > 0:
                        # Get the first cancellation status index
                        first_cancellation_idx = cancellation_indices[0]
                        # Find the position of this index in the sorted group
                        cancellation_position = group.index.get_loc(first_cancellation_idx)

                        # Get the status before the cancellation (previous row before cancellation in ascending order)
                        if cancellation_position > 0:
                            return group.iloc[cancellation_position - 1]['suborder_status']
                        else:
                            # If cancellation is the first status, return None
                            return None
                    else:
                        # If no cancellation status found, return the second most recent status
                        if len(group) > 1:
                            return group.iloc[-2]['suborder_status']  # Second last in ascending order
                        else:
                            return None

                # Apply the function to each suborder group
                previous_status_data = []
                for suborder_number, group in lifecycle_df.groupby('suborder_number'):
                    previous_status = get_status_before_cancellation(group)
                    previous_status_data.append({
                        'suborder_number': suborder_number,
                        'previous_suborder_status': previous_status
                    })

                # Convert to DataFrame
                previous_status_df = pd.DataFrame(previous_status_data)

                logger.info(f"lifecycledf: {lifecycle_df.to_dict(orient='records')}")
                logger.info(f"previous_statusdf: {previous_status_df.to_dict(orient='records')}")

                # Merge with suborder_payment_df
                suborder_payment_df = pd.merge(
                    suborder_payment_df,
                    previous_status_df,
                    on='suborder_number',
                    how='left'
                )
            else:
                # If no lifecycle data found, add empty column
                suborder_payment_df['previous_suborder_status'] = None
        else:
            # If no valid suborder numbers, add empty column
            suborder_payment_df['previous_suborder_status'] = None
        
        # Update refund_cost_on for customer-initiated cancellations
        # Set refund_cost_on to 'USER' for all rows where cancelled_by is 'buyer'
        suborder_payment_df.loc[suborder_payment_df['cancelled_by'] == 'buyer', 'refund_cost_on'] = 'USER'
        
        # For rows without cancelled_by (like store delivery fee rows) or non-buyer cancellations,
        # apply the original logic
        non_buyer_mask = (suborder_payment_df['cancelled_by'] != 'buyer') | (suborder_payment_df['cancelled_by'].isna())
        if non_buyer_mask.any():
            condition = any((suborder_payment_df['delivery_fee_scope'] == 'store-df') &
                (suborder_payment_df['suborder_number'] != '') &
                (suborder_payment_df['suborder_status'] != 'WAITING_FOR_CONFIRMATION'))
            
            suborder_payment_df.loc[(suborder_payment_df['suborder_number'] == '') & non_buyer_mask, 'refund_cost_on'] = 'STORE' if condition else 'USER'
            
            # Apply the original logic for non-buyer cancellations with suborder numbers
            non_buyer_with_suborder = (suborder_payment_df['suborder_number'] != '') & non_buyer_mask
            if non_buyer_with_suborder.any():
                suborder_payment_df.loc[non_buyer_with_suborder, 'refund_cost_on'] = suborder_payment_df.loc[non_buyer_with_suborder].apply(
                    lambda row: 'USER' if ((row['suborder_status'] == 'WAITING_FOR_CONFIRMATION') or (not row['refund_cost_on']))
                    else 'STORE', axis=1
                )
        
        suborder_payment_df.loc[suborder_payment_df['suborder_number'] == '', 'is_shipped'] = any((suborder_payment_df['is_shipped'] == True) & (suborder_payment_df['delivery_fee_scope'] == 'store-df'))
        suborder_payment_df.loc[suborder_payment_df['suborder_number'] == '', 'delivery_fee_scope'] = 'store-df'
        # Get transaction fee percentage from order table
        suborder_payment_df['transaction_fee'] = suborder_payment_df[
                                                     'product_amount'] * order_instance.pg_transctionfee_with_tax_perc / 100

        # Generate dummy data for suborder_payment_df (5 rows)

        # suborder_payment_df = pd.DataFrame(dummy_data)
        # print(suborder_payment_df)

        pass
        suborder_payment_df, (product_amounts, product_delivery_fees, amount_paid_for_selected_products,
                              refunded_amounts, refunded_product_amounts,
                              refunded_delivery_amounts) = self.process_suborder_payment(suborder_payment_df)

        # The DataFrame now includes 'previous_suborder_status' column which contains
        # the status that was active before any cancellation occurred (not just the second most recent)
        # Cancellation statuses: ORDER_CANCELLED, ORDER_CANCELLED_BY_BUYER, ORDER_CANCELLED_BY_SELLER, ORDER_AUTO_CANCELLED, CANCELLED_IN_TRANSIT
        print(suborder_payment_df.to_dict(orient='records'))

        response = format_df_to_json(df=suborder_payment_df, breakups=(product_amounts, product_delivery_fees, amount_paid_for_selected_products,
                              refunded_amounts, refunded_product_amounts,
                              refunded_delivery_amounts), tranaction_fee_deduction_perc=self.transaction_fee_deduction_perc)

        if is_seller:

            order_payout_instance = OrderPayout.objects.filter(suborder_number=refund_requested_suborder_number_list[0]).first()

            # Calculate TDS
            tds = order_payout_instance.tds_calculated
            tds_str = f"₹{tds:.2f}"

            # Calculate transaction fee
            tf = order_payout_instance.transaction_fee_calculated
            tf_str = f"₹{tf:.2f}"

            # Calculate total receivable
            total_receivable = order_payout_instance.payout_amount
            total_receivable_str = f"₹{total_receivable:.2f}"

            # Determine refund type
            is_shipped = suborder_payment_df['is_shipped'].any()
            refund_type = "Partial refund" if is_shipped else "Full refund"

            # Calculate delivery fee to be received by seller
            df_seller_received = order_payout_instance.product_delivery_fee
            df_seller_received_str = f"₹{df_seller_received:.2f}"

            # Create seller_received_list
            seller_received_list = [
                {"order_breakup_item_text": "Refund type", "order_breakup_item_subtext": None, "order_breakup_item_value": refund_type},
                {"order_breakup_item_text": "Delivery fee to be received", "order_breakup_item_subtext": None, "order_breakup_item_value": df_seller_received_str},
                {"order_breakup_item_text": "TDS(1%)", "order_breakup_item_subtext": None, "order_breakup_item_value": f"-{tds_str}"},
                {"order_breakup_item_text": f"Transaction fee({self.transaction_fee_deduction_perc}%)", "order_breakup_item_subtext": None, "order_breakup_item_value": f"-{tf_str}"},
                {"order_breakup_item_text": "Settlement amount", "order_breakup_item_subtext": None, "order_breakup_item_value": total_receivable_str}
            ]

            # Check if all other suborders of the current order are cancelled or returned to seller
            all_suborders_cancelled_or_returned = all(
                suborder.suborder_status in ['ORDER_CANCELLED', 'RETURNED_TO_SELLER']
                for suborder in SubOrder.objects.filter(order_number=order_number).exclude(suborder_number__in=refund_requested_suborder_number_list)
            )

            store_df_payout_list = []
            if all_suborders_cancelled_or_returned:
                store_df_payout = OrderPayout.objects.filter(
                    order_number=order_instance.order_number,
                    order_type='STORE_DF'
                ).first()

                if store_df_payout:
                    # Calculate TDS
                    tds = store_df_payout.tds_calculated
                    tds_str = f"₹{tds:.2f}"

                    # Calculate transaction fee
                    tf = store_df_payout.transaction_fee_calculated
                    tf_str = f"₹{tf:.2f}"

                    # Calculate total receivable
                    total_receivable = store_df_payout.payout_amount
                    total_receivable_str = f"₹{total_receivable:.2f}"

                    # Calculate delivery fee to be received by store
                    df_store_received = store_df_payout.product_delivery_fee
                    df_store_received_str = f"₹{df_store_received:.2f}"

                    store_df_payout_list = [
                        {"order_breakup_item_text": "Store Delivery Fee", "order_breakup_item_subtext": None, "order_breakup_item_value": df_store_received_str},
                        {"order_breakup_item_text": "TDS(1%)", "order_breakup_item_subtext": None, "order_breakup_item_value": f"-{tds_str}"},
                        {"order_breakup_item_text": f"Transaction fee({self.transaction_fee_deduction_perc}%)", "order_breakup_item_subtext": None, "order_breakup_item_value": f"-{tf_str}"},
                        {"order_breakup_item_text": "Store Delivery Fee Settlement", "order_breakup_item_subtext": None, "order_breakup_item_value": total_receivable_str}
                    ]


            # Add more details for seller
            more_details_list = [
                {
                    "request_and_suborder_status": [
                        {
                            "order_breakup_item_text": "Request type",                          
                            "order_breakup_item_subtext": None, 
                            "order_breakup_item_value": "Cancel" if ("ORDER_CANCELLED_BY_BUYER" in suborder_payment_df['suborder_status'].values or "ORDER_CANCELLED_BY_SELLER" in suborder_payment_df['suborder_status'].values) else "Return"
                        },
                        {
                            "order_breakup_item_text": "Requested by", 
                            "order_breakup_item_subtext": None, 
                            "order_breakup_item_value": "Customer" if "ORDER_CANCELLED_BY_BUYER" in suborder_payment_df['suborder_status'].values else "Store"
                        }, 
                        {
                            "order_breakup_item_text": "Status before request", 
                            "order_breakup_item_subtext": None, 
                            "order_breakup_item_value": suborder_payment_df.loc[suborder_payment_df['suborder_number'] == refund_requested_suborder_number_list[0], 'previous_suborder_status'].values[0]
                        }
                    ]
                },
                {
                    "product_and_payment": [
                        {"order_breakup_item_text": "Product total", "order_breakup_item_subtext": None, "order_breakup_item_value": order_payout_instance.product_amount},
                        {"order_breakup_item_text": "Delivery fee for the product", "order_breakup_item_subtext": None, "order_breakup_item_value": order_payout_instance.product_delivery_fee},
                        {"order_breakup_item_text": "Delivery fee type", "order_breakup_item_subtext": None, "order_breakup_item_value": "Store-per product" if suborder_payment_df['delivery_fee_scope'].iloc[0] == 'store-df' else "Product-per product"}
                    ]
                },
                {
                    "store_refund_policy": [
                        {"order_breakup_item_text": "Fee responsibility", "order_breakup_item_subtext": None, "order_breakup_item_value": f"{suborder_payment_df['refund_cost_on'].iloc[0].capitalize()} pays"},
                        {"order_breakup_item_text": "Seller cancel waiting order", "order_breakup_item_subtext": None, "order_breakup_item_value": "Seller pays"},
                        {"order_breakup_item_text": "Auto-cancelled orders", "order_breakup_item_subtext": None, "order_breakup_item_value": "Seller pays"},
                        {"order_breakup_item_text": "Buyer cancel waiting order", "order_breakup_item_subtext": None, "order_breakup_item_value": "Buyer pays"}
                    ]
                }
            ]

            response["refund_cost_on_store"] = seller_received_list
            response["more_details"] = more_details_list

        return Response(response, status=status.HTTP_200_OK)
        # print('product_amounts:', self.flat_sum(product_amounts), product_amounts)
        # print('product_delivery_fees:', self.flat_sum(product_delivery_fees), product_delivery_fees)
        # print('amount_paid_for_selected_products:', self.flat_sum(amount_paid_for_selected_products),
        #       amount_paid_for_selected_products)
        # print('*' * 100)
        #
        # print('refunded_product_amounts:', self.flat_sum(refunded_product_amounts), refunded_product_amounts)
        # print('refunded_delivery_amounts:', self.flat_sum(refunded_delivery_amounts), refunded_delivery_amounts)
        # print('refunded_amounts:', self.flat_sum(refunded_amounts), refunded_amounts)

    # sum of sublists
    def flat_sum(self, lst):
        return round(sum([item for sublist in lst for item in (sublist if isinstance(sublist, list) else [sublist])]),
                     2)

    # sum of sublists
    def flat_sum_tuple(self, lst):
        return round(sum([item for sublist in lst for item in (sublist if isinstance(sublist, tuple) else [sublist])]),
                     2)

    def _calculate_transaction_fee_deduction(self,refund_cost_on):
        if refund_cost_on in ['USER', 'STORE_TO_USER']:
            return self.transaction_fee_deduction_perc
        return 0

    def calculate_refund_amount(self, row):
        calculate_refund_amount = row['product_amount'] + row['product_delivery_fee']
        delivery_deduction = row['product_delivery_fee'] if row['is_shipped'] else 0
        transaction_fee_deduction = self._calculate_transaction_fee_deduction(row['refund_cost_on'])
        transaction_fee_and_tds_deduction_perc = transaction_fee_deduction * (
                1 + 0.01)  # tds is 1% of transaction fee
        refund_amount = (calculate_refund_amount - delivery_deduction) * (
                    100 - transaction_fee_and_tds_deduction_perc) / 100
        return round(refund_amount, 2)

    def calculate_product_refund_amount(self, row):
        product_amount = row['product_amount']
        product_delivery_fee = 0  # row['product_delivery_fee']
        calculate_refund_amount = product_amount  # + product_delivery_fee
        delivery_deduction = product_delivery_fee if row['is_shipped'] else 0
        transaction_fee_deduction = self._calculate_transaction_fee_deduction(row['refund_cost_on'])
        transaction_fee_and_tds_deduction_perc = transaction_fee_deduction * (1 + 0.01)  # tds is 1% of transaction fee
        refund_amount = (calculate_refund_amount - delivery_deduction) * (
                    100 - transaction_fee_and_tds_deduction_perc) / 100
        return round(refund_amount, 2)

    def calculate_delivery_fee_refund_amount(self, row):
        product_amount = 0  # row['product_amount']
        product_delivery_fee = row['product_delivery_fee']
        calculate_refund_amount = product_amount + product_delivery_fee
        delivery_deduction = product_delivery_fee if row['is_shipped'] else 0
        transaction_fee_deduction = self._calculate_transaction_fee_deduction(row['refund_cost_on'])
        transaction_fee_and_tds_deduction_perc = transaction_fee_deduction * (1 + 0.01)  # tds is 1% of transaction fee
        refund_amount = (calculate_refund_amount - delivery_deduction) * (
                    100 - transaction_fee_and_tds_deduction_perc) / 100
        return round(refund_amount, 2)

    def update_store_df_requested_refund(self, group):
        """
        Update is_requested_refund for store-df rows if all other store-df rows
        with non-empty suborders are either requested for refund or deleted.
        """
        non_empty_store_df = group[(group['suborder_number'] != '') & (group['delivery_fee_scope'] == 'store-df')]

        if not non_empty_store_df.empty:
            all_requested_or_deleted = ((non_empty_store_df['is_requested_refund'].fillna(False) |
                                         non_empty_store_df['is_suborder_deleted'].fillna(False))).all()

            if all_requested_or_deleted:
                group.loc[group['delivery_fee_scope'] == 'store-df', 'is_requested_refund'] = True

        return group

    def create_breakups(self, suborder_payment_df):
        # create lists of product amounts, product delivery fees, amount paid for selected products
        # product amounts should only come from rows with suborder_number not empty
        # delivery fees can come from all rows but suborder number empty rows should come in list format
        # amount paid for selected products should be sum of product amounts and product delivery fees for is_requested_refund == True rows
        product_amounts = suborder_payment_df[(suborder_payment_df['suborder_number'] != '') & (suborder_payment_df['is_requested_refund'] == True)]['product_amount'].tolist()

        product_delivery_fees = suborder_payment_df[suborder_payment_df['is_requested_refund'] == True].apply(
            lambda row: (row['product_delivery_fee'],) if row['suborder_number'] == '' else row['product_delivery_fee'],
            axis=1
        ).tolist()

        amount_paid_for_selected_products = suborder_payment_df[
            suborder_payment_df['is_requested_refund'] == True].apply(
            lambda row: (row['product_amount'] + row['product_delivery_fee'],) if row['suborder_number'] == '' else row[
                                                                                                                        'product_amount'] +
                                                                                                                    row[
                                                                                                                        'product_delivery_fee'],
            axis=1
        ).tolist()

        # refund amounts for is_requested_refund == True rows
        refunded_amounts = suborder_payment_df[suborder_payment_df['is_requested_refund'] == True].apply(
            lambda row: (row['refunded_amount'],) if row['suborder_number'] == '' else row['refunded_amount'],
            axis=1
        ).tolist()

        refunded_product_amounts = suborder_payment_df[suborder_payment_df['is_requested_refund'] == True].apply(
            lambda row: (row['product_refund_amount'],) if row['suborder_number'] == '' else row[
                'product_refund_amount'],
            axis=1
        ).tolist()

        refunded_delivery_amounts = suborder_payment_df[suborder_payment_df['is_requested_refund'] == True].apply(
            lambda row: (row['delivery_fee_refund_amount'],) if row['suborder_number'] == '' else row[
                'delivery_fee_refund_amount'],
            axis=1
        ).tolist()

        return (product_amounts, product_delivery_fees, amount_paid_for_selected_products,
                refunded_amounts, refunded_product_amounts, refunded_delivery_amounts)

    def process_suborder_payment(self, suborder_payment_df):
        # Get transaction fee percentage from order table
        suborder_payment_df['transaction_fee'] = (suborder_payment_df['product_amount'] + suborder_payment_df[
            'product_delivery_fee']) * self.transaction_fee_deduction_perc
        suborder_payment_df['transaction_fee_product'] = suborder_payment_df[
                                                             'product_amount'] * self.transaction_fee_deduction_perc
        suborder_payment_df['transaction_fee_delivery'] = suborder_payment_df[
                                                              'product_delivery_fee'] * self.transaction_fee_deduction_perc

        # apply calculate_refund_amount only to rows with suborder_number not empty
        suborder_payment_df['old_refunded_amount'] = suborder_payment_df[
            suborder_payment_df['suborder_number'] != ''].apply(self.calculate_refund_amount, axis=1)

        # Refund breakups
        suborder_payment_df['product_refund_amount'] = suborder_payment_df.apply(self.calculate_product_refund_amount,
                                                                                 axis=1)
        suborder_payment_df['delivery_fee_refund_amount'] = suborder_payment_df.apply(
            self.calculate_delivery_fee_refund_amount, axis=1)
        suborder_payment_df['refunded_amount'] = suborder_payment_df['product_refund_amount'] + suborder_payment_df[
            'delivery_fee_refund_amount']

        # Update store df request refund flag
        suborder_payment_df = suborder_payment_df.groupby('order_number').apply(self.update_store_df_requested_refund)

        # Reset the index if needed
        suborder_payment_df = suborder_payment_df.reset_index(drop=True)

        (product_amounts, product_delivery_fees, amount_paid_for_selected_products,
         refunded_amounts, refunded_product_amounts, refunded_delivery_amounts) = self.create_breakups(suborder_payment_df)

        return suborder_payment_df, (product_amounts, product_delivery_fees, amount_paid_for_selected_products,
                                     refunded_amounts, refunded_product_amounts, refunded_delivery_amounts)


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        if isinstance(obj, float):
            if math.isnan(obj):
                return None
            if math.isinf(obj):
                return str(obj)
        return super().default(obj)


def format_df_to_json(df, breakups, tranaction_fee_deduction_perc):
    # Helper function to format currency
    def format_currency(value):
        return f"₹{value:.2f}"

    def format_breakup(value):
        return ' + '.join(['₹' + str(v) for v in value])

    def get_date(date):
        if date is None:
            return None
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%Y-%m-%d %H:%M:%S")

    (product_amounts, product_delivery_fees, amount_paid_for_selected_products,
     refunded_amounts, refunded_product_amounts,
     refunded_delivery_amounts) = breakups

    # Calculate totals
    product_total = round(sum(product_amounts), 2)
    delivery_fee_total = round(sum([item[0] if isinstance(item, tuple) else item for item in product_delivery_fees]), 2)
    amount_paid_total = round(sum([item[0] if isinstance(item, tuple) else item for item in amount_paid_for_selected_products]), 2)
    product_refund_total = round(sum([item[0] if isinstance(item, tuple) else item for item in refunded_product_amounts]), 2)
    delivery_refund_total = round(sum([item[0] if isinstance(item, tuple) else item for item in refunded_delivery_amounts]), 2)
    total_refund = round(sum([item[0] if isinstance(item, tuple) else item for item in refunded_amounts]), 2)

    # Calculate transaction fee (assuming 2% of product total)
    transaction_fee = round(Decimal(amount_paid_total) * Decimal(tranaction_fee_deduction_perc/100), 2)

    tds = (amount_paid_total - total_refund)*0.01

    result = {
        "message": "success",
        "product_details": [
            {
                "order_breakup_item_text": "Product total",
                "order_breakup_item_subtext": format_breakup(product_amounts),
                "order_breakup_item_value": format_currency(product_total)
            },
            {
                "order_breakup_item_text": "Delivery fee",
                "order_breakup_item_subtext": format_breakup(product_delivery_fees),
                "order_breakup_item_value": format_currency(delivery_fee_total)
            },
            {
                "order_breakup_item_text": "Amount paid for selected products",
                "order_breakup_item_subtext": format_breakup(amount_paid_for_selected_products),
                "order_breakup_item_value": format_currency(amount_paid_total)
            }
        ],
        "refund_details": [
            {
                "order_breakup_item_text": "Product total refund",
                "order_breakup_item_subtext": format_breakup(refunded_product_amounts),
                "order_breakup_item_value": format_currency(product_refund_total)
            },
            {
                "order_breakup_item_text": "Delivery fee refund",
                "order_breakup_item_subtext": format_breakup(refunded_delivery_amounts),
                "order_breakup_item_value": format_currency(delivery_refund_total)
            },
            {
                "order_breakup_item_text": "Promotion discount adjustment",
                "order_breakup_item_subtext": None,
                "order_breakup_item_value": "₹0"
            },
            {
                "order_breakup_item_text": f"Transaction fee({tranaction_fee_deduction_perc}%)",
                "order_breakup_item_subtext": None,
                "order_breakup_item_value": f"-{format_currency(transaction_fee)}"
            },
            {
                "order_breakup_item_text": "TDS",
                "order_breakup_item_subtext": None,
                "order_breakup_item_value": f"-{format_currency(tds)}"
            },
            {
                "order_breakup_item_text": "Total refund amount",
                "order_breakup_item_subtext": format_breakup(refunded_amounts),
                "order_breakup_item_value": format_currency(total_refund)
            }
        ],
        "cancelled_products_details": []
    }

    # Add cancelled products details
    for _, row in df.iterrows():
        if row['suborder_number'] and row['suborder_status'] == 'ORDER_CANCELLED_BY_BUYER':
            refunded_item = RefundedAmount.objects.filter(
                suborder_reference=row['suborder_number']
            ).first()
            if refunded_item:
                refunded_date = get_date(refunded_item.refunded_datetime)
                requested_date = get_date(refunded_item.refund_scheduled_datetime)
                refund_status = refunded_item.refund_status
            else:
                refunded_date = None
                requested_date = None
                refund_status = None

            cancelled_product = {
                "suborder_number": row['suborder_number'],
                "suborder_status": row['suborder_status'],
                "return_cost_on": row['refund_cost_on'].lower(),
                "is_shipped": row['is_shipped'],
                "df_settings": "store_settings" if row['delivery_fee_scope'] == 'store-df' else "product_settings",
                "df_type": "per product" if row['product_delivery_fee'] > 0 else "per order",
                "product_price": row['product_amount'],
                "product_df": row['product_delivery_fee'],
                "df_reversal": row['delivery_fee_refund_amount'],
                "transaction_fee": row['transaction_fee'],
                "refunded_amount": row['refunded_amount'],
                "requested_date": requested_date,
                "refunded_date": refunded_date,
                "refund_status": refund_status
            }
            result["cancelled_products_details"].append(cancelled_product)

    return json.loads(json.dumps(result, cls=CustomJSONEncoder))




class OrderedProductDetailsWithSpecificVersion(generics.RetrieveAPIView):
    @staticmethod
    def get_date(date):
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

    @staticmethod
    def get_delivery_info(delivery_settings_id, req_type):
        delivery_info = None
        if req_type == "delivery_by":
            delivery_info = 0
        if req_type == "delivery_partner":
            delivery_info = None
        if req_type == "log_partner_name":
            delivery_info = None

        delivery_settings_instance = DeliverySettings.objects.get(
            deliverysettingid=delivery_settings_id
        )

        if req_type == "delivery_by":
            delivery_info = delivery_settings_instance.time_to_deliver

        if (
            req_type == "delivery_partner"
            and delivery_settings_instance.deliverymethod_logistics
        ):
            delivery_info = "logistics"

        if req_type == "delivery_fee":

            if delivery_settings_instance.deliveryfee_value:
                df_value = delivery_settings_instance.deliveryfee_value
                df_value_type = delivery_settings_instance.deliveryfee_valuetype
                delivery_info = "₹{} {}".format(df_value, df_value_type)

        if req_type == "log_partner_name":
            if delivery_settings_instance.deliverymethod_logistics:
                delivery_info = delivery_settings_instance.default_logistic_partner
        return delivery_info

    @staticmethod
    def get_return_info(refund_warranty_id, req_type):
        return_info = None
        if req_type == "return_period":
            return_info = None
        elif req_type == "return_conditions":
            return_info = []
        elif req_type == "return_pick_up":
            return_info = None
        elif req_type == "return_cost_on":
            return_info = None

        refund_instance = RefundAndWarranty.objects.get(
            refundandwarrantyid=refund_warranty_id
        )

        if req_type == "return_period":
            if refund_instance.return_type:
                return_info = refund_instance.return_period
            else:
                return_info = None

        elif req_type == "return_pick_up":
            return_pick_up = refund_instance.return_pickup
            if return_pick_up:
                return_info = return_pick_up

        elif req_type == "return_conditions":
            return_conditions = refund_instance.return_conditions
            if return_conditions:
                return_info = return_conditions.split("|")

        elif req_type == "return_cost_on":
            if refund_instance.return_cost_on_customer:
                return_info = "customer"
            elif refund_instance.return_cost_on_seller:
                return_info = "seller"

        return return_info

    def get(self, request, *args, **kwargs):
        user_pincode = request.query_params.get("user_pincode")
        suborder_number = kwargs["suborder_number"]
        suborder_instance = SubOrder.objects.get(suborder_number=suborder_number)
        product_version = suborder_instance.product_version
        product_reference = suborder_instance.product_reference
        store_reference = suborder_instance.store_reference

        delivery_settings_id = suborder_instance.delivery_settings_id
        refund_warranty_id = suborder_instance.refund_warranty_id

        product_instance = Product.objects.get(product_reference=product_reference)
        updated_date = self.get_date(product_instance.modified_date)
        created_date = self.get_date(product_instance.created_date)
        product_category = product_instance.product_category
        count_of_ratings = product_instance.count_of_ratings
        rating = product_instance.rating
        swadeshi_made = product_instance.swadeshi_made
        swadeshi_brand = product_instance.swadeshi_brand
        like_count = product_instance.like_count
        repost_count = product_instance.repost_count
        repost_plus_count = product_instance.repost_plus_count
        comment_count = product_instance.comment_count
        save_count = product_instance.save_count
        share_count = product_instance.share_count
        orders_count = product_instance.orders_count
        returns_count = product_instance.returns_count
        cancels_count = product_instance.cancels_count
        product_list_serializer = GetProductListSerializer(context={"user_pincode": user_pincode})
        deliverability = product_list_serializer.get_deliverability(obj=product_instance)
        image = None
        location = None
        swadeshi_owned = None
        if TrustCenter.objects.filter(store_reference=store_reference).exists():
            trust_center = TrustCenter.objects.get(store_reference=store_reference)
            location = trust_center.city
            store_icon = trust_center.store_reference.icon
            swadeshi_owned = trust_center.swadeshi_owned
            if store_icon:
                image = store_icon.url


        # if product_version and delivery_settings_id and refund_warranty_id:

        product_version_instance = ProductVersion.objects.filter(
            product_reference=product_reference, product_version=product_version
        ).first()
        latest_product_version = Product.objects.values_list(
            "product_version", flat=True
        ).get(product_reference=product_reference)
        serializer = ProductVersionSerializer(product_version_instance)

        serializer_copy = serializer.data
        serializer_copy["updated_date"] = updated_date
        serializer_copy["created_date"] = created_date
        serializer_copy["location"] = location
        serializer_copy["product_category"] = product_category
        serializer_copy["count_of_ratings"] = count_of_ratings
        serializer_copy["rating"] = rating
        serializer_copy["store_icon"] = image

        delivery_by = self.get_delivery_info(delivery_settings_id, "delivery_by")
        delivery_partner = self.get_delivery_info(
            delivery_settings_id, "delivery_partner"
        )
        logistic_partner_name = self.get_delivery_info(
            delivery_settings_id, "log_partner_name"
        )
        delivery_fee = self.get_delivery_info(delivery_settings_id, "delivery_fee")

        serializer_copy["delivery_by"] = delivery_by
        serializer_copy["delivery_partner"] = delivery_partner
        serializer_copy["logistic_partner_name"] = logistic_partner_name
        serializer_copy["delivery_fee"] = delivery_fee

        return_period = self.get_return_info(refund_warranty_id, "return_period")
        return_cost_on = self.get_return_info(refund_warranty_id, "return_cost_on")
        return_conditions = self.get_return_info(
            refund_warranty_id, "return_conditions"
        )
        return_pick_up = self.get_return_info(refund_warranty_id, "return_pick_up")

        serializer_copy["return_period"] = return_period
        serializer_copy["return_cost_on"] = return_cost_on
        serializer_copy["return_conditions"] = return_conditions
        serializer_copy["return_pick_up"] = return_pick_up

        serializer_copy["swadeshi_made"] = swadeshi_made
        serializer_copy["swadeshi_brand"] = swadeshi_brand
        serializer_copy["swadeshi_owned"] = swadeshi_owned

        serializer_copy["like_count"] = like_count
        serializer_copy["repost_count"] = repost_count
        serializer_copy["repost_plus_count"] = repost_plus_count
        serializer_copy["comment_count"] = comment_count
        serializer_copy["save_count"] = save_count
        serializer_copy["share_count"] = share_count
        serializer_copy["orders_count"] = orders_count
        serializer_copy["returns_count"] = returns_count
        serializer_copy["cancels_count"] = cancels_count
        serializer_copy["deliverability"] = deliverability
        product_list_serializer = GetProductListSerializer()
        refund_responsibility = product_list_serializer.get_refund_responsibility(obj=product_instance)
        serializer_copy['refund_responsibility'] = refund_responsibility

        return Response(
            {
                "message": "success",
                "latest_product_version": latest_product_version,
                "data": serializer_copy,
            },
            status=status.HTTP_200_OK,
        )


@api_view(["GET"])
def auto_cancel_order_every_day_midnight(request):
    if request.method == "GET":
        # auto cancellation of order.
        # if seller haven't confirmed the order even after three days of order creation, then the order will get
        # auto cancelled.
        logger.info("Entered order auto cancel function.")
        all_suborder = SubOrder.objects.filter(
            suborder_status="WAITING_FOR_CONFIRMATION", is_deleted=False
        )
        execution_count = 0
        for suborder in all_suborder:
            valid_period = suborder.created_date + datetime.timedelta(hours=1)

            if datetime.date.today() > valid_period.date():
                execution_count += 1
                suborder_numbers = suborder.suborder_number
                suborder_number_list = [suborder_numbers]
                user_reference = suborder.user_reference
                order_reference = suborder_number_list[0].split("-")[0]
                cancelled_by = "auto cancelled"
                refund_request_initiated_date = datetime.datetime.now()
                suborder_instance = SubOrder.objects.get(
                    suborder_number=suborder_numbers
                )

                RefundedAmount.objects.create(
                    user_reference=user_reference,
                    order_reference=order_reference,
                    suborder_reference=suborder_instance,
                    refund_request_initiated_date=refund_request_initiated_date,
                    refund_status=RefundedAmount.Status.UNPROCESSED,
                )
                update_refund_amount_old(
                    suborder_number_list, order_reference, cancelled_by
                )
                logger.info(
                    "refunded amount entry for suborder %s has created",
                    suborder_numbers,
                )

                result = initiate_refund(suborder_number_list)

                if result == "error":
                    logger.info(
                        "refund initiation for sub_order %s cancellation has failed.",
                        suborder_numbers,
                    )
                else:
                    refund_instance = RefundedAmount.objects.get(
                        suborder_reference=suborder_numbers
                    )
                    refund_instance.refund_requested_date = datetime.datetime.now()
                    refund_instance.save(update_fields=["refund_requested_date"])
                    logger.info(
                        "refund initiation for sub_order %s cancellation is success.",
                        suborder_numbers,
                    )

                suborder.suborder_status = "ORDER_AUTO_CANCELLED"
                suborder.cancelled_by = cancelled_by
                now = datetime.datetime.now()
                suborder.cancelled_date = now.strftime("%d/%m/%Y %H:%M:%S")
                suborder.is_deleted = True
                suborder.save(
                    update_fields=[
                        "suborder_status",
                        "cancelled_date",
                        "cancelled_by",
                        "is_deleted",
                    ]
                )

                OrderLifeCycle.objects.create(
                    orderid=suborder.orderid,
                    suborderid_id=suborder.suborderid,
                    created_by=suborder.orderid.userid,
                    suborder_status=suborder.suborder_status,
                    suborder_number=suborder.suborder_number,
                    order_number=suborder.order_number,
                    cancelled_date=suborder.cancelled_date,
                )
                if SubOrderPaymentDetails.objects.filter(
                    suborder_number=suborder.suborder_number
                ).exists():
                    order_payment_details_instance = SubOrderPaymentDetails.objects.get(
                        suborder_number=suborder.suborder_number
                    )
                    order_payment_details_instance.is_suborder_deleted = True
                    order_payment_details_instance.suborder_deleted_condition = (
                        "AUTO_CANCELLED"
                    )
                    order_payment_details_instance.save(
                        update_fields=[
                            "is_suborder_deleted",
                            "suborder_deleted_condition",
                        ]
                    )

                if OrderPayout.objects.filter(
                    suborder_number=suborder.suborder_number
                ).exists():
                    order_payout_instance = OrderPayout.objects.get(
                        suborder_number=suborder.suborder_number
                    )
                    order_payout_instance.payout_status = (
                        OrderPayout.Payout_Status.WAITING_FOR_RELEASE
                    )
                    order_payout_instance.save(update_fields=["payout_status"])
                logger.info("%s suborders got auto cancelled", execution_count)
                logger.info("Exited order auto cancel function.")

        return Response({"message": "success"}, status=status.HTTP_200_OK)


class LogisticPartnerAV(generics.ListCreateAPIView):
    serializer_class = LogisticPartnerSerializer

    def get(self, request):
        """get list of categories (in store onboard page)"""
        couriers = LogisticPartner.objects.all()
        serializer = self.get_serializer(couriers, many=True)
        return Response({"message": "success", "data": serializer.data})

    # here create will be a bulk create. instead of an object,
    # input will be a list of objects.
    def get_serializer(self, *args, **kwargs):
        if isinstance(kwargs.get("data", {}), list):
            kwargs["many"] = True
        return super(LogisticPartnerAV, self).get_serializer(*args, **kwargs)


class InvoiceAPIView(APIView):
    def get(self, request, order_id):
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
        shared_media_dir = os.path.join(project_root, 'media')
        invoice_path = os.path.join(shared_media_dir, 'templates', f'generated_invoice_{order_id}.html')

        order_fees_details = OrderFeesDetails()
        order_payment_response = order_fees_details.get(request, is_seller=True, order_number=order_id)
        order_response = order_payment_response.data
        store_id = 0
        user_id = 0

        try:
            order = Order.objects.get(order_number=order_id)
            store_id = order.storeid
            user_id = order.userid
        except Order.DoesNotExist:
            return Response({"message": "Order not found"}, status=status.HTTP_404_NOT_FOUND)

        if order_response:
            order_data = order_response
            order_breakup_items = order_data
            item_1_subtext = int(order_breakup_items['grand_total'][0]["order_breakup_item_value"].replace("₹", "").replace(",", ""))
            item_2_value = int(order_breakup_items['grand_total'][1]["order_breakup_item_value"].replace("₹", "").replace(",", ""))

            customer_details = GetCustomerDetails()
            customer_response = customer_details.get(request, order_number=order_id, user_id=user_id)

            if customer_response:
                customer_data = customer_response.data

                billing_address = (
                    f"{customer_data['data']['billing_person_name']}\n"
                    f"{customer_data['data']['billing_address']}\n"
                    f"{customer_data['data']['billing_city']}, {customer_data['data']['billing_state']}, {customer_data['data']['billing_pincode']}"
                )

                shipping_address = (
                    f"{customer_data['data']['delivered_person_name']}\n"
                    f"{customer_data['data']['delivered_address']}\n"
                    f"{customer_data['data']['delivered_city']}, {customer_data['data']['delivered_state']}, {customer_data['data']['delivered_pincode']}\n"
                    f"{customer_data['data']['delivered_contact_number']}"
                )

                store_order_details = GetOrderDetailsByStore(kwargs={'storeid': store_id, 'order_number': order_id})
                store_order_response = store_order_details.get(request, order_number=order_id)
                store_order_data = store_order_response.data
                order_details = store_order_data['data'][0]
                order_items = order_details['orderitems'][0]
                store_reference = order_items['store_details']['store_reference']

                if store_order_response.data:
                    order_number = order_details['order_number']
                    order_date = order_details['ordered_date']
                    delivery_person_name = order_items['delivery_person_name']
                    delivery_person_contact = order_items['delivery_person_contact']
                    shipping_fee = item_2_value - item_1_subtext

                    store_name = None
                    pan_number = None
                    gst_number = None
                    store_handle = None
                    store_address = None
                    store_email = None
                    category_name = None
                    store_signature = None

                    store_details = StoreDetailsAV()
                    store_response = store_details.get(request, store_reference=store_reference)

                    if store_response.data:
                        store_data = store_response.data.get('data', {})
                        store_name = store_data.get('store_name')
                        store_handle = store_data.get('storehandle')
                        pan_number = store_data.get('pan_number')
                        gst_number = store_data.get('gst_number')
                        category_name = store_data.get('category_name')
                        store_signature_str = store_data.get('store_signature')
                        store_signature = os.path.normpath(store_signature_str)

                        trust_center = GetTrustCenterAV()
                        trust_center_response = trust_center.get(request, store_reference=store_reference)

                        if trust_center_response.data:
                            trust_center_data = trust_center_response.data.get("data", {})
                            store_email = trust_center_data.get('emailid')
                            store_address = (
                                f"{trust_center_data.get('address')},\n"
                                f"{trust_center_data.get('city')},\n"
                                f"{trust_center_data.get('state')} - {trust_center_data.get('pincode')}"
                            )

                    invoice_data = {
                        'order_details': order_data,
                        'billing_address': billing_address,
                        'shipping_address': shipping_address,
                        'order_number': order_number,
                        'order_date': order_date,
                        'shipping_fee': shipping_fee,
                        'delivery_person_name': delivery_person_name,
                        'delivery_person_contact': delivery_person_contact,
                        'store_name': store_name,
                        'storehandle': store_handle,
                        'store_email': store_email,
                        'pan_number': pan_number,
                        'gst_number': gst_number,
                        'store_address': store_address,
                        'category_name': category_name,
                        'store_signature': store_signature
                    }

                    invoice_html = self.create_invoice_html(invoice_data)
                    with open(invoice_path, 'w', encoding='utf-8') as file:
                        file.write(invoice_html)

                    # Return HTML as a string within a JSON response
                    return Response({
                        "message": "success",
                        "data": invoice_html
                    }, status=status.HTTP_200_OK)

                else:
                    return Response({"message": "Failed to get store order details"}, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"message": "Failed to get customer details"}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({"message": "Failed to get order payment details"}, status=status.HTTP_400_BAD_REQUEST)

    def create_invoice_html(self, invoice_data):
        conversion = inflect.engine()
        order_details = invoice_data['order_details']
        billing_address = invoice_data['billing_address']
        shipping_address = invoice_data['shipping_address']
        order_number = invoice_data['order_number']
        order_date = invoice_data['order_date']
        shipping_fee = invoice_data['shipping_fee']
        delivery_person_name = invoice_data['delivery_person_name']
        delivery_person_contact = invoice_data['delivery_person_contact']
        company_name = invoice_data['store_name']
        store_email = invoice_data['store_email']
        storehandle = invoice_data['storehandle']
        pan_no = invoice_data['pan_number']
        gstin_no = invoice_data['gst_number']
        registered_address = invoice_data['store_address']
        store_signature = invoice_data['store_signature']
        invoice_number = 'INV' + order_number[-5:]
        invoice_details = 'Invoice for ' + invoice_data['category_name'] + ' purchased on ' + order_date

        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
        shared_media_dir = os.path.join(project_root, 'media')
        template_path = os.path.join(shared_media_dir, 'templates', 'invoice-template.html')
        logo_path = os.path.join(shared_media_dir, 'images', 'swadesic_logo.jpg')
        store_signature_path = os.path.join(project_root, store_signature.lstrip('\\'))

        with open(template_path, 'r', encoding='utf-8') as file:
            template_content = file.read()
        template = Template(template_content)

        product_rows = []
        sub_total = 0
        for i, product in enumerate(order_details["product_details"]["group1"], start=1):
            total_product_price = product.get('total_product_price')
            sub_total = sub_total + total_product_price
            product_rows.append({
                'index': i,
                'brand': product.get('product_brand'),
                'name': product.get('product_name'),
                'unit_price': product.get('product_price'),
                'quantity': product.get('product_quantity'),
                'total': product.get('total_product_price')
            })

        with open(logo_path, 'rb') as image_file:
            encoded_logo = base64.b64encode(image_file.read()).decode('utf-8')
        with open(store_signature_path, 'rb') as image_file:
            encoded_signature = base64.b64encode(image_file.read()).decode('utf-8')

        total_amount_paid = sub_total + shipping_fee
        amount_in_words = conversion.number_to_words(total_amount_paid, andword='and', zero='Zero').replace(',', '')
        amount_in_words = amount_in_words.capitalize() + ' Only'

        html_output = template.render(
            logo=encoded_logo,
            signature_image=encoded_signature,
            product_rows=product_rows,
            billing_address=billing_address.replace("\n", "<br>"),
            shipping_address=shipping_address.replace("\n", "<br>"),
            order_number=order_number,
            order_date=order_date,
            sub_total=sub_total,
            shipping_fee=shipping_fee,
            total_amount_paid=total_amount_paid,
            amount_in_words=amount_in_words,
            store_contact_name=delivery_person_name,
            store_phone=delivery_person_contact,
            store_email=store_email,
            company_name=company_name,
            storehandle=storehandle,
            pan_no=pan_no,
            gstin_no=gstin_no,
            registered_address=registered_address,
            supplied_company_name=company_name,
            supplied_address=registered_address.replace("\n", "<br>"),
            invoice_details=invoice_details,
            invoice_date=order_date,
            invoice_number=invoice_number
        )

        return html_output


class InvoiceAPIView_new(APIView):
    def get(self, request):

        try:
            # Get order_number from query params
            order_number = request.query_params.get('order_number')
            if not order_number:
                return Response({"error": "order_number is required in query parameters"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get invoice data from the request
            invoice_data = self.get_invoice_data(order_number)

            # Generate HTML using the existing function

            html_output = InvoiceGenerator().create_invoice_html(invoice_data)
            return Response({
                "message": "success",
                "html": f"@@@{html_output}@@@"
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": f"An unexpected error occurred: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def number_to_words(self,number):
        """Convert any number to words"""
        units = ["", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"]
        teens = ["Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"]
        tens = ["", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"]
        
        if number == 0:
            return "Zero"
        
        def convert_below_thousand(n):
            if n < 10:
                return units[n]
            elif n < 20:
                return teens[n - 10]
            elif n < 100:
                return tens[n // 10] + (" " + units[n % 10] if n % 10 != 0 else "")
            else:
                return units[n // 100] + " Hundred" + (" and " + convert_below_thousand(n % 100) if n % 100 != 0 else "")
        
        result = ""
        if number >= 100000:
            result += convert_below_thousand(number // 100000) + " Lakh "
            number %= 100000
        
        if number >= 1000:
            result += convert_below_thousand(number // 1000) + " Thousand "
            number %= 1000
        
        if number > 0:
            result += convert_below_thousand(number)
        
        return result.strip() + "Rupees" + " Only"

    def get_invoice_data(self, order_number):
        # Implement the logic to fetch invoice data based on order_number
        """     
        invoice_data = {
            'storehandle': '@swadesic_store',
            'supplied_company_name': 'Organic Supplier Ltd.',
            'supplied_address': '123 Organic Farms,\nGreenfield Lane,\nNatural City, NC 12345',
            'company_name': 'Store Pvt Ltd.',
            'registered_address': '456 Market Street,\nTech Park,\nInnovation Hub,\nBengaluru,\nKarnataka, 560001',
            'pan_no': '**********',
            'gstin_no': '29**********1Z1',
            'buyer_name': 'Rajesh Kumar',
            'billing_address': '789 Residential Area,\nSunshine Apartments,\nFlat 12B,\nHyderabad,\nTelangana, 500032',
            'shipping_address': '789 Residential Area,\nSunshine Apartments,\nFlat 12B,\nHyderabad,\nTelangana, 500032',
            'phone_number': '+91 98765-43210',
            'order_number': 'ORD67890',
            'order_date': '2023-07-25',
            'invoice_number': 'INV67890',
            'invoice_details': 'Invoice for organic products purchased on 2023-07-25',
            'invoice_date': '2023-07-25',
            'products': [
                {'brand': 'Organic Farms', 'name': 'Organic Apples', 'unit_price': 200, 'quantity': 3, 'total': 600},
                {'brand': 'Eco Goods', 'name': 'Bamboo Toothbrush', 'unit_price': 50, 'quantity': 5, 'total': 250},
                {'brand': 'Green Life', 'name': 'Reusable Shopping Bag', 'unit_price': 100, 'quantity': 2, 'total': 200},
                {'brand': 'Eco Goods', 'name': 'Stainless Steel Straw', 'unit_price': 150, 'quantity': 2, 'total': 300},
            ],
            'sub_total': '1350.00',
            'shipping_fee': '50.00',
            'total_amount_paid': '1400.00',
            'amount_in_words': 'One Thousand Four Hundred Only',
            'store_contact_name': 'Amit Sharma',
            'store_phone': '+91 91234-56789',
            'store_email': '<EMAIL>'
        }
        """



        try:
            # Fetch the order
            order = Order.objects.get(order_number=order_number)
            
            # Fetch the store
            store = Store.objects.get(storeid=order.storeid)

            # Fetch the store trustcenter
            trustcenter = TrustCenter.objects.filter(store_reference=store).first()
            
            # Fetch the billing address
            billing_address = UserAddress.objects.get(useraddressid=order.billing_address_id.useraddressid)

            # Fetch the order payment details
            payment_details = SubOrderPaymentDetails.objects.filter(order_number=order)
            
            # order_payouts = order.order_payout_items.filter(order_type__in = [OrderPayout.OrderType.PRODUCT, OrderPayout.OrderType.STORE_DF, OrderPayout.OrderType.COMMISSION])

            # Define successful suborder statuses
            successful_suborder_statuses = [
                SubOrder.Suborder_Status.ORDER_DELIVERED,
            ]

            
            # Calculate totals for successful suborders only
            payment_details_for_successful_suborders = payment_details.filter(
                suborder_number__suborder_status__in=successful_suborder_statuses
            )

            sub_total = payment_details_for_successful_suborders.aggregate(
                total=Sum('product_amount')
            )['total'] or 0
            shipping_fee = (payment_details_for_successful_suborders.aggregate(
                total=Sum('product_delivery_fee')
            )['total'] or 0)

            if payment_details_for_successful_suborders.filter(delivery_fee_scope='store-df').exists():
                shipping_fee += payment_details.filter(
                    Q(suborder_number__isnull=True) & Q(delivery_fee_scope='store-df')
                ).aggregate(
                    total=Sum('product_delivery_fee')
                )['total'] or 0

            total_amount_paid = sub_total + shipping_fee


            # Prepare product details for successful suborders only
            product_details = []
            for payment_detail in payment_details_for_successful_suborders:
                suborder = payment_detail.suborder_number
                product_details.append({
                    'brand': suborder.product_brand,
                    'name': suborder.product_name,
                    'unit_price': payment_detail.product_unit_price,
                    'quantity': payment_detail.product_quantity,
                    'total': payment_detail.product_amount
                })

            # calculate refund handling costs

            refund_handling_costs = total_amount_paid - sub_total - shipping_fee

            base_url = config("BASE_URL")

            invoice_data = {
                'order_number': order.order_number,
                'order_date': order.created_date.strftime('%d/%m/%Y'),
                'supplied_company_name': store.store_name,
                'supplied_address': f"{trustcenter.address}, {trustcenter.city}, {trustcenter.state}, {trustcenter.pincode}",
                'billing_address': f"{billing_address.address}, {billing_address.city}, {billing_address.state}, {billing_address.pincode}",
                'shipping_address': f"{billing_address.address}, {billing_address.city}, {billing_address.state}, {billing_address.pincode}",
                'company_name': store.store_name,
                'registered_address': f"{trustcenter.address}, {trustcenter.city}, {trustcenter.state}, {trustcenter.pincode}",
                'delivery_person_name': '',  # Assuming store name as delivery person name
                'delivery_person_contact': '',
                'storehandle': store.storehandle,
                'pan_number': store.pan_number,
                'gst_number': store.gst_number,
                'store_address': f"{trustcenter.address}, {trustcenter.city}, {trustcenter.state}, {trustcenter.pincode}",
                'store_signature': f"{base_url}{store.store_signature.url}" if store.store_signature else None,
                'category_name': store.category_name,
                'products': product_details,
                'sub_total': sub_total,
                'shipping_fee': shipping_fee,
                'refund_handling_fee': refund_handling_costs,
                'total_amount_paid': total_amount_paid,
                'amount_in_words': self.number_to_words(int(total_amount_paid)),
                'store_contact_name': store.pan_name if store.pan_name else '',
                'store_phone': trustcenter.phonenumber if trustcenter.phonenumber else '',
                'store_email': trustcenter.emailid if trustcenter.emailid else '',
                'buyer_name': order.user_reference.user_name if order.user_reference and order.user_reference.user_name else 'Swadeshi consumer',
                'phone_number': order.user_reference.phonenumber if order.user_reference and order.user_reference.phonenumber else '',
                'invoice_number': f"INV-{order.order_number[-6:]}",  # Generate invoice number based on last 6 digits of order number
                'invoice_details': f"Invoice for order {order.order_number}",
                'invoice_date': order.created_date.strftime('%d/%m/%Y'),  # Use order creation date as invoice date
            
            }

            return invoice_data

        except Order.DoesNotExist:
            raise ValidationError("Order not found")
        except Store.DoesNotExist:
            raise ValidationError("Store not found")
        except UserAddress.DoesNotExist:
            raise ValidationError("Billing address not found")
        except Exception as e:
            raise ValidationError(f"Error fetching invoice data: {str(e)}")


class CreateOrderAndInitiatePayment(generics.GenericAPIView):
    are_all_test_store_products = False

    def get_deliverability(self, product_reference, store_reference, delivery_pincode):
        deliverability = False

        if DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
        ).exists():
            delivery_settings_instance = DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
            ).last()
        else:
            delivery_settings_instance = DeliverySettings.objects.filter(
                store_reference=store_reference, product_reference__isnull=True, is_deleted=False
            ).last()
        delivery_locations = delivery_settings_instance.delivery_locations

        if delivery_settings_instance.fulfillment_options == 'IN_STORE_PICKUP':
            return deliverability

        if delivery_locations:
            list_of_pincode = self.convert_delivery_locations_to_pincode(delivery_locations)
            if delivery_pincode in list_of_pincode:
                deliverability = True
        return deliverability

    @staticmethod
    def convert_delivery_locations_to_pincode(delivery_locations):
        queryset = DeliveryLocations.objects.all()
        '''Use python django-pandas package to filter the pincodes easily
        Read the querysets into a dataframe using read_frame method
        '''

        df_all_delivery_locations = read_frame(queryset)

        '''
        Convert the values in 'city', 'state' columns in dataframe and input 'delivery_locations' string 
        as lower case to avoid comparison issues
        '''

        df_all_delivery_locations['city'] = df_all_delivery_locations['city'].str.lower()
        df_all_delivery_locations['state'] = df_all_delivery_locations['state'].str.lower()

        list_of_locations = delivery_locations.lower().split("|")

        '''
        Filter the DataFrame (df_all_delivery_locations) to get pincodes based on specified 
        locations in either the 'state' or 'city' columns. The resulting pincodes are then converted to lists (tolist())
        '''

        state_pincodes = df_all_delivery_locations[df_all_delivery_locations.state.isin(list_of_locations)][
            'pincode'].tolist()
        city_pincodes = df_all_delivery_locations[df_all_delivery_locations.city.isin(list_of_locations)][
            'pincode'].tolist()
        input_pincodes = [x for x in list_of_locations if str(x).isnumeric()]

        ''' uncomment below line to test the lengths of each list'''

        # print([len(x) for  x  in [state_pincodes, city_pincodes, input_pincodes]])

        lst = list(set(input_pincodes + state_pincodes + city_pincodes))
        return lst

    """
    Handles the creation of a new order, including validating input data, checking deliverability, calculating fees, 
    and initiating the payment process.
    
    This method is part of the `OrderAPIView` class, which is a Django REST Framework view that handles 
    order-related API requests.
    
    Args:
        request (django.http.request.HttpRequest): The HTTP request object containing the order data.
        *args: Additional positional arguments.
        **kwargs: Additional keyword arguments.
    
    Returns:
        django.http.response.Response: A JSON response containing the order details, including any errors or issues 
        encountered during the order creation process.
    """

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        ## Wrapper method start
        # Extract and validate input data
        user_id, cart_items, billing_address_id, phone_number, delivery_note, seller_note, is_webpage = self.extract_input_data(request.data)

        # Validate input data
        self.validate_input_data(user_id, cart_items, billing_address_id, phone_number)

        # Get deliverypincode to validate deliverability,get billingaddress instance for further usage in creating order
        billing_address_instance, delivery_pincode = self.get_delivery_pincode(billing_address_id)

        # Calculate order and delivery fee breakup
        input_for_payment_details = self.calculate_fees(user_id, cart_items)

        # Group cart items at store level
        final_lst = self.group_cart_items(user_id, cart_items)

        # Create order request entry and update cart
        order_request_number = self.create_order_request(user_id, cart_items)

        # Validate stores and products
        validation_result, self.are_all_test_store_products = self.validate_stores_and_products(user_id, final_lst, delivery_pincode)
        if validation_result:
            return validation_result

        # Create orders and suborders
        output = self.create_orders_and_suborders(final_lst, user_id, order_request_number, billing_address_instance, phone_number, delivery_note, seller_note, input_for_payment_details)

        # Calculate final fees
        total_order_amount, total_product_amount, total_delivery_fee, store_list, order_delivery_fee_after_promotion, grant_total_after_promotion, store_order_df, order_amount_list, order_df_list = self.calculate_final_fees(user_id, cart_items)

        # Set payment initiation status to Orders, Payments & Suborders
        new_order_number, order_id = self.set_payment_initiation(order_request_number, total_order_amount, total_delivery_fee, total_product_amount)

        # Generate payment response data
        response_data = self.generate_payment_response_data(total_order_amount, new_order_number, order_id, user_id)
        
        if is_webpage:
            html_short_url = self.create_html_short_url(response_data)
        else:
            html_short_url = None

        return Response(
            {
                "message": "success",
                "payment_details": response_data,
                "html_short_url": html_short_url
            },
            status=status.HTTP_200_OK,
            )


    def extract_input_data(self, data):
        return (
            data.get("user_id"),
            data.get("cart_items"),
            data.get("billing_address_id"),
            data.get("phone_number"),
            data.get("delivery_note"),
            data.get("seller_note"),
            data.get("is_webpage"),
        )

    def validate_input_data(self, user_id, cart_items, billing_address_id, phone_number):
        if not all([user_id, cart_items, billing_address_id, phone_number]):
            raise ValidationError("Missing required fields")

    def get_delivery_pincode(self, billing_address_id):
        billing_address = UserAddress.objects.get(useraddressid=billing_address_id)
        return billing_address, billing_address.pincode

    def get_transaction_fee(self):
        order_configuration = OrderConfiguration.objects.all().first()
        return order_configuration.transaction_fee

    # def calculate_fees(self, user_id, cart_items):
    #     final_input = input_formatter_cart_to_fee_calculation_wrapper(user_id, cart_items)
    #     result = fee_calculation_wrapper(final_input)
    #     return result[3]

    def create_store_json(self,df):
        if not isinstance(df, pd.DataFrame):
            raise ValueError("Input must be a pandas DataFrame")

        df['delivery_fee_value'] = df['delivery_fee_value'].fillna(0)
        df['calculated_delivery_fee'] = df.apply(
            lambda row: row['delivery_fee_value'] * row['product_quantity'] if row['delivery_fee_valuetype'] == 'per product' else row['delivery_fee_value'], axis=1).fillna(0)
        df['delivery_fee_scope'] = ['store-df' if i else 'product-df' for i in (df.df_settings == 'store_settings') & (df.delivery_fee_valuetype == 'per order')]#df.apply(lambda row: 'store-df' if (row['df_settings'] == 'store_settings') & (row['delivery_fee_valuetype'] == 'per order') else 'product-df')
        df['product_delivery_fee'] = df.apply(lambda row: row['calculated_delivery_fee'] if row['delivery_fee_scope'] == 'product-df' else 0, axis=1)

        store_delivery_fee = df.loc[df['delivery_fee_scope'] == 'store-df', 'calculated_delivery_fee'].iloc[0] if df['delivery_fee_scope'].eq('store-df').any() else 0

        df['product_order_amount'] = df['total_product_price'] + df['product_delivery_fee']

        storeid = df['storeid'].iloc[0]
        store_reference = df['store_reference'].iloc[0]
        total_product_amount = df['total_product_price'].sum() # total product price = price * quantity
        total_delivery_fee = df['product_delivery_fee'].sum() + store_delivery_fee
        total_order_amount = total_product_amount + total_delivery_fee
        promotion_amount = 0
        delivery_fee_after_promotions = total_delivery_fee - promotion_amount

        df_product = df[['productid', 'delivery_settings_id','product_reference', 'product_quantity', 'product_price','product_delivery_fee','delivery_fee_scope']]

        df_product_dict = df_product.to_dict('records')


        final_json = {
            'storeid': storeid,
            'store_reference': store_reference,
            'total_product_amount': total_product_amount,
            'total_order_amount': total_order_amount,
            'total_delivery_fee': total_delivery_fee,
            'store_delivery_fee': store_delivery_fee,
            'delivery_fee_after_promotions': delivery_fee_after_promotions,
            'product_split': df_product_dict
        }

        return final_json

    def convert_to_serializable(self,obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return obj

    def create_multiple_store_json(self,df):
        all_store_data = []
        for storeid in df.storeid.unique():
            df_store = df[df.storeid == storeid]
            store_json = self.create_store_json(df_store)
            all_store_data.append(store_json)

        # serializable_data = [{k: self.convert_to_serializable(v) for k, v in store.items()} for store in all_store_data]
        return all_store_data

    def calculate_fees(self, user_id, cart_items):
        final_input = input_formatter_cart_to_fee_calculation_wrapper(user_id, cart_items)
        df = pd.DataFrame(final_input)
        return self.create_multiple_store_json(df) # alternate for # result = fee_calculation_wrapper(final_input)

    def group_cart_items(self, user_id, cart_items):
        cart_items = CartItem.new_objects.filter(
            userid=user_id, cartitemid__in=cart_items
        ).values("storeid", "product_reference", "product_quantity")
        input_data = list(cart_items)

        final_lst = {}
        for d in input_data:
            final_lst.setdefault(d["storeid"], []).append(
                [d["product_reference"], d["product_quantity"]]
            )
        return [{"storeid": k, "product_details": v} for k, v in final_lst.items()]

    def create_order_request(self, user_id, cart_items):
        order_request_number = get_order_request_number()
        # Updating CartItem table as well
        CartItem.new_objects.filter(userid=user_id, cartitemid__in=cart_items).update(order_request_number=order_request_number)
        return order_request_number

    def get_delivery_settings_instance(self, product_reference, store_reference):
        product_delvery_settings = DeliverySettings.objects.filter(product_reference=product_reference,
                                                                   is_deleted=False).last()
        if product_delvery_settings:
            return product_delvery_settings
        else:
            store_delvery_settings = DeliverySettings.objects.filter(store_reference=store_reference,
                                                                     is_deleted=False).last()
            return store_delvery_settings

    def validate_stores_and_products(self, user_id, final_lst, delivery_pincode):
        closed_store, deleted_store, deleted_product, non_deliverable_products, only_pickup_products = [], [], [], [], []
        test_stores = []
        real_stores = []
        is_user_profile_completed = User.objects.filter(userid=user_id).values_list("user_name",flat=True).first() is not None

        for elem in final_lst:
            store_id = elem["storeid"]
            store_instance = Store.objects.get(storeid=store_id)
            store_reference = store_instance.store_reference

            if store_instance.deleted:
                deleted_store.append(store_instance.storeid)
            elif not store_instance.open_for_order:
                closed_store.append(store_instance.storeid)
            else:
                # Check if the store is a test store or a real store
                if store_instance.is_test_store:
                    test_stores.append(store_instance.storeid)
                else:
                    real_stores.append(store_instance.storeid)

                for product_reference in elem["product_details"]:
                    product_instance = Product.objects.get(product_reference=product_reference[0])
                    if product_instance.deleted:
                        deleted_product.append(product_reference[0])
                    elif not self.get_deliverability(product_reference[0], store_reference, delivery_pincode):
                        non_deliverable_products.append(product_reference[0])

                    delivery_setting_instance = self.get_delivery_settings_instance(product_reference[0],
                                                                                    store_reference)
                    if delivery_setting_instance.fulfillment_options == 'IN_STORE_PICKUP':
                        only_pickup_products.append(product_reference[0])


        all_test_stores = len(test_stores) > 0 and len(real_stores) == 0


        # Check if there's a combination of test and real stores
        if test_stores and real_stores:
            return Response({
                "message": "You can't order products from test and original stores at the same time.",
                "is_custom": True
            }, status=status.HTTP_400_BAD_REQUEST), all_test_stores

        if closed_store or deleted_store or deleted_product or non_deliverable_products or not is_user_profile_completed:
            return Response({
                "message": "success",
                "closed_store": closed_store,
                "deleted_store": deleted_store,
                "deleted_product": deleted_product,
                "non_deliverable_products": non_deliverable_products,
                "is_user_profile_completed": is_user_profile_completed,
                "payment_details": {},
                "only_pickup_products": only_pickup_products
            }, status=status.HTTP_200_OK), all_test_stores

        return None, all_test_stores

    def create_orders_and_suborders(self, final_lst, user_id, order_request_number, billing_address_instance, phone_number, delivery_note, seller_note, input_for_payment_details):
        output = {}
        for elem in final_lst:
            store_id = elem["storeid"]
            new_order = self.create_order(user_id, store_id, order_request_number, billing_address_instance, phone_number, delivery_note, seller_note)
            order_reference = new_order.order_number
            output.setdefault(store_id, order_reference)

            self.create_suborders(elem, store_id, order_reference, new_order, input_for_payment_details, order_request_number)

        return output

    def create_order(self, user_id, store_id, order_request_number, billing_address_instance, phone_number, delivery_note, seller_note):

        new_order = Order.objects.create(
                userid=user_id,
                storeid=store_id,
                order_request_number=order_request_number,
                billing_address_id=billing_address_instance,
                order_phone_number=phone_number,
            )


        if delivery_note:
            new_order.delivery_note = delivery_note
            new_order.save(update_fields=["delivery_note"])

        if seller_note:
            for note in seller_note:
                if note["storeid"] == store_id:
                    new_order.seller_note = note["note"]
                    new_order.save(update_fields=["seller_note"])

        return new_order

    def create_suborders(self, elem, store_id, order_reference, new_order, input_for_payment_details, order_request_number):
        store = Store.objects.get(storeid=store_id)
        trust_center = TrustCenter.objects.get(storeid=store_id)

        for product in elem["product_details"]:
            product_instance = Product.objects.get(product_reference=product[0])
            product_image_instance = ProductImages.objects.filter(product_reference=product_instance).first()
            image = product_image_instance.product_image if product_image_instance else None

            sub_order = self.create_suborder(store, trust_center, product_instance, image, product, order_reference, new_order)

            create_suborder_payment_detail(input_for_payment_details, store_id, product_instance.product_reference, order_request_number, sub_order.suborder_number, order_reference)


    def create_suborder(self, store, trust_center, product_instance, image, product, order_reference, new_order):
        return SubOrder.objects.create(
            storeid=store.storeid,
            store_name=store.store_name,
            store_image=store.icon,
            store_category=store.category_name,
            store_description=store.store_desc,
            trust_score=trust_center.trustscore,
            seller_level=trust_center.sellerlevel,
            return_and_warranty_description=calculate_refund_warranty_info(store.storeid, product[0], flag="message"),
            productid=product_instance.productid,
            estimated_delivery_date=calculate_delivery_info(store.storeid, product[0], flag="estimated_delivery_date"),
            product_reference=product_instance.product_reference,
            product_image=image,
            product_name=product_instance.product_name,
            product_description=product_instance.product_description,
            product_brand=product_instance.brand_name,
            product_quantity=product[1],
            mrp_price=product_instance.mrp_price,
            selling_price=product_instance.selling_price,
            order_number=order_reference,
            suborder_number=create_suborder_number(order_reference),
            orderid=new_order,
            # transaction_fee=transaction_fee_percentage, # we are getting txn fee after the payment
            delivery_settings_version=calculate_delivery_info(store.storeid, product[0], flag="delivery_settings_version"),
            delivery_settings_type=calculate_delivery_info(store.storeid, product[0], flag="delivery_settings_type"),
            delivery_settings_id=calculate_delivery_info(store.storeid, product[0], flag="delivery_settings_id"),
            refund_warranty_version=calculate_refund_warranty_info(store.storeid, product[0], flag="refund_warranty_version"),
            refund_warranty_settings_type=calculate_refund_warranty_info(store.storeid, product[0], flag="refund_warranty_settings"),
            refund_warranty_id=calculate_refund_warranty_info(store.storeid, product[0], flag="refund_warranty_id"),
        )

    def calculate_final_fees(self, user_id, cart_items):
        final_input = input_formatter_cart_to_fee_calculation_wrapper(user_id, cart_items)
        return fee_calculation_wrapper(final_input)

    def set_payment_initiation(self, order_request_number, total_order_amount, total_delivery_fee, total_product_amount):
        # If Order entry exists, we update all tables with new order request number else we create a new entries in related (not all) tables
        # if Order.new_objects.filter(order_request_number=order_request_number, order_status=Order.Order_Status.PAYMENT_INITIATED).exists():
        #     new_order_request_number = get_order_request_number()
        #     Order.new_objects.filter(order_request_number=order_request_number).update(order_request_number=new_order_request_number)
        #     SubOrderPaymentDetails.objects.filter(order_request_number=order_request_number).update(order_request_number=new_order_request_number)
        #     new_order_number = Order.new_objects.filter(order_request_number=new_order_request_number).values_list("order_request_number", flat=True).first()
        #     order_id = Order.new_objects.filter(order_request_number=new_order_request_number).values_list("orderid", flat=True)
        # else:
        #     Order.new_objects.filter(order_request_number=order_request_number).update(
        #         order_status=Order.Order_Status.PAYMENT_INITIATED,
        #         total_delivery_fee=total_delivery_fee,
        #         total_product_amount=total_product_amount,
        #         total_order_amount=total_order_amount,
        #     )
        Order.new_objects.filter(order_request_number = order_request_number).update(order_status=Order.Order_Status.PAYMENT_INITIATED)
        self.update_order_payment_details(order_request_number)
        order_id = Order.new_objects.filter(order_request_number=order_request_number).values_list("orderid", flat=True)
        self.update_suborder_status(order_id)
        # new_order_number = Order.new_objects.filter(order_request_number=order_request_number).values_list("order_request_number", flat=True).first()

        return order_request_number, order_id

    def update_order_payment_details(self, order_request_number):
        suborder_payment_details_instance = SubOrderPaymentDetails.objects.filter(order_request_number=order_request_number)
        for suborder_payment_details in suborder_payment_details_instance:
            suborder_payment_details.payment_status = SubOrderPaymentDetails.Payment_Status.PAYMENT_INITIATED
            suborder_payment_details.save(update_fields=["payment_status"])

    def update_suborder_status(self, order_id):
        sub_order_items = SubOrder.new_objects.filter(orderid__in=order_id)
        for order in sub_order_items:
            order.suborder_status = SubOrder.Suborder_Status.PAYMENT_INITIATED
            order.save(update_fields=["suborder_status"])

    def generate_payment_response_data(self, grand_total, order_request_number,order_id, user_id):
        user_instance = User.objects.get(userid=user_id, deleted=False)
        dummy_token = get_dummy_txn_token()

        if SKIP_RAZORPAY_API:
            return self.generate_dummy_payment_data(grand_total, order_request_number, dummy_token, user_instance)
        else:
            return self.generate_razorpay_payment_data(grand_total, order_request_number, dummy_token, order_id, user_instance)

    def generate_dummy_payment_data(self, grand_total, order_request_number, dummy_token, user_instance):
        return {
            "key": RAZORPAY_ID,
            "amount": grand_total,
            "currency": "INR",
            "name": "Swadesic by Socially X",
            "description": "dummy_order_Oet4sfJJESHafo",
            "image": "https://raw.githubusercontent.com/Socially-X/public_assets/MASTER/Swadesic%20logo%20(1).png",
            "order_id": "dummy_order_Oet4sfJJESHafo",
            "prefill": {
                "name": user_instance.display_name if user_instance.display_name else user_instance.user_name,
                "email": user_instance.email,
                "contact": user_instance.phonenumber,
            },
            "notes": {
                "order_request_number": order_request_number,
                "txn_token": dummy_token
            },
            "theme": {
                "color": "#10c057"
            }
        }

    def generate_razorpay_payment_data(self, grand_total, order_request_number, dummy_token, order_id, user_instance):
        def prepare_order_data():
            return {
                "amount": int(grand_total) * 100,
                "currency": "INR",
                "receipt": dummy_token,
                "notes": {
                    "user_name": user_instance.display_name or user_instance.user_name,
                    "user_email": user_instance.email,
                    "user_contact": str(user_instance.phonenumber),
                    "order_request_number": order_request_number,
                    "transaction_id": dummy_token
                }
            }

        # 99.91 rupees 9991, 9990

        def make_razorpay_request(data):
            headers = {"Content-Type": "application/json"}
            if self.are_all_test_store_products:
                auth = HTTPBasicAuth(TEST_RAZORPAY_ID, TEST_RAZORPAY_KEY)
                logger.info(f"Using TEST Razorpay keys for order request number: {order_request_number}")

            else:
                auth = HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY)
                logger.info(f"Using LIVE Razorpay keys for order request number: {order_request_number}")


            return requests.post(
                'https://api.razorpay.com/v1/orders',
                headers=headers,
                auth=auth,
                json=data
            )

        def prepare_response_data(api_response):
            return {
                "key": RAZORPAY_ID,
                "amount": api_response["amount"]/100,
                "currency": api_response["currency"],
                "name": "Swadesic by Socially X",
                "description": api_response["id"],
                "image": "https://raw.githubusercontent.com/Socially-X/public_assets/MASTER/Swadesic%20logo%20(1).png",
                "order_id": api_response["id"],
                "prefill": {
                    "name": api_response["notes"]["user_name"],
                    "email": api_response["notes"]["user_email"],
                    "contact": api_response["notes"]["user_contact"],
                },
                "notes": {
                    "order_request_number": api_response["notes"]["order_request_number"],
                    "txn_token": api_response["receipt"]
                },
                "theme": {
                    "color": "#10c057"
                }
            }

        try:
            order_data = prepare_order_data()
            response = make_razorpay_request(order_data)
            api_response = response.json()

            if response.status_code == 200:
                try:
                    response_data = prepare_response_data(api_response)
                    txn_token = response_data["notes"]["txn_token"]
                    # Updating Order transaction token in Orders & Order life cycle table
                    Order.new_objects.filter(order_request_number=order_request_number).update(
                        txn_token=txn_token
                    )
                    OrderLifeCycle.objects.filter(orderid__in=order_id).update(txn_token=txn_token)
                    return response_data
                except Exception as e:
                    return Response({"message": "error", "data": str(e)}, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"message": "error", "data": f"Razorpay Order could not be created: {response.content}"},
                                status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"message": "error", "data": str(e)}, status=status.HTTP_400_BAD_REQUEST)


    def create_html_short_url(self, response_data):
        import os
        from django.conf import settings
        import uuid

        filename = f"payment_{response_data['notes']['order_request_number']}{uuid.uuid4().hex}.html"
        payments_dir = os.path.join(settings.MEDIA_ROOT, 'payment_html_pages')
        os.makedirs(payments_dir, exist_ok=True)
        file_path = os.path.join(payments_dir, filename)

        # Prepare context for template
        context = {
            'key': response_data['key'],
            'amount': str(int(float(response_data['amount']) * 100)),
            'currency': response_data['currency'],
            'name': response_data['name'],
            'description': response_data['description'],
            'image': response_data['image'],
            'order_id': response_data['order_id'],
            'callback_url': config('RAZORPAY_CALLBACK_URL'),
            'prefill': response_data['prefill'],
            'notes': response_data['notes'],
            'theme': response_data['theme']
        }

        # Render the template with context using the correct template path
        template_path = os.path.join(settings.BASE_DIR, 'orders', 'payment_api', 'pay.html')
        with open(template_path, 'r', encoding='utf-8') as template_file:
            template_content = template_file.read()

        template = Template(template_content)
        html_content = template.render(Context(context))

        # Write the rendered HTML to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return f"{settings.MEDIA_URL}payment_html_pages/{filename}"
