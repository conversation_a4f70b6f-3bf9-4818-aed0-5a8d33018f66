# Generated by Django 4.2.7 on 2023-12-22 15:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('stores', '0045_merge_20231005_1006'),
        ('users', '0050_unregistereduser'),
    ]

    operations = [
        migrations.CreateModel(
            name='Posts',
            fields=[
                ('post_id', models.AutoField(primary_key=True, serialize=False)),
                ('post_reference', models.CharField(max_length=20, unique=True)),
                ('post_text', models.TextField(blank=True, null=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('store_reference', models.ForeignKey(blank=True, db_column='store_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='posted_by_store', to='stores.store', to_field='store_reference')),
                ('user_reference', models.ForeignKey(blank=True, db_column='user_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='posted_by_user', to='users.user', to_field='user_reference')),
            ],
            options={
                'verbose_name_plural': 'posts',
                'db_table': '"content"."posts"',
            },
        ),
        migrations.CreateModel(
            name='PostLikes',
            fields=[
                ('post_likes_id', models.AutoField(primary_key=True, serialize=False)),
                ('liked_by_store', models.ForeignKey(blank=True, db_column='liked_by_store', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='liked_by_store', to='stores.store', to_field='store_reference')),
                ('liked_by_user', models.ForeignKey(blank=True, db_column='liked_by_user', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='liked_by_user', to='users.user', to_field='user_reference')),
                ('post_reference', models.ForeignKey(blank=True, db_column='post_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_likes', to='content.posts', to_field='post_reference')),
            ],
            options={
                'verbose_name_plural': 'post likes',
                'db_table': '"content"."post_likes"',
            },
        ),
        migrations.CreateModel(
            name='PostImages',
            fields=[
                ('post_image_id', models.AutoField(primary_key=True, serialize=False)),
                ('post_image', models.ImageField(upload_to='post_images/')),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('post_reference', models.ForeignKey(blank=True, db_column='post_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_images', to='content.posts', to_field='post_reference')),
            ],
            options={
                'verbose_name_plural': 'post images',
                'db_table': '"content"."post_images"',
            },
        ),
    ]
