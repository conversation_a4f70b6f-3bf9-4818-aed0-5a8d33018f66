# Generated by Django 3.2.13 on 2022-12-29 15:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0027_alter_notifications_notification_hide_date'),
    ]

    operations = [
        migrations.AlterField(
            model_name='inviteuser',
            name='invite_status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('USED', 'Used'), ('CLOSED', 'Closed'), ('EXPIRED', 'Expired'), ('DISABLED', 'Disabled')], default='CREATED', max_length=200),
        ),
        migrations.AlterField(
            model_name='inviteuser',
            name='invite_type',
            field=models.CharField(choices=[('NON_MEMBER', 'Non member'), ('MEMBER', 'Member'), ('SELLER', 'Seller'), ('CORPORATE_SELLER', 'Corporate seller'), ('CORPORATE_MEMBER', 'Corporate member'), ('MEMBER_TO_SELLER', 'Member to seller')], max_length=100),
        ),
    ]
