# Generated by Django 3.2.13 on 2022-12-22 09:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0019_auto_20221222_1459"),
    ]

    operations = [
        migrations.AddField(
            model_name="refundandwarranty",
            name="delivery_fee_reevaluation_on_customer",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="refundandwarranty",
            name="delivery_fee_reevaluation_on_seller",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="refundandwarranty",
            name="return_cost_on_customer",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="refundandwarranty",
            name="return_cost_on_seller",
            field=models.BooleanField(default=False),
        ),
    ]
