# Generated by Django 3.2.22 on 2025-05-05 07:54

from django.db import migrations, models
from django.db.models import Sum
import logging

logger = logging.getLogger(__name__)

def populate_store_counts(apps, schema_editor):
    """
    Populate the new store count fields with data from related models:
    - store_order_count: Count of orders from the Order model
    - store_sales_count: Sum of product_quantity from SubOrder model
    - store_supporters_count: Count of followers from Neo4j
    """
    Store = apps.get_model('stores', 'Store')
    Order = apps.get_model('orders', 'Order')
    SubOrder = apps.get_model('orders', 'SubOrder')

    # Get all active, non-deleted stores
    stores = Store.objects.filter(is_active=True, deleted=False)

    # Log the start of the migration
    logger.info(f"Starting to populate count fields for {stores.count()} stores")

    for store in stores:
        try:
            # 1. Populate store_order_count from Order model
            order_count = Order.objects.filter(
                store_reference=store,
                is_deleted=False,
                order_status = 'PAYMENT_SUCCESS',
            ).count()

            # 2. Populate store_sales_count from SubOrder model
            # Get all orders for this store
            order_numbers = Order.objects.filter(
                store_reference=store,
                is_deleted=False,
                order_status = 'PAYMENT_SUCCESS',
            ).values_list('order_number', flat=True)

            # Sum product quantities from all suborders related to these orders
            sales_count = SubOrder.objects.filter(
                order_number__in=order_numbers,
                is_deleted=False,
                suborder_status = 'ORDER_DELIVERED',
            ).aggregate(total_quantity=Sum('product_quantity'))['total_quantity'] or 0

            # 3. Populate store_supporters_count from Neo4j
            # This requires a direct database query since we can't use the ORM models in migrations
            supporters_count = 0
            try:
                # Import here to avoid issues with app loading
                from neomodel import db

                # Cypher query to get follower count
                query = """
                MATCH (s:Neo4jStore {reference: $store_reference})<-[:FOLLOWS]-(f)
                WHERE NOT f.is_deleted
                RETURN COUNT(f) as follower_count
                """

                results, _ = db.cypher_query(
                    query,
                    {'store_reference': store.store_reference}
                )

                if results and len(results) > 0:
                    supporters_count = results[0][0]
            except Exception as e:
                logger.error(f"Error getting Neo4j follower count for store {store.store_reference}: {str(e)}")

            # Update the store with the new counts
            store.store_order_count = order_count
            store.store_sales_count = sales_count
            store.store_supporters_count = supporters_count
            store.save()

            logger.info(f"Updated store {store.store_reference} with counts: orders={order_count}, sales={sales_count}, supporters={supporters_count}")

        except Exception as e:
            logger.error(f"Error updating counts for store {store.store_reference}: {str(e)}")

    logger.info("Finished populating store count fields")


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0094_store_store_valuation'),
    ]

    operations = [
        migrations.AddField(
            model_name='store',
            name='store_order_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='store',
            name='store_sales_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='store',
            name='store_supporters_count',
            field=models.IntegerField(default=0),
        ),
        # Add a RunPython operation to populate the new fields
        migrations.RunPython(
            populate_store_counts,
            reverse_code=migrations.RunPython.noop
        ),
    ]
