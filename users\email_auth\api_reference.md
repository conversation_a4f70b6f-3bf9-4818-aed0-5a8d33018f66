# Swadesic Email Authentication API Reference

This document provides a concise reference for the email authentication API endpoints.

## API Endpoints

### 1. Check User Info by Email

**Endpoint:** `GET /user/email/check_user_info/{email}/`

**Headers:**
```
Content-Type: application/json
```

**Response:**
```json
{
  "user_exists": true,
  "phonenumber": "+91XXXX43210"
}
```

---

### 2. Email Sign In

**Endpoint:** `POST /user/email/signin/`

**Headers:**
```
Content-Type: application/json
```

**Request Body (Email OTP):**
```json
{
  "email": "<EMAIL>"
}
```

**Request Body (Google Auth):**
```json
{
  "email": "<EMAIL>",
  "access_token": "google_oauth_access_token"
}
```

**Response (Email OTP - Existing User):**
```json
{
  "user_exists": true,
  "is_email_verified": false,
  "sent_email_otp": true,
  "phonenumber": "+91XXXX43210"
}
```

**Response (Email OTP - New User):**
```json
{
  "user_exists": false,
  "is_email_verified": false,
  "sent_email_otp": true,
  "phonenumber":null
}
```

**Response (Google Auth - Successful):**
```json
{
  "token_info": {
    "message": "success",
    "user_exist": true,
    "refresh": "refresh_token",
    "access": "access_token",
    "refresh_token_validity": "timestamp",
    "access_token_validity": "timestamp"
  },
  "user_info": {
    "message": "welcome",
    "userid": "user_id",
    "user_reference": "user_reference",
    "pin_code": "pincode",
    "role": "user_role",
    "profile_complete": true,
    "member_invite_balance": 0,
    "seller_invite_balance": 0,
    "access_control": {
      // Access control permissions
    }
  }
}
```

---

### 3. Verify Email Sign In

**Endpoint:** `POST /user/email/verify_signin/`

**Headers:**
```
Content-Type: application/json
```

**Request Body (Email OTP):**
```json
{
  "email": "<EMAIL>",
  "email_otp": "123456"
}
```

**Request Body (Google Auth):**
```json
{
  "email": "<EMAIL>",
  "access_token": "google_oauth_access_token"
}
```

**Response (Successful):**
```json
{
  "token_info": {
    "message": "success",
    "user_exist": true,
    "refresh": "refresh_token",
    "access": "access_token",
    "refresh_token_validity": "timestamp",
    "access_token_validity": "timestamp"
  },
  "user_info": {
    "message": "welcome",
    "userid": "user_id",
    "user_reference": "user_reference",
    "pin_code": "pincode",
    "role": "user_role",
    "profile_complete": true,
    "member_invite_balance": 0,
    "seller_invite_balance": 0,
    "access_control": {
      // Access control permissions
    }
  }
}
```

**Response (Failed):**
```json
{
  "message": "Please enter valid email OTP",
  "is_custom": true
}
```

---

### 4. Resend Email OTP

**Endpoint:** `POST /user/email/resend_otp/`

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "OTP sent successfully"
}
```

---

## Error Responses

| Status Code | Response |
|-------------|----------|
| 400 | `{"message": "Please enter a valid email address", "is_custom": true}` |
| 400 | `{"message": "Please enter valid email OTP", "is_custom": true}` |
| 400 | `{"message": "Invalid Google authentication token", "is_custom": true}` |
| 400 | `{"message": "User not found with this email", "is_custom": true}` |
| 400 | `{"message": "Either email OTP or Google access token is required", "is_custom": true}` |

---

## Testing

For testing in non-production environments (when `SEND_OTP=false`), use `222222` as the OTP for any email address.
