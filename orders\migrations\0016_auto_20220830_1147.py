# Generated by Django 3.2.13 on 2022-08-30 06:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0015_suborder_delivered_date"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="order",
            name="order_status",
            field=models.Char<PERSON><PERSON>(default="ORDER_INITIATED", max_length=200),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="cancelled_date",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="confirmation_date",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="suborder",
            name="delivered_date",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="delivery_person_contact",
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=15, null=True),
        ),
        migrations.Alt<PERSON><PERSON><PERSON>(
            model_name="suborder",
            name="delivery_person_name",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="estimated_delivery_date",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="suborder_status",
            field=models.CharField(default="ORDER_INITIATED", max_length=200),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="tracking_link",
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
    ]
