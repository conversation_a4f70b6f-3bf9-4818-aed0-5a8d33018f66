# Generated by Django 3.2.13 on 2022-12-14 09:52

from django.db import migrations


def enter_store_reference_data(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in store_reference column, where
    # storeid of SubOrder model is same as storeid in Store table.

    SubOrder = apps.get_model("orders", "SubOrder")
    Store = apps.get_model("stores", "Store")
    for item in SubOrder.objects.all():
        if Store.objects.filter(storeid=item.storeid).exists():
            store = Store.objects.get(storeid=item.storeid)
            item.store_reference = store.store_reference
        else:
            item.store_reference = None
        item.save(update_fields=["store_reference"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    SubOrder = apps.get_model("orders", "SubOrder")
    for item in SubOrder.objects.all():
        item.store_reference = None
        item.save(update_fields=["store_reference"])


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0057_auto_20221214_1521"),
    ]

    operations = [migrations.RunPython(enter_store_reference_data, reverse_func)]
