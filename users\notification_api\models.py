from django.db import models
import datetime
import random
import string
from django.utils.translation import gettext_lazy as _


class Notifications(models.Model):
    class Notifications_Type(models.TextChoices):
        ONBOARDING = "ONBOARDING", _("onboarding")
        PRODUCT_VERSION_CHANGED = "PRODUCT_VERSION_CHANGED", _(
            "Product version changed"
        )
        STORE_CREATED = "STORE_CREATED", _("New store created near you")
        NEW_ORDER = "NEW_ORDER", _("New order")
        ORDER_CONFIRMED = "ORDER_CONFIRMED", _("Order confirmed")
        ORDER_UPDATE = "ORDER_UPDATE", _("Order update") # TODO: create a action for this
        ORDER_SHIPPED = "ORDER_SHIPPED", _("Order shipped")
        SHIPPING_PACAKGE_UPDATED = "SHIPPING_PACAKGE_UPDATED", _(
            "Shipping package updated"
        )
        DELIVERY_OTP = "DELIVERY_OTP", _("Delivery otp")
        PACKAGE_DELIVERED = "PACKAGE_DELIVERED", _("Package delivered")
        DELIVERY_FAILED = "DELIVERY_FAILED", _("Delivery failed")
        ORDER_CANCELLED = "ORDER_CANCELLED", _("Order cancelled")
        SELLER_CANCELLED_ORDER = "SELLER_CANCELLED_ORDER", _("Seller cancelled order")
        BUYER_CANCELLED_ORDER = "BUYER_CANCELLED_ORDER", _("Buyer cancelled order")
        AUTO_CANCELLED = "AUTO_CANCELLED", _("Auto cancelled")
        RETURN_REQUESTED = "RETURN_REQUESTED", _("Return requested")
        RETURN_REQUEST_CANCELLED = "RETURN_REQUEST_CANCELLED", _("Return request cancelled")
        RETURN_ACCEPTED = "RETURN_ACCEPTED", _("Return accepted")
        RETURN_RECEIVED = "RETURN_RECEIVED", _("Return received")
        REFUND_INITIATED = "REFUND_INITIATED", _("Refund initiated")
        REFUND_HOLD = "REFUND_HOLD", _("Refund hold")
        REFUND_RELEASED = "REFUND_RELEASED", _("Refund released")
        RETURN_OTP = "RETURN_OTP", _("Return otp")
        COMMENTED = "COMMENTED", _("Commented")
        # SOMEONE_FOLLOWED_STORE = "SOMEONE_FOLLOWED_STORE", _("Someone followed store")
        # SOMEONE_FOLLOWED_USER = "SOMEONE_FOLLOWED_USER", _("Someone followed user")
        PAYOUT_CREDITED = "PAYOUT_CREDITED", _("Payout credited")
        PAYOUT_DEBITED = "PAYOUT_DEBITED", _("Payout debited")
        CONTENT_LIKED = "CONTENT_LIKED", _("content_liked")
        SOMEONE_FOLLOWED_ENTITY = "SOMEONE_FOLLOWED_ENTIY", _("Someone_followed_entity")
        SOMEONE_COMMENTED_ON_CONTENT = "SOMEONE_COMMENTED_ON_CONTENT", _("Someone_commented_on_content")
        SOMEONE_REPOSTED_CONTENT = "SOMEONE_REPOSTED_CONTENT", _("Someone reposted content")
        SOMEONE_SIGNED_UP_WITH_YOUR_CODE = "SOMEONE_SIGNED_UP_WITH_YOUR_CODE", _("Someone signed up with your code")
        YOU_SIGNED_UP_WITH_SOMEONES_CODE = "YOU_SIGNED_UP_WITH_SOMEONES_CODE", _("You signed up with someones code")
        PAYOUT_OTP = "PAYOUT_OTP", _("Payout otp")
        STATE_CHANGE = "STATE_CHANGE", _("State change")
        STORE_VERIFICATION_ONBOARDING_REWARD = "STORE_VERIFICATION_ONBOARDING_REWARD", _("Store verification onboarding reward")
        STORE_VERIFICATION_PROMOTION_REWARD = "STORE_VERIFICATION_PROMOTION_REWARD", _("Store verification promotion reward")
        STORE_VERIFICATION_APPROVED = "STORE_VERIFICATION_APPROVED", _("Store verification approved")
        NORMAL_STORE_VERIFICATION_REWARD = "NORMAL_STORE_VERIFICATION_REWARD", _("Normal store verification reward")
        GST_VERIFICATION_APPROVED = "GST_VERIFICATION_APPROVED", _("GST verification approved")
        PAN_VERIFICATION_APPROVED = "PAN_VERIFICATION_APPROVED", _("PAN verification approved")
        GST_VERIFICATION_REJECTED = "GST_VERIFICATION_REJECTED", _("GST verification rejected")
        PAN_VERIFICATION_REJECTED = "PAN_VERIFICATION_REJECTED", _("PAN verification rejected")
        PRODUCT_REMOVED_FROM_SHIPPING_GROUP = "PRODUCT_REMOVED_FROM_SHIPPING_GROUP", _("Product removed from shipping group")
        PRODUCT_UPDATED_FROM_SHIPPING_GROUP = "PRODUCT_UPDATED_FROM_SHIPPING_GROUP", _("Product updated from shipping group")
        PAYOUT_SUCCESS = "PAYOUT_SUCCESS", _("Payout success")
        PAYOUT_FAILED = "PAYOUT_FAILED", _("Payout failed")
        MESSAGE_RECEIVED = "MESSAGE_RECEIVED", _("Message received")
    class Notification_Status(models.TextChoices):
        SEEN = "SEEN", _("Seen")
        NOT_SEEN = "NOT_SEEN", _("Not seen")

    notification_id = models.AutoField(primary_key=True)
    notification_reference = models.CharField(max_length=20, blank=True, null=True)
    notified_user = models.CharField(max_length=50, null=True, blank=True)
    notification_type = models.CharField(
        max_length=100, choices=Notifications_Type.choices, null=True, blank=True
    )
    notification_message = models.CharField(max_length=1000, null=True, blank=True)
    notification_status = models.CharField(
        max_length=20,
        choices=Notification_Status.choices,
        default=Notification_Status.NOT_SEEN,
    )
    notification_about = models.CharField(max_length=25, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    notification_hide_date = models.DateField(null=True, blank=True)
    is_hidden = models.BooleanField(default=False)
    image = models.ImageField(max_length=110,null=True, blank=True, upload_to="notification_images")

    def get_notification_reference(self):
        random_string = "".join(
            random.choices(string.ascii_uppercase + string.digits, k=12)
        )
        final_code = ("N_", random_string)
        notification_reference = "".join(final_code)
        if Notifications.objects.filter(
            notification_reference=self.notification_reference
        ).exists():
            notification_reference = self.get_notification_reference()
            return notification_reference
        return notification_reference

    def save(self, *args, **kwargs):
        if self.notification_id is None:
            self.notification_reference = self.get_notification_reference()
            self.notification_hide_date = datetime.date.today() + datetime.timedelta(1)

        super(Notifications, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "notifications"
        db_table = '"user"."notifications"'
