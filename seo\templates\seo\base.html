<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ meta.title }}</title>
    <meta name="description" content="{{ meta.description }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ meta.title }}">
    <meta property="og:description" content="{{ meta.description }}">
    {% if meta.image %}
    <meta property="og:image" content="{{ meta.image }}">
    {% endif %}
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ meta.title }}">
    <meta name="twitter:description" content="{{ meta.description }}">
    {% if meta.image %}
    <meta name="twitter:image" content="{{ meta.image }}">
    {% endif %}
    
    <!-- Product Schema (if applicable) -->
    {% block schema %}{% endblock %}
    
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .store-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        .store-icon {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
        }
        .product-grid, .post-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .product-card, .post-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
        }
        .product-image, .post-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
        }
        .story-detail {
            max-width: 800px;
            margin: 0 auto;
        }
        .story-header {
            margin-bottom: 30px;
        }
        .store-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .store-info img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }
        .timestamp {
            color: #666;
        }
        .story-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
        }
        .story-text {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    {% block content %}{% endblock %}
</body>
</html>
