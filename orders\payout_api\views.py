from rest_framework.views import APIView
from decouple import config
import requests


from django.db.models import Sum, FloatField
from django.db.models import Q
from django.db.models.functions import Coalesce


from .serializers import AccountPayoutDetailsSerializer, PayoutTransactionsSerializer, PayoutWithdrawalSerializer
from .models import OrderPayout, PayoutBalance, PayoutTransactions
from orders.order_api.models import OrderConfiguration, RefundedAmount, Order, SubOrder
from stores.store_api.models import Store
from shared_utils.payout_utils import create_payout_transaction, create_store_payout_balance
import logging
import random
import math
import datetime
import pytz
from rest_framework import generics, status
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from orders.order_api.models import OrderOtp
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from users.user_api.models import User
from .models import BankDetails, encrypt_string, decrypt_string
import time
from django.core.exceptions import ObjectDoesNotExist
from rest_framework.exceptions import Throttled, ValidationError
from .serializers import GetBankDetailsSerializer, BankDetailsSerializer
from django.core.cache import cache
from django.db import transaction



logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class AccountBalance(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="Getting account balance of a store.")
    def get(self, request, *args, **kwargs):

        """
        To get overall payout balance of a store. it will provide payouts which are in_process, waiting for release,
        total account balance and lifetime balance separately.
        """
        logger.info("Entered payout balance api")
        store_reference = kwargs["store_reference"]
        store_instance = Store.objects.get(store_reference=store_reference)

        # Payout status as 'IN_PROCESS' means payment of an order is successful
        in_progress = OrderPayout.objects.filter(
            store_reference=store_instance,
            payout_status=OrderPayout.Payout_Status.IN_PROCESS,
        ).aggregate(in_progress=Coalesce(Sum("expected_payout_amount", output_field=FloatField()), 0.0))["in_progress"]

        in_progress = round(in_progress, 2)
        # payout status as 'WAITING_FOR_RELEASE' means delivery of that order is completed and waiting for the
        # completion of return period so that it can be accessible to store.
        waiting_for_release = OrderPayout.objects.filter(
            store_reference=store_instance,
            payout_status=OrderPayout.Payout_Status.WAITING_FOR_RELEASE,
        ).aggregate(waiting_for_release=Coalesce(Sum('payout_amount', output_field=FloatField()), 0.0))["waiting_for_release"]

        waiting_for_release = round(waiting_for_release, 2)
        # We can get the lifetime balance and current balance of store from payout balance table, this table will update
        # on everyday basis, order values that completed their period will mark as "AMOUNT_RELEASED"  and will add to this
        # payout balance table.
        if PayoutBalance.objects.filter(store_reference=store_reference).exists():
            payout_balance_instance = PayoutBalance.objects.get(
                store_reference=store_reference
            )
            life_time_balance = payout_balance_instance.life_time_balance
            life_time_balance = round(life_time_balance, 2)
            current_balance = payout_balance_instance.current_balance
            current_balance = round(current_balance, 2)
            missed_revenue = payout_balance_instance.missed_revenue
            missed_revenue = round(missed_revenue, 2)
        else:
            life_time_balance = 0.0
            current_balance = 0.0
            missed_revenue = 0.0

        # Calculate available withdrawal limit
        available_withdrawal_limit = current_balance - (in_progress + waiting_for_release) * 0.03
        available_withdrawal_limit = round(available_withdrawal_limit, 2)

        account_balance_result = {
            "in_progress": in_progress,
            "waiting_for_release": waiting_for_release,
            "life_time_balance": life_time_balance,
            "current_balance": current_balance,
            "missed_revenue": missed_revenue,
            "available_withdrawal_limit": available_withdrawal_limit if available_withdrawal_limit > 0 else 0.0,
        }
        logger.info("Exited payout balance api")
        return Response(
            {"message": "success", "data": account_balance_result},
            status=status.HTTP_200_OK,
        )


class AccountPayoutDetails(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="Getting account balance details")
    def get(self, request, *args, **kwargs):

        """
        Getting account balance details of a store. This includes all the order details with customer name and
        payout status like if the payout in process or waiting for release or successful.
        """
        logger.info("Entered account payout balance details api")
        store_reference = kwargs["store_reference"]
        store_instance = Store.objects.get(store_reference=store_reference)

        # Getting all the order payout of a single store distinct by order number.
        order_payout_instance = OrderPayout.objects.filter(
            store_reference=store_instance
        ).select_related('user_reference').distinct("order_number")
        serializer = AccountPayoutDetailsSerializer(order_payout_instance, many=True)
        logger.info("Exited account payout balance details api")
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class UpdatePayoutAndPayoutBalanceEveryday(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="update payout and payout balance table.")
    def get(self, request, *args, **kwargs):

        """
        Everyday update payout and payout balance. ie, for an order value if the return period and grace period is
        over then mark that order value as released. Along with that update that in payout balance table.
        """

        logger.info("run everyday once")
        # Step1:  Get all the suborders of payout table with status 'WAITING_FOR_RELEASE'

        if OrderPayout.objects.filter(
            payout_status=OrderPayout.Payout_Status.WAITING_FOR_RELEASE
        ).exists():
            order_payout_instance = OrderPayout.objects.filter(
                payout_status=OrderPayout.Payout_Status.WAITING_FOR_RELEASE
            )
            execution_count = 0
            for instance in order_payout_instance:

                order_number = instance.order_number
                suborder_number = instance.suborder_number.suborder_number

                order_configuration = OrderConfiguration.objects.all().first()

                #payoutbug
                if datetime.date.today() >= instance.payout_release_date:

                    # Step2: find the order_amount of that suborder ie; product_total + df.
                    order_amount = instance.order_amount

                    # Step3: for the cancelled or returned suborder, get the refunded amount from the RefundedAmount table,
                    # otherwise keep refunded_amount as 0
                    refunded_amount = 0
                    if RefundedAmount.objects.filter(suborder_reference=suborder_number).exists():
                        refund_instance = RefundedAmount.objects.get(suborder_reference=suborder_number)
                        refunded_amount = refund_instance.refunded_amount

                    logger.info("refunded amount is %s",refunded_amount)
                    # Step4: calculate 1% tds for the amount that is crediting to seller
                    tds = (order_amount - refunded_amount) * 0.01
                    tds_calculated = round(tds, 2)
                    instance.tds_calculated = tds_calculated

                    # Step5: calculate the transaction fee on top of order_amount

                    # check payment mode, for upi transaction fee is zero
                    order_instance = Order.objects.filter(order_number=order_number.order_number).first()
                    payment_mode = order_instance.payment_mode

                    if payment_mode == 'UPI':
                        transaction_fee_percentage = 0
                    else:
                        transaction_fee_percentage = order_configuration.transaction_fee
                    transaction_fee = order_amount * (transaction_fee_percentage / 100)
                    transaction_fee_calculated = round(transaction_fee, 2)
                    instance.transaction_fee_calculated = transaction_fee_calculated

                    # Step6: deduct commission fee for first successful payout of the order.

                    # To check if commission fee is already deducted for an order, we will check if any of the suborder
                    # status of that particular order has changed to 'AMOUNT_RELEASED' if not deduct commission fee
                    # so that it will happen only once for an order.
                    commission_fee = order_configuration.commission_fee
                    commission_fee_discount = order_configuration.commission_fee_discount
                    lst = [SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
                           SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
                           SubOrder.Suborder_Status.RETURN_CONFIRMED,
                           SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
                           SubOrder.Suborder_Status.RETURNED_TO_SELLER,
                           SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED]
                    commission_calculated = 0
                    if OrderPayout.objects.filter(Q(order_number=order_number) & Q(payout_status="AMOUNT_RELEASED") &
                                                  ~Q(suborder_number__suborder_status__in=lst)).count() == 0 and refunded_amount == 0:
                        commission_calculated = -abs(commission_fee) + commission_fee_discount
                    instance.swadesic_fee = commission_calculated

                    # Step7: Calculate the payout amount
                    payout_amount = order_amount - refunded_amount - tds_calculated - transaction_fee_calculated + commission_calculated
                    payout_amount = round(payout_amount, 2)

                    instance.payout_amount = payout_amount
                    instance.payout_status = OrderPayout.Payout_Status.AMOUNT_RELEASED
                    instance.transaction_fee_percentage = transaction_fee_percentage
                    instance.expected_swadesic_fee = commission_fee
                    instance.promotional_value = commission_fee_discount
                    instance.order_amount = order_amount
                    instance.save(update_fields=['payout_status', 'transaction_fee_calculated', 'order_amount',
                                                 'commission_calculated', 'transaction_fee_percentage',
                                                 'commission_fee', 'promotional_value', 'tds_calculated',
                                                 'payout_amount'])
                    execution_count += 1
                    # once the amount is in released state, it should update in payout balance table, where
                    # account balance and current balance of all stores are kept.

                    # For this first check, for this store any entry exist in the payout balance table or not.
                    # if exists then update new amount to that row

                    if PayoutBalance.objects.filter(
                        store_reference=instance.store_reference
                    ).exists():
                        payout_balance_instance = PayoutBalance.objects.get(
                            store_reference=instance.store_reference
                        )
                        update_store_payout_balance(payout_balance_instance, instance)
                        create_payout_transaction(instance)
                    # otherwise create a new entry for the store in payout balance table and add amount there.
                    else:
                        create_store_payout_balance(instance)
                        create_payout_transaction(instance)
        return Response({"message": "success"}, status=status.HTTP_200_OK)

def update_store_payout_balance(payout_balance_instance, instance):
    if instance.expected_payout_amount:
        missed_revenue = instance.expected_payout_amount - instance.payout_amount
        missed_revenue = round(missed_revenue, 2)

        payout_balance_instance.life_time_balance += instance.payout_amount
        payout_balance_instance.current_balance += instance.payout_amount
        payout_balance_instance.missed_revenue += missed_revenue
        payout_balance_instance.save(update_fields=["life_time_balance", "current_balance", "missed_revenue"])
        logger.info("Payout balance updated")


class GetTransactionsOfStore(generics.RetrieveAPIView):
    serializer_class = PayoutTransactionsSerializer

    @swagger_auto_schema(operation_summary="Get all transactions of a store")
    def get(self, request, *args, **kwargs):
        """Get all the transactions of a store, all the amount credited to the store and debited from the store."""
        store_reference = kwargs["store_reference"]
        instance = self.get_queryset(store_reference)
        serializer = self.get_serializer(instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def get_queryset(self, store_reference):
        return PayoutTransactions.objects.filter(store_reference=store_reference).select_related('store_reference', 'order_number', 'suborder_number')


class GetTransactionsOfOrder(generics.RetrieveAPIView):
    serializer_class = PayoutTransactionsSerializer

    @swagger_auto_schema(operation_summary="Get all transactions of a order")
    def get(self, request, *args, **kwargs):
        """Get all the transactions of an order"""
        order_number = kwargs["order_number"]
        instance = self.get_queryset(order_number)
        serializer = self.get_serializer(instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def get_queryset(self, order_number):
        return PayoutTransactions.objects.filter(order_number=order_number)


class PayoutWithdrawal(APIView):
    def post(self, request):
        logger.info("inside PayoutWithdrawal api")
        store_reference = request.data.get("store_reference")
        amount = request.data.get("amount")
        primary_bank_detail_instance = BankDetails.objects.filter(entity_reference=store_reference, is_primary=True).first()
        bank_account_number = decrypt_string(primary_bank_detail_instance.account_number)
        bank_account_name = primary_bank_detail_instance.account_name
        bank_ifsc_code = decrypt_string(primary_bank_detail_instance.ifsc_code)
        bank_name = primary_bank_detail_instance.bank_name
        bank_branch = primary_bank_detail_instance.bank_branch

        try:
            store = Store.objects.get(store_reference=store_reference)

            if int(amount) == 0 or int(amount) < 0:
                return Response({
                    "message": "Please enter a valid amount",
                    "error": "Invalid amount",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            if not self.is_amount_within_withdrawal_limit(store_reference, amount):
                return Response({
                    "message": "Entered amount exceeds the available withdrawal limit",
                    "error": "Insufficient balance",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create a new PayoutTransactions instance
            payout_transaction = PayoutTransactions(
                store_reference=store,
                payout_amount=amount,
                transaction_type=PayoutTransactions.Transaction_Type.WITHDRAWAL,
                transaction_status=PayoutTransactions.Transaction_Status.PENDING,
                bank_account_number=bank_account_number,
                bank_account_name=bank_account_name,
                bank_ifsc_code=bank_ifsc_code,
                bank_name=bank_name,
                bank_branch=bank_branch,
                payout_release_date=datetime.datetime.now(),
            )
            
            # Save the transaction
            payout_transaction.save()
            
            # Serialize the created transaction
            serializer = PayoutTransactionsSerializer(payout_transaction)
            
            return Response({
                "message": "Withdrawal transaction created successfully",
                "data": serializer.data
            }, status=status.HTTP_200_OK)
        
        except Store.DoesNotExist:
            return Response({
                "message": "Store not found",
                "error": f"No store found with reference {store_reference}",
                "is_custom": True
            }, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            logger.error(f"Error in PayoutWithdrawal: {str(e)}")
            return Response({
                "message": "An error occurred while processing the withdrawal",
                "error": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def is_amount_within_withdrawal_limit(self, store_reference, amount):
        store_instance = Store.objects.filter(store_reference=store_reference, deleted=False).first()
        order_payout_balance = PayoutBalance.objects.filter(store_reference=store_reference).first()
        if order_payout_balance is None:
            return False
        else:
            current_account_balance = order_payout_balance.current_balance
            if store_instance:
                in_progress = OrderPayout.objects.filter(
                    store_reference=store_instance,
                    payout_status=OrderPayout.Payout_Status.IN_PROCESS,
                ).aggregate(in_progress=Coalesce(Sum("expected_payout_amount", output_field=FloatField()), 0.0))["in_progress"]

                waiting_for_release = OrderPayout.objects.filter(
                    store_reference=store_instance,
                    payout_status=OrderPayout.Payout_Status.WAITING_FOR_RELEASE,
                ).aggregate(waiting_for_release=Coalesce(Sum('payout_amount', output_field=FloatField()), 0.0))["waiting_for_release"]
            else:
                in_progress = 0
                waiting_for_release = 0

            available_withdrawal_limit = current_account_balance - (in_progress + waiting_for_release) * 0.03
            available_withdrawal_limit = round(available_withdrawal_limit, 2)

            return available_withdrawal_limit >= float(amount)


class GeneratePayoutOtp(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Payout OTP generation",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["store_reference"],
            properties={"store_reference": openapi.Schema(type=openapi.TYPE_STRING)},
        ),
    )
    def send_sms_otp(self, phone_number, otp):
        url = "https://www.fast2sms.com/dev/bulkV2"
        querystring = {"authorization": config('FAST2SMS_API_KEY'),
                       "sender_id": config("DLT_SENDER_ID"),
                       "message": config("BANK_DETAIL_OTP_MESSAGE_ID"),
                       "variables_values": f"{otp}|",
                       "route": "dlt",
                       "numbers": phone_number}

        headers = {
            'cache-control': "no-cache"
        }
        response = requests.request("GET", url, headers=headers, params=querystring)
        return response.text
    def post(self, request, *args, **kwargs):
        store_reference = request.data.get("store_reference")
        amount = request.data.get("amount")

        try:
            store = Store.objects.get(store_reference=store_reference)
            store_owner = User.objects.get(userid=store.created_by.userid)

            order_payout_balance = PayoutBalance.objects.filter(store_reference=store_reference).first()
            if order_payout_balance is None:
                current_account_balance = 0
            else:
                current_account_balance = order_payout_balance.current_balance

            digits = "**********"
            otp = ""
            for i in range(4):
                otp += digits[math.floor(random.random() * 10)]
            
            OrderOtp.objects.create(
                user_reference=store.created_by.user_reference,
                package_number=f"[current_account_balance:{str(current_account_balance)}]",
                otp=otp
            )

            notification_handler = NotificationHandler(
                notified_user=store.created_by.user_reference,
                notification_type=Notifications.Notifications_Type.PAYOUT_OTP,
                notification_about=store.store_reference,
                otp=otp,
                payout_amount=amount,
            )
            notification_handler.create_notification(notification_handler)

            # otp_response_text = self.send_sms_otp(store_owner.phone_number[3:], otp)

            return Response({"message": "OTP sent successfully"}, status=status.HTTP_200_OK)
        
        except Store.DoesNotExist:
            return Response({"message": "Store not found"}, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VerifyPayoutOtp(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Payout OTP verification",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["store_reference", "otp"],
            properties={
                "store_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "otp": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        store_reference = request.data.get("store_reference")
        input_otp = request.data.get("otp")
        amount = request.data.get("amount")
        
        try:
            store = Store.objects.get(store_reference=store_reference)

            order_payout_balance = PayoutBalance.objects.filter(store_reference=store_reference).first()
            if order_payout_balance is None:
                current_account_balance = 0
            else:
                current_account_balance = order_payout_balance.current_balance

            otp_object = OrderOtp.objects.filter(
                user_reference=store.created_by.user_reference,
                otp_status=OrderOtp.Status.UNUSED,
            ).order_by("generated_time").last()

            if otp_object:
                saved_otp = otp_object.otp
                otp_created_time = otp_object.generated_time
                otp_expiry_time = otp_created_time + datetime.timedelta(minutes=2)
                now = datetime.datetime.now().astimezone(pytz.timezone("Asia/Kolkata"))
                
                otp_expired = now > otp_expiry_time

                if current_account_balance < int(amount):
                    return Response({
                        "message": "Insufficient balance",
                        "error": "Insufficient balance"
                    }, status=status.HTTP_400_BAD_REQUEST)

                if input_otp == saved_otp and not otp_expired:
                    otp_object.otp_status = OrderOtp.Status.USED
                    otp_object.save(update_fields=["otp_status"])
                    return Response({"message": "OTP verified successfully", "is_valid": True}, status=status.HTTP_200_OK)
                elif input_otp == saved_otp and otp_expired:
                    otp_object.otp_status = OrderOtp.Status.EXPIRED
                    otp_object.save(update_fields=["otp_status"])
                    return Response({"message": "OTP expired", "is_valid": False}, status=status.HTTP_200_OK)
                else:
                    return Response({"message": "Invalid OTP", "is_valid": False}, status=status.HTTP_200_OK)
            else:
                return Response({"message": "No OTP found"}, status=status.HTTP_400_BAD_REQUEST)
        
        except Store.DoesNotExist:
            return Response({"message": "Store not found"}, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ##############################Bank Details api###########################################################


class AddBankDetailsView(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Add bank details",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["entity_reference", "account_name", "account_holder_name", "ifsc_code", "account_number", "bank_branch", "bank_name"],
            properties={
                "entity_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "account_name": openapi.Schema(type=openapi.TYPE_STRING),
                "account_holder_name": openapi.Schema(type=openapi.TYPE_STRING),
                "ifsc_code": openapi.Schema(type=openapi.TYPE_STRING),
                "account_number": openapi.Schema(type=openapi.TYPE_STRING),
                "bank_branch": openapi.Schema(type=openapi.TYPE_STRING),
                "bank_name": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )   

    def send_sms_otp(self, phone_number, otp):
        autofetch_id = config('AUTOFETCH_ID')
        url = "https://www.fast2sms.com/dev/bulkV2"
        querystring = {"authorization": config('FAST2SMS_API_KEY'),
                       "sender_id": config("DLT_SENDER_ID"),
                       "message": config("MESSAGE_ID"),
                       "variables_values": f"{otp}|{autofetch_id}",
                       "route": "dlt",
                       "numbers": phone_number}

        headers = {
            'cache-control': "no-cache"
        }
        response = requests.request("GET", url, headers=headers, params=querystring)
        return response.text

    def post(self, request, *args, **kwargs):
        serializer = BankDetailsSerializer(data=request.data)
        if serializer.is_valid():
            entity_reference = request.data.get('entity_reference')
            cache_key = f'bank_details_otp_{entity_reference}'

            # Check if an OTP already exists and is not expired
            existing_data = cache.get(cache_key)
            if existing_data:
                time_elapsed = time.time() - existing_data.get('timestamp', 0)
                if time_elapsed < 60:  # If less than 1 minute has passed
                    raise Throttled(detail="Please wait before requesting a new OTP.")

            # Generate OTP
            if config("SEND_OTP", cast=bool):
                otp = ''.join([str(random.randint(0, 9)) for _ in range(4)])
            else:
                otp = '2222'
            # Determine the phone number to send OTP to
            try:
                if entity_reference.startswith('U'):
                    user = User.objects.get(user_reference=entity_reference, deleted=False)
                    phone_number = user.phonenumber
                elif entity_reference.startswith('S'):
                    store = Store.objects.get(store_reference=entity_reference, deleted=False)
                    phone_number = store.created_by.phonenumber
                else:
                    raise ValidationError("Invalid entity reference")
            except ObjectDoesNotExist:
                return Response({"error": "Entity not found"}, status=status.HTTP_404_NOT_FOUND)
            
            # Encrypt sensitive data before caching
            encrypted_data = serializer.validated_data.copy()
            encrypted_data['account_number'] = encrypt_string(encrypted_data['account_number'])
            encrypted_data['ifsc_code'] = encrypt_string(encrypted_data['ifsc_code'])

            cache_data = {
                'otp': otp,
                'bank_details': encrypted_data,
                'timestamp': time.time()
            }
            cache.set(cache_key, cache_data, timeout=300)  # OTP valid for 5 minutes
            
            # Send OTP
            if config("SEND_OTP", cast=bool):
                sms_response = self.send_sms_otp(str(phone_number)[3:], otp)
            # You might want to log sms_response or handle potential errors

            return Response({
                "message": "OTP sent for verification. Any previous OTPs are now invalid.",
                "valid_for": "5 minutes"
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        serializer = BankDetailsSerializer(data=request.data)
        input_otp = request.data.get("otp")

        if not input_otp:
            return Response({"error": "OTP is required"}, status=status.HTTP_400_BAD_REQUEST)

        if serializer.is_valid():
            entity_reference = request.data.get('entity_reference')
            cache_key = f'bank_details_otp_{entity_reference}'
            cached_data = cache.get(cache_key)
            
            if not cached_data:
                return Response({"error": "No active OTP found. Please request a new OTP."}, status=status.HTTP_400_BAD_REQUEST)
            
            if cached_data['otp'] != input_otp:
                return Response({"error": "Invalid OTP. Please try again or request a new OTP."}, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if OTP has expired
            time_elapsed = time.time() - cached_data.get('timestamp', 0)
            if time_elapsed > 300:  # 5 minutes
                cache.delete(cache_key)
                return Response({"error": "OTP has expired. Please request a new OTP."}, status=status.HTTP_400_BAD_REQUEST)
            
            # Decrypt cached data for comparison
            decrypted_cached_data = cached_data['bank_details'].copy()
            decrypted_cached_data['account_number'] = decrypt_string(decrypted_cached_data['account_number'])
            decrypted_cached_data['ifsc_code'] = decrypt_string(decrypted_cached_data['ifsc_code'])

            # Validate that decrypted cached bank details and input bank details are the same
            if decrypted_cached_data != serializer.validated_data:
                return Response({"error": "Bank details do not match the original request"}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                with transaction.atomic():
                    # Use the encrypted data from cache to create the BankDetails object
                    bank_details = BankDetails.objects.create(**serializer.validated_data)

                    # If the new bank details are set as primary, update other bank details
                    if bank_details.is_primary:
                        BankDetails.objects.filter(
                            entity_reference=entity_reference
                        ).exclude(
                            bank_details_id=bank_details.bank_details_id
                        ).update(is_primary=False)

            except Exception as e:
                return Response({"error": f"Failed to create bank details: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Clear the cache
            cache.delete(cache_key)
            
            return Response({"message": "Bank details added successfully"}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetBankDetailsView(generics.RetrieveAPIView):
    @swagger_auto_schema(
        operation_summary="Get bank details",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["entity_reference"],
            properties={
                "entity_reference": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def get(self, request, *args, **kwargs):
        entity_reference = request.query_params.get("entity_reference")
        
        try:
            bank_details = BankDetails.objects.filter(entity_reference=entity_reference)

            if bank_details:
                serializer = GetBankDetailsSerializer(bank_details, many=True)
                return Response({"data": serializer.data}, status=status.HTTP_200_OK)
            else:
                return Response({"data": []}, status=status.HTTP_200_OK)

        except ObjectDoesNotExist:
            return Response({"error": "Entity not found"}, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeleteBankDetailsView(generics.DestroyAPIView):
    @swagger_auto_schema(
        operation_summary="Delete bank details",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["bank_detail_reference"],
            properties={
                "bank_detail_reference": openapi.Schema(type=openapi.TYPE_STRING),
            }
        )
    )
    def delete(self, request, *args, **kwargs):
        bank_detail_reference = request.query_params.get("bank_detail_reference")
        
        if not bank_detail_reference:
            return Response({"error": "bank_detail_reference is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                bank_detail = BankDetails.objects.get(bank_detail_reference=bank_detail_reference)
                
                # Check if this is the primary account
                is_primary = bank_detail.is_primary
                entity_reference = bank_detail.entity_reference

                # Delete the bank detail
                bank_detail.delete()

                # If the deleted account was primary, set another account as primary if available
                if is_primary:
                    remaining_account = BankDetails.objects.filter(entity_reference=entity_reference).order_by("created_date").first()
                    if remaining_account:
                        remaining_account.is_primary = True
                        remaining_account.save(update_fields=["is_primary"])

            return Response({
                "message": "Bank details deleted successfully",
                "bank_detail_reference": bank_detail_reference
            }, status=status.HTTP_200_OK)

        except BankDetails.DoesNotExist:
            return Response({"error": "Bank detail not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": f"An error occurred: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SetPrimaryBankAccountView(generics.UpdateAPIView):
    @swagger_auto_schema(
        operation_summary="Set primary account",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["bank_detail_reference"],
            properties={
                "bank_detail_reference": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def put(self, request, *args, **kwargs):
        bank_detail_reference = request.data.get("bank_detail_reference")
        
        try:
            bank_detail = BankDetails.objects.get(bank_detail_reference=bank_detail_reference)
            entity_reference = bank_detail.entity_reference
            bank_detail.is_primary = True
            bank_detail.save(update_fields=["is_primary"])
            
            # Update other bank details to not be primary
            BankDetails.objects.filter(entity_reference=entity_reference).exclude(bank_detail_reference=bank_detail_reference).update(is_primary=False)
            
            return Response({"message": "Primary account set successfully"}, status=status.HTTP_200_OK)
        
        except BankDetails.DoesNotExist:
            return Response({"error": "Bank detail not found"}, status=status.HTTP_404_NOT_FOUND)
