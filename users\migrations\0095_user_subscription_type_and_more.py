# Generated by Django 4.2.7 on 2024-11-14 22:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0094_alter_notifications_notification_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="subscription_type",
            field=models.CharField(default="FREE", max_length=100),
        ),
        migrations.AddField(
            model_name="userrewards",
            name="user_affiliate_balance",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="infinityrewardshistory",
            name="reward_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                    ("INVITE_CODE_REWARD", "Invite Code Reward"),
                    ("STORE_VERIFICATION", "Store Verification"),
                    ("U2U_TRANSFER", "U2U Transfer"),
                    ("S2U_TRANSFER", "S2U Transfer"),
                    ("AFFILIATE_REWARD", "Affiliate Reward"),
                ],
                max_length=20,
                null=True,
            ),
        ),
    ]
