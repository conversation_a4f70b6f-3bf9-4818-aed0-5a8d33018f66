# Generated by Django 4.2.7 on 2024-04-08 11:05

from django.db import migrations, models
from GraphDB.queries import dbqueries


def populate_parent_handles(apps, schema_editor):
    Comments = apps.get_model("content", "Comments")  # Replace "Comments" with your actual model name
    for comment in Comments.objects.all():
        # Assuming you have a method to get the creator handle, replace get_creator_handle() with the appropriate method
        creator_handle = dbqueries.get_content_creator_handle(content_reference=comment.comment_reference)
        comment.parent_handle = creator_handle
        comment.save()


class Migration(migrations.Migration):

    dependencies = [
        ("content", "0019_rename_product_rating_comments_rating_count"),
    ]

    operations = [
        migrations.AddField(
            model_name="comments",
            name="parent_handle",
            field=models.CharField(blank=True, max_length=35, null=True),

        ),
        migrations.RunPython(populate_parent_handles),

    ]
