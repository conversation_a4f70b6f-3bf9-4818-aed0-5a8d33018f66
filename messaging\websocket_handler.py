import json
import asyncio
import websockets
import logging
import time
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from decouple import config
from .views import get_or_create_auth_token, get_entity_profile_from_neo4j, get_instance_by_reference, get_node_by_jid, get_or_create_room_id
from urllib.parse import parse_qs
from users.user_api.models import User
from stores.store_api.models import Store
from GraphDB.models import <PERSON>4jE<PERSON><PERSON>, Neo4j<PERSON>ser, Neo4jStore
from django.core.cache import cache
import requests
from datetime import datetime
from collections import deque
from typing import Dict, List
from datetime import timedelta
import uuid
import random
from asgiref.sync import sync_to_async
from django.utils import timezone
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class MessageBatcher:
    def __init__(self, batch_timeout: float = 0.1):
        self.pending_messages: deque = deque()
        self.batch_timeout = batch_timeout
        self.batch_task = None
        self.consumer = None

    async def add_message(self, message: dict):
        """Add a message to the pending batch"""
        self.pending_messages.append(message)
        
        # Start a new batch task if one isn't running
        if self.batch_task is None or self.batch_task.done():
            self.batch_task = asyncio.create_task(self._process_batch())

    async def _process_batch(self):
        """Process pending messages after a timeout"""
        await asyncio.sleep(self.batch_timeout)
        
        if not self.pending_messages or not self.consumer:
            return

        # Collect all pending messages
        messages = list(self.pending_messages)
        self.pending_messages.clear()

        # Always send in the same format, whether it's one message or multiple
        await self.consumer.send(text_data=json.dumps({
            'type': 'new_incoming_messages',
            'data': {
                'new_incoming_messages': messages
            }
        }))

class RateLimiter:
    def __init__(self, calls_per_second=1):
        self.calls_per_second = calls_per_second
        self.last_call_time = {}
        self.locks = {}

    async def acquire(self, key):
        """Acquire rate limit lock"""
        if key not in self.locks:
            self.locks[key] = asyncio.Lock()
            
        async with self.locks[key]:
            current_time = time.time()
            if key in self.last_call_time:
                time_since_last_call = current_time - self.last_call_time[key]
                if time_since_last_call < (1.0 / self.calls_per_second):
                    await asyncio.sleep((1.0 / self.calls_per_second) - time_since_last_call)
            
            self.last_call_time[key] = time.time()

class RocketChatRealtimeConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.message_lock = asyncio.Lock()
        self.receive_lock = asyncio.Lock()  # New lock for receive operations
        self.rocket_chat_ws = None
        self.user = None
        self.entity_reference = None
        self.rooms = set()
        self.subscriptions = set()
        self.pending_messages = {}
        self.message_batcher = MessageBatcher()
        self.rate_limiter = RateLimiter(calls_per_second=0.5)  # 1 call every 2 seconds

    async def connect(self):
        """Handle WebSocket connection"""
        try:
            # Set consumer reference in message batcher
            self.message_batcher.consumer = self

            # Get query parameters from the URL
            query_string = self.scope.get('query_string', b'').decode()
            query_params = parse_qs(query_string)

            # Get entity_reference from query params
            self.entity_reference = query_params.get('entity_reference', [None])[0]

            if not self.entity_reference:
                logger.error("No entity_reference provided")
                await self.close(code=4001)
                return

            # Get entity instance
            self.user = await database_sync_to_async(get_instance_by_reference)(self.entity_reference)
            if not self.user:
                logger.error(f"Entity not found for reference: {self.entity_reference}")
                await self.close(code=4002)
                return

            # Accept the Django WebSocket connection first
            await self.accept()

            # Get auth token
            auth_data = cache.get(f"messaging_auth_{self.user.xmpp_jid}")
            if not auth_data:
                success, auth_data, error = await database_sync_to_async(get_or_create_auth_token)(
                    self.user.xmpp_jid,
                    self.user.xmpp_password
                )
                if not success:
                    logger.error(f"Could not get auth token: {error}")
                    await self.close(code=4003)
                    return

            self.auth_token = auth_data['auth_token']

            # Connect to Rocket.Chat WebSocket
            self.rocket_chat_ws = await websockets.connect(
                f"{config('MESSAGING_SERVER_WS_URL')}/websocket"
            )

            # Send initial connection message
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "connect",
                "version": "1",
                "support": ["1"]
            }))

            # Wait for connection confirmation
            connect_response = await self.rocket_chat_ws.recv()
            connect_data = json.loads(connect_response)
            # logger.info(f"Connection response: {connect_data}")

            if connect_data.get('msg') != 'connected':
                logger.error(f"Failed to connect to Rocket.Chat WebSocket: {connect_data}")
                await self.close(code=4004)
                return

            # Login to Rocket.Chat WebSocket
            login_id = str(uuid.uuid4())
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "method",
                "method": "login",
                "id": login_id,
                "params": [{
                    "resume": self.auth_token
                }]
            }))

            # Wait for login response with matching ID
            while True:
                response = await self.rocket_chat_ws.recv()
                login_data = json.loads(response)
                # logger.info(f"Login response: {login_data}")

                if login_data.get('msg') == 'result' and login_data.get('id') == login_id:
                    if 'error' in login_data:
                        logger.error(f"Login failed: {login_data['error']}")
                        await self.close(code=4005)
                        return

                    self.user_id = login_data['result']['id']

                    # Send connection established message
                    await self.send(text_data=json.dumps({
                        "type": "connection_established",
                        "message": f"Connected as {self.entity_reference}",
                        "user": {
                            "entity_reference": self.entity_reference,
                            "username": self.user.user_name if hasattr(self.user, 'user_name') else self.user.storehandle
                        }
                    }))

                    break
                elif login_data.get('msg') == 'failed':
                    logger.error(f"Login failed: {login_data}")
                    await self.close(code=4005)
                    return

            # Subscribe to all messages stream
            # logger.info("Subscribing to all messages stream")
            messages_sub_id = str(uuid.uuid4())
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "sub",
                "id": messages_sub_id,
                "name": "stream-room-messages",
                "params": [
                    "__my_messages__",  # Special identifier for all messages
                    {
                        "useCollection": False,
                        "args": []
                    }
                ]
            }))
            # logger.info(f"Subscribed to stream-room-messages with ID: {messages_sub_id}")

            # Subscribe to user's subscriptions stream
            sub_id = str(uuid.uuid4())
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "sub",
                "id": sub_id,
                "name": "stream-notify-user",
                "params": [
                    f"{self.user_id}/subscriptions-changed",
                    False
                ]
            }))

            # Subscribe to user's notifications
            notif_id = str(uuid.uuid4())
            # logger.info(f"Subscribing to user notifications for user: {self.user_id}")
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "sub",
                "id": notif_id,
                "name": "stream-notify-user",
                "params": [
                    f"{self.user_id}/notification",
                    {
                        "useCollection": False,
                        "args": []
                    }
                ]
            }))
            # logger.info("Successfully subscribed to user notifications")

            # Get initial rooms list
            await self.fetch_initial_rooms()

            # Start WebSocket message handler
            asyncio.create_task(self.handle_rocket_chat_messages())

        except websockets.ConnectionClosed:
            logger.info("Rocket.Chat WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error in connect: {str(e)}")
            await self.close(code=4000)

    async def fetch_initial_rooms(self):
        """Fetch initial rooms/DMs list"""
        try:
            # Get DM list from Rocket.Chat
            dm_list = []
            has_existing_dms = False

            # Get subscriptions to get unread counts
            sub_id = str(uuid.uuid4())
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "method",
                "method": "subscriptions/get",
                "id": sub_id,
                "params": []
            }))

            # Store subscriptions data
            subscriptions_data = {}
            while True:
                response = await self.rocket_chat_ws.recv()
                data = json.loads(response)
                # logger.info(f"Subscription response for room: {data}")

                if data.get('msg') == 'result' and data.get('id') == sub_id:
                    if data.get('error'):
                        logger.error(f"Error getting subscriptions: {data.get('error')}")
                        break

                    for sub in data.get('result', []):
                        if sub.get('t') == 'd':  # Only store DM subscriptions
                            room_id = sub.get('rid')
                            subscriptions_data[room_id] = {
                                'unread': sub.get('unread', 0)
                            }

                            # Subscribe to room user activity
                            activity_sub_id = str(uuid.uuid4())
                            await self.rocket_chat_ws.send(json.dumps({
                                "msg": "sub",
                                "id": activity_sub_id,
                                "name": "stream-notify-room",
                                "params": [
                                    f"{room_id}/user-activity",
                                    False
                                ]
                            }))
                    break

            # Continue with existing room fetching logic...
            # Get rooms list
            rooms_id = str(uuid.uuid4())
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "method",
                "method": "rooms/get",
                "id": rooms_id,
                "params": [{"$date": 0}]
            }))

            while True:
                response = await self.rocket_chat_ws.recv()
                data = json.loads(response)
                # logger.info(f"Rooms response: {data}")

                if data.get('msg') == 'result' and data.get('id') == rooms_id:
                    if data.get('error'):
                        logger.error(f"Error getting rooms: {data.get('error')}")
                        break

                    result = data.get('result', {})
                    rooms = result.get('update', [])

                    if rooms:
                        has_existing_dms = True
                        for room in rooms:
                            if room.get('t') == 'd':  # Only process DMs
                                try:
                                    # Get the other user's username from the usernames list
                                    usernames = room.get('usernames', [])
                                    other_username = next((username for username in usernames
                                                         if username != self.user.xmpp_jid), None)

                                    if other_username:
                                        other_user = await sync_to_async(get_entity_profile_from_neo4j)(other_username)

                                        if other_user:
                                            room_id = room.get('_id')
                                            subscription = subscriptions_data.get(room_id, {})

                                            # Get last message
                                            last_message = None
                                            if room.get('lastMessage'):
                                                last_msg = room['lastMessage']
                                                timestamp = last_msg.get('ts', {}).get('$date')
                                                formatted_timestamp = datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S') if timestamp else None

                                                last_message = {
                                                    'text': last_msg.get('msg', ''),
                                                    'timestamp': formatted_timestamp,
                                                    'sender': last_msg.get('u', {}).get('username', '')
                                                }

                                            # Get last read timestamp
                                            last_read = await self.get_subscription_last_read(room_id)

                                            # Format updatedAt timestamp
                                            updated_at = room.get('_updatedAt', {}).get('$date')
                                            formatted_updated_at = datetime.fromtimestamp(updated_at/1000).strftime('%Y-%m-%d %H:%M:%S') if updated_at else None

                                            dm_info = {
                                                'handle': other_user['handle'],
                                                'entity_reference': other_user['reference'],
                                                'icon': other_user['icon'],
                                                'is_store': other_user['reference'].startswith('S'),
                                                'last_message': last_message,
                                                'room_id': room_id,
                                                'unread_count': subscription.get('unread', 0),
                                                'last_read': last_read,
                                                '_updatedAt': formatted_updated_at
                                            }
                                            dm_list.append(dm_info)
                                except Exception as e:
                                    logger.error(f"Error processing DM: {str(e)}")
                                    continue
                    break

            # Sort DM list by last update timestamp
            dm_list.sort(key=lambda x: datetime.strptime(x.get('_updatedAt', '1970-01-01 00:00:00'), '%Y-%m-%d %H:%M:%S') if x.get('_updatedAt') else datetime.min, reverse=True)

            # Get recommendations if DM list is less than 20
            recommendations = []
            if len(dm_list) < 20:
                recommendations = await self.get_recommendations()

            # Send the DM list to the client
            await self.send(text_data=json.dumps({
                'type': 'dm_list_update',
                'message': 'Success',
                'data': {
                    'has_existing_dms': has_existing_dms,
                    'dm_list': dm_list,
                    'recommendations': recommendations
                }
            }))

        except Exception as e:
            logger.error(f"Error in fetch_initial_rooms: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Failed to fetch rooms',
                'error': str(e),
                'is_custom': True
            }))

    async def get_subscription_last_read(self, room_id):
        """Get the last read timestamp for a specific room without marking as read"""
        try:
            # Get all subscriptions and find the one we want
            sub_id = str(uuid.uuid4())
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "method",
                "method": "subscriptions/get",
                "id": sub_id,
                "params": []
            }))

            unread_count = 0
            last_seen = None
            while True:
                response = await self.rocket_chat_ws.recv()
                data = json.loads(response)
                # logger.info(f"Subscription response for room {room_id}: {data}")

                if data.get('msg') == 'result' and data.get('id') == sub_id:
                    if data.get('error'):
                        logger.error(f"Error getting subscriptions: {data.get('error')}")
                        return None

                    # Find the subscription for our room
                    for sub in data.get('result', []):
                        if sub.get('rid') == room_id:
                            unread_count = sub.get('unread', 0)
                            last_seen = sub.get('ls')

                            # Handle different timestamp formats
                            if last_seen:
                                try:
                                    if isinstance(last_seen, dict) and '$date' in last_seen:
                                        # Handle MongoDB date format
                                        timestamp = last_seen.get('$date')
                                        if isinstance(timestamp, int):
                                            return datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S') if timestamp else None
                                    elif isinstance(last_seen, str):
                                        # Handle ISO format string
                                        parsed_date = datetime.fromisoformat(last_seen.replace('Z', '+00:00'))
                                        return parsed_date.isoformat() + 'Z'
                                except (ValueError, TypeError) as e:
                                    logger.error(f"Error parsing last_seen timestamp {last_seen}: {str(e)}")
                            break
                    break

            # If no valid last_seen or subscription not found, try to get it from message history
            history_id = str(uuid.uuid4())
            await self.rocket_chat_ws.send(json.dumps({
                "msg": "method",
                "method": "loadHistory",
                "id": history_id,
                "params": [
                    room_id,  # roomId
                    None,    # last timestamp (null to get latest)
                    unread_count + 1,  # limit (unread + 1 to get last read message)
                    None     # ls
                ]
            }))

            while True:
                response = await self.rocket_chat_ws.recv()
                data = json.loads(response)
                if data.get('msg') == 'result' and data.get('id') == history_id:
                    if data.get('error'):
                        logger.error(f"Error getting history: {data.get('error')}")
                        return None

                    result = data.get('result', {})
                    messages = result.get('messages', [])

                    if messages:
                        if unread_count > 0 and len(messages) > unread_count:
                            # Get the timestamp of the last read message
                            last_read_msg = messages[unread_count]  # Messages are in reverse chronological order
                            timestamp = last_read_msg.get('ts', {}).get('$date')
                            if isinstance(timestamp, int):
                                try:
                                    return datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S') if timestamp else None
                                except (ValueError, TypeError) as e:
                                    logger.error(f"Error formatting timestamp {timestamp}: {str(e)}")
                        else:
                            # If no unread messages or couldn't find last read message,
                            # use the oldest message timestamp
                            oldest_msg = messages[-1]
                            timestamp = oldest_msg.get('ts', {}).get('$date')
                            if isinstance(timestamp, int):
                                try:
                                    return datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S') if timestamp else None
                                except (ValueError, TypeError) as e:
                                    logger.error(f"Error formatting timestamp {timestamp}: {str(e)}")
                    break

            # logger.info(f"No last read timestamp found for room {room_id}")
            return None

        except Exception as e:
            logger.error(f"Error getting subscription last read: {str(e)}", exc_info=True)
            return None

    async def handle_rocket_chat_messages(self):
        """Handle incoming messages from Rocket.Chat WebSocket"""
        try:
            last_update = time.time()
            update_interval = 2.0  # Minimum seconds between updates

            while True:
                message = await self.rocket_chat_ws.recv()
                data = json.loads(message)
                # logger.info(f"Received WebSocket message: {data}")
                current_time = time.time()

                # Handle message confirmations
                if data.get('msg') == 'result':
                    message_id = data.get('id')
                    if message_id in self.pending_messages:
                        # Store the response and set the event
                        self.pending_messages[message_id]['response'] = data
                        self.pending_messages[message_id]['event'].set()
                # Handle different types of messages
                elif data.get('msg') == 'changed':
                    collection = data.get('collection')
                    # logger.info(f"Handling changed event for collection: {collection}")

                    # Handle stream-room-messages
                    if collection == 'stream-room-messages':
                        # logger.info("Processing stream-room-messages event")
                        fields = data.get('fields', {})
                        args = fields.get('args', [])
                        # logger.info(f"Message args: {args}")

                        new_messages = []  # Initialize new_messages list here

                        if args and len(args) > 0:
                            msg = args[0]  # Get the first message object
                            # logger.info(f"Processing message: {msg}")

                            # Skip if message is from current user
                            if msg.get('u', {}).get('username') == self.user.xmpp_jid:
                                # logger.info(f"Skipping own message from {self.user.xmpp_jid}")
                                continue

                            # Get sender info
                            sender_username = msg.get('u', {}).get('username')
                            if sender_username:
                                # logger.info(f"Processing message from: {sender_username}")
                                # Format timestamp
                                ts = msg.get('ts', {}).get('$date')
                                if ts:
                                    dt = datetime.fromtimestamp(ts/1000, tz=timezone.utc)
                                    timestamp = dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
                                else:
                                    # Use current UTC time if no timestamp
                                    timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

                                new_message = {
                                    "message_id": msg.get('_id'),
                                    "room_id": msg.get('rid'),
                                    "text": msg.get('msg'),
                                    "timestamp": timestamp,
                                    "sender_reference": msg.get('u', {}).get('username')
                                }
                                new_messages.append(new_message)
                                # logger.info(f"Added new message: {new_message}")

                        if new_messages:
                            # Get unique room IDs from messages
                            unique_room_ids = {msg.get('room_id') for msg in new_messages if msg.get('room_id')}
                            
                            # Mark all rooms as read in one go
                            if unique_room_ids:
                                try:
                                    # Mark each room as read individually using readMessages method
                                    for room_id in unique_room_ids:
                                        read_msg_id = str(uuid.uuid4())
                                        await self.rocket_chat_ws.send(json.dumps({
                                            "msg": "method",
                                            "method": "readMessages",
                                            "id": read_msg_id,
                                            "params": [room_id]
                                        }))

                                        # Wait for the read confirmation
                                        while True:
                                            response = await self.rocket_chat_ws.recv()
                                            data = json.loads(response)
                                            if data.get('msg') == 'result' and data.get('id') == read_msg_id:
                                                if data.get('error'):
                                                    logger.error(f"Error marking room {room_id} as read: {data.get('error')}")
                                                break
                                except Exception as e:
                                    logger.error(f"Error marking messages as read: {str(e)}")

                            # logger.info(f"Sending {len(new_messages)} new messages to frontend")
                            # Send new messages notification
                            await self.send(text_data=json.dumps({
                                "type": "new_incoming_messages",
                                "data": {
                                    "new_incoming_messages": new_messages
                                }
                            }))
                            # logger.info("Successfully sent messages to frontend")

                            # Send DM list update after new messages
                            # logger.info("Fetching updated DM list after new message")
                            await self.fetch_initial_rooms()
                            # logger.info("Successfully sent updated DM list")

                    # Handle user notifications
                    elif collection == 'stream-notify-user':
                        # logger.info("Processing stream-notify-user event")
                        args = data.get('args', [])
                        if args and len(args) >= 2:
                            [notification_data, _] = args
                            # logger.info(f"Notification data: {notification_data}")

                            # Check if it's a new message notification
                            if notification_data.get('type') == 'message':
                                message = notification_data.get('payload', {}).get('message', {})
                                room_id = message.get('rid')

                                if room_id:
                                    # logger.info(f"Received message notification for room: {room_id}")
                                    # Ensure we're subscribed to this room
                                    if room_id not in self.subscriptions:
                                        # logger.info(f"Auto-subscribing to room: {room_id}")
                                        sub_msg = {
                                            "msg": "sub",
                                            "id": str(uuid.uuid4()),
                                            "name": "stream-room-messages",
                                            "params": [
                                                room_id,
                                                {
                                                    "useCollection": False,
                                                    "args": []
                                                }
                                            ]
                                        }
                                        await self.rocket_chat_ws.send(json.dumps(sub_msg))
                                        self.subscriptions.add(room_id)

                                    # Update rooms list
                                    if current_time - last_update >= update_interval:
                                        await self.fetch_initial_rooms()
                                        last_update = current_time
                    # Handle other collections...

                elif data.get('msg') == 'ping':
                    # Respond to ping
                    async with self.message_lock:
                        await self.rocket_chat_ws.send(json.dumps({
                            "msg": "pong"
                        }))

        except websockets.ConnectionClosed:
            logger.info("Rocket.Chat WebSocket connection closed")
            await self.close()
        except Exception as e:
            logger.error(f"Error in handle_rocket_chat_messages: {str(e)}")
            await self.close()

    async def handle_room_change(self, room_data):
        """Handle room changes and forward to frontend"""
        # Subscribe to room messages if not already subscribed
        room_id = room_data.get('_id')
        if room_id and room_id not in self.subscriptions:
            # logger.info(f"Subscribing to messages for room: {room_id}")
            sub_msg = {
                "msg": "sub",
                "id": str(uuid.uuid4()),
                "name": "stream-room-messages",
                "params": [
                    room_id,
                    {
                        "useCollection": False,
                        "args": []
                    }
                ]
            }
            await self.rocket_chat_ws.send(json.dumps(sub_msg))
            self.subscriptions.add(room_id)
            # logger.info(f"Successfully subscribed to room: {room_id}")

        # Format room data similar to current implementation
        formatted_data = {
            'type': 'room_update',
            'data': self.format_room_data(room_data)
        }
        await self.send(text_data=json.dumps(formatted_data))

    def format_room_data(self, room_data):
        """Format room data to match current implementation"""
        return {
            'room_id': room_data.get('_id'),
            'room_name': room_data.get('name'),
            'room_type': room_data.get('t'),
            'last_message': room_data.get('lastMessage'),
            'unread': room_data.get('unread', 0),
            'users': room_data.get('usernames', [])
        }

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if self.rocket_chat_ws:
            await self.rocket_chat_ws.close()

    async def receive(self, text_data):
        """Handle messages from frontend"""
        try:
            async with self.receive_lock:
                data = json.loads(text_data)
                message_type = data.get('type')

                if message_type == 'send_message':
                    to_entity_reference = data.get('to_entity_reference')
                    message_text = data.get('message')
                    
                    if not to_entity_reference or not message_text:
                        await self.send(text_data=json.dumps({
                            'type': 'error',
                            'message': 'to_entity_reference and message are required',
                            'is_custom': True
                        }))
                        return

                    success, error = await self.send_message(to_entity_reference, message_text)
                    
                    if success:
                        to_entity_instance = await database_sync_to_async(get_instance_by_reference)(to_entity_reference)
                        room_id = success[0]  # Get room_id from success tuple
                        # Create notification for the recipient
                        @database_sync_to_async
                        def create_message_notification(to_instance, room_id):
                            try:
                                # Get auth token for recipient to check their unread count
                                success_auth, auth_data, error = get_or_create_auth_token(
                                    to_instance.xmpp_jid,
                                    to_instance.xmpp_password
                                )
                                
                                unread_count = 1  # Default to 1 for the new message
                                if success_auth:
                                    # Get subscriptions from Rocket.Chat
                                    response = requests.get(
                                        f"{config('MESSAGING_SERVER_URL')}/api/v1/subscriptions.get",
                                        headers={
                                            'X-Auth-Token': auth_data['auth_token'],
                                            'X-User-Id': auth_data['user_id']
                                        }
                                    )

                                    if response.status_code == 200:
                                        subscription_data = response.json()
                                        # Find the subscription for our room
                                        for sub in subscription_data.get('update', []):
                                            if sub.get('rid') == room_id:
                                                unread_count = sub.get('unread', 1)  # Get actual unread count, default to 1
                                                break
                                
                                notification_handler = NotificationHandler(
                                    notified_user=to_entity_reference,  # Recipient's reference
                                    notification_type=Notifications.Notifications_Type.MESSAGE_RECEIVED,
                                    notification_about=self.entity_reference,  # self.entity_reference
                                    notified_from=self.entity_reference,  # Sender's reference
                                    image=self.user.icon,
                                    notes=unread_count  # Add actual unread count to notes
                                )
                                notification_handler.create_notification(notification_handler)
                            except Exception as e:
                                logger.error(f"Error creating message notification: {str(e)}")

                        # Create notification asynchronously
                        await create_message_notification(to_entity_instance, room_id)

                        await self.send(text_data=json.dumps({
                            'type': 'message_sent',
                            'message': 'Message sent successfully',
                            'data':{
                                'room_id': room_id,
                                'message_id': success[1],
                                'timestamp': datetime.utcfromtimestamp(success[2]['$date'] / 1000).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
                                }
                        }))
                    else:
                        await self.send(text_data=json.dumps({
                            'type': 'error',
                            'message': f'Failed to send message: {error}',
                            'is_custom': True
                        }))

                elif message_type == 'get_dm_list':
                    await self.fetch_initial_rooms()

        except Exception as e:
            logger.error(f"Error in receive: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Internal server error',
                'error': str(e),
                'is_custom': True
            }))

    async def send_message(self, to_entity_reference, message_text):
        """
        Send a message to an entity using WebSocket
        Args:
            to_entity_reference: The reference of the entity to send the message to
            message_text: The text message to send
        Returns:
            tuple: ((room_id, message_id, timestamp), None) on success, (False, error) on failure
        """
        try:
            # Get the target instance
            to_instance = await sync_to_async(get_instance_by_reference)(to_entity_reference)
            if not to_instance:
                return False, "Invalid target entity reference"

            # Get or create room ID
            success, room_id, error = await sync_to_async(get_or_create_room_id)(self.user, to_instance)
            if not success:
                return False, f"Failed to get chat room: {error}"

            # Generate unique message ID
            message_id = str(uuid.uuid4())
            
            # Add to pending messages before sending
            self.pending_messages[message_id] = {
                'event': asyncio.Event(),
                'response': None
            }
            
            # Send message through WebSocket
            async with self.message_lock:
                await self.rocket_chat_ws.send(json.dumps({
                    "msg": "method",
                    "method": "sendMessage",
                    "id": message_id,
                    "params": [{
                        "rid": room_id,
                        "msg": message_text
                    }]
                }))

            # Wait for the message confirmation with a timeout
            try:
                await asyncio.wait_for(self.pending_messages[message_id]['event'].wait(), timeout=5.0)
                response = self.pending_messages[message_id]['response']
                del self.pending_messages[message_id]
                
                if response and 'result' in response:
                    rocket_chat_msg_id = response['result'].get('_id')
                    timestamp = response['result'].get('ts')
                    return (room_id, rocket_chat_msg_id, timestamp), None
                return False, "Failed to get message ID from response"
                
            except asyncio.TimeoutError:
                del self.pending_messages[message_id]
                return False, "Message send timeout"
                    
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}", exc_info=True)
            if 'message_id' in locals() and message_id in self.pending_messages:
                del self.pending_messages[message_id]
            return False, str(e)

    async def get_recommendations(self):
        """
        Get chat recommendations based on follower count and following status
        """
        try:
            recommendations = []
            needed_recommendations = 20
            
            @database_sync_to_async
            def get_neo4j_nodes():
                # Get top users with most followers
                top_users_nodes = Neo4jUser.nodes.filter(
                    is_deleted=False,
                    name__isnull=False,
                    handle__isnull=False,
                ).exclude(
                    reference=self.entity_reference
                ).order_by('-follower_count')[:needed_recommendations]

                # Get top stores with most followers
                top_stores_nodes = Neo4jStore.nodes.filter(
                    is_deleted=False,
                    is_active=True,
                    name__isnull=False,
                    handle__isnull=False
                ).exclude(
                    handle__istartswith='test_'
                ).exclude(
                    reference=self.entity_reference
                ).order_by('-follower_count')[:needed_recommendations]

                return list(top_users_nodes), list(top_stores_nodes)

            @database_sync_to_async
            def get_django_models(user_references, store_references):
                top_users = User.objects.filter(
                    user_reference__in=user_references,
                    icon__isnull=False
                ).exclude(icon='')

                top_stores = Store.objects.filter(
                    store_reference__in=store_references,
                    icon__isnull=False
                ).exclude(icon='')

                return list(top_users), list(top_stores)

            # Get Neo4j nodes asynchronously
            top_users_nodes, top_stores_nodes = await get_neo4j_nodes()

            # Get references
            user_references = [node.reference for node in top_users_nodes]
            store_references = [node.reference for node in top_stores_nodes]

            # Get Django models asynchronously
            top_users, top_stores = await get_django_models(user_references, store_references)

            # Combine recommendations
            for user in top_users:
                recommendations.append({
                    'handle': user.user_name,
                    'entity_reference': user.user_reference,
                    'icon': user.icon.url if user.icon else None,
                    'is_store': False,
                })

            for store in top_stores:
                recommendations.append({
                    'handle': store.storehandle,
                    'entity_reference': store.store_reference,
                    'icon': store.icon.url if store.icon else None,
                    'is_store': True,
                })

            # Shuffle recommendations for variety
            random.shuffle(recommendations)
            return recommendations[:needed_recommendations]

        except Exception as e:
            logger.error(f"Error getting recommendations: {str(e)}")
            return []
