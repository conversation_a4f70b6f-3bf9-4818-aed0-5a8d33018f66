# Generated by Django 3.2.13 on 2023-05-09 09:03

from django.db import migrations

def enter_transaction_and_commission_fee_reduction_in_order_payout_table(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in product_reference column, where
    # productid of CartItem model is same as productid in Product table.

    OrderPayout = apps.get_model("orders", "OrderPayout")
    OrderConfiguration = apps.get_model("orders", "OrderConfiguration")
    for item in OrderPayout.objects.all():
        order_value = item.order_value
        order_configuration = OrderConfiguration.objects.all().first()
        transaction_fee = order_configuration.transaction_fee
        commission_fee = order_configuration.commission_fee
        early_commission_fee = order_configuration.early_commission_fee
        order_value_after_transaction_fee_reduction = order_value - (order_value * (transaction_fee / 100))
        item.order_value_after_transaction_fee_reduction = order_value_after_transaction_fee_reduction
        item.order_value_after_total_reduction = order_value_after_transaction_fee_reduction - (
                    commission_fee + early_commission_fee)

        item.save(update_fields=["order_value_after_transaction_fee_reduction", "order_value_after_total_reduction"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    OrderPayout = apps.get_model("orders", "OrderPayout")
    for item in OrderPayout.objects.all():
        item.order_value_after_transaction_fee_reduction = None
        item.order_value_after_total_reduction = None
        item.save(update_fields=["order_value_after_total_reduction", "order_value_after_transaction_fee_reduction"])

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0078_auto_20230508_1550'),
    ]

    operations = [migrations.RunPython(enter_transaction_and_commission_fee_reduction_in_order_payout_table, reverse_func)
    ]
