import datetime
import time
from rest_framework import generics, mixins
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from .serializers import (
    GetUserOrderItemsSerializer,
    GetstoreOrderItemsSerializer,
    AllOrderSellerSerializer,
    OrderItemsSerializer,
    GetCustomerDetailsSerializer,
    GetOrderDetailsSerializer,
    OrderLifeCycleSerializer,
    DeliveryTrackingSerializer,
    ReturnTrackingSerializer,
    ShippingUpdatesSerializer,
    OrderRatingSerializer,
    SubOrderHistorySerializer,
    LogisticPartnerSerializer,
)
import string
import random
from .models import (
    Order,
    SubOrder,
    OrderLifeCycle,
    ShippingHistory,
    RefundedAmount,
    OrderConfiguration,
    LogisticPartner,
    OrderOtp
)
from ..payout_api.models import OrderPayout, PayoutTransactions
from ..payment_api.models import SubOrderPaymentDetails
from ..helpers import (
    initiate_refund,
    get_order_request_number#,
    # update_payout_after_cancel_or_return,
)
from ...shared_utils.payout_utils import update_payout_after_cancel_or_return
from django.db.models import Q
from django.db.models import Sum
from itertools import groupby
from itertools import chain
from operator import itemgetter
from common.util.support_helper import GenerateNotifications
from common.util.notification_handler import NotificationHandler
from stores.store_api.models import Store
from stores.store_api.serializers import StoreFollowerSerializer
from stores.store_settings_api.models import DeliveryLocations, DeliverySettings
from stores.store_settings_api.models import (
    TrustCenter,
    RefundAndWarranty,
    DeliverySettings,
)
from users.user_api.models import User, UserAddress
from users.user_api.serializers import UserFollowerListSerializer
from users.notification_api.models import Notifications
from products.models import Product, ProductImages, ProductVersion
from products.comment_and_reply_api.models import Comment
from products.api.serializers import ProductVersionSerializer, GetProductListSerializer
from ..cart_api.models import CartItem
from ..helpers import (
    input_formatter_cart_to_fee_calculation_wrapper,
    input_formatter_order_to_fee_calculation_wrapper,
    fee_calculation_wrapper,
    update_refund_amount_old,
)
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging
import pytz
from django_pandas.io import read_frame
from django.db import transaction
from ..payment_api.utils import get_dummy_txn_token
from decouple import config
import requests
from requests.auth import HTTPBasicAuth
from ..helpers import input_formatter_cart_to_fee_calculation_wrapper, fee_calculation_wrapper


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

SKIP_RAZORPAY_API = config("SKIP_RAZORPAY_API", cast=bool)
RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")

##############################################################################################


def create_refund_entry(user_reference, order_reference, suborder, suborder_numbers):
    if RefundedAmount.objects.filter(
        user_reference=user_reference,
        order_reference=order_reference,
        suborder_reference=suborder,
        refund_status="UNPROCESSED"
    ).exists():
        logger.info(
            "refunded amount entry for suborder %s already exists", suborder_numbers
        )
    else:
        refund_request_initiated_date = datetime.datetime.now()
        RefundedAmount.objects.create(
            user_reference=user_reference,
            order_reference=order_reference,
            suborder_reference=suborder,
            refund_request_initiated_date=refund_request_initiated_date,
            refund_status=RefundedAmount.Status.UNPROCESSED,
        )

        suborder_number_list = [suborder_numbers]
        cancelled_by = None
        update_refund_amount_old(suborder_number_list, order_reference, cancelled_by)


def update_order_status(value):
    suborder_numbers = value["suborder_number"]
    suborder_status = value["status"]
    status_transitions = {
        "WAITING_FOR_CONFIRMATION": ["PAYMENT_SUCCESS"],
        "ORDER_CONFIRMED": ["WAITING_FOR_CONFIRMATION", "PAYMENT_SUCCESS"],
        "ORDER_CANCELLED": ["ORDER_CONFIRMED", "WAITING_FOR_CONFIRMATION", "PAYMENT_SUCCESS"],
        "DELIVERY_IN_PROGRESS": ["ORDER_CONFIRMED"],
        "ORDER_DELIVERED": ["DELIVERY_IN_PROGRESS"],
        "DELIVERY_FAILED": ["DELIVERY_IN_PROGRESS"],
        "RETURN_REQUESTED": ["ORDER_DELIVERED"],
        "RETURN_CONFIRMED": ["RETURN_REQUESTED"],
        "REFUND_HOLD": ["RETURN_CONFIRMED", "ORDER_CANCELLED"],
        "RETURNED_TO_SELLER": ["RETURN_CONFIRMED"],
        "RETURN_FAILED": ["RETURN_CONFIRMED"]
    }

    suborder = SubOrder.objects.get(suborder_number=suborder_numbers)
    current_status = suborder.suborder_status

    if current_status not in status_transitions.get(suborder_status, []):
        logger.error(f"Invalid status transition from {current_status} to {suborder_status}")
        return Response(
            {"message": f"Invalid status transition from {current_status} to {suborder_status}"},
            status=400
        )
    if suborder_status == SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION:
        logger.info ("Entered Update Delivery date section")
        estimated_delivery_date = value["estimated_delivery_date"]
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.estimated_delivery_date = estimated_delivery_date
        suborder.save(
            update_fields=[
                "estimated_delivery_date"
            ]
        )
        order_lifecycle = OrderLifeCycle.objects.filter(
            suborder_number=suborder_numbers,
            suborder_status=SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION,
        ).last()
        order_lifecycle.estimated_delivery_date = estimated_delivery_date
        order_lifecycle.save(
            update_fields=["estimated_delivery_date"]
        )

    if suborder_status == SubOrder.Suborder_Status.ORDER_CONFIRMED:
        logger.info("Entered order confirmed section")
        estimated_delivery_date = value["estimated_delivery_date"]
        now = datetime.datetime.now()
        confirmation_date = now.strftime("%d/%m/%Y %H:%M:%S")
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        if suborder.suborder_status == suborder_status:
            suborder.estimated_delivery_date = estimated_delivery_date
            cols = ["estimated_delivery_date"]
            toNotify = True
        else:
            suborder.suborder_status = suborder_status
            suborder.confirmation_date = confirmation_date
            suborder.estimated_delivery_date = estimated_delivery_date
            cols = [
                "suborder_status",
                "confirmation_date",
                "estimated_delivery_date",
            ]
            toNotify = True
        suborder.save(
            update_fields= cols
        )
        # Update payout status as in process for the order payout instances having the given suborder number
        OrderPayout.objects.filter(suborder_number=suborder_numbers).update(payout_status=OrderPayout.Payout_Status.IN_PROCESS)

        order_lifecycle = OrderLifeCycle.objects.filter(
            suborder_number=suborder_numbers,
            suborder_status=SubOrder.Suborder_Status.ORDER_CONFIRMED,
        ).last()
        order_lifecycle.confirmation_date = confirmation_date
        order_lifecycle.estimated_delivery_date = estimated_delivery_date
        order_lifecycle.save(
            update_fields=["confirmation_date", "estimated_delivery_date"]
        )
        order_number = suborder.order_number
        order = Order.new_objects.filter(order_number=order_number).first()
        ordered_user = order.user_reference.user_reference

        notification_handler = NotificationHandler(
            notified_user=ordered_user,
            notification_type=Notifications.Notifications_Type.ORDER_CONFIRMED,
            notification_about=order_number,
            image=suborder.store_image,
        )
        if toNotify:
            notification_handler.create_notification(notification_handler)

        # notification_gen = GenerateNotifications()
        # notification_gen.create_notifications(
        #     notified_user=ordered_user,
        #     notification_type=Notifications.Notifications_Type.ORDER_CONFIRMED,
        #     notification_about=order_number,
        #     image=suborder.product_image
        # )
        logger.info("Exited order confirmed section")

    elif suborder_status == SubOrder.Suborder_Status.ORDER_CANCELLED:
        logger.info("Entered order cancelled section")
        cancellation_reason = value["cancellation_reason"]
        cancelled_by = value["cancelled_by"]
        now = datetime.datetime.now()
        cancelled_date = now.strftime("%d/%m/%Y %H:%M:%S")
        suborder = SubOrder.objects.get(suborder_number=suborder_numbers)
        # get order configuration instance
        order_configuration_instance=OrderConfiguration.objects.all().first()

        suborder_number_list = [suborder_numbers]
        user_reference = suborder.user_reference
        order_reference = suborder_numbers.split("-")[0]
        new_return_cost_on = None
        if cancelled_by == SubOrder.Cancelled_By.BUYER:
            if current_status == SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION:
                new_return_cost_on = 'seller to buyer'
            suborder.suborder_status = SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER
            # notification_gen = GenerateNotifications()
            # notification_gen.create_notifications(
            #     notified_user=suborder.store_reference,
            #     notification_type=Notifications.Notifications_Type.ORDER_CANCELLED,
            #     notification_about=order_reference,
            #     image=suborder.product_image
            # )

            refund_processing_waiting_time = order_configuration_instance.cancel_refund_wait_time
            # date on which refund processing will happen.
            refund_request_initiated_date = datetime.datetime.now()
            refund_requested_date = datetime.datetime.now() + datetime.timedelta(
                minutes=refund_processing_waiting_time
            )
            # refund request date updating

            # data needed to create notification to buyer that seller cancelled this suborder
            notified_to = suborder.store_reference
            notification_type = Notifications.Notifications_Type.BUYER_CANCELLED_ORDER

        elif cancelled_by == SubOrder.Cancelled_By.SELLER:
            suborder.suborder_status = (
                SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER
            )
            refund_request_initiated_date = datetime.datetime.now()
            refund_requested_date = datetime.datetime.now()

            # data needed to create notification to buyer that seller cancelled this suborder
            notified_to = user_reference
            notification_type = Notifications.Notifications_Type.SELLER_CANCELLED_ORDER

        RefundedAmount.objects.create(
            user_reference=user_reference,
            order_reference=order_reference,
            suborder_reference=suborder,
            refund_status=RefundedAmount.Status.UNPROCESSED,
            refund_request_initiated_date=refund_request_initiated_date,
        )

        # refund amount should calculate before the suborder status got changed to "ORDER_CANCELLED_BY_SELLER or ORDER_CANCELLED_BY_BUYER"
        # because this status will affect the refund calculation function logic.
        update_refund_amount_old(suborder_number_list, order_reference, cancelled_by, new_return_cost_on=new_return_cost_on)

        suborder.cancelled_date = cancelled_date
        suborder.cancellation_reason = cancellation_reason
        suborder.cancelled_by = cancelled_by
        suborder.is_deleted = True
        suborder.save(
            update_fields=[
                "suborder_status",
                "cancelled_date",
                "cancellation_reason",
                "cancelled_by",
                "is_deleted",
            ]
        )
        notification_handler = NotificationHandler(
            notified_user=notified_to,
            notification_type=notification_type,
            notification_about=order_reference,
            image=suborder.store_image,
        )
        notification_handler.create_notification(notification_handler)

        logger.info(
            "refunded amount entry for suborder %s has created", suborder_numbers
        )

        logger.info("Refund process initiated")
        # result = initiate_refund(suborder_number_list, cancelled_by)
        # if result == "error":
        #     logger.info(
        #         "refund initiation for sub_order %s cancellation has failed.",
        #         suborder_numbers,
        #     )
        # else:
        #     refund_instance = RefundedAmount.objects.get(
        #         suborder_reference=suborder_numbers
        #     )
        #     refund_instance.refund_requested_date = refund_requested_date
        #     refund_instance.save(update_fields=["refund_requested_date"])
        #
        #     logger.info(
        #         "refund initiation for cancelled suborder %s is success.",
        #         suborder_numbers,
        #     )
        order_lifecycle = OrderLifeCycle.objects.get(
            Q(suborder_number=suborder_numbers)
            & (
                Q(suborder_status=SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER)
                | Q(suborder_status=SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER)
            )
        )
        order_lifecycle.cancelled_date = cancelled_date
        order_lifecycle.save(update_fields=["cancelled_date"])
        SubOrderPaymentDetails.objects.filter(suborder_number=suborder).update(
            is_suborder_deleted=True,
            suborder_deleted_condition=SubOrderPaymentDetails.Deleted_Conditions.CANCELLED,
        )
        update_payout_after_cancel_or_return(suborder_numbers)
        order_payout_instance = OrderPayout.objects.get(suborder_number=suborder)
        order_payout_instance.payout_status = (
            OrderPayout.Payout_Status.WAITING_FOR_RELEASE
        )
        #payoutbug payout update after cancellation
        order_payout_instance.payout_release_date = datetime.date.today()
        order_payout_instance.save(
            update_fields=["payout_status", "payout_release_date"]
        )
        logger.info("order cancellation completed")

    elif suborder_status == SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS:
        logger.info("Entered delivery in progress section")
        package_number = value["package_number"]
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.suborder_status = suborder_status
        suborder.package_number = package_number
        suborder.save(update_fields=["suborder_status", "package_number"])
        order_lifecycle = OrderLifeCycle.objects.filter(
            suborder_number=suborder_numbers,
            suborder_status=SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS,
        ).last()
        order_lifecycle.package_number = package_number
        order_lifecycle.save(update_fields=["package_number"])

        order_number = suborder.order_number
        # package_name = suborder.display_package_number

        order = Order.new_objects.filter(order_number=order_number).first()
        ordered_user = order.user_reference.user_reference
        notification_handler = NotificationHandler(notified_user=ordered_user,
                                                   notification_type=Notifications.Notifications_Type.ORDER_SHIPPED,
                                                   notification_about=order_number,
                                                   package_name=suborder.display_package_number,
                                                   image=suborder.store_image)
        notification_handler.create_notification(notification_handler)
        # notification_gen = GenerateNotifications()
        # notification_gen.create_notifications(
        #     notified_user=ordered_user,
        #     notification_type=Notifications.Notifications_Type.ORDER_SHIPPED,
        #     notification_about=order_number,
        #     image=suborder.product_image
        # )
        logger.info("Exited delivery in progress section")

    elif suborder_status == SubOrder.Suborder_Status.ORDER_DELIVERED:
        logger.info("Entered order delivered section")
        now = datetime.datetime.now()
        delivered_date = now.strftime("%d/%m/%Y %H:%M:%S")
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.delivered_date = delivered_date
        suborder.suborder_status = suborder_status
        suborder.save(update_fields=["suborder_status", "delivered_date"])
        order_lifecycle = OrderLifeCycle.objects.filter(
            suborder_number=suborder_numbers,
            suborder_status=SubOrder.Suborder_Status.ORDER_DELIVERED,
        ).last()
        order_lifecycle.delivered_date = delivered_date
        order_lifecycle.save(update_fields=["delivered_date"])
        OrderPayout.objects.filter(suborder_number=suborder).update(
            payout_status=OrderPayout.Payout_Status.WAITING_FOR_RELEASE
        )
        logger.info("Exited order delivered section")

    elif suborder_status == SubOrder.Suborder_Status.DELIVERY_FAILED:
        logger.info("Entered delivery failed section")
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.suborder_status = suborder_status
        suborder.save(update_fields=["suborder_status"])

        notification_handler = NotificationHandler(
            notified_user=suborder.user_reference,
            notification_type=Notifications.Notifications_Type.DELIVERY_FAILED,
            notification_about=suborder.order_number,
            image=suborder.store_image,
        )
        notification_handler.create_notification(notification_handler)

        logger.info("Exited delivery failed section")

    elif suborder_status == SubOrder.Suborder_Status.RETURN_REQUESTED:
        logger.info("Entered return requested section")
        return_reason = value["return_reason"]
        now = datetime.datetime.now()
        return_initiate_date = now.strftime("%d/%m/%Y %H:%M:%S")
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.return_initiate_date = return_initiate_date
        suborder.suborder_status = suborder_status
        suborder.return_reason = return_reason
        suborder.save(
            update_fields=["suborder_status", "return_initiate_date", "return_reason"]
        )
        order_lifecycle = OrderLifeCycle.objects.filter(
            suborder_number=suborder_numbers,
            suborder_status=SubOrder.Suborder_Status.RETURN_REQUESTED,
        ).last()
        order_lifecycle.return_initiate_date = return_initiate_date
        order_lifecycle.save(update_fields=["return_initiate_date"])

        user_reference = suborder.user_reference
        order_reference = suborder_numbers.split("-")[0]
        
        # create_refund_entry(user_reference, order_reference, suborder, suborder_numbers)
        
        user_icon = User.objects.get(user_reference=suborder.user_reference).icon

        notification_handler = NotificationHandler(
            notified_user=suborder.store_reference,
            notification_type=Notifications.Notifications_Type.RETURN_REQUESTED,
            notification_about=suborder.order_number,
            image=user_icon,
        )
        notification_handler.create_notification(notification_handler)
        logger.info("Exited return requested section")

    elif suborder_status == SubOrder.Suborder_Status.RETURN_CONFIRMED:
        logger.info("Entered return confirmed section")
        now = datetime.datetime.now()
        return_confirmation_date = now.strftime("%d/%m/%Y %H:%M:%S")
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.return_confirmation_date = return_confirmation_date
        suborder.suborder_status = suborder_status
        suborder.save(update_fields=["return_confirmation_date", "suborder_status"])

        # Refund entry is created only when the refund is agreed by seller
        create_refund_entry(user_reference, order_reference, suborder, suborder_numbers)

        notification_handler = NotificationHandler(
            notified_user=suborder.user_reference,
            notification_type=Notifications.Notifications_Type.RETURN_ACCEPTED,
            notification_about=suborder.order_number,
            image=suborder.store_image,
        )
        notification_handler.create_notification(notification_handler)

        logger.info("Exited return confirmed section")

    elif suborder_status == SubOrder.Suborder_Status.REFUND_HOLD:
        logger.info("Entered refund hold section")
        refund_hold_reason = value["refund_hold_reason"]

        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.suborder_status = suborder_status
        suborder.save(update_fields=["suborder_status"])

        refund_instance = RefundedAmount.objects.get(
            suborder_reference=suborder_numbers
        )
        refund_instance.refund_status = RefundedAmount.Status.HOLD
        refund_instance.refund_hold_reason = refund_hold_reason
        refund_instance.save(update_fields=["refund_status", "refund_hold_reason"])

        notification_handler = NotificationHandler(
            notified_user=suborder.user_reference,
            notification_type=Notifications.Notifications_Type.REFUND_HOLD,
            notification_about=suborder.order_number,
            image=suborder.store_image,
        )
        notification_handler.create_notification (notification_handler)

        logger.info("Exited refund hold section")


    elif suborder_status == SubOrder.Suborder_Status.RETURNED_TO_SELLER:
        logger.info("Entered return to seller section")

        now = datetime.datetime.now()
        returned_date = now.strftime("%d/%m/%Y %H:%M:%S")

        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.returned_date = returned_date
        suborder.suborder_status = suborder_status
        suborder.save(update_fields=["suborder_status", "returned_date"])

        order_lifecycle = OrderLifeCycle.objects.filter(
            suborder_number=suborder_numbers,
            suborder_status=SubOrder.Suborder_Status.RETURNED_TO_SELLER,
        ).last()
        order_lifecycle.returned_date = returned_date
        order_lifecycle.save(update_fields=["returned_date"])

        SubOrderPaymentDetails.objects.filter(suborder_number=suborder).update(
            is_suborder_deleted=True,
            suborder_deleted_condition=SubOrderPaymentDetails.Deleted_Conditions.RETURNED,
        )
        order_payout_instance = OrderPayout.objects.get(suborder_number=suborder)
        order_payout_instance.payout_status = (
            OrderPayout.Payout_Status.WAITING_FOR_RELEASE
        )
        order_payout_instance.save(update_fields=["payout_status"])
        refund_processing_waiting_time = OrderConfiguration.objects.all().first().return_refund_wait_time
        refund_instance = RefundedAmount.objects.get(
            suborder_reference=suborder_numbers
        )
        refund_instance.refund_requested_date = (
            datetime.datetime.now()
            + datetime.timedelta(minutes=refund_processing_waiting_time)
        )
        refund_instance.save(update_fields=["refund_requested_date"])

        notification_handler = NotificationHandler(
            notified_user=suborder.user_reference,
            notification_type=Notifications.Notifications_Type.RETURN_RECEIVED,
            notification_about=suborder.order_number,
            image=suborder.store_image,
        )
        notification_handler.create_notification(notification_handler)

        logger.info("Exited return to seller section")

    elif suborder_status == SubOrder.Suborder_Status.RETURN_FAILED:
        logger.info("Entered return failed section")
        suborder = SubOrder.new_objects.get(suborder_number=suborder_numbers)
        suborder.suborder_status = suborder_status
        suborder.save(update_fields=["suborder_status"])
        logger.info("Exited return failed section")


class UpdateOrderStatus(APIView):
    @swagger_auto_schema(operation_summary="update order status.")
    def post(self, request, *args, **kwargs):
        """for any updates of an order uses this api. Only the input varies."""
        suborder_lists = request.data["suborder_list"]

        # To get the order_number
        suborder_number = suborder_lists[0]["suborder_number"]

        # from suborder number split and take order_number
        order_number = suborder_number.split("-")[0]

        if suborder_lists[0]["status"] == SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS:
            created_package_number = create_package_number(packaging_type="DELIVERY")
            suborder_lists = [
                dict(item, **{"package_number": created_package_number})
                for item in suborder_lists
            ]
            list(map(update_order_status, suborder_lists))
            (
                deliverymethod_self,
                logistics,
                name,
                phone,
                partner,
                tracking_link,
            ) = get_default_delivery_details(created_package_number)

            suborder = SubOrder.new_objects.get(
                suborder_number=suborder_number, package_number=created_package_number
            )

            return Response(
                {
                    "message": "success",
                    "self_delivery_by_store": deliverymethod_self,
                    "delivery_by_logistic_partner": logistics,
                    "delivery_person_name": name,
                    "delivery_person_contact": phone,
                    "logistic_partner": partner,
                    "tracking_link": tracking_link,
                    "display_package_number": suborder.display_package_number,
                    "package_number": created_package_number,
                },
                status=status.HTTP_200_OK,
            )

        elif suborder_lists[0]["status"] == "RETURN_IN_PROGRESS":
            created_return_package_number = create_package_number(
                packaging_type="RETURN"
            )
            suborder_lists = [
                dict(item, **{"return_package_number": created_return_package_number})
                for item in suborder_lists
            ]
            list(map(update_order_status, suborder_lists))
            return Response(
                {
                    "message": "success",
                    "display_return_package_number": None,
                    "return_package_number": created_return_package_number,
                },
                status=status.HTTP_200_OK,
            )
        elif suborder_lists[0]["status"] == "ORDER_DELIVERED":
            suborder = SubOrder.new_objects.get(suborder_number=suborder_number)

            if not OrderOtp.objects.filter(
                    package_number=suborder.package_number,
                    user_reference=suborder.user_reference,
                    otp_status=OrderOtp.Status.USED
            ).exists():
                return Response({"message": "Delivery OTP is not Verified"}, status=status.HTTP_200_OK)

            list(map(update_order_status, suborder_lists))

            order = Order.new_objects.filter(order_number=order_number).first()
            ordered_user = order.user_reference.user_reference
            notification_handler = NotificationHandler(
                notified_user=ordered_user,
                notification_type=Notifications.Notifications_Type.PACKAGE_DELIVERED,
                notification_about=order_number,
                image=suborder.store_image,
                package_number=suborder.package_number,
            )
            notification_handler.create_notification(notification_handler)
            return Response({"message": "success"}, status=status.HTTP_200_OK)
        else:
            list(map(update_order_status, suborder_lists))
            return Response({"message": "success"}, status=status.HTTP_200_OK)



def get_default_delivery_details(package_number):
    instance = SubOrder.new_objects.filter(package_number=package_number).first()
    product_reference = instance.product_reference
    store_reference = instance.store_reference
    delivery_setting_instance = get_delivery_settings_of_product(
        product_reference, store_reference
    )
    deliverymethod_self = delivery_setting_instance.deliverymethod_self
    deliverymethod_logistics = delivery_setting_instance.deliverymethod_logistics
    delivery_personal_name = delivery_setting_instance.delivery_personal_name
    delivery_personal_phone = delivery_setting_instance.delivery_personal_phone
    default_logistic_partner = delivery_setting_instance.default_logistic_partner
    delivery_tracking_link = delivery_setting_instance.delivery_tracking_link
    return (
        deliverymethod_self,
        deliverymethod_logistics,
        delivery_personal_name,
        delivery_personal_phone,
        default_logistic_partner,
        delivery_tracking_link,
    )



def create_package_number(packaging_type=None):
    now = datetime.datetime.now()
    code = now.strftime("%y%m%d%H%M%S")
    N = 3
    random_string = "".join(random.choices(string.ascii_uppercase, k=N))
    if packaging_type == "RETURN":
        my_code = ("R", code, random_string)
    elif packaging_type == "DELIVERY":
        my_code = ("P", code, random_string)
    return "".join(my_code)

