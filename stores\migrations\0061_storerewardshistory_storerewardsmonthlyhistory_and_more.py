# Generated by Django 4.2.7 on 2024-07-17 06:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0060_remove_store_flash_points_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="StoreRewardsHistory",
            fields=[
                ("reward_id", models.AutoField(primary_key=True, serialize=False)),
                ("reward_reference", models.CharField(max_length=20, unique=True)),
                (
                    "reward_category",
                    models.CharField(
                        blank=True,
                        choices=[("INFINITY", "Infinity"), ("FLASH", "Flash")],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "reward_value",
                    models.CharField(blank=True, max_length=15, null=True),
                ),
                (
                    "reward_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("MONTHLY_CREDIT", "Monthly Credit"),
                            ("TRANSFER", "Transfer"),
                            ("REDEEM", "Redeem"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        blank=True,
                        choices=[("CREDIT", "Credit"), ("DEBIT", "Debit")],
                        max_length=7,
                        null=True,
                    ),
                ),
                (
                    "store_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "event_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "reward_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("SUCCESS", "Success"),
                            ("PENDING", "Pending"),
                            ("FAILED", "Failed"),
                        ],
                        default="SUCCESS",
                        max_length=10,
                        null=True,
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "store rewards histories",
                "db_table": '"store"."store_rewards_history"',
            },
        ),
        migrations.CreateModel(
            name="StoreRewardsMonthlyHistory",
            fields=[
                (
                    "month_reward_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "month_with_year",
                    models.CharField(blank=True, max_length=14, null=True),
                ),
                ("reward_granted", models.PositiveIntegerField(default=0)),
                ("reward_used", models.PositiveIntegerField(default=0)),
                (
                    "store_reference",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "store rewards monthly",
                "db_table": '"store"."store_rewards_monthly_history"',
            },
        ),
        migrations.CreateModel(
            name="StoreRewards",
            fields=[
                (
                    "store_reward_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("infinity_points", models.PositiveIntegerField(default=0)),
                ("flash_points", models.PositiveIntegerField(default=0)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "store_reference",
                    models.ForeignKey(
                        db_column="store_reference",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="store_rewards",
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "store rewards",
                "db_table": '"store"."store_rewards"',
            },
        ),
    ]
