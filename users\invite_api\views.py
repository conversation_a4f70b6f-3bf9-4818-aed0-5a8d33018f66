from rest_framework import status, mixins, generics
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import viewsets

from django.db.models import Q
from django.db.models import Case, When, Value, CharField
from .models import InviteUser, UserRewards, RewardsHistory
from .serializers import (
    GetInviteSerializer,
    InviteSerializer,
    GroupInviteSerializer,
    GetAllInvitesCreatedByOneUserSerializer,
    ListOfUserContactsSerializer,
    RewardsHistorySerializer,
)
from ..user_api.models import User
from stores.store_api.models import Store
from ..user_api.utils import get_user_total_earnings, get_user_total_savings
from ..notification_api.models import Notifications
from common.util.support_helper import GenerateNotifications
from ..helpers import save_invite_credentials, find_applicable_invite_type
import logging
import random
import string

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def create_member_invite_code(created_by):
    created_user = created_by
    random_digits = "".join(random.choices(string.digits, k=4))
    code = "M-" + created_user.upper() + "-" + random_digits
    if InviteUser.objects.filter(invite_code=code).exists():
        code = create_member_invite_code(created_user)
        return code
    return code

def create_seller_invite_code(created_by):
    created_user = created_by
    random_digits = "".join(random.choices(string.digits, k=4))
    code = "S-" + created_user.upper() + "-" + random_digits
    if InviteUser.objects.filter(invite_code=code).exists():
        code = create_seller_invite_code(created_user)
        return code
    return code


def create_corporate_invite_code():
    random_digits = "".join(random.choices(string.digits, k=4))
    code = "G-SWDC" + "-" + random_digits
    if InviteUser.objects.filter(invite_code=code).exists():
        code = create_corporate_invite_code()
        return code
    return code

class CreateInviteCode(
    mixins.ListModelMixin, mixins.CreateModelMixin, generics.GenericAPIView
):
    def get(self, request, *args, **kwargs):
        logger.info("entered get method CreateInviteCode")
        instance = InviteUser.objects.filter(is_deleted=False)
        serializer = GetInviteSerializer(instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def post(self, request, *args, **kwargs):
        logger.info("entered post method of CreateInviteCode")
        user_instance = User.objects.get(userid=request.data["invited_user"])
        seller_invite_balance = user_instance.seller_invite_balance
        member_invite_balance = user_instance.member_invite_balance

        if request.data["invite_type"] == InviteUser.Invite_Type_Choices.MEMBER:
            logger.info("Inside if condition: where invite_type=member")
            if member_invite_balance == 0:
                return Response(
                    {
                        "message": "error",
                        "data": "not eligible to create member invites",
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                created_by = user_instance.user_name[:4]
                invite_code = create_member_invite_code(created_by)
                request.data["invite_code"] = invite_code
                serializer = InviteSerializer(data=request.data)
                if serializer.is_valid():
                    serializer.save()
                    user_instance.member_invite_balance = (
                        user_instance.member_invite_balance - 1
                    )
                    user_instance.save()
                    return Response(
                        {"message": "success", "data": serializer.data},
                        status=status.HTTP_200_OK,
                    )
                return Response(
                    {"message": "error", "data": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        elif request.data["invite_type"] == InviteUser.Invite_Type_Choices.SELLER:
            logger.info("Inside elif condition: where invite_type=seller")
            if seller_invite_balance == 0:
                return Response(
                    {
                        "message": "success",
                        "data": "not eligible to create member invites",
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                created_by = user_instance.user_name[:4]
                invite_code = create_seller_invite_code(created_by)
                request.data["invite_code"] = invite_code
                serializer = InviteSerializer(data=request.data)
                if serializer.is_valid():
                    serializer.save()
                    user_instance.seller_invite_balance = (
                        user_instance.seller_invite_balance - 1
                    )
                    user_instance.save()
                    return Response(
                        {"message": "success", "data": serializer.data},
                        status=status.HTTP_200_OK,
                    )
                return Response(
                    {"message": "error", "data": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        elif (
            request.data["invite_type"] == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER
            or request.data["invite_type"] == InviteUser.Invite_Type_Choices.CORPORATE_SELLER
        ):
            logger.info(
                "Inside elif condition: where invite_type=corporate_seller or corporate_member"
            )
            invite_code = create_corporate_invite_code()
            request.data["invite_code"] = invite_code
            request.data["remaining_invites"] = request.data["number_of_invites"]
            serializer = GroupInviteSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        logger.info("exited post method of CreateInviteCode")


class CreateInviteCodeDetails(APIView):
    def get(self, request, invite_code):
        invite_instance = InviteUser.objects.get(
            invite_code=invite_code, is_deleted=False
        )
        if (
            invite_instance.invite_type == InviteUser.Invite_Type_Choices.MEMBER
            or invite_instance.invite_type == InviteUser.Invite_Type_Choices.SELLER
        ):
            serializer = InviteSerializer(invite_instance)
        elif (
            invite_instance.invite_type == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER
            or invite_instance.invite_type == InviteUser.Invite_Type_Choices.CORPORATE_SELLER
        ):
            serializer = GroupInviteSerializer(invite_instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def put(self, request, invite_code):
        invite_instance = InviteUser.objects.get(
            invite_code=invite_code, is_deleted=False
        )
        if (
            invite_instance.invite_type == InviteUser.Invite_Type_Choices.MEMBER
            or invite_instance.invite_type == InviteUser.Invite_Type_Choices.SELLER
        ):
            serializer = InviteSerializer(invite_instance, data=request.data)
        elif (
            invite_instance.invite_type == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER
            or invite_instance.invite_type == InviteUser.Invite_Type_Choices.CORPORATE_SELLER
        ):
            serializer = GroupInviteSerializer(invite_instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )

    def delete(self, request, invite_code):
        invite_instance = InviteUser.objects.get(
            invite_code=invite_code, is_deleted=False
        )
        if (
            invite_instance.phone_number is not None
            and invite_instance.invite_status == InviteUser.Invite_Status.CREATED
        ):
            user = User.objects.get(userid=invite_instance.invited_user.userid)
            if invite_instance.invite_type == InviteUser.Invite_Type_Choices.MEMBER:
                user.member_invite_balance += 1
            elif invite_instance.invite_type == InviteUser.Invite_Type_Choices.SELLER:
                user.seller_invite_balance += 1
            user.save()
        invite_instance.is_deleted = True
        invite_instance.save(update_fields=["is_deleted"])
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class ApplyInviteCode(generics.CreateAPIView):
    def post(self, request, *args, **kwargs):

        # get the request data
        user_id = request.data["user_id"]
        input_invite_code = request.data["invite_code"]

        # get the user instance
        user = User.objects.get(userid=user_id)

        # get required data from the above user instance
        phonenumber = user.phonenumber
        current_invite = user.invite_type
        if user.icon:
            image = user.icon
        else:
            image = None

        if InviteUser.objects.filter(invite_code=input_invite_code).exists():
            invited_user = InviteUser.objects.get(invite_code=input_invite_code)
            user_reference = invited_user.invited_user.user_reference
            notification_gen = GenerateNotifications()

        # Check if this personal invite code exists or not
        if InviteUser.objects.filter(
            invite_code=input_invite_code, phone_number=phonenumber, is_deleted=False
        ).exists():

            # Take the invite user instance by using the invite code.
            input_invite_type = InviteUser.objects.values_list(
                "invite_type", flat=True
            ).get(invite_code=input_invite_code, is_deleted=False)
            if current_invite == InviteUser.Invite_Type_Choices.NON_MEMBER:
                # try:
                invite_code, invite_type = find_applicable_invite_type(
                    input_invite_code, phonenumber
                )
                save_invite_credentials(
                    invite_code, invite_type, current_invite, phonenumber, user
                )
                # generate notification that a user joined with invite code.

                notification_gen.create_notifications(
                    notified_user=user_reference,
                    notification_type=Notifications.Notifications_Type.ONBOARDING,
                    notification_about=user.user_reference,
                    image=image,
                )
                return Response(
                    {"message": "success", "invite_type": invite_type},
                    status=status.HTTP_200_OK,
                )
                # except:
                #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
            elif current_invite == InviteUser.Invite_Type_Choices.MEMBER or current_invite == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER:
                if (
                    input_invite_type == InviteUser.Invite_Type_Choices.CORPORATE_SELLER
                    or input_invite_type == InviteUser.Invite_Type_Choices.SELLER
                ):
                    invite_code, invite_type = find_applicable_invite_type(
                        input_invite_code, phonenumber
                    )
                    save_invite_credentials(
                        invite_code, invite_type, current_invite, phonenumber, user
                    )

                    notification_gen.create_notifications(
                        notified_user=user_reference,
                        notification_type=Notifications.Notifications_Type.ONBOARDING,
                        notification_about=user.user_reference,
                        image=image,
                    )
                    return Response(
                        {"message": "success", "invite_type": invite_type},
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "message": "error",
                            "data": "invite code can't be applied, since you are a "
                            + current_invite,
                        },
                        status=status.HTTP_200_OK,
                    )
            else:
                return Response(
                    {
                        "message": "error",
                        "data": "invite code can't be applied, since you are a "
                        + current_invite,
                    },
                    status=status.HTTP_200_OK,
                )
        elif InviteUser.objects.filter(
            invite_code=input_invite_code,
            phone_number__isnull=True,
            invite_status=InviteUser.Invite_Status.CREATED,
            is_deleted=False,
        ).exists():
            input_invite_type = InviteUser.objects.values_list(
                "invite_type", flat=True
            ).get(invite_code=input_invite_code, is_deleted=False)
            if current_invite == InviteUser.Invite_Type_Choices.NON_MEMBER:
                # try:
                invite_code, invite_type = find_applicable_invite_type(
                    input_invite_code, phonenumber
                )
                save_invite_credentials(
                    invite_code, invite_type, current_invite, phonenumber, user
                )
                notification_gen.create_notifications(
                    notified_user=user_reference,
                    notification_type=Notifications.Notifications_Type.ONBOARDING,
                    notification_about=user.user_reference,
                    image=image
                )
                return Response(
                    {"message": "success", "invite_type": invite_type},
                    status=status.HTTP_200_OK,
                )
                # except:
                #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
            elif current_invite == InviteUser.Invite_Type_Choices.MEMBER or current_invite == InviteUser.Invite_Type_Choices.CORPORATE_MEMBER:
                if (
                    input_invite_type == InviteUser.Invite_Type_Choices.CORPORATE_SELLER
                    or input_invite_type == InviteUser.Invite_Type_Choices.SELLER
                ):
                    invite_code, invite_type = find_applicable_invite_type(
                        input_invite_code, phonenumber
                    )
                    save_invite_credentials(
                        invite_code, invite_type, current_invite, phonenumber, user
                    )
                    notification_gen.create_notifications(
                        notified_user=user_reference,
                        notification_type=Notifications.Notifications_Type.ONBOARDING,
                        notification_about=user.user_reference,
                        image=image,
                    )
                    return Response(
                        {"message": "success", "invite_type": invite_type},
                        status=status.HTTP_200_OK,
                    )
                else:
                    return Response(
                        {
                            "message": "error",
                            "data": "invite code can't be applied, since you are a "
                            + current_invite,
                        },
                        status=status.HTTP_200_OK,
                    )
            else:
                return Response(
                    {
                        "message": "error",
                        "data": "invite code can't be applied, since you are a "
                        + current_invite,
                    },
                    status=status.HTTP_200_OK,
                )
        else:
            return Response(
                {"message": "error", "detail": "Invalid invite code"},
                status=status.HTTP_200_OK,
            )


class UpdateInviteBalance(APIView):
    def post(self, request):
        logger.info("entered post method of UpdateInviteBalance")
        user_id = request.data["user_id"]
        invite_balance_type = request.data["invite_balance_type"]
        update_method = request.data["update_method"]
        invite_limits = request.data["invite_limits"]
        user_obj = User.objects.get(userid=user_id, deleted=False)
        if update_method == "INCREASE":
            logger.info("inside if condition: increased")
            if invite_balance_type == "member_invite_balance":
                current_invite_limit = user_obj.member_invite_balance
                new_invite_limit = current_invite_limit + invite_limits
                User.objects.filter(userid=user_id, deleted=False).update(
                    member_invite_balance=new_invite_limit
                )
            elif invite_balance_type == "seller_invite_balance":
                current_invite_limit = user_obj.seller_invite_balance
                new_invite_limit = current_invite_limit + invite_limits
                User.objects.filter(userid=user_id, deleted=False).update(
                    seller_invite_balance=new_invite_limit
                )

        elif update_method == "DECREASE":
            logger.info("inside elif condition: decrease")
            if invite_balance_type == "member_invite_balance":
                current_invite_limit = user_obj.member_invite_balance
                new_invite_limit = current_invite_limit - invite_limits
                if new_invite_limit < 0:
                    return Response(
                        {
                            "message": "error",
                            "current_invite_limit": current_invite_limit,
                            "description": "choose a small number",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                User.objects.filter(userid=user_id, deleted=False).update(
                    member_invite_balance=new_invite_limit
                )
            elif invite_balance_type == "seller_invite_balance":
                current_invite_limit = user_obj.seller_invite_balance
                new_invite_limit = current_invite_limit - invite_limits
                if new_invite_limit < 0:
                    return Response(
                        {
                            "message": "error",
                            "current_invite_limit": current_invite_limit,
                            "description": "choose a small number",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                User.objects.filter(userid=user_id, deleted=False).update(
                    seller_invite_balance=new_invite_limit
                )
        logger.info("exited post method of UpdateInviteBalance")
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetAllInvitesCreatedByOneUser(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        user_id = self.kwargs["user_id"]
        logger.info("entered list method of GetAllInvitesCreatedByOneUser")
        user_invite_instance = InviteUser.objects.filter(
            Q(invited_user=user_id)
            & (Q(invite_type=InviteUser.Invite_Type_Choices.MEMBER) | Q(invite_type=InviteUser.Invite_Type_Choices.SELLER))
            & Q(is_deleted=False)
        )
        serializer = GetAllInvitesCreatedByOneUserSerializer(
            user_invite_instance, context={"user_id": user_id}
        )
        logger.info("exit list method")
        return Response(
            {"message": "success", "invited_user": user_id, "data": serializer.data},
            status=status.HTTP_200_OK,
        )


class ListOfUserContacts(APIView):
    def post(self, request):
        logger.info("entered post method of ListOfUserContacts")
        list_of_phone_number = request.data["phone_number_list"]
        user_id = request.data["user_id"]
        user_instance = User.objects.filter(
            phonenumber__in=list_of_phone_number, deleted=False
        )
        phone_number_in = [instance.phonenumber for instance in user_instance]
        phone_number_not_in = set(list_of_phone_number) - set(phone_number_in)
        serializer = ListOfUserContactsSerializer(
            user_instance, many=True, context={"user_id": user_id}
        )
        serialized_data = serializer.data

        for number in list(phone_number_not_in):
            new_dict = {}
            new_dict["phonenumber"] = number
            new_dict["user_reference"] = None
            new_dict["userid"] = 0
            new_dict["user_name"] = None
            new_dict["membership_type"] = None
            new_dict["is_onboarded"] = False
            if InviteUser.objects.filter(
                phone_number=number, invited_user=user_id, is_deleted=False
            ).exists():
                invite_user_instance = (
                    InviteUser.objects.filter(phone_number=number, invited_user=user_id)
                    .order_by("-created_date")
                    .first()
                )
                new_dict["invite_type_by_this_user"] = invite_user_instance.invite_type
                new_dict["invite_code_by_this_user"] = invite_user_instance.invite_code
            else:
                new_dict["invite_type_by_this_user"] = None
                new_dict["invite_code_by_this_user"] = None
            new_dict["onboarded_by_this_user"] = False
            serialized_data.append(new_dict)
        logger.info("exited post method of ListOfUserContacts")
        return Response({"message": "success", "data": serialized_data})


class GetUserRewardData(APIView):
    def get(self,request):
        user_reference = request.query_params.get("reference")
        user_instance = User.objects.get(user_reference=user_reference, deleted=False)
        invite_code = user_instance.invite_code
        user_points_instance = UserRewards.objects.filter(user_reference=user_reference).first()
        if user_points_instance:
            user_points = user_points_instance.infinity_points
        else:
            user_points = 0

        user_total_earnings = get_user_total_earnings(user_instance=user_instance)
        user_total_savings = get_user_total_savings(user_instance=user_instance)

        return Response(
            {"reward_data": {
                "current_balance": user_points,
                "invite_code": invite_code,
                "total_earnings": user_total_earnings,
                "total_savings": user_total_savings
            }}, status=200
        )
    

class GetInviteCode(APIView):

    def get(self, request):
        reference = request.query_params.get("reference")
        
        if reference.startswith('S'):
            # Handle store reference
            store_instance = Store.objects.get(store_reference=reference, deleted=False)
            invite_code = store_instance.invite_code
 
        elif reference.startswith('U'):
            # Handle user reference
            user_instance = User.objects.get(user_reference=reference, deleted=False)
            invite_code = user_instance.invite_code

        else:
            return Response({"error": "Invalid reference"}, status=400)

        return Response(
            {"invite_code": invite_code}, status=200
        )
        

class GetUserRewardsHistory(APIView):
    def get(self,request):
        user_reference = request.query_params.get("user_reference")
        limit = int(request.query_params.get("limit", 10))  # Default limit to 10 if not provided
        offset = int(request.query_params.get("offset", 0))  # Default offset to 0 if not provided

        user_instance = User.objects.get(user_reference=user_reference, deleted=False)
        invite_code = user_instance.invite_code
        response_data = []
        if invite_code:
            """
            This query fetches the combined history of rewards for a given user reference.
            It includes rewards where the user is either the sender, receiver, or the event reference.
            It excludes rewards of type ONBOARDING_REFERRAL where the user is not the receiver,
            and rewards of type INVITE_CODE_REWARD where the user is the event reference.
            The results are ordered by the most recent first and paginated based on the offset and limit.
            """
            combined_query = RewardsHistory.objects.filter(
                Q(sender_reference=user_reference) | Q(receiver_reference=user_reference) | Q(event_reference=user_reference)
            ).exclude(
                Q(reward_type=RewardsHistory.RewardTypeChoices.ONBOARDING_REFERRAL) & ~Q(receiver_reference=user_reference)
            ).exclude(
                Q(reward_type=RewardsHistory.RewardTypeChoices.INVITE_CODE_REWARD) & Q(event_reference=user_reference)
            ).order_by('-created_date')[offset:offset + limit]

            response_data = RewardsHistorySerializer(combined_query, many=True).data
        return Response({"data": response_data}, status=200)


class GetInvitedCounts(APIView):
    def get(self, request):
        invite_code = request.query_params.get("invite_code")
        # Fetch the invited users count from Django ORM
        invited_users_count = User.objects.filter(invited_by_code=invite_code, deleted=False).count()

        # Fetch the store count created by the invited users
        invited_users = User.objects.filter(invited_by_code=invite_code, deleted=False)
        invited_stores_count = Store.objects.filter(created_by__in=invited_users, deleted=False).count()

        return Response({"data": {
            "invited_users_count": invited_users_count,
            "invited_stores_count": invited_stores_count
        }}, status=200)


class GetInviteRewardsInfo(APIView):
    def get(self, request):
        data = {
            "user_view": {
                "detail_list": [
                    {
                        "title": "For every friend you bring who stays for a year, we plant a tree in your name. Grow together a Greener Bharat with us!"
                    },
                    {
                        "title": "Plus, both you and your friend will instantly earn ₹51 in Infinity Points, redeemable soon for discounts on product purchases!"
                    },
                    {
                        "title": "Invite a Store and earn 20% of revenue Swadesic earns from the store orders for lifetime.",
                        "sub_title": "Conditions: Either you have a store with 10 qualifying orders or Swadesic premium subscription to receive the incentive before it resets monthly."
                    }
                ],
                "additional_info": ""
            },
            "store_view": {
                "detail_list": [
                    {
                        "title": "Swadesic adds ₹250 in Flash points on the first day of every month to support your small business in the Swadeshi Movement."
                    },
                    {
                        "title": "Flash points automatically save you ₹49 order fee"
                    }
                ],
                "additional_info": ""
            },
            "common_view": {
                "detail_list": [
                    {
                        "title": "For every friend you bring who stays for a year, we plant a tree in your name. Grow together a Greener Bharat with us!"
                    },
                    {
                        "title": "Share your invite code or link with your friends, followers, or customers. When one of your referral creates a store, it is linked to you."
                    },
                    {
                        "title": "You'll earn 20% of order revenue Swadesic makes from that Store for life**",
                        "sub_title": "Receive last month’s affiliate earnings by meeting either of these conditions:\nSwadesic Premium: Active subscription during the payout period (10th-16th).\nStore Orders: 5 qualifying orders (over ₹500) by month-end."
                    }
                ],
                "additional_info": "",
                "swadesic_message": "For Buyers:\n\n🔍 Discover trending Swadeshi products and brands and check their authenticity with Swadeshi labels.\n💬 Engage directly with sellers and fellow buyers, sharing and interacting with content.\n🛒 Enjoy a seamless shopping experience with in-app order tracking and communication.\n💼 Explore opportunities to become a seller yourself.\n\nFor Stores:\n\n🌐Leverage a unified platform for branding and e-commerce with easy inventory management.\n🤝 Build a loyal community and expand your supporter base.\n🚚 Streamlined order processing and timely payouts with valuable insights for growth."
                }
            }
        return Response(data, status=status.HTTP_200_OK)


class GetUserAffiliateBalanceDetails(APIView):
    def get(self, request):
        try:
            user_reference = request.query_params.get("user_reference")
            if not user_reference:
                return Response({"error": "User reference is required"}, status=400)
            user_rewards_instance = UserRewards.objects.filter(user_reference=user_reference).first()
            user_affiliate_balance = user_rewards_instance.user_affiliate_balance
            user_affiliate_reward_history = RewardsHistory.objects.filter(receiver_reference=user_reference, reward_type=RewardsHistory.RewardTypeChoices.RUPEE).order_by('-created_date')
            is_auto_pay_enabled = user_rewards_instance.is_auto_pay_enabled
            user_affiliate_reward_history_serializer = RewardsHistorySerializer(user_affiliate_reward_history, many=True)
            return Response({"user_affiliate_balance": user_affiliate_balance,"is_auto_pay_enabled": is_auto_pay_enabled, "user_affiliate_reward_history": user_affiliate_reward_history_serializer.data}, status=200)
        except Exception as e:
            logger.info({"error": str(e)})
            return Response({"error": str(e)}, status=500)
