from pdb import post_mortem
import graphene
from neomodel import db
import datetime
from datetime import datetime, timedelta
import logging
from .queries import *

from .mutations import *
from .types import *
from .models import *
from .helpers import *
from content.models import Comments, Posts
from products.models import Product
from users.user_api.models import User
from stores.store_api.models import Store
from stores.store_settings_api.models import Address
from orders.order_api.models import Order, SubOrder

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class ContentUnion(graphene.types.union.Union):
    @staticmethod
    def resolve_type(instance, info):
        # Return the appropriate type based on the instance
        if isinstance(instance, Posts):
            return Neo4jPostType
        elif isinstance(instance, Product):
            return Neo4jProductType
        elif isinstance(instance, Comments):
            return Neo4jCommentType
        return None  # Return None if the type cannot be determined

    class Meta:
        types = (Neo4jPostType, Neo4jProductType, Neo4jCommentType)


class Mutation(graphene.ObjectType):
    delete_post = DeletePostMutation.Field()
    like_content = LikeContentMutation.Field()
    delete_post_image = DeletePostImageMutation.Field()
    follow_or_support = FollowOrSupportMutation.Field()
    get_follow_status = GetFollowStatusMutation.Field()
    add_visit = AddVisitMutation.Field()
    save_content = SaveContentMutation.Field()
    unsync_contacts = UnsyncContactsMutation.Field()
    delete_comment = DeleteCommentMutation.Field()
    delete_comment_image = DeleteCommentImageMutation.Field()
    add_repost = AddRepostMutation.Field()


class Query(graphene.ObjectType):

    all_posts = graphene.List(
        of_type=ContentUnion,
        limit=graphene.Int(),
        offset=graphene.Int(),
    )
    posts = graphene.List(Neo4jPostType, post_references=graphene.List(graphene.String, required=True))
    user_and_store_posts = graphene.List(
        Neo4jPostType,
        creator_reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int(),
    )
    feed = graphene.List(
        of_type=ContentUnion,
        reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int(),
    )

    feed_posts = graphene.List(
        Neo4jPostType,
        reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    recommended_users = graphene.List(
        Neo4jEntityType,
        reference=graphene.String(),
        offset=graphene.Int(default_value=0),
        limit=graphene.Int(default_value=10)
    )

    recommended_stores = graphene.List(
        Neo4jEntityType,
        reference=graphene.String(),
        offset=graphene.Int(default_value=0),
        limit=graphene.Int(default_value=10)
    )

    contact_data = graphene.List(
        Neo4jContactType,
        # Neo4jEntityType,
        entity_reference=graphene.String(),
        phonenumbers=graphene.List(graphene.String),
        sync=graphene.String()
    )

    follow_all = graphene.List(
        Neo4jContactType,
        asking_reference=graphene.String(),
        entity_type=graphene.String(),
        action=graphene.String(),
        time_window_in_minutes=graphene.Int()
    )

    get_follower_following = graphene.List(
        Neo4jContactType,
        creator_reference=graphene.String(),
        visitor_reference=graphene.String(),
        required_list=graphene.String(),
        entity_type=graphene.String(),
        offset=graphene.Int(),
        limit=graphene.Int()
        # need to add search_query parameter to return results based on the search query key word
    )

    get_children_comments = graphene.List(
        Neo4jCommentType,
        parent_reference=graphene.String(),
        limit=graphene.Int(),
        offset=graphene.Int()
        )

    get_comment = graphene.Field(
        Neo4jCommentType,
        comment_reference=graphene.String(),
        )

    get_parent_comment = graphene.Field(
        Neo4jCommentType,
        comment_reference=graphene.String()
    )

    get_suborder_reviews = graphene.List(
        Neo4jCommentType,
        suborder_numbers=graphene.List(graphene.String),
        visitor_reference=graphene.String(),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    get_parent_content = graphene.Field(
        GetParentContent,
        comment_reference=graphene.String()
    )

    get_reposts = graphene.List(
        of_type=ContentUnion,
        reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int(),
    )

    get_liked_entities = graphene.List(
        Neo4jEntityType,
        content_reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    get_recommended_products = graphene.List(
        Neo4jProductType,
        # reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    get_referral_entities = graphene.List(
        Neo4jEntityType,
        entity_type=graphene.String(required=True),
        invite_code=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    get_saved_content = graphene.List(
        of_type=ContentUnion,
        content_type=graphene.String(required=True),
        reference=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int(),
    )

    get_entity_preview = graphene.Field(
        EntityPreviewType,
        reference=graphene.String(required=True)
    )

    get_post_preview = graphene.Field(
        Neo4jPostType,
        reference=graphene.String(required=True)
    )

    get_product_preview = graphene.Field(
        ProductPreviewType,
        reference=graphene.String(required=True)
    )

    get_order_preview = graphene.Field(
        OrderPreviewType,
        reference=graphene.String(required=True)
    )


######################unused queries######################
    get_stores_by_category = graphene.List(
        Neo4jStoreType,
        category=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    get_state_stores = graphene.List(
        Neo4jStoreType,
        state=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    get_products_by_state = graphene.List(
        Neo4jProductType,
        state=graphene.String(required=True),
        limit=graphene.Int(),
        offset=graphene.Int()
    )

    store_products = graphene.List(
        Neo4jProductType,
        store_reference=graphene.String(required=True),
        visitor_reference=graphene.String(),
        limit=graphene.Int(),
        offset=graphene.Int(),
    )
############################################################

#todo add search feature to unr users

    def resolve_get_entity_preview(self, info, reference):
        return Neo4jEntity.nodes.get(reference=reference)

    def resolve_get_post_preview(self, info, reference):
        return Posts.objects.get(post_reference=reference)

    def resolve_get_product_preview(self, info, reference):
        return Product.objects.get(product_reference=reference)

    def resolve_get_order_preview(self, info, reference):
        # the reference can be a order or suborder reference
        if "-" in reference:
            suborder_instance = SubOrder.objects.get(suborder_number=reference)
            order_instance = suborder_instance.orderid
            # data to send to OrderPreviewType
            order_preview = OrderPreviewType(
                order_number=suborder_instance.order_number,
                suborder_number=suborder_instance.suborder_number,
                user_reference=order_instance.user_reference.user_reference,
                user_icon=order_instance.user_reference.icon,
                user_name=order_instance.user_reference.user_name,
                store_reference=order_instance.store_reference.store_reference,
                store_icon=order_instance.store_reference.icon,
                store_name=order_instance.store_reference.storehandle,
                product_preview_image=suborder_instance.product_image,
            )
            return order_preview
        else:
            order_instance = Order.objects.get(order_number=reference)
            # data to send to OrderPreviewType
            order_preview = OrderPreviewType(
                order_number=order_instance.order_number,
                suborder_number="",
                user_reference=order_instance.user_reference.user_reference,
                user_icon=order_instance.user_reference.icon,
                user_name=order_instance.user_reference.user_name,
                store_reference=order_instance.store_reference.store_reference,
                store_icon=order_instance.store_reference.icon,
                store_name=order_instance.store_reference.storehandle,
                product_preview_image=order_instance.orderitems.first().product_image,
            )
            return order_preview

    def resolve_get_saved_content(self, info, content_type, reference, limit, offset):
        saved_content = dbqueries.get_saved_content(content_type=content_type, entity_reference=reference, limit=limit, offset=offset)
        if content_type == "POST":
            saved_content_instances = Posts.objects.filter(post_reference__in=saved_content)
            return saved_content_instances
        elif content_type == "PRODUCT":
            saved_content_instances = Product.objects.filter(product_reference__in=saved_content)
            return saved_content_instances

    def resolve_get_state_stores(self, info, state, limit, offset):
        stores = Neo4jStore.nodes.filter(state=state, is_active=True, is_deleted=False)[offset:(offset+limit)]
        return list(stores)

    def resolve_get_products_by_state(self, info, state, limit, offset):
        store_ref = list(Address.objects.filter(state=state).values_list('store_reference', flat=True))
        store_active = Store.objects.filter(store_reference__in=store_ref, is_active=True, deleted=False).values_list('store_reference', flat=True)
        stores_product = Product.objects.filter(store_reference__in=store_active, deleted=False)[offset:(offset+limit)]
        return list(stores_product)

    def resolve_get_stores_by_category(self, info, category, limit, offset):
        stores = Neo4jStore.nodes.filter(category_name=category, is_active=True, is_deleted=False)[offset:(offset+limit)]
        return list(stores)

    def resolve_store_products(self, info, store_reference, visitor_reference=None, limit=None, offset=None):
        # Set visitor reference in context for resolvers that need it
        info.context.visitor_reference = visitor_reference

        # Query products for the store
        products = Product.objects.filter(
            store_reference__store_reference=store_reference,
            deleted=False
        ).select_related('store_reference').prefetch_related(
            'prod_images',
            'product_comments'
        ).order_by('-created_date')

        # Apply pagination
        if offset is not None and offset >= 0:
            products = products[offset:]

        if limit is not None and limit >= 0:
            products = products[:limit]

        return products

    def resolve_get_suborder_reviews(self, info, suborder_numbers, visitor_reference, limit, offset):
        try:
            query = Comments.objects.filter(event_reference__in=suborder_numbers, is_deleted=False)

            if visitor_reference:
                query = query.filter(commenter_reference=visitor_reference)

            return query[offset:limit+offset]
        except Exception as e:
            raise Exception(f"{e}")

    def resolve_get_referral_entities(self, info, entity_type, invite_code, limit, offset):
        if entity_type not in ["USER", "STORE"]:
            raise Exception("Invalid entity_type. Must be either 'USER' or 'STORE'.")

        if not invite_code:
            raise Exception("Please provide an invite code")

        if invite_code.startswith('SWS'):
            # Handle store invite code
            store = Store.objects.filter(invite_code=invite_code, deleted=False).first()
            if not store:
                raise Exception("Invalid store invite code")

            invited_users = User.objects.filter(invited_by_code=invite_code, deleted=False)
            invited_user_references = list(invited_users.values_list("user_reference", flat=True))

            if entity_type == "USER":
                invited_user_nodes = Neo4jUser.nodes.filter(reference__in=invited_user_references, is_deleted=False).order_by("-created_date")
                return invited_user_nodes[offset:offset + limit]

            elif entity_type == "STORE":
                invited_store_references = list(Store.objects.filter(
                    created_by__in=invited_users,
                    deleted=False
                ).values_list("store_reference", flat=True))
                invited_store_nodes = Neo4jStore.nodes.filter(reference__in=invited_store_references, is_deleted=False).order_by("-created_date")
                return invited_store_nodes[offset:offset + limit]

        else:
            # Handle user invite code
            user = User.objects.filter(invite_code=invite_code, deleted=False).first()
            if not user:
                raise Exception("Invalid user invite code")

            invited_users = User.objects.filter(invited_by_code=invite_code, deleted=False)
            invited_user_references = list(invited_users.values_list("user_reference", flat=True))

            if entity_type == "USER":
                invited_user_nodes = Neo4jUser.nodes.filter(reference__in=invited_user_references, is_deleted=False).order_by("-created_date")
                return invited_user_nodes[offset:offset + limit]

            elif entity_type == "STORE":
                invited_store_references = list(Store.objects.filter(
                    created_by__in=invited_users,
                    deleted=False
                ).values_list("store_reference", flat=True))
                invited_store_nodes = Neo4jStore.nodes.filter(reference__in=invited_store_references, is_deleted=False).order_by("-created_date")
                return invited_store_nodes[offset:offset + limit]

        ###############

    def resolve_get_recommended_products(self, info, limit, offset):
        active_products = Product.objects.filter(
            store_reference__is_active=True,
            store_reference__is_test_store=False,
            store_reference__deleted=False,
            deleted=False
        ).order_by('-modified_date')[offset:offset+limit]
        return active_products

    def resolve_get_liked_entities(self, info, content_reference, limit, offset):
        liked_entity_references = dbqueries.get_liked_entities(content_reference=content_reference, limit=limit, offset=offset)
        liked_entities = Neo4jEntity.nodes.filter(reference__in=liked_entity_references, is_deleted=False)
        return liked_entities

    def resolve_get_reposts(self, info, reference, limit, offset):
        repost_content_references = dbqueries.get_reposts_of_entity(reference=reference, limit=limit, offset=offset)

        # Fetch items individually from each model
        required_posts = Posts.objects.filter(post_reference__in=repost_content_references, is_deleted=False)
        required_comments = Comments.objects.filter(comment_reference__in=repost_content_references, is_deleted=False)
        required_products = Product.objects.filter(product_reference__in=repost_content_references, deleted=False)

        # Create dictionaries with reference as key and corresponding items as value
        posts_dict = {post.post_reference: post for post in required_posts}
        comments_dict = {comment.comment_reference: comment for comment in required_comments}
        products_dict = {product.product_reference: product for product in required_products}

        # Combine all dictionaries into one
        items_dict = {**posts_dict, **comments_dict, **products_dict}

        # Retrieve items in the same order as repost_content_references
        sorted_items = [items_dict[ref] for ref in repost_content_references if ref in items_dict]

        return sorted_items

    def resolve_get_parent_content(self, info, comment_reference):
        try:
            comment = Comments.objects.get(comment_reference=comment_reference, is_deleted=False)
            parent_reference = comment.parent_comment_id if comment.parent_comment_id else comment.main_parent_id

            # if parent_reference.startswith('P'):
            #     parent_product = Product.objects.get(product_reference=parent_reference)
            #     return GetParentComment(parent_product=parent_product)

            if parent_reference.startswith('C'):
                parent_comment = Comments.objects.get(comment_reference=parent_reference, is_deleted=False)
                return GetParentContent(parent_comment=parent_comment)
            elif parent_reference.startswith('PO'):
                parent_post = Neo4jPost.nodes.get(reference=parent_reference, is_deleted=False)
                return GetParentContent(parent_post=parent_post)
            elif parent_reference.startswith('P'):
                parent_product = Product.objects.get(product_reference=parent_reference, deleted=False)
                return GetParentContent(parent_product=parent_product)
            else:
                raise Exception("The given comment has no valid parent")

        except Comments.DoesNotExist:
            raise Exception("Comment with the given reference does not exist.")

    def resolve_get_parent_comment(self,info, comment_reference):
        try:
            comment = Comments.objects.get(comment_reference=comment_reference, is_deleted=False)
            parent_reference = comment.parent_comment_id if comment.parent_comment_id else comment.main_parent_id
            if parent_reference.startswith('C'):
                parent = Comments.objects.get(comment_reference=parent_reference, is_deleted=False)
                return parent
            else:
                raise Exception("The given comment has no parent comment")

        except Comments.DoesNotExist:
            raise Exception("Post with the given reference does not exist.")

    def resolve_get_comment(self, info, comment_reference):
        try:
            comment = Comments.objects.get(comment_reference=comment_reference, is_deleted=False)
            return comment
        except Comments.DoesNotExist:
            raise Exception("Post with the given reference does not exist.")

    def resolve_get_children_comments(self, info, parent_reference, limit=None, offset=None):
        parent_type = 'COMMENT' if parent_reference.startswith('C') else (
            'POST' if parent_reference.startswith('PO') else 'PRODUCT')
        if parent_type != 'COMMENT':
            comments = Comments.objects.filter(main_parent_id=parent_reference, level=1, is_deleted=False)

        if parent_type == 'COMMENT':
            parent_comment = Comments.objects.get(comment_reference=parent_reference, is_deleted=False)
            parent_comment_level = parent_comment.level

            comments = Comments.objects.filter(parent_comment_id=parent_reference, level=parent_comment_level+1, is_deleted=False)
        return comments[offset:(offset+limit)]

    def resolve_get_follower_following(self, info, creator_reference,visitor_reference, required_list, entity_type, limit=None, offset=None):
        entities = dbqueries.get_follower_following_entities(creator_reference,visitor_reference, required_list, entity_type, limit, offset)
        return entities

    def resolve_follow_all(self,info, asking_reference, entity_type, action, time_window_in_minutes):

        followed_or_unfollowed_entities = dbqueries.follow_or_unfollow_all(
            asking_reference=asking_reference,
            entity_type=entity_type,
            action=action,
            time_window_in_minutes=time_window_in_minutes
        )
        return followed_or_unfollowed_entities

    def resolve_contact_data(self,info, entity_reference, phonenumbers, sync):

        if sync=='TRUE':
            # get references for all entites connected to given phonenumbers
            # references_connected_to_phonenumbers, connected_phonenumbers = [],[]
            references_connected_to_phonenumbers, connected_phonenumbers = dbqueries.get_references_of_connected_entities(phonenumbers)

            ## create unr nodes and get references for phone numbers that are not connected to any entity
            phonenumbers_of_nodes_to_be_created = set(phonenumbers).difference(connected_phonenumbers)


            references_connected_to_new_nodes = dbqueries.create_unregistered_nodes(list(phonenumbers_of_nodes_to_be_created))
            all_references_in_DB = references_connected_to_new_nodes.union(references_connected_to_phonenumbers)

            # get references for entities that have has_contact relationship with asking entity
            references_with_existing_relationship = dbqueries.get_references_of_has_contacts(asking_reference=entity_reference)

            # Updating relationships
            ## ignore references that are common in all_references_in_DB and references_with_existing_relationship because they are already related and aligned with contacs
            all_references_in_DB.intersection(references_with_existing_relationship)

            ## create new relationships to references that are present in all_references_in_DB but not in references_with_existing_relationship
            references_of_relationship_to_be_created = all_references_in_DB.difference(references_with_existing_relationship)

            ## remove existing relationships present in references_with_existing_relationship that are not in all_references_in_DB
            references_of_relationship_to_be_removed = references_with_existing_relationship.difference(all_references_in_DB)

            # update relationships
            dbqueries.create_or_remove_has_contacts(asking_reference=entity_reference,
                                                    references_to_create=list(references_of_relationship_to_be_created),
                                                    references_to_remove=list(references_of_relationship_to_be_removed)
                                                    )

        return dbqueries.get_has_contact_entities(entity_reference)

    def resolve_recommended_stores(self, info, reference, offset, limit):
        def has_icon(entity):
            return bool(entity.icon)
        try:
            user_or_store = Neo4jUser.nodes.get(reference=reference, is_deleted=False)
        except Neo4jUser.DoesNotExist:
            try:
                user_or_store = Neo4jStore.nodes.get(reference=reference, is_deleted=False)
            except Neo4jStore.DoesNotExist:
                raise Exception("User or Store with the given reference does not exist.")

        user_or_store_following_list = dbqueries.get_references_followed_by_entity(entity_reference=reference)

        stores_with_highest_supporters = Neo4jStore.nodes.filter(
            is_deleted=False,
            is_active=True
        ).exclude(
            handle__istartswith='test_'
        ).exclude(
            reference=user_or_store.reference
        # ).exclude(
        #     reference__in=user_or_store_following_list
        ).order_by('-follower_count')


        # Get users from the same city and pincode as the visiting user
        stores_same_pincode = Neo4jStore.nodes.filter(
            pincode=user_or_store.pincode,
            is_deleted=False,
            is_active=True
        ).exclude(
            handle__istartswith='test_'
        # ).exclude(
        #     reference__in=user_or_store_following_list
        ).exclude(
            reference=user_or_store.reference
        )

        stores_same_city = Neo4jStore.nodes.filter(
            city=user_or_store.city,
            is_deleted=False,
            is_active=True
        ).exclude(
            handle__istartswith='test_'
        # ).exclude(
        #     reference__in=user_or_store_following_list
        ).exclude(
            reference=user_or_store.reference
        )

        # Get users with the highest followers
        # stores_with_highest_supporters = Neo4jStore.nodes.filter(is_deleted=False).order_by('-follower_count')

        # # Get users from the same city and pincode as the visiting user
        # stores_same_pincode = (
        #     Neo4jStore.nodes.filter(pincode=user_or_store.pincode, is_deleted=False)
        # )

        # stores_same_city = (
        #     Neo4jStore.nodes.filter(city=user_or_store.city, is_deleted=False)
        # )

        # Get users who are being followed by users which the visitor follows
        # users_followed_by_followed_users = Neo4jEntity.nodes.filter(
        #     followed_by__follows__exact=user_or_store,
        #     is_deleted=False
        # )



        # Combine all recommended users and shuffle the list
        all_recommended_stores = []

        if len(stores_with_highest_supporters) != 0 :
            all_recommended_stores += list(stores_with_highest_supporters)

        if len(stores_same_pincode) != 0 :
            all_recommended_stores += list(stores_same_pincode)

        if len(stores_same_city) != 0 :
            all_recommended_stores += list(stores_same_pincode)

        unique_recommended_stores = list(set(all_recommended_stores))
        #
        # if users_followed_by_followed_users is not None:
        #     all_recommended_users += list(users_followed_by_followed_users)

        sorted_stores = sorted(unique_recommended_stores,
                              key=lambda x: (not has_icon(x), x.reference in user_or_store_following_list))

        if offset is not None and offset >= 0:
            sorted_stores = sorted_stores[offset:]

        if limit is not None and limit >= 0:
            sorted_stores = sorted_stores[:limit]

        return sorted_stores

    def resolve_recommended_users(self, info, reference, offset, limit):
        def has_icon(entity):
            return bool(entity.icon)  # Simplified check for an icon

        try:
            user_or_store = Neo4jUser.nodes.get(reference=reference, is_deleted=False)
        except Neo4jUser.DoesNotExist:
            try:
                user_or_store = Neo4jStore.nodes.get(reference=reference, is_deleted=False)
            except Neo4jStore.DoesNotExist:
                raise Exception("User or Store with the given reference does not exist.")

        user_or_store_following_list = dbqueries.get_references_followed_by_entity(entity_reference=reference)


        # Get users with the highest followers
        users_with_highest_followers = Neo4jUser.nodes.filter(
            is_deleted=False
        ).filter(
            Q(name__isnull=False) & Q(name__ne="") &
            Q(handle__isnull=False) & Q(handle__ne="")
        ).exclude(
            reference=user_or_store.reference
        # ).exclude(
        #     reference__in=user_or_store_following_list
        ).order_by('-follower_count')

        # Get users from the same city and pincode as the visiting user
        users_same_pincode = Neo4jUser.nodes.filter(
            pincode=user_or_store.pincode,
            is_deleted=False
        ).filter(
            Q(name__isnull=False) & Q(name__ne="") &
            Q(handle__isnull=False) & Q(handle__ne="")
        ).exclude(
            reference=user_or_store.reference
        # ).exclude(
        #     reference__in=user_or_store_following_list
        )


        users_same_city = Neo4jUser.nodes.filter(
            city=user_or_store.city,
            is_deleted=False
        ).filter(
            Q(name__isnull=False) & Q(name__ne="") &
            Q(handle__isnull=False) & Q(handle__ne="")
        # ).exclude(
        #     reference__in=user_or_store_following_list
        ).exclude(
            reference=user_or_store.reference
        )

        # Get users who are being followed by users which the visitor follows
        # users_followed_by_followed_users = Neo4jEntity.nodes.filter(
        #     followed_by__follows__exact=user_or_store,
        #     is_deleted=False
        # )

        # Combine all recommended users and shuffle the list
        all_recommended_users = []

        if users_with_highest_followers is not None:
            all_recommended_users += list(users_with_highest_followers)

        if users_same_pincode is not None:
            all_recommended_users += list(users_same_pincode)

        if users_same_city is not None:
            all_recommended_users += list(users_same_city)

        unique_recommended_users = list(set(all_recommended_users))

        sorted_users = sorted(unique_recommended_users,
                              key=lambda x: (not has_icon(x), x.reference in user_or_store_following_list))

        return sorted_users[offset:(offset+limit)]

    def resolve_all_posts(self, info, limit=None, offset=None):
        try:
            content_references = dbqueries.get_content_references(limit=limit, offset=offset)
            required_posts = Posts.objects.filter(post_reference__in=content_references, is_deleted=False)
            # required_comments = Comments.objects.filter(comment_reference__in=content_references, is_deleted=False)
            required_products = Product.objects.filter(product_reference__in=content_references, deleted=False)

            posts_dict = {post.post_reference: post for post in required_posts}
            # comments_dict = {comment.comment_reference: comment for comment in required_comments}
            products_dict = {product.product_reference: product for product in required_products}

            items_dict = {**posts_dict, **products_dict}

            # Retrieve items in the same order as repost_content_references
            sorted_items = [items_dict[ref] for ref in content_references if ref in items_dict]

            return sorted_items
        except Exception as e:
            raise Exception(f"Error: {e}")

    def resolve_posts(self, info, post_references):
        try:
            return Posts.objects.filter(post_reference__in=post_references, is_deleted=False).order_by('-created_date')
        except Neo4jPost.DoesNotExist:
            raise Exception("Post with the given reference does not exist.")

    def resolve_user_and_store_posts(self, info, creator_reference, limit=None, offset=None):
        try:
            user_or_store = User.objects.get(user_reference=creator_reference, deleted=False)
        except User.DoesNotExist:
            try:
                user_or_store = Store.objects.get(store_reference=creator_reference, deleted=False)
            except Store.DoesNotExist:
                raise Exception("User or Store with the given reference does not exist.")

        posts = user_or_store.posted_by_user.filter(is_deleted=False).order_by('-created_date') if isinstance(user_or_store, User) else user_or_store.posted_by_store.filter(is_deleted=False).order_by('-created_date')

        if offset is not None and offset >= 0:
            posts = posts[offset:]

        if limit is not None and limit >= 0:
            posts = posts[:limit]
        return posts

    def resolve_feed(self, info, reference, limit=None, offset=None):
        try:
            feed_content_references = dbqueries.get_sorted_feed_content_references(entity_reference=reference, limit=limit, offset=offset)
            feed_content_references_list = feed_content_references['references']
            required_posts = Posts.objects.filter(post_reference__in=feed_content_references_list, is_deleted=False)
            required_comments = Comments.objects.filter(comment_reference__in=feed_content_references_list, is_deleted=False)
            required_products = Product.objects.filter(product_reference__in=feed_content_references_list, deleted=False)

            # Create dictionaries with reference as key and corresponding items as value
            posts_dict = {post.post_reference: post for post in required_posts}
            comments_dict = {comment.comment_reference: comment for comment in required_comments}
            products_dict = {product.product_reference: product for product in required_products}

            # Combine all dictionaries into one
            items_dict = {**posts_dict, **comments_dict, **products_dict}

            # Retrieve items in the same order as repost_content_references
            sorted_items = [items_dict[ref] for ref in feed_content_references_list if ref in items_dict]

            return sorted_items

        except Exception as e:
            raise Exception(f"Error: {e}")


    # def resolve_feed(self, info, reference, limit=None, offset=None):
    #     try:
    #         try:
    #             user_or_store = Neo4jUser.nodes.get(reference=reference)
    #         except Neo4jUser.DoesNotExist:
    #             try:
    #                 user_or_store = Neo4jStore.nodes.get(reference=reference)
    #             except Neo4jStore.DoesNotExist:
    #                 raise Exception("User or Store with the given reference does not exist.")
    #
    #         feed_node = user_or_store.feed.single()
    #
    #         if feed_node:
    #             # Call get_posts_in_feed on the Neo4jFeed node
    #             feed_posts = feed_node.get_posts_in_feed(limit=limit, offset=offset)
    #             all_test_store_post_references = Posts.objects.filter(
    #                 store_reference__storehandle__startswith='test_').values_list(
    #                 "post_reference", flat=True)
    #             all_inactive_store_post_references = Posts.objects.filter(store_reference__is_active=False).values_list(
    #                 "post_reference", flat=True)
    #
    #             all_test_store_comment_references = Comments.objects.filter(parent_handle__startswith='test_').values_list(
    #                 "comment_reference", flat=True)
    #
    #             posts_to_omit_in_feed = set(all_test_store_post_references).union(
    #                 set(all_inactive_store_post_references)).union(set(all_test_store_comment_references))
    #
    #             try:
    #                 if reference.startswith('U'):
    #                     visitor_id = User.objects.get(user_reference=reference, deleted=False).userid
    #                 elif reference.startswith('S'):
    #                     visitor_id = Store.objects.get(store_reference=reference, deleted=False).created_by
    #                 user_active_test_stores = Store.objects.filter(created_by=visitor_id, is_active=True,
    #                                                                storehandle__startswith='test_')
    #                 user_active_test_stores_references = user_active_test_stores.values_list('store_reference',
    #                                                                                          flat=True)
    #                 user_active_test_store_post_references = Posts.objects.filter(
    #                     store_reference__in=user_active_test_stores_references,
    #                     is_deleted=False).values_list('post_reference', flat=True)
    #             except Exception as e:
    #                 user_active_test_store_post_references = []
    #                 logger.info(f"An exception occured :{e}")
    #
    #             items = []
    #
    #             real_store_feed_posts = set(feed_posts).difference(posts_to_omit_in_feed)
    #             required_feed_posts = real_store_feed_posts.union(user_active_test_store_post_references)
    #
    #             required_posts = Posts.objects.filter(post_reference__in=required_feed_posts)
    #             required_comments = Comments.objects.filter(comment_reference__in=required_feed_posts)
    #             required_products = Product.objects.filter(product_reference__in=required_feed_posts)
    #
    #             # Combine all items
    #             items = list(required_posts) + list(required_comments) + list(required_products)
    #
    #             # Sort items by created date
    #             sorted_items = sorted(items, key=lambda item: item.created_date, reverse=True)
    #
    #             # required_posts = [post for post in required_feed_posts if post.startswith('PO')]
    #             # posts = Posts.objects.filter(post_reference__in=required_posts)
    #             # items.extend(posts)
    #             # '''uncomment below lines to get products in feed'''
    #             #
    #             # required_comments = [post for post in required_feed_posts if post.startswith('CO')]
    #             # comments = Comments.objects.filter(comment_reference__in=required_comments)
    #             # items.extend(comments)
    #             #
    #             # required_products = required_feed_posts.difference(required_posts + required_comments)
    #             # products = Product.objects.filter(product_reference__in=required_products)
    #             # items.extend(products)
    #             #
    #             # sorted_items = sorted(items, key=lambda item: item.created_date, reverse=True)
    #
    #             return sorted_items
    #         else:
    #             raise Exception("No feed node connected to the user or store.")
    #
    #     except (Neo4jUser.DoesNotExist, Neo4jStore.DoesNotExist):
    #         raise Exception("No entity with the given reference.")

    def resolve_feed_posts(self, info, reference, limit=None, offset=None):
        try:
            try:
                user_or_store = Neo4jUser.nodes.get(reference=reference)
            except Neo4jUser.DoesNotExist:
                try:
                    user_or_store = Neo4jStore.nodes.get(reference=reference)
                except Neo4jStore.DoesNotExist:
                    raise Exception("User or Store with the given reference does not exist.")

            feed_node = user_or_store.feed.single()

            if feed_node:
                # Call get_posts_in_feed on the Neo4jFeed node
                feed_posts = feed_node.get_posts_in_feed(limit=limit, offset=offset)
                all_test_store_post_references = Posts.objects.filter(
                    store_reference__storehandle__startswith='test_', is_deleted=False).values_list(
                    "post_reference", flat=True)
                all_inactive_store_post_references = Posts.objects.filter(store_reference__is_active=False, is_deleted=False).values_list(
                    "post_reference", flat=True)

                posts_to_omit_in_feed = set(all_test_store_post_references).union(
                    set(all_inactive_store_post_references))

                try:
                    if reference.startswith('U'):
                        visitor_id = User.objects.get(user_reference=reference, deleted=False).userid
                    elif reference.startswith('S'):
                        visitor_id = Store.objects.get(store_reference=reference, deleted=False).created_by
                    user_active_test_stores = Store.objects.filter(created_by=visitor_id, is_active=True,
                                                                   storehandle__startswith='test_')
                    user_active_test_stores_references = user_active_test_stores.values_list('store_reference',
                                                                                             flat=True)
                    user_active_test_store_post_references = Posts.objects.filter(
                        store_reference__in=user_active_test_stores_references,
                        is_deleted=False).values_list('post_reference', flat=True)
                except Exception as e:
                    user_active_test_store_post_references = []
                    logger.info(f"An exception occured :{e}")

                real_store_feed_posts = set(feed_posts).difference(posts_to_omit_in_feed)
                required_feed_posts = real_store_feed_posts.union(user_active_test_store_post_references)
                return Posts.objects.filter(post_reference__in=required_feed_posts)
            else:
                raise Exception("No feed node connected to the user or store.")

        except (Neo4jUser.DoesNotExist, Neo4jStore.DoesNotExist):
            raise Exception("No entity with the given reference.")


schema = graphene.Schema(query=Query, mutation=Mutation)


# def resolve_contact_data_old(self, info, entity_reference, phonenumbers, sync):
#     if sync:
#         # Attempt to find the asking entity as a User or Store
#         logger.info(f"resolver triggered @ {datetime.now()}")
#         asking_entity = Neo4jUser.nodes.get_or_none(reference=entity_reference) or Neo4jStore.nodes.get_or_none(
#             reference=entity_reference)
#
#         if not asking_entity:
#             raise Exception("User or Store with the given reference does not exist.")
#         # phone numbers of users and unregistered user that have has contact relationship with asking
#         query = """
#
#         MATCH (asking: Neo4jEntity {reference: $asking_reference})
#         WITH asking
#         MATCH (asking)-[:HAS_CONTACT]->(contact_user: Neo4jUser), (asking)-[:HAS_CONTACT]->(contact_unruser: Neo4jUnrUser)
#         WITH DISTINCT ( COLLECT(contact_user.phonenumber) + COLLECT(contact_unruser.phonenumber)) AS all_phone_numbers
#         RETURN all_phone_numbers
#         """
#         parameters = {'asking_reference': asking_entity.reference}
#         phonenumbers_with_hasContact = db.cypher_query(query, parameters)[0][0][0]
#
#         # Find or create unregistered users for phonenumbers not linked to any User
#         registered_users, existing_unregistered_users, unregistered_users = find_or_create_users(phonenumbers)
#
#         # Get stores created by the registered users
#         stores = get_stores_from_users(phonenumbers)
#
#         # Connect entities to the asking entity
#         logger.info(f"connect_entities_to_asking started @ {datetime.now()}")
#         final_list = registered_users + stores + (existing_unregistered_users + unregistered_users)
#
#         connect_entities_to_asking(asking_entity, (registered_users + stores),
#                                    (existing_unregistered_users + unregistered_users))
#
#         logger.info(f"connect_entities_to_asking ended @ {datetime.now()}")
#
#         final_results = registered_users + stores + (unregistered_users + existing_unregistered_users)
#
#     query = """
#         MATCH (user:Neo4jUser)
#         WHERE user.reference IN $references_list
#         RETURN user AS entity, CASE
#             WHEN (user)-[:FOLLOWS]->(:Neo4jUser {reference: $asking_reference}) THEN 'Follow back'
#             WHEN (:Neo4jUser {reference: $asking_reference})-[:FOLLOWS]->(user) THEN 'Following'
#             ELSE 'Follow'
#         END AS follow_status,
#         'USER' AS entity_type
#
#         UNION
#
#         MATCH (store:Neo4jStore)
#         WHERE store.reference IN $references_list
#         RETURN store AS entity, CASE
#             WHEN (store)-[:FOLLOWS]->(:Neo4jStore {reference: $asking_reference}) THEN 'Support back'
#             WHEN (:Neo4jStore {reference: $asking_reference})-[:FOLLOWS]->(store) THEN 'Supporting'
#             ELSE 'Support'
#         END AS follow_status,
#         'STORE' AS entity_type
#
#         UNION
#
#         MATCH (unregistered:Neo4jUnrUser)
#         WHERE unregistered.reference IN $references_list
#         RETURN unregistered AS entity,  CASE
#             WHEN (:Neo4jUnrUser {reference: $asking_reference})-[:FOLLOWS]->(unregistered) THEN 'Pending'
#             ELSE 'Follow'
#         END AS follow_status,
#         'UNREGISTERED' AS entity_type
#     """
#
#     query = """
#     MATCH (asking: Neo4jEntity {reference:$asking_reference})
#     WITH asking
#     MATCH (asking)-[:HAS_CONTACT]->(contact_user:Neo4jUser)
#     WHERE NOT EXISTS((asking)-[:FOLLOWS]->(contact_user))
#     WITH asking, contact_user
#     RETURN contact_user AS entity, CASE
#             WHEN (contact_user)-[:FOLLOWS]->(asking) THEN 'Follow back'
#             WHEN (asking)-[:FOLLOWS]->(contact_user) THEN 'Following'
#             ELSE 'Follow'
#         END AS follow_status,
#         'USER' AS entity_type
#
#     UNION
#
#     MATCH (asking: Neo4jEntity {reference:$asking_reference})
#     WITH asking
#     MATCH (asking)-[:HAS_CONTACT]->(contact_unruser:Neo4jUnrUser)
#     WHERE NOT EXISTS((asking)-[:FOLLOWS]->(contact_unruser))
#     WITH asking, contact_unruser
#     RETURN contact_unruser AS entity, CASE
#             WHEN (asking)-[:FOLLOWS]->(contact_unruser) THEN 'Pending'
#             ELSE 'Follow'
#         END AS follow_status,
#         'UNREGISTERED' AS entity_type
#
#     UNION
#
#     MATCH (asking: Neo4jEntity {reference:$asking_reference})
#     WITH asking
#     MATCH (asking)-[:HAS_CONTACT]->(contact_store:Neo4jStore)
#     WHERE NOT EXISTS ((asking)-[:FOLLOWS]->(contact_store))
#     with asking, contact_store
#     RETURN contact_store AS entity, CASE
#         WHEN (contact_store)-[:FOLLOWS]->(asking) THEN 'Support back'
#         WHEN (asking)-[:FOLLOWS]->(contact_store) THEN 'Supporting'
#         ELSE 'Support'
#     END AS follow_status,
#     'STORE' AS entity_type
#     """
#
#     parameters = {'asking_reference': asking_entity.reference}
#     logger.info(f"cypher querying started @ {datetime.now()}")
#
#     result, _ = db.cypher_query(query, parameters)
#     logger.info(f"cypher querying ended @ {datetime.now()}")
#
#     def add_node_property(x):
#         x[0]._properties['follow_status'] = x[1]
#         x[0]._properties['entity_type'] = x[2]
#         return x[0]
#
#     final_results_with_status = [add_node_property(x) for x in result]
#     logger.info(f"resolver ended @ {datetime.now()}")
#
#     return final_results_with_status
