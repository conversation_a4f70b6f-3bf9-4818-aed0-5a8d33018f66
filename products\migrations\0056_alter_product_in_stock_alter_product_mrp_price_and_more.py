# Generated by Django 4.2.7 on 2024-09-27 10:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0055_alter_product_product_category"),
    ]

    operations = [
        migrations.AlterField(
            model_name="product",
            name="in_stock",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="product",
            name="mrp_price",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="product",
            name="selling_price",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="productimages",
            name="reorder",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="productversion",
            name="in_stock",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="productversion",
            name="mrp_price",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="productversion",
            name="selling_price",
            field=models.PositiveIntegerField(blank=True, default=0, null=True),
        ),
    ]
