from django.urls import path
from .views import (
    MessagingLogin, 
    SendMessage, 
    GetChatList,
    VisitChat,
    GetMessages,
    MarkAsRead,
    GetDMMetadata
)

urlpatterns = [
    path('login/', MessagingLogin.as_view(), name='messaging_login'),
    path('send_message/', SendMessage.as_view(), name='send_message'),
    path('get_chat_list/', GetChatList.as_view(), name='get_chat_list'),
    path('get_dm_messages/', GetMessages.as_view(), name='get_messages'),
    path('mark_as_read/', MarkAsRead.as_view(), name='mark_as_read'),
    path('visit_chat/', VisitChat.as_view(), name='visit_chat'),
    path('get_dm_metadata/', GetDMMetadata.as_view(), name='get_dm_metadata'),
]