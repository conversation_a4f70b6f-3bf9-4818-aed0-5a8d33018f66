# Generated by Django 4.2.7 on 2024-03-19 10:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0055_alter_notifications_image"),
    ]

    operations = [
        migrations.AlterField(
            model_name="notifications",
            name="notification_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING", "onboarding"),
                    ("PRODUCT_VERSION_CHANGED", "Product version changed"),
                    ("STORE_CREATED", "New store created near you"),
                    ("NEW_ORDER", "New order"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("ORDER_SHIPPED", "Order shipped"),
                    ("SHIPPING_PACAKGE_UPDATED", "Shipping package updated"),
                    ("DELIVERY_OTP", "Delivery otp"),
                    ("PACKAGE_DELIVERED", "Package delivered"),
                    ("DELIVERY_FAILED", "Delivery failed"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("SELLER_CANCELLED_ORDER", "Seller cancelled order"),
                    ("BUYER_CANCELLED_ORDER", "Buyer cancelled order"),
                    ("AUTO_CANCELLED", "Auto cancelled"),
                    ("RETURN_REQUESTED", "Return requested"),
                    ("RETURN_ACCEPTED", "Return accepted"),
                    ("RETURN_RECEIVED", "Return received"),
                    ("REFUND_INITIATED", "Refund initiated"),
                    ("REFUND_HOLD", "Refund hold"),
                    ("REFUND_RELEASED", "Refund released"),
                    ("RETURN_OTP", "Return otp"),
                    ("COMMENTED", "Commented"),
                    ("SOMEONE_FOLLOWED_STORE", "Someone followed store"),
                    ("SOMEONE_FOLLOWED_USER", "Someone followed user"),
                    ("CONTENT_LIKED", "content_liked"),
                ],
                max_length=100,
                null=True,
            ),
        ),
    ]
