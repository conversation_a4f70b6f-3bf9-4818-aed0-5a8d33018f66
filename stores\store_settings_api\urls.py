from django.urls import path
from .views import (
    AddTrustCenterAV,
    GetTrustCenterAV,
    EditTrustCenterContact,
    EditTrustCenterLocation,
    TrustCenterSwadeshiLabel,
    AddDocumentsAV,
    GetDocumentAV,
    EditDocumentAV,
    EditDocumentName,
    DeleteDocument,
    DeliverySettingsAV,
    DeliverySettingsDetailsAV,
    DeliveryLocationListAV,
    AddDeliveryLocation,
    DeliveryLocationDetailsAV,
    GetDeliverySettingsAV,
    AddRefundAndWarranty,
    RefundAndWarrantyDetails,
    GetRefundAndWarrantyAV,
    AddAddress,
    GetAllStoreAddress,
    GetAddressForProductPickup,
    ResetDefaultDS,
    ResetDefaultRefundSettings,
    GetStoreConfig,
    GetStoresByStates,
    GetStoresByCategories
    # TestLocations
)

urlpatterns = [
    # trust center
    path("addtrustcenter/", AddTrustCenterAV.as_view(), name="add-trust-center"),
    path(
        "gettrustcenter/<str:store_reference>/",
        GetTrustCenterAV.as_view(),
        name="get-trust-center",
    ),
    path(
        "edit_trustcenter_contact/<int:trust_center_id>/",
        EditTrustCenterContact.as_view(),
        name="edit-trust-center-contact",
    ),
    path(
        "edit_trustcenter_location/<int:trust_center_id>/",
        EditTrustCenterLocation.as_view(),
        name="edit-trust-center-location",
    ),
    path(
        "trustcenter_swadeshi_label/<str:store_reference>/",
        TrustCenterSwadeshiLabel.as_view(),
        name="edit-trust-center-swadeshi_label",
    ),
    path("adddocument/", AddDocumentsAV.as_view(), name="add-document"),
    path(
        "getdocument/<str:store_reference>/",
        GetDocumentAV.as_view(),
        name="get-document",
    ),
    path(
        "editdocument/<int:document_id>/",
        EditDocumentAV.as_view(),
        name="edit-document",
    ),
    path(
        "editdocument_name/<int:document_id>/",
        EditDocumentName.as_view(),
        name="edit-document-name",
    ),
    path(
        "deletedocument/<int:document_id>/",
        DeleteDocument.as_view(),
        name="delete-document",
    ),
    # delivery settings
    path("deliverysettings/", DeliverySettingsAV.as_view(), name="deliverysettings"),
    path(
        "deliverysettingsdetails/<int:delivery_settings_id>/",
        DeliverySettingsDetailsAV.as_view(),
        name="delivery-settings-details",
    ),
    path(
        "deliverylocations/store/<str:store_reference>/product/<int:product_id>/",
        DeliveryLocationListAV.as_view(),
        name="delivery-locations",
    ),
    # path(
    #         "test_locations/store/<str:store_reference>/product/<int:product_id>/",
    #         TestLocations.as_view(),
    #         name="delivery-locations",
    #     ),
    path(
        "add_deliverylocations/",
        AddDeliveryLocation.as_view(),
        name="add-delivery-locations",
    ),
    path(
        "deliverylocationsdetails/<int:pk>/",
        DeliveryLocationDetailsAV.as_view(),
        name="delivery-locations-details",
    ),
    path(
        "get_deliverysettings/store/<str:store_reference>/product/<str:product_reference>/",
        GetDeliverySettingsAV.as_view(),
        name="get-delivery-settings",
    ),
    path(
        "get_deliverysettings/store/<str:store_reference>/product/<str:product_reference>/<str:version>/",
        GetDeliverySettingsAV.as_view(),
        name="get-delivery-settings",
    ),
    #  refund and warranty
    path(
        "add_refund_warranty/",
        AddRefundAndWarranty.as_view(),
        name="add-refund-warranty",
    ),
    path(
        "refund_warranty_updates/<int:refund_warranty_id>/",
        RefundAndWarrantyDetails.as_view(),
        name="refund-warranty-updates",
    ),
    path(
        "get_refund_warranty/store/<str:store_reference>/product/<str:product_reference>/",
        GetRefundAndWarrantyAV.as_view(),
        name="get-refund-warranty",
    ),
    path(
        "get_refund_warranty/store/<str:store_reference>/product/<str:product_reference>/<str:version>/",
        GetRefundAndWarrantyAV.as_view(),
        name="get-refund-warranty",
    ),
    path("add_and_delete_address/<int:pk>/", AddAddress.as_view(), name="add-address"),
    path(
        "get_all_store_address/<str:store_reference>/",
        GetAllStoreAddress.as_view(),
        name="add-address",
    ),
    path(
        "get_address_for_product_pickup/<str:product_reference>/",
        GetAddressForProductPickup.as_view(),
        name="get-address-for-product-pickup",
    ),
    path(
        "reset_default_delivery_settings/<str:store_reference>/<str:product_reference>/",
        ResetDefaultDS.as_view(),
        name="reset-default-ds",
    ),
    path(
        "reset_default_refund_warranty_settings/<str:store_reference>/<str:product_reference>/",
        ResetDefaultRefundSettings.as_view(),
        name="reset-default-refund-settings",
    ),
    path("get_store_configs/<str:store_reference>/", GetStoreConfig.as_view(), name="get-store-config"),
    path('stores_by_state/', GetStoresByStates.as_view(), name='store_info_list'),
    path('stores_by_category/', GetStoresByCategories.as_view(), name='populate-stores-by-category'),

]
