# Generated by Django 3.2.13 on 2023-01-09 12:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0026_auto_20230103_1316'),
    ]

    operations = [
        migrations.AddField(
            model_name='feedback',
            name='responses',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='feedback',
            name='upvote_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='feedback',
            name='upvote_users',
            field=models.CharField(blank=True, max_length=300, null=True),
        ),
        migrations.AlterField(
            model_name='feedback',
            name='feedback_type',
            field=models.CharField(blank=True, choices=[('REPORT', 'Report'), ('SUGGESTION', 'Suggestion'), ('USER_RESPONSE', 'User response'), ('SWADESIC_RESPONSE', 'Swadesic response')], max_length=30, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='feedback',
            name='status',
            field=models.Char<PERSON>ield(blank=True, choices=[('CREATED', 'Created'), ('REVIEWED', 'Reviewed'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('PLANNED', 'Planned'), ('SNAR', 'SNAR'), ('FIXED', 'Fixed')], default='CREATED', max_length=20, null=True),
        ),
    ]
