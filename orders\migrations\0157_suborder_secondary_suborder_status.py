# Generated by Django 4.2.7 on 2025-04-22 08:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0156_suborder_return_estimated_pickup_date"),
    ]

    operations = [
        migrations.AddField(
            model_name="suborder",
            name="secondary_suborder_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ORDER_INITIATED", "Order initiated"),
                    ("PAYMENT_INITIATED", "Payment initiated"),
                    ("PAYMENT_SUCCESS", "Payment success"),
                    ("WAITING_FOR_CONFIRMATION", "waiting for confirmation"),
                    ("PAYMENT_PENDING", "Payment pending"),
                    ("PAYMENT_FAILED", "Payment failed"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("SCHEDULED_FOR_PICKUP", "Scheduled for pickup"),
                    ("DELIVERY_IN_PROGRESS", "Delivery in progress"),
                    ("ORDER_DELIVERED", "Order delivered"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("ORDER_CANCELLED_BY_BUYER", "Order cancelled by buyer"),
                    ("ORDER_CANCELLED_BY_SELLER", "Order cancelled by seller"),
                    ("CANCELLED_IN_TRANSIT", "Cancelled in transit"),
                    ("DELIVERY_FAILED", "Delivery failed"),
                    ("RETURN_REQUESTED", "Return requested"),
                    ("RETURN_REQUEST_CANCELLED", "Return request cancelled"),
                    ("RETURN_CONFIRMED", "Return confirmed"),
                    ("RETURN_IN_PROGRESS", "Return in progress"),
                    ("RETURNED_TO_SELLER", "Return to seller"),
                    ("RETURN_FAILED", "Return failed"),
                    ("REFUND_HOLD", "Refund hold"),
                    ("REFUNDED", "Refunded"),
                    ("ORDER_AUTO_CANCELLED", "Order auto cancelled"),
                ],
                max_length=50,
                null=True,
            ),
        ),
    ]
