"""
SEO utility functions for generating canonical URLs and meta tags
"""
from django.conf import settings
from django.urls import reverse


def get_seo_url(content_type, reference):
    """
    Generate SEO-friendly URLs for different content types
    
    Args:
        content_type: 'store', 'product', 'post', or 'story'
        reference: The reference ID of the content
    
    Returns:
        Full SEO URL for the content
    """
    base_url = f"https://{settings.SITE_DOMAIN}"
    
    url_patterns = {
        'store': f'/seo/store/{reference}/',
        'product': f'/seo/product/{reference}/',
        'post': f'/seo/post/{reference}/',
        'story': f'/seo/story/{reference}/',
    }
    
    if content_type in url_patterns:
        return base_url + url_patterns[content_type]
    
    return base_url


def get_canonical_url(request, content_type, reference):
    """
    Generate canonical URL for SEO
    """
    return get_seo_url(content_type, reference)


def generate_meta_tags(title, description, image_url=None, url=None):
    """
    Generate meta tags dictionary for templates
    """
    meta = {
        'title': title,
        'description': description[:160] if description else '',
        'url': url,
    }
    
    if image_url:
        meta['image'] = image_url
    
    return meta


def get_structured_data_product(product):
    """
    Generate structured data for products
    """
    return {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": product.product_name,
        "description": product.product_description,
        "brand": {
            "@type": "Brand",
            "name": product.brand_name
        },
        "category": product.product_category,
        "offers": {
            "@type": "Offer",
            "price": str(product.selling_price),
            "priceCurrency": "INR",
            "availability": "https://schema.org/InStock" if product.in_stock > 0 else "https://schema.org/OutOfStock"
        }
    }


def get_structured_data_organization(store):
    """
    Generate structured data for stores/organizations
    """
    return {
        "@context": "https://schema.org/",
        "@type": "Organization",
        "name": store.store_name,
        "description": store.store_desc,
        "url": get_seo_url('store', store.store_reference),
    }
