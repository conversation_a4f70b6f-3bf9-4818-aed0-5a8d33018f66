from rest_framework import serializers
from .models import SearchHistory, SearchedItems
import pytz

from ..store_api.models import Store
from ..store_settings_api.models import TrustCenter
from products.models import Product, ProductImages
from users.user_api.models import User

import logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class ProductTypingSuggestionsSerializer(serializers.Serializer):
    """
    Lean serializer for content search results.
    Optimized for performance with minimal fields.
    """
    type = serializers.CharField()
    reference = serializers.CharField()
    primary_text = serializers.CharField()
    secondary_text = serializers.CharField(allow_null=True)
    image_url = serializers.URLField(allow_null=True)

    # Optional fields for products
    store_handle = serializers.CharField(required=False, allow_null=True)
    store_name = serializers.CharField(required=False, allow_null=True)
    mrp_price = serializers.IntegerField(required=False, allow_null=True)
    selling_price = serializers.IntegerField(required=False, allow_null=True)


class EntityTypingSuggestionsSerializer(serializers.Serializer):
    """
    Lean serializer for user/store search results from Neo4j.
    Minimal fields for fast response times.
    """
    type = serializers.CharField()
    reference = serializers.CharField()
    primary_text = serializers.CharField()
    secondary_text = serializers.CharField(allow_null=True)
    image_url = serializers.URLField(allow_null=True)

class SearchHistorySerializer(serializers.ModelSerializer):
    date = serializers.SerializerMethodField('get_date')
    class Meta:
        fields = ['search_history_id', 'user_reference', 'search_input_text', 'date']
        model = SearchHistory

    def get_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone ("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

class SearchedItemsSerializer(serializers.ModelSerializer):
    search_history_id = serializers.IntegerField(default=0, read_only=True)
    date = serializers.SerializerMethodField('get_date')
    name = serializers.SerializerMethodField('get_search_item_name')
    icon = serializers.SerializerMethodField('get_search_item_icon')
    location = serializers.SerializerMethodField('get_location')
    handle = serializers.SerializerMethodField('get_handle')
    subscription_type = serializers.SerializerMethodField('get_subscription_type')

    class Meta:
        fields = ['searched_item_id', 'search_history_id', 'user_reference', 'search_input_text', 'search_item', 'name', 'icon', 'location', 'handle', 'search_item_type', 'date', 'subscription_type']
        model = SearchedItems

    def get_subscription_type(self, obj):
        search_item_type = obj.search_item_type
        search_item = obj.search_item

        if search_item_type == SearchedItems.SearchItemType.PRODUCT:
            return ""
        elif search_item_type == SearchedItems.SearchItemType.STORE:
            store = Store.objects.get(store_reference=search_item)
            return store.subscription_type
        elif search_item_type == SearchedItems.SearchItemType.USER:
            user = User.objects.get(user_reference=search_item)
            return user.subscription_type

    def get_handle(self, obj):
        search_item_type = obj.search_item_type
        search_item = obj.search_item

        if search_item_type == SearchedItems.SearchItemType.PRODUCT:
            product = Product.objects.get(product_reference=search_item)
            return product.brand_name
        elif search_item_type == SearchedItems.SearchItemType.STORE:
            store = Store.objects.get(store_reference=search_item)
            return store.storehandle
        elif search_item_type == SearchedItems.SearchItemType.USER:
            user = User.objects.get(user_reference=search_item)
            if user.user_name:
                return user.user_name
            else:
                return None

    def get_location(self, obj):
        search_item_type = obj.search_item_type
        search_item = obj.search_item

        if search_item_type == SearchedItems.SearchItemType.PRODUCT:
            product = Product.objects.get(product_reference=search_item)
            if TrustCenter.objects.filter(store_reference=product.store_reference).exists():
                trust_center = TrustCenter.objects.get(store_reference=product.store_reference)
                location = trust_center.city
                return location
            else:
                return None
        elif search_item_type == SearchedItems.SearchItemType.STORE:
            if TrustCenter.objects.filter(store_reference=search_item).exists():
                trust_center = TrustCenter.objects.get(store_reference=search_item)
                location = trust_center.city
                return location
            else:
                return None
        elif search_item_type == SearchedItems.SearchItemType.USER:
            user = User.objects.get(user_reference=search_item)
            if user.user_location:
                return user.user_name
            else:
                return None

    def get_search_item_icon(self, obj):
        search_item_type = obj.search_item_type
        search_item = obj.search_item

        if search_item_type == SearchedItems.SearchItemType.PRODUCT:
            if ProductImages.objects.filter(product_reference=search_item).exists():
                product_icon = ProductImages.objects.filter(product_reference=search_item).first()
                if product_icon.product_image:
                    return product_icon.product_image.url
            return None

        elif search_item_type == SearchedItems.SearchItemType.STORE:

            store = Store.objects.get(store_reference=search_item)
            if store.icon:
                return store.icon.url
            return None
        elif search_item_type == SearchedItems.SearchItemType.USER:

            user = User.objects.get(user_reference=search_item)
            if user.icon:
                return user.icon.url
            return None

    def get_search_item_name(self, obj):
        search_item_type = obj.search_item_type
        search_item = obj.search_item

        if search_item_type == SearchedItems.SearchItemType.PRODUCT:
            product = Product.objects.get(product_reference=search_item)
            return product.product_name
        elif search_item_type == SearchedItems.SearchItemType.STORE:
            store = Store.objects.get(store_reference=search_item)
            return store.store_name
        elif search_item_type == SearchedItems.SearchItemType.USER:
            user = User.objects.get(user_reference=search_item)
            return user.display_name

    def get_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")

class RecentSearchSerializer(serializers.ModelSerializer):
    searched_item_id = serializers.IntegerField(default=0)
    date = serializers.SerializerMethodField('get_date')
    name = serializers.CharField(allow_null=True)
    icon = serializers.ImageField(allow_null=True)
    search_item = serializers.CharField(allow_null=True)
    search_item_type = serializers.CharField(allow_null=True)
    location = serializers.CharField(allow_null=True)
    handle = serializers.CharField(allow_null=True)

    class Meta:
        fields = ['searched_item_id', 'search_history_id', 'user_reference', 'search_input_text', 'search_item', 'name', 'icon', 'location', 'handle', 'search_item_type', 'date']
        model = SearchHistory

    def get_date(self, obj):
        # date = obj['created_date']
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")
