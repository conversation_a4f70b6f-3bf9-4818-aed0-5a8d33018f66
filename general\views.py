from django.apps import AppConfig
from stores.store_settings_api.models import DeliveryLocations, DeliverySettings
from stores.store_api.models import Store, StoreConfig
from .models import AppConfigurations
from django.db.models.functions import Upper
from django.db.models import F, Q, Window
from django.db.models.functions import RowNumber


#
# class AppConfigSingleton:
#     _instance = None
#     _last_updated = None
#     _cache_duration = timedelta(minutes=1)  # Cache for 5 minutes
#
#     @classmethod
#     def get_instance(cls):
#         now = timezone.now()
#         if cls._instance is None or cls._last_updated is None or now - cls._last_updated > cls._cache_duration:
#             from .models import AppConfigurations
#             try:
#                 cls._instance = AppConfigurations.objects.first()
#                 cls._last_updated = now
#             except Exception:
#                 # If there's an error (e.g., table doesn't exist), return None
#                 return None
#         return cls._instance
#
#     @classmethod
#     def reset_instance(cls):
#         cls._instance = None
#         cls._last_updated = None

def get_app_config():
    return AppConfigurations.objects.first()

class GeneralConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'general'

    def ready(self):
        # Don't load the configuration here
        pass


from collections import defaultdict

import logging
from collections import defaultdict
import time

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class BuyStatusCheckerBatch:
    def __init__(self, products, user_pincode):
        self.products = products
        self.user_pincode = user_pincode
        self.app_config = get_app_config()
        self.store_map = {p.store_reference.store_reference: p.store_reference for p in self.products}
        self.store_config_map = self._prepare_store_config_map()
        self.delivery_settings_map = self._prepare_delivery_settings_map()

        # self.raw_settings = self._raw_delivery_settings()

    # def _prepare_store_map(self):
    #     store_refs = set(p.store_reference.store_reference for p in self.products)
    #     store_refs = self.products.values_list("store_reference", flat = True)
    #     stores = Store.objects.filter(store_reference__in=store_refs)
    #     store_map = {store.store_reference: store for store in stores}
    #     logger.info(f"Loaded {len(store_map)} stores for batch")
    #     return store_map

    def _prepare_store_config_map(self):
        configs = StoreConfig.objects.filter(store_reference__in=self.store_map.keys())
        config_map = {conf.store_reference.store_reference: conf for conf in configs}
        logger.info(f"Loaded {len(config_map)} store configurations")
        return config_map

    def _prepare_delivery_settings_map(self):
        settings_qs = DeliverySettings.objects.filter(
            Q(product_reference__in=self.products.values_list("product_reference", flat=True)) | Q(product_reference=None),
            store_reference__store_reference__in=self.store_map.keys(),
            is_deleted=False
        ).select_related('store_reference')

        # Mapping: {(product_reference, store_reference): DeliverySettings instance}
        settings_map = {}

        for setting in settings_qs:
            key = (setting.product_reference, setting.store_reference.store_reference)

            # If key doesn't exist or this is more specific (product-level)
            if key not in settings_map or setting.product_reference is not None:
                settings_map[key] = setting.delivery_locations

        # settings = DeliverySettings.objects.filter(
        #     is_deleted=False,
        #     store_reference__in=self.store_map.keys()
        # )
        # result = {}
        # for setting in settings:
        #     key = (setting.product_reference, setting.store_reference.store_reference)
        #     if key not in result or setting.product_reference is not None:
        #         result[key] = setting
        logger.info(f"Prepared delivery settings map with {len(settings_map)} entries")
        return settings_map


    # def _raw_delivery_settings(self):
    #     from django.db import connection

    #     # Prepare a raw SQL query to get the latest DeliverySettings for each product-reference, store-reference combination
    #     query = """
    #         SELECT ds.product_reference, ds.store_reference_id, ds.delivery_locations
    #         FROM delivery_settings ds
    #         INNER JOIN (
    #             SELECT product_reference, store_reference_id, MAX(delivery_settings_version) AS max_version
    #             FROM delivery_settings
    #             WHERE product_reference IN %s OR product_reference IS NULL
    #             AND store_reference_id IN %s
    #             AND is_deleted = FALSE
    #             GROUP BY product_reference, store_reference_id
    #         ) latest
    #         ON ds.product_reference = latest.product_reference
    #         AND ds.store_reference_id = latest.store_reference_id
    #         AND ds.delivery_settings_version = latest.max_version
    #         WHERE ds.is_deleted = FALSE
    #     """

    #     # Execute the raw SQL query
    #     with connection.cursor() as cursor:
    #         cursor.execute(query, [tuple(self.products.values_list("product_reference", flat=True)), tuple(self.store_map.keys())])
    #         raw_settings = cursor.fetchall()

    #     return raw_settings

    #     # raw_settings will now contain the result in a tuple format: (product_reference, store_reference, delivery_locations)


    def _is_deliverable(self, delivery_locations):
        """
        Checks if the user's pincode is deliverable based on the delivery_settings.
        The delivery_settings.delivery_locations is a pipe-separated string like:
        '524004|hyderabad|andhra pradesh'
        """

        # Check if delivery_locations is None or empty
        if not delivery_locations:
            return False
        
        logger.info(f"_is_deliverable- Input Delivery locations: {delivery_locations}")

        # Clean and normalize all entries (lowercased strings, no whitespace)
        delivery_items = [
            item.strip().upper()
            for item in delivery_locations.split('|')
            if item.strip()
        ]
        logger.info(f"_is_deliverable- Cleaned Delivery locations: {delivery_items}")

        # Separate numeric items as pincodes and rest as text (city/state)
        pincodes = [item for item in delivery_items if item.isdigit()]
        text_items = [item for item in delivery_items if not item.isdigit()]

        logger.info(f"_is_deliverable- pincodes: {pincodes}")
        logger.info(f"_is_deliverable- text_items: {text_items}")

        # Direct pincode match
        if str(self.user_pincode) in pincodes:
            logger.info(f"_is_deliverable- Direct pincode match: {self.user_pincode}")
            return True

        # Check if user's pincode is linked to any city/state in the text list
        return DeliveryLocations.objects.filter(
            pincode=self.user_pincode
        ).filter(
            Q(city__in=text_items) | Q(state__in=text_items)
        ).exists()

    def get_buy_status_map1(self):
        results = {}
        app_orders_enabled = self.app_config.enable_app_orders

        for product in self.products:

            # App-level check
            if not app_orders_enabled:
                results[product.product_reference] = {
                "is_buy_enabled": False,
                "product_status_message": "Swadesic ordering is temporarily unavailable",
                }
                logger.debug(f"[{product.product_reference}] App ordering disabled")
                continue

            # Store checks

            store_ref = product.store_reference.store_reference
            store = self.store_map.get(store_ref, [None])

            if store:
                 # Store configuration by Swadesic check
                store_config = self.store_config_map.get(store_ref)
                if not store_config:
                    results[product.product_reference] = {
                    "is_buy_enabled": False,
                    "product_status_message": "Store is not accepting orders",
                    }
                    logger.debug(f"[{product.product_reference}] Store config disallows orders")
                    continue



                # Store-level verification check
                if not store.is_verification_completed:
                    results[product.product_reference] = {
                    "is_buy_enabled": False,
                    "product_status_message": "Store is not verified yet",
                    }
                    logger.debug(f"[{product.product_reference}] Store not verified")
                    continue



                # Store-level activation check
                if not store.is_active:
                    results[product.product_reference] = {
                    "is_buy_enabled": False,
                    "product_status_message": "Store is temporarily not active",
                    }
                    continue


                 # Store order accepting check
                if not store.open_for_order:
                    results[product.product_reference] = {
                    "is_buy_enabled": False,
                    "product_status_message": "Store is currently closed for orders",
                    }
                    logger.debug(f"[{product.product_reference}] Store config disallows orders")
                    continue

                # Delivery check
                delivery_locations = (
                    self.delivery_settings_map.get((product.product_reference, store_ref)) or
                    self.delivery_settings_map.get((None, store_ref))
                )

                is_deliverable = self._is_deliverable(delivery_locations) if self.user_pincode else False
                if not is_deliverable:
                    results[product.product_reference] = {
                    "is_buy_enabled": False,
                    "product_status_message": "Not deliverable to your pincode",
                    }
                    continue
            else:
                results[product.product_reference] = {
                "is_buy_enabled": False,
                "product_status_message": "Not available",
                }
                continue


            results[product.product_reference] = {
                "is_buy_enabled": True,
                "product_status_message": None,
                }


            # # Flag checks
            # flags = {
            #     "app_orders_enabled": app_orders_enabled,
            #     "store_orders_enabled": getattr(store_config, 'enable_orders', False),
            #     "is_verified": getattr(store, 'is_verification_completed', False),
            #     "is_active": getattr(store, 'is_active', False),
            #     "open_for_order": getattr(store, 'open_for_order', False),
            #     "deliverable": is_deliverable,
            # }

            # is_buy_enabled = all(flags.values())

            # # Status message
            # message = None
            # if not flags["app_orders_enabled"]:
            #     message = "Swadesic ordering is temporarily unavailable"
            #     logger.debug(f"[{product.product_reference}] App ordering disabled")
            # elif not flags["store_orders_enabled"]:
            #     message = "Store is not accepting orders"
            #     logger.debug(f"[{product.product_reference}] Store config disallows orders")
            # elif not flags["is_verified"]:
            #     message = "Store is not verified yet"
            #     logger.debug(f"[{product.product_reference}] Store not verified")
            # elif not flags["is_active"]:
            #     message = "Store is temporarily not active"
            #     logger.debug(f"[{product.product_reference}] Store inactive")
            # elif not flags["open_for_order"]:
            #     message = "Store is currently closed for orders"
            #     logger.debug(f"[{product.product_reference}] Store closed")
            # elif not flags["deliverable"]:
            #     message = "Not deliverable to your pincode"
            #     logger.debug(f"[{product.product_reference}] Undeliverable to {self.user_pincode}")

            # results[product.product_reference] = {
            #     "is_buy_enabled": is_buy_enabled,
            #     "product_status_message": message,
            # }

        return results

    def get_buy_status_map(self):
        start_time = time.perf_counter()
        results = {}

        for product in self.products:
            store_ref = product.store_reference.store_reference
            store = self.store_map.get(store_ref)
            store_config = self.store_config_map.get(store.store_reference if store else None)

            delivery_settings = self.delivery_settings_map.get((product.product_reference, store_ref)
                                                               ) or self.delivery_settings_map.get((None, store_ref))

            config_receive_orders = self.app_config.enable_app_orders

            checks = [
                config_receive_orders,
                getattr(store_config, 'enable_orders', False),
                getattr(store, 'is_verification_completed', False),
                getattr(store, 'is_active', False),
                getattr(store, 'open_for_order', False)
            ]

            is_deliverable = False
            if product and self.user_pincode:
                start_time = time.perf_counter()
                is_deliverable = self._is_deliverable(delivery_settings)
                end_time = time.perf_counter()
                logger.info(f"Deliverability check in {end_time - start_time:.4f} seconds")
                checks.append(is_deliverable)

            is_buy_enabled = all(checks)

            # Status message with logging
            message = None
            if not self.app_config.enable_app_orders:
                message = "Swadesic ordering is temporarily unavailable"
                logger.debug(f"[{product.product_reference}] App ordering disabled")
            elif not getattr(store_config, 'enable_orders', False):
                message = "Store is not accepting orders"
                logger.debug(f"[{product.product_reference}] Store config disallows orders")
            elif not getattr(store, 'is_verification_completed', False):
                message = "Store is not verified yet"
                logger.debug(f"[{product.product_reference}] Store not verified")
            elif not getattr(store, 'is_active', False):
                message = "Store is temporarily not active"
                logger.debug(f"[{product.product_reference}] Store inactive")
            elif not getattr(store, 'open_for_order', False):
                message = "Store is currently closed for orders"
                logger.debug(f"[{product.product_reference}] Store closed")
            elif not is_deliverable:
                message = "Not deliverable to your pincode"
                logger.debug(f"[{product.product_reference}] Undeliverable to {self.user_pincode}")

            disclaimer_message = ""
            if product.store_reference and (
                getattr(product.store_reference, 'store_supporters_count', 0) < 30 or
                getattr(product.store_reference, 'store_order_count', 0) < 5):
                    disclaimer_message = "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."


            results[product.product_reference] = {
                "deliverability": is_buy_enabled,
                "is_buy_enabled": is_buy_enabled,
                "product_status_message": message,
                "config_receive_orders": config_receive_orders,
                "disclaimer_message": disclaimer_message
            }
        end_time = time.perf_counter()
        logger.info(f"Buy status check in {end_time - start_time:.4f} seconds")
        return results

# class BuyStatusCheckerBatch:
#     def __init__(self, products, user_pincode):
#         self.products = products  # list of Product instances
#         self.user_pincode = user_pincode
#         self.app_config = get_app_config()
#         self.store_map = self._prepare_store_map()
#         self.store_config_map = self._prepare_store_config_map()
#         self.delivery_settings_map = self._prepare_delivery_settings_map()
#         self.delivery_location_pincode_map = self._prepare_delivery_location_pincode_map()

#     def _prepare_store_map(self):
#         store_refs = set(p.store_reference for p in self.products)
#         stores = Store.objects.in_bulk(store_refs)
#         return stores

#     def _prepare_store_config_map(self):
#         store_ids = list(self.store_map.keys())
#         configs = StoreConfig.objects.filter(store_id__in=store_ids)
#         return {c.store_id: c for c in configs}

#     def _prepare_delivery_settings_map(self):
#         product_store_keys = set((p.product_reference, p.store_reference_id) for p in self.products)
#         all_settings = DeliverySettings.objects.filter(
#             is_deleted=False,
#             store_reference_id__in=[k[1] for k in product_store_keys]
#         )
#         settings_map = {}
#         for ds in all_settings:
#             key = (ds.product_reference, ds.store_reference_id)
#             if key not in settings_map or ds.product_reference is not None:
#                 settings_map[key] = ds
#         return settings_map

#     def _prepare_delivery_location_pincode_map(self):
#         # One-time query for delivery locations
#         locations = DeliveryLocations.objects.all()
#         location_lookup = {
#             'states': defaultdict(list),
#             'cities': defaultdict(list),
#             'pincodes': set(),
#         }
#         for loc in locations:
#             state = loc.state.upper() if loc.state else None
#             city = loc.city.upper() if loc.city else None
#             if state:
#                 location_lookup['states'][state].append(loc.pincode)
#             if city:
#                 location_lookup['cities'][city].append(loc.pincode)
#             location_lookup['pincodes'].add(loc.pincode)
#         return location_lookup

#     def _convert_to_pincodes(self, delivery_locations_str):
#         locations = delivery_locations_str.split("|")
#         pincodes = set()
#         for loc in locations:
#             loc_upper = loc.upper()
#             if loc_upper in self.delivery_location_pincode_map["states"]:
#                 pincodes.update(self.delivery_location_pincode_map["states"][loc_upper])
#             elif loc_upper in self.delivery_location_pincode_map["cities"]:
#                 pincodes.update(self.delivery_location_pincode_map["cities"][loc_upper])
#             elif loc in self.delivery_location_pincode_map["pincodes"]:
#                 pincodes.add(loc)
#         return pincodes

#     def _is_deliverable(self, delivery_settings):
#         if not delivery_settings or not delivery_settings.delivery_locations:
#             return False
#         pincode_list = self._convert_to_pincodes(delivery_settings.delivery_locations)
#         return self.user_pincode in pincode_list

#     def get_buy_status_map(self):
#         results = {}

#         for product in self.products:
#             store = self.store_map.get(product.store_reference_id)
#             store_config = self.store_config_map.get(store.id if store else None)
#             delivery_settings = self.delivery_settings_map.get((product.product_reference, store.id if store else None)) \
#                                or self.delivery_settings_map.get((None, store.id if store else None))

#             checks = [
#                 self.app_config.enable_app_orders,
#                 store_config.enable_orders if store_config else False,
#                 store.is_verification_completed if store else False,
#                 store.is_active if store else False,
#                 store.open_for_order if store else False
#             ]

#             is_deliverable = False
#             if product and self.user_pincode:
#                 is_deliverable = self._is_deliverable(delivery_settings)
#                 checks.append(is_deliverable)

#             is_buy_enabled = all(checks)

#             # status message
#             if not self.app_config.enable_app_orders:
#                 message = "Swadesic ordering is temporarily unavailable"
#             elif not (store_config and store_config.enable_orders):
#                 message = "Store is not accepting orders"
#             elif not (store and store.is_verification_completed):
#                 message = "Store is not verified yet"
#             elif not (store and store.is_active):
#                 message = "Store is temporarily not active"
#             elif not (store and store.open_for_order):
#                 message = "Store is currently closed for orders"
#             elif not is_deliverable:
#                 message = "Not deliverable to your pincode"
#             else:
#                 message = None

#             results[product.product_reference] = {
#                 "is_buy_enabled": is_buy_enabled,
#                 "product_status_message": message,
#             }

#         return results


class BuyStatusChecker:
    def __init__(self, store, product=None, user_pincode=None):
        self.store = store if isinstance(store, Store) else Store.objects.get(store_reference=store)
        self.product = product
        self.user_pincode = user_pincode
        self.app_config = get_app_config()
        self.store_config = store.store_configurations.get()

    def get_deliverability(self, product_reference, store_reference, user_pincode):
        deliverability = False
        delivery_settings_instance = self.get_delivery_settings_instance(product_reference, store_reference)
        if delivery_settings_instance:
            deliverability = self.check_deliverability(delivery_settings_instance, user_pincode)
        return deliverability

    def get_delivery_settings_instance(self, product_reference, store_reference):
        if DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
        ).exists():
            return DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
            ).last()
        elif DeliverySettings.objects.filter(
                store_reference=store_reference, product_reference__isnull=True, is_deleted=False
        ).exists():
            return DeliverySettings.objects.filter(
                store_reference=store_reference, product_reference__isnull=True, is_deleted=False
            ).last()
        else:
            return None

    def check_deliverability(self, delivery_settings_instance, user_pincode):
        delivery_locations = delivery_settings_instance.delivery_locations
        if delivery_locations:
            list_of_pincode = self.convert_delivery_locations_to_pincode(delivery_locations)
            if user_pincode is None:
                    return False
            if user_pincode in list_of_pincode:
                return True

        return False

    def convert_delivery_locations_to_pincode(self, delivery_locations):
        queryset = DeliveryLocations.objects.all()
        all_states = set(queryset.values_list(Upper('state'), flat=True).distinct())
        all_cities = set(queryset.values_list(Upper('city'), flat=True).distinct())

        list_of_locations = delivery_locations.split("|")
        lst = []

        for elem in list_of_locations:
            elem_upper = elem.upper()
            if elem_upper in all_states:
                pincode = list(queryset.filter(state__iexact=elem).values_list("pincode", flat=True))
                lst.extend(pincode)
            elif elem_upper in all_cities:
                pincode = list(queryset.filter(city__iexact=elem).values_list("pincode", flat=True))
                lst.extend(pincode)
            else:
                lst.append(elem)

        return lst

    def is_buy_enabled(self):
        checks = [
            self.app_config.enable_app_orders,
            self.store_config.enable_orders,
            self.store.is_verification_completed,
            self.store.is_active,
            self.store.open_for_order
        ]

        if self.product and self.user_pincode:
            deliverability = self.get_deliverability(
                product_reference=self.product.product_reference,
                store_reference=self.store.store_reference,
                user_pincode=self.user_pincode)
            checks.append(deliverability)

        return all(checks)

    def get_status_message(self):
        if not self.app_config.enable_app_orders:
            return "Swadesic ordering is temporarily unavailable"
        elif not self.store_config.enable_orders:
            return "Store is not accepting orders"
        elif not self.store.is_verification_completed:
            return "Store is not verified yet"
        elif not self.store.is_active:
            return "Store is temporarily not active"
        elif not self.store.open_for_order:
            return "Store is currently closed for orders"
        elif self.product and self.user_pincode:
            deliverability = self.get_deliverability(
                product_reference=self.product.product_reference,
                store_reference=self.store.store_reference,
                user_pincode=self.user_pincode)
            if not deliverability:
                return "Not deliverable to your pincode"
        return None