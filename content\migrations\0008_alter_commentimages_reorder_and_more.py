# Generated by Django 4.2.7 on 2024-03-12 08:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("content", "0007_alter_commentimages_comment_image_id"),
    ]

    operations = [
        migrations.AlterField(
            model_name="commentimages",
            name="reorder",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="comments",
            name="comment_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="comments",
            name="like_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="comments",
            name="repost_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="comments",
            name="repost_plus_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="comments",
            name="save_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="comments",
            name="share_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="postimages",
            name="reorder",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="posts",
            name="comment_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="posts",
            name="like_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="posts",
            name="repost_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="posts",
            name="repost_plus_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="posts",
            name="save_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="posts",
            name="share_count",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
    ]
