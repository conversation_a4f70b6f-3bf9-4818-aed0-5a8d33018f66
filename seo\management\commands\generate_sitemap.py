from django.core.management.base import BaseCommand
from django.contrib.sitemaps import ping_google
from django.conf import settings
import requests


class Command(BaseCommand):
    help = 'Generate and submit sitemap to search engines'

    def add_arguments(self, parser):
        parser.add_argument(
            '--submit',
            action='store_true',
            help='Submit sitemap to search engines',
        )

    def handle(self, *args, **options):
        sitemap_url = f"{settings.SITE_DOMAIN}/seo/sitemap.xml"
        
        self.stdout.write(f"Sitemap URL: {sitemap_url}")
        
        # Test if sitemap is accessible
        try:
            response = requests.get(sitemap_url, timeout=10)
            if response.status_code == 200:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Sitemap is accessible at {sitemap_url}")
                )
                
                # Count URLs in sitemap
                url_count = response.text.count('<url>')
                self.stdout.write(f"📊 Sitemap contains {url_count} URLs")
                
            else:
                self.stdout.write(
                    self.style.ERROR(f"❌ Sitemap not accessible. Status: {response.status_code}")
                )
                return
                
        except requests.RequestException as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Error accessing sitemap: {e}")
            )
            return

        if options['submit']:
            try:
                # Ping Google
                ping_google(sitemap_url)
                self.stdout.write(
                    self.style.SUCCESS("✅ Sitemap submitted to Google")
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Error submitting to Google: {e}")
                )

        self.stdout.write("\n📋 Next steps:")
        self.stdout.write("1. Submit sitemap manually to Google Search Console")
        self.stdout.write("2. Submit sitemap manually to Bing Webmaster Tools")
        self.stdout.write(f"3. Sitemap URL: {sitemap_url}")
        self.stdout.write(f"4. Robots.txt URL: https://{settings.SITE_DOMAIN}/seo/robots.txt")
