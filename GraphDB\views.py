from django.db import transaction
from django.db.models import Max
from django.shortcuts import render
from django.shortcuts import get_object_or_404
from neomodel import db
import datetime
from datetime import datetime, timedelta, timezone
# from django.utils import timezone
import uuid
import time
import json
import random
import string
import logging
from django.http import Http404
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import Neo4jComment, Neo4jPost, Neo4jMedia, Neo4jStore, Neo4jContent, <PERSON>4j<PERSON>ser, <PERSON>4jUnr<PERSON>ser, Neo4jEntity, Neo4jProduct
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from .ignore import delete_unr_users

from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from users.user_api.models import User
from stores.store_api.models import Store
from products.models import Product
from content.models import Posts, PostImages, Comments, CommentImages, ReviewRequest ,Story
from content.serializers import SinglePostSerializer
from common.util.support_helper import compress
from .helpers import get_content_creator, check_review_access, check_if_review_exists, check_comment_access,check_question_access, check_store_review_access
from .queries import dbqueries, sqlqueries
import re
from django.core.validators import validate_email
from django.core.exceptions import ValidationError

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
# Create your views here.


class Neo4jPostsCreateView(APIView):

    def get_post_reference(self):
        while True:
            current_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
            random_number = "".join(random.choices(string.digits, k=4))
            post_reference = f"PO{current_time_str}{random_number}"
            if not Neo4jPost.nodes.filter(reference=post_reference).all():
                break
        return post_reference

    def post(self, request, *args, **kwargs):
        post_text = request.data.get('post_text')
        user_reference = request.data.get('user_reference')
        store_reference = request.data.get('store_reference')
        post_images = request.FILES.getlist('post_images')
        raw = request.data.get('tagged_references_json')
        tagged_references_json = json.loads(raw) if raw else []


        if not post_text and not post_images:
            return Response({"message": "Please enter at least one key value"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_or_store = Neo4jUser.nodes.get(reference=user_reference) \
                if user_reference else Neo4jStore.nodes.get(reference=store_reference)
        except (Neo4jUser.DoesNotExist, Neo4jStore.DoesNotExist):
            return Response({"message": f"User/Store with reference {user_reference or store_reference} does not exist."},
                            status=status.HTTP_404_NOT_FOUND)

        user = User.objects.get(user_reference=user_reference, deleted=False) if user_reference else None
        store = Store.objects.get(store_reference=store_reference, deleted=False) if store_reference else None

        with db.transaction:
            IST = timezone(timedelta(hours=5, minutes=30))
            new_post_reference = self.get_post_reference()
            creation_date = datetime.now(IST)
            neo4j_post = Neo4jPost(
                reference=new_post_reference,
                created_date=creation_date
                # Add other properties as needed
            )
            if post_text is not None:
                neo4j_post.post_text = post_text
            neo4j_post.save()
            post = Posts.objects.create(
                post_reference=new_post_reference,
                post_text=post_text,
                created_date=creation_date,
                user_reference=user,
                store_reference=store
            )

            media_properties = []

            logger.info(f"Neo4jPost node created successfully: {neo4j_post}")
            if post_images:
                for idx, image in enumerate(post_images):
                    image = compress(image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
                    file_path = 'social_post_images/'+image.name
                    file_path = default_storage.save(file_path, image)
                    file_url = default_storage.url(file_path)
                    media_id = str(uuid.uuid4()) 
                    media_properties.append({'media_id': media_id, 'media_type': 'IMAGE', 'media_path': file_url, 'order':idx + 1})
                    PostImages.objects.create(
                        post_reference=post,
                        post_image_id=media_id,
                        post_image=file_url,
                        reorder=idx+1
                    )
            media_node = Neo4jMedia(media=media_properties)
            media_node.save()
            neo4j_post.media.connect(media_node)

            if user_or_store:
                neo4j_post.posted_by.connect(user_or_store)

            if tagged_references_json:
                # Group references by type for batch processing
                entity_refs = []
                product_refs = []
                
                for tag_data in tagged_references_json:
                    reference = tag_data.get('reference')
                    tag_type = tag_data.get('type')
                    
                    if not reference or not tag_type:
                        continue
                        
                    if tag_type in ['USER', 'STORE']:
                        entity_refs.append(reference)
                    elif tag_type == 'PRODUCT':
                        product_refs.append(reference)
                
                # Batch query entities
                if entity_refs:
                    entities = Neo4jEntity.nodes.filter(reference__in=entity_refs)
                    for entity in entities:
                        neo4j_post.tagged_entity.connect(entity)
                
                # Batch query products  
                if product_refs:
                    products = Neo4jProduct.nodes.filter(reference__in=product_refs)
                    for product in products:
                        neo4j_post.tagged_product.connect(product)
                
                # Save the tagged_references_json to the Posts model
                post.tagged_references_json = tagged_references_json
                post.save()

        return Response(
            {
                "message": "success",
                "post": SinglePostSerializer(post).data
            }, status=status.HTTP_201_CREATED)


class Neo4jPostsUpdateView(APIView):

    def put(self, request, *args, **kwargs):
        post_reference = request.data.get('post_reference')
        new_post_text = request.data.get('post_text')
        new_images = request.FILES.getlist('post_images')
        raw = request.data.get('tagged_references_json')
        tagged_references_json = json.loads(raw) if raw else []

        try:
            neo4j_post = Neo4jPost.nodes.get(reference=post_reference)
        except Neo4jPost.DoesNotExist:
            return Response({"message": f"Post node with reference {post_reference} does not exist."},
                            status=status.HTTP_404_NOT_FOUND)
        try:
            post = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        except Posts.DoesNotExist:
            return Response({"message": f"Post Entry with reference {post_reference} does not exist."},
                            status=status.HTTP_404_NOT_FOUND)

        with db.transaction:
            if new_post_text:
                neo4j_post.post_text = new_post_text
                neo4j_post.save()

                post.post_text = new_post_text
                post.save()
            media_properties = []
            existing_images = PostImages.objects.filter(post_reference=post)
            highest_order = 0
            if existing_images.exists():
                highest_order = existing_images.aggregate(Max('reorder'))['reorder__max']

            if new_images:
                for idx, image in enumerate(new_images):
                    image = compress(image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
                    file_path = 'social_post_images/' + image.name
                    file_path = default_storage.save(file_path, image)
                    file_url = default_storage.url(file_path)
                    media_id = str(uuid.uuid4())
                    new_order = highest_order + idx + 1
                    media_properties.append({'media_id': media_id, 'media_type': 'IMAGE', 'media_path': file_url, 'order':new_order})

                    PostImages.objects.create(
                        post_reference=post,
                        post_image_id=media_id,
                        post_image=file_url,
                        reorder=new_order
                    )
            for media_node in neo4j_post.media.all():
                media_node.media.extend(media_properties)
                media_node.save()
            # If there are no existing media nodes, create a new one
            if not neo4j_post.media.all():
                media_node = Neo4jMedia(media=media_properties)
                media_node.save()
                neo4j_post.has_media.connect(media_node)

            if tagged_references_json:
                # Deduplicate tagged_references_json by 'reference'
                seen_references = set()
                deduplicated_json = []
                for tag in tagged_references_json:
                    ref = tag.get('reference')
                    if ref and ref not in seen_references:
                        seen_references.add(ref)
                        deduplicated_json.append(tag)

                # Now use the deduplicated list
                tagged_references_json = deduplicated_json
        
                neo4j_post.tagged_entity.disconnect_all()
                neo4j_post.tagged_product.disconnect_all()

                # Group references by type for batch processing
                entity_refs = []
                product_refs = []
                
                for tag_data in tagged_references_json:
                    reference = tag_data.get('reference')
                    tag_type = tag_data.get('type')
                    
                    if not reference or not tag_type:
                        continue
                            
                    if tag_type in ['USER', 'STORE']:
                        entity_refs.append(reference)
                    elif tag_type == 'PRODUCT':
                        product_refs.append(reference)
                
                # Batch query entities
                if entity_refs:
                    entities = Neo4jEntity.nodes.filter(reference__in=entity_refs)
                    for entity in entities:
                        neo4j_post.tagged_entity.connect(entity)
                                
                if product_refs:
                    products = Neo4jProduct.nodes.filter(reference__in=product_refs)
                    for product in products:
                        neo4j_post.tagged_product.connect(product)
            else:
                neo4j_post.tagged_entity.disconnect_all()
                neo4j_post.tagged_product.disconnect_all()

            post.tagged_references_json = tagged_references_json
            post.save()

        return Response({"message": "Post updated successfully", "post": SinglePostSerializer(post).data},
                        status=status.HTTP_200_OK)


class GetPostTaggedObjectDetails(APIView):
    def get(self, request, *args, **kwargs):
        post_reference = request.query_params.get('post_reference')
        tagged_object_details = sqlqueries().get_post_tagged_object_details(post_reference)
        return Response({"message": "success", "data": json.loads(tagged_object_details)}, status=status.HTTP_200_OK)


class AddCommentView(APIView):
    def get_comment_reference(self):
        while True:
            current_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
            random_number = "".join(random.choices(string.digits, k=4))
            comment_reference = f"CO{current_time_str}{random_number}"
            if not Neo4jComment.nodes.filter(reference=comment_reference).all():
                break
        return comment_reference

    def calculate_comment_level(self, parent_reference):
        if parent_reference.startswith('C'):
            parent_comment = Comments.objects.get(comment_reference=parent_reference)
            return parent_comment.level + 1
        else:
            return 1

    def validate_token(self, external_review_token,product_or_store_reference):
        if product_or_store_reference.startswith('P'):
            review_request = ReviewRequest.objects.filter(
                token=external_review_token, 
                product_reference=product_or_store_reference, 
                is_used=False).first()
        elif product_or_store_reference.startswith('S'):
            review_request = ReviewRequest.objects.filter(
                token=external_review_token, 
                store_reference=product_or_store_reference, 
                is_used=False).first()
        else:
            return Response({"error": "Invalid product or store reference"}, status=status.HTTP_400_BAD_REQUEST)
        if not review_request:
            return Response({"error": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST)

        IST = timezone(timedelta(hours=5, minutes=30))
        if review_request.expires_at.replace(tzinfo=IST) < datetime.now(IST):
            return Response({"error": "Token has expired"}, status=status.HTTP_400_BAD_REQUEST)
        
        self.review_request = review_request
        return True

    def validate_user_identifier(self, user_identifier):
        """
        Validate and categorize user identifier
        Returns: (type, normalized_identifier)
        """
        # User Reference
        if re.match(r'^U\d{13}$', user_identifier):
            return 'user_reference', user_identifier

        # Phone Number
        phone_match = re.match(r'^\+?(\d{12})$', user_identifier)
        if phone_match:
            # Normalize phone number: remove non-digit characters
            normalized_phone = re.sub(r'\D', '', user_identifier)
            return 'phone', '+' + normalized_phone

        # user Name
        if re.match(r'^[a-zA-Z0-9_.-]+$', user_identifier):
            return 'user_name', user_identifier

        # Email
        try:
            validate_email(user_identifier)
            return 'email', user_identifier.lower()
        except ValidationError:
            logger.warning(f"Invalid user identifier format: {user_identifier}")
            return None, None



    def post(self, request):
        parent_reference = request.data.get('parent_reference') #post or product or comment or story or store
        comment_text = request.data.get('comment_text')
        comment_images = request.FILES.getlist('comment_images')
        comment_type = request.data.get('comment_type')
        entity_reference = request.data.get('entity_reference')
        rating_count = float(request.data.get('rating_count'))

        #optional input fields for External review type Comments
        external_review_token = request.data.get('external_review_token')

        if comment_type == 'EXTERNAL_REVIEW':
            if not external_review_token:
                return Response({"message": "Please provide an external review token"}, status=status.HTTP_400_BAD_REQUEST)

        if not parent_reference.startswith(('C', 'PO', 'P', 'S')):
            raise ValueError("Invalid parent reference format")

        if parent_reference.startswith('C'):
            parent_type = 'COMMENT'
        elif parent_reference.startswith('PO'):
            parent_type = 'POST'
        elif parent_reference.startswith('P'):
            parent_type = 'PRODUCT'
        elif parent_reference.startswith('ST'):
            parent_type = 'STORY'
        elif parent_reference.startswith('S'):
            parent_type = 'STORE'

        main_parent_reference = parent_reference
        if parent_type == 'COMMENT':
            main_parent_reference = Comments.objects.get(comment_reference=parent_reference).main_parent_id

        if not comment_text and not comment_images:
            return Response({"message": "Please enter at least one key comment value"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_or_store = Neo4jUser.nodes.get(reference=entity_reference) \
                if entity_reference.startswith('U') else Neo4jStore.nodes.get(reference=entity_reference)
        except (Neo4jUser.DoesNotExist, Neo4jStore.DoesNotExist):
            return Response({"message": f"User/Store with reference {entity_reference} does not exist."},
                            status=status.HTTP_404_NOT_FOUND)

        if parent_reference.startswith('S'):
            neo4j_store = Neo4jStore.nodes.get(reference=parent_reference, is_deleted=False)
            parent_handle = neo4j_store.handle

            if user_or_store.handle == parent_handle:
                return Response({
                    "message": "You are not allowed to review your own store",
                    "is_custom": True
                },status=status.HTTP_400_BAD_REQUEST)
        else:
            neo4j_content = Neo4jContent.nodes.get(reference=parent_reference, is_deleted=False)
            parent_handle = dbqueries.get_content_creator_handle(parent_reference)

        if user_or_store.handle.startswith("test_") and user_or_store.handle != parent_handle:
            return Response({
                "message": "Test stores are not allowed to Comment",
                "is_custom": True
            },status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():

            # creating comment entry in RDB
            IST = timezone(timedelta(hours=5, minutes=30))
            new_comment_reference = self.get_comment_reference()
            comment_level = self.calculate_comment_level(parent_reference)
            creation_date = datetime.now(IST)

            if comment_type == 'REVIEW':
                if parent_type not in ['PRODUCT', 'STORE']:
                    raise ValueError("Invalid parent type for review")
                if parent_type == 'PRODUCT':
                    review_access, review_suborder_number = check_review_access(entity_reference=entity_reference, product_reference=parent_reference)
                    if not review_access:
                        is_previously_reviewed = check_if_review_exists(product_reference=parent_reference,
                                                                    user_reference=entity_reference,
                                                                    event_reference=review_suborder_number)
                        message = "You do not have access to review this product" if not is_previously_reviewed else "You have already reviewed this product",
                        return Response({
                        "message": message,
                        "is_custom": True
                    }, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        comment = Comments.objects.create(
                        comment_reference=new_comment_reference,
                        created_date=creation_date,
                        comment_text=comment_text,
                        main_parent_id=main_parent_reference,
                        parent_comment_id=parent_reference if parent_type == 'COMMENT' else None,
                        level=comment_level,
                        comment_type=comment_type,
                        rating_count=rating_count,
                        event_reference = review_suborder_number,
                        commenter_reference = entity_reference
                    )
                        
                if parent_type == 'STORE':
                    review_access = check_store_review_access(entity_reference=entity_reference, store_reference=parent_reference)
                    if not review_access:
                        return Response({
                        "message": "You do not have access to review this store",
                        "is_custom": True
                    }, status=status.HTTP_400_BAD_REQUEST)                   
                else:
                    comment = Comments.objects.create(
                        comment_reference=new_comment_reference,
                        created_date=creation_date,
                        comment_text=comment_text,
                        main_parent_id=main_parent_reference,
                        parent_comment_id=parent_reference if parent_type == 'COMMENT' else None,
                        level=comment_level,
                        comment_type=comment_type,
                        rating_count=rating_count,
                        commenter_reference = entity_reference
                    )


            elif comment_type == 'QUESTION':
                # check if parent_reference has proper product reference nomenclature
                if not parent_reference.startswith('P') and not parent_reference.startswith('ST'):
                    raise ValueError("Invalid Product reference format")

                # create comment object with question as comment type
                comment = Comments.objects.create(
                    comment_reference=new_comment_reference,
                    created_date=creation_date,
                    comment_text=comment_text,
                    main_parent_id=main_parent_reference,
                    parent_comment_id=parent_reference if parent_type == 'COMMENT' else None,
                    level=comment_level,
                    comment_type=comment_type,
                    commenter_reference=entity_reference
                )


            elif comment_type == 'EXTERNAL_REVIEW':
                # check if parent_reference has proper product reference nomenclature
                if parent_type not in ['PRODUCT', 'STORE']:
                    raise ValueError("Invalid parent type for review")

                if not external_review_token:
                    return Response({"message": "Please provide an external review token"}, status=status.HTTP_400_BAD_REQUEST)

                validate_token_response = self.validate_token(external_review_token=external_review_token, product_or_store_reference=parent_reference)

                if isinstance(validate_token_response, Response):
                    return validate_token_response
                elif validate_token_response:
                    # check if the identifier from review request matches with the commenter
                    user_identifier = self.review_request.user_identifier
                    identifier_type, normalized_identifier = self.validate_user_identifier(user_identifier)

                    # User lookup based on identifier type
                    user_query_kwargs = {
                        'deleted': False
                    }
                    if identifier_type == 'user_reference':
                        user_query_kwargs['user_reference'] = normalized_identifier
                    elif identifier_type == 'phone':
                        user_query_kwargs['phonenumber'] = normalized_identifier
                    elif identifier_type == 'user_name':
                        user_query_kwargs['user_name'] = normalized_identifier
                    else:
                        user_query_kwargs['email'] = normalized_identifier

                    user = User.objects.filter(**user_query_kwargs).first()

                    if not user:
                        logger.info(f"No user found for {identifier_type}: {normalized_identifier}")
                        return Response({
                            'error': f'No user found with the given {identifier_type}'
                        }, status=status.HTTP_404_NOT_FOUND)

                    # create comment object with question as comment type
                    comment = Comments.objects.create(
                        comment_reference=new_comment_reference,
                        commenter_reference=entity_reference,
                        created_date=creation_date,
                        comment_text=comment_text,
                        main_parent_id=main_parent_reference,
                        parent_comment_id=parent_reference if parent_type == 'COMMENT' else None,
                        level=comment_level,
                        comment_type=comment_type,
                        rating_count=rating_count,
                    )

                    self.review_request.is_used = True
                    self.review_request.save()


            else:
                comment = Comments.objects.create(
                    comment_reference=new_comment_reference,
                    commenter_reference=entity_reference,
                    created_date=creation_date,
                    comment_text=comment_text,
                    main_parent_id=main_parent_reference,
                    parent_comment_id=parent_reference if parent_type == 'COMMENT' else None,
                    level=comment_level,
                    comment_type=comment_type,
                    rating_count=rating_count,
                )

            # creating comment images entry in RDB
            if comment_images:
                for idx, image in enumerate(comment_images):
                    image = compress(image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
                    file_path = 'comment_images/'+image.name
                    file_path = default_storage.save(file_path, image)
                    file_url = default_storage.url(file_path)
                    media_id = str(uuid.uuid4())
                    CommentImages.objects.create(
                        comment_reference=comment,
                        comment_image_id=media_id,
                        comment_image=file_url,
                        reorder=idx+1
                    )

            # Creating nodes,  establishing relationships & updating RDB metrics counts
            neo4j_comment = Neo4jComment(
                reference=new_comment_reference,
                created_date=creation_date,
                level=comment_level,
                comment_type=comment_type
            )
            neo4j_comment.save()
            logger.info(f"Neo4jComment node and Entry created successfully: {neo4j_comment}")

            if neo4j_comment:
                if comment_type == 'EXTERNAL_REVIEW':
                    # For external reviews, add relationship attributes
                    if parent_type == 'STORE':
                        neo4j_store.has_comment.connect(neo4j_comment, {
                            'external_review_token': str(external_review_token),
                            'is_external_comment': True
                        })
                        user_or_store.comments.connect(neo4j_comment)
                    else:
                        neo4j_content.has_comment.connect(neo4j_comment, {
                            'external_review_token': str(external_review_token),
                            'is_external_comment': True
                        })
                        user_or_store.comments.connect(neo4j_comment)
                else:
                    # Normal comment connection without attributes
                    neo4j_content.has_comment.connect(neo4j_comment)
                    user_or_store.comments.connect(neo4j_comment)

            if parent_reference and parent_reference.startswith('C'):
                parent = Comments.objects.get(comment_reference=parent_reference, is_deleted=False)
                content_type = "comment"
            elif parent_reference and parent_reference.startswith('PO'):
                parent = Posts.objects.get(post_reference=parent_reference, is_deleted=False)
                content_type = "post"
            elif parent_reference and parent_reference.startswith('P'):
                parent = Product.objects.get(product_reference=parent_reference, deleted=False)
                store=parent.store_reference
                content_type = "product"
                if comment_type in ['EXTERNAL_REVIEW', 'REVIEW']:
                    parent.count_of_ratings += 1
                    parent.rating = (parent.rating * (parent.count_of_ratings - 1) + rating_count) / parent.count_of_ratings
                    store.store_product_review_count += 1
                    store.store_product_avg_rating = (store.store_product_avg_rating * (store.store_product_review_count - 1) + rating_count) / store.store_product_review_count
                    store.save()
                    parent.save()
            elif parent_reference and parent_reference.startswith('ST'):
                parent = Story.objects.get(story_reference=parent_reference, is_deleted=False)
                content_type = "story"
            elif parent_reference and parent_reference.startswith('S'):
                parent = Store.objects.get(store_reference=parent_reference, deleted=False)
                content_type = "store"
                if comment_type in ['EXTERNAL_REVIEW', 'REVIEW']:
                    parent.store_review_count += 1
                    parent.store_avg_rating = (parent.store_avg_rating * (parent.store_review_count - 1) + rating_count) / parent.store_review_count
                    parent.save()

            if not isinstance(parent, Store):
                parent.comment_count += 1
            else:
                parent.store_review_count += 1
            parent.save()

            parent_content_creator = get_content_creator(parent_reference)
            visitor_instance = User.objects.get(user_reference=entity_reference) if entity_reference.startswith('U') else Store.objects.get(store_reference=entity_reference)

            if parent_content_creator.reference != entity_reference:
                notification_handler = NotificationHandler(
                    notified_user=parent_content_creator.reference,
                    notification_type=Notifications.Notifications_Type.SOMEONE_COMMENTED_ON_CONTENT,
                    notification_about=new_comment_reference,
                    notified_from=user_or_store.handle,
                    image=visitor_instance.icon if visitor_instance.icon else None,
                    content_type=content_type
                )
                notification_handler.create_notification(notification_handler)

        return Response({"message": "Comment added successfully", "reference": f"{new_comment_reference}"}, status=status.HTTP_201_CREATED)


class UpdateCommentView(APIView):
    def post(self, request):
        comment_reference = request.data.get('comment_reference')
        updated_comment_text = request.data.get('comment_text')
        updated_comment_images = request.FILES.getlist('comment_images')
        updated_rating_count = request.data.get('rating_count')

        try:
            comment = Comments.objects.get(comment_reference=comment_reference)
        except Comments.DoesNotExist:
            return Response({"message": "Comment not found"}, status=status.HTTP_404_NOT_FOUND)

        with transaction.atomic():
            # Update comment text if provided
            if updated_comment_text:
                comment.comment_text = updated_comment_text

            existing_images = CommentImages.objects.filter(comment_reference=comment_reference)
            highest_order = 0
            if existing_images.exists():
                highest_order = existing_images.aggregate(Max('reorder'))['reorder__max']

            # Update comment images if provided
            if updated_comment_images:
                # Add updated comment images
                for idx, image in enumerate(updated_comment_images):
                    image = compress(image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
                    file_path = 'comment_images/'+image.name
                    file_path = default_storage.save(file_path, image)
                    file_url = default_storage.url(file_path)
                    media_id = str(uuid.uuid4())
                    new_order = highest_order + idx + 1
                    comment_image = CommentImages.objects.create(
                        comment_reference=comment,
                        comment_image_id=media_id,
                        comment_image=file_url,
                        reorder=new_order
                    )
                    comment_image.save()

            if updated_rating_count:
                comment.rating_count = updated_rating_count
            # Save the updated comment
            comment.save()
            if comment_type in ['EXTERNAL_REVIEW', 'REVIEW']:
                if comment.main_parent_id.startswith('P'):
                    parent = Product.objects.get(product_reference=comment.main_parent_id, deleted=False)
                    parent.rating = (parent.rating * (parent.count_of_ratings - 1) + updated_rating_count) / parent.count_of_ratings
                    parent.save()
                elif comment.main_parent_id.startswith('S'):
                    parent = Store.objects.get(store_reference=comment.main_parent_id, deleted=False)
                    parent.store_avg_rating = (parent.store_avg_rating * (parent.store_review_count - 1) + updated_rating_count) / parent.store_review_count
                    parent.save()
            
        return Response({"message": "Comment updated successfully","reference": f"{comment_reference}"}, status=status.HTTP_200_OK)


class DeleteCommentView(APIView):
    def post(self, request):
        comment_reference = request.data.get('comment_reference')

        try:
            comment = Comments.objects.get(comment_reference=comment_reference, is_deleted=False)
            neo4j_comment = Neo4jComment.nodes.get(reference=comment_reference, is_deleted=False)
        except Comments.DoesNotExist or Neo4jComment.DoesNotExist:
            return Response({"message": "Comment not found"}, status=status.HTTP_404_NOT_FOUND)

        with transaction.atomic():
            # Delete comment text and images but keep other references intact
            comment.comment_text = None
            comment.comment_images.all().delete()
            comment.is_deleted = True
            comment.save()

            neo4j_comment.comment_text = None
            neo4j_comment.is_deleted = True
            neo4j_comment.save()

            if comment.parent_comment_id:
                parent_comment = Comments.objects.get(comment_reference=comment.parent_comment_id)
                parent_comment.comment_count -= 1
                parent_comment.save()

            if not comment.parent_comment_id:
                #if there is no parent comment it means its a direct comment of a post or a product
                if comment.main_parent_id.startswith('PO'):
                    parent = Posts.objects.get(post_reference=comment.main_parent_id, is_deleted=False)
                elif comment.main_parent_id.startswith('P'):
                    parent = Product.objects.get(product_reference=comment.main_parent_id, deleted=False)
                    parent.count_of_ratings -= 1
                    parent.rating = (parent.rating * (parent.count_of_ratings - 1) + updated_rating_count) / parent.count_of_ratings
                    parent.save()
                elif comment.main_parent_id.startswith('ST'):
                    parent = Story.objects.get(story_reference=comment.main_parent_id, is_deleted=False)
                elif comment.main_parent_id.startswith('S'):
                    parent = Store.objects.get(store_reference=comment.main_parent_id, deleted=False)
                    parent.store_review_count -= 1
                    parent.store_avg_rating = (parent.store_avg_rating * (parent.store_review_count - 1) + updated_rating_count) / parent.store_review_count
                    parent.save()   
                parent.comment_count -= 1
                parent.save()
        return Response({"message": "Comment text and images deleted successfully"}, status=status.HTTP_200_OK)


class PostTagManagementView(APIView):
    """
    API for managing post tags - remove, reorder, and update tagged references
    """

    def remove_neo4j_relationship(self, neo4j_post, reference, tag_type):
        """Remove Neo4j relationship based on tag type"""
        try:
            if tag_type in ['USER', 'STORE']:
                entity = Neo4jEntity.nodes.get(reference=reference)
                neo4j_post.tagged_entity.disconnect(entity)
            elif tag_type == 'PRODUCT':
                product = Neo4jProduct.nodes.get(reference=reference)
                neo4j_post.tagged_product.disconnect(product)
            return True
        except (Neo4jEntity.DoesNotExist, Neo4jProduct.DoesNotExist):
            logger.warning(f"Failed to remove Neo4j relationship for {reference} of type {tag_type}")
            return False

    def create_neo4j_relationship(self, neo4j_post, reference, tag_type):
        """Create Neo4j relationship based on tag type"""
        try:
            if tag_type in ['USER', 'STORE']:
                entity = Neo4jEntity.nodes.get(reference=reference)
                neo4j_post.tagged_entity.connect(entity)
            elif tag_type == 'PRODUCT':
                product = Neo4jProduct.nodes.get(reference=reference)
                neo4j_post.tagged_product.connect(product)
            return True
        except (Neo4jEntity.DoesNotExist, Neo4jProduct.DoesNotExist):
            logger.warning(f"Failed to create Neo4j relationship for {reference} of type {tag_type}")
            return False

    def reorder_tags(self, tagged_references_json):
        """Reorder tags and update order values"""
        for i, tag in enumerate(tagged_references_json, 1):
            tag['order'] = i
        return tagged_references_json

    def post(self, request):
        """
        Handle tag operations: remove_tag, reorder_tags, update_tags
        """
        post_reference = request.data.get('post_reference')
        operation = request.data.get('operation')  # 'remove_tag', 'reorder_tags', 'update_tags'

        if not post_reference or not operation:
            return Response({"error": "post_reference and operation are required"},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            neo4j_post = Neo4jPost.nodes.get(reference=post_reference)
            post = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        except (Neo4jPost.DoesNotExist, Posts.DoesNotExist):
            return Response({"error": "Post not found"}, status=status.HTTP_404_NOT_FOUND)

        with db.transaction:
            if operation == 'remove_tag':
                return self.handle_remove_tag(request, neo4j_post, post)
            elif operation == 'reorder_tags':
                return self.handle_reorder_tags(request, neo4j_post, post)
            elif operation == 'update_tags':
                return self.handle_update_tags(request, neo4j_post, post)
            else:
                return Response({"error": "Invalid operation. Use 'remove_tag', 'reorder_tags', or 'update_tags'"},
                              status=status.HTTP_400_BAD_REQUEST)

    def handle_remove_tag(self, request, neo4j_post, post):
        """Remove a specific tag and reorder remaining tags"""
        tag_reference = request.data.get('tag_reference')

        if not tag_reference:
            return Response({"error": "tag_reference is required for remove_tag operation"},
                          status=status.HTTP_400_BAD_REQUEST)

        current_tags = post.tagged_references_json or []

        # Find and remove the tag
        tag_to_remove = None
        updated_tags = []

        for tag in current_tags:
            if tag.get('reference') == tag_reference:
                tag_to_remove = tag
            else:
                updated_tags.append(tag)

        if not tag_to_remove:
            return Response({"error": "Tag not found in post"}, status=status.HTTP_404_NOT_FOUND)

        # Remove Neo4j relationship
        self.remove_neo4j_relationship(neo4j_post, tag_to_remove['reference'], tag_to_remove['type'])

        # Reorder remaining tags
        updated_tags = self.reorder_tags(updated_tags)

        # Update Posts model
        post.tagged_references_json = updated_tags
        post.save()

        return Response({
            "message": "Tag removed successfully",
            "removed_tag": tag_to_remove,
            "updated_tags": updated_tags
        }, status=status.HTTP_200_OK)

    def handle_reorder_tags(self, request, neo4j_post, post):
        """Reorder existing tags based on new order"""
        new_order = request.data.get('new_order')  # List of references in new order

        if not new_order or not isinstance(new_order, list):
            return Response({"error": "new_order must be a list of tag references"},
                          status=status.HTTP_400_BAD_REQUEST)

        current_tags = post.tagged_references_json or []

        # Create a mapping of reference to tag data
        tag_map = {tag['reference']: tag for tag in current_tags}

        # Validate that all references in new_order exist in current tags
        for ref in new_order:
            if ref not in tag_map:
                return Response({"error": f"Tag reference {ref} not found in current tags"},
                              status=status.HTTP_400_BAD_REQUEST)

        # Validate that all current tags are included in new_order
        if set(new_order) != set(tag_map.keys()):
            return Response({"error": "new_order must include all current tag references"},
                          status=status.HTTP_400_BAD_REQUEST)

        # Reorder tags based on new_order
        reordered_tags = []
        for i, ref in enumerate(new_order, 1):
            tag = tag_map[ref].copy()
            tag['order'] = i
            reordered_tags.append(tag)

        # Update Posts model
        post.tagged_references_json = reordered_tags
        post.save()

        return Response({
            "message": "Tags reordered successfully",
            "updated_tags": reordered_tags
        }, status=status.HTTP_200_OK)

    def handle_update_tags(self, request, neo4j_post, post):
        """Update tags completely - remove old relationships and create new ones"""
        new_tagged_references = request.data.get('tagged_references_json')

        if not isinstance(new_tagged_references, list):
            return Response({"error": "tagged_references_json must be a list"},
                          status=status.HTTP_400_BAD_REQUEST)

        current_tags = post.tagged_references_json or []

        # Remove all existing Neo4j relationships
        for tag in current_tags:
            self.remove_neo4j_relationship(neo4j_post, tag['reference'], tag['type'])

        # Create new Neo4j relationships
        for tag_data in new_tagged_references:
            reference = tag_data.get('reference')
            tag_type = tag_data.get('type')

            if not reference or not tag_type:
                continue

            self.create_neo4j_relationship(neo4j_post, reference, tag_type)

        # Update Posts model
        post.tagged_references_json = new_tagged_references
        post.save()

        return Response({
            "message": "Tags updated successfully",
            "updated_tags": new_tagged_references
        }, status=status.HTTP_200_OK)


class ReviewAccessAPI(APIView):
    def post(self, request):
        entity_reference = request.data.get('entity_reference')
        product_reference = request.data.get('product_reference')

        if entity_reference == 'U9999999999999':
            data = {
                "review_access": False,
                "comment_access": True,
                "question_access": True
            }
        else:
            if not any([entity_reference, product_reference]):
                return Response({"error": "Missing required parameters"}, status=status.HTTP_400_BAD_REQUEST)

            review_access, _ = check_review_access(entity_reference, product_reference)

            comment_access = check_comment_access(entity_reference, product_reference)
            question_access = check_question_access(entity_reference, product_reference)
            data = {
                    "review_access": review_access, # access if not previously_reviewed else False,
                    "comment_access": comment_access,
                    "question_access": question_access}
        return Response(data, status=status.HTTP_200_OK)





#  these apis are only for testing scheduler tasks


class CreateFeedPostsView(APIView):
    def get(self, request):
        logger.info("entered create neo4j feedposts task")
        # Calculate the timestamp five minutes ago
        five_minutes_ago = datetime.now() - timedelta(minutes=5)
        logger.info(f"five minutes ago : {five_minutes_ago}")

        # Fetch new posts created in the last 5 minutes
        new_posts = Neo4jPost.nodes.filter(created_date__gte=five_minutes_ago, is_deleted=False)
        for neo4j_post in new_posts:
            post_creator = neo4j_post.get_post_creator()
            post_creator_followers = post_creator.get_followers()
            for follower in post_creator_followers:
                # Create an instance of Neo4jFeed
                follower.add_post_to_feed(post=neo4j_post, seen_status=False, category='CONNECTION')

        return Response({"message": "Post updated successfully"},
                        status=status.HTTP_200_OK)


class AutoFollowView(APIView):

    def get(self, request):
        logger.info("Entered autofollow_entities function")

        # Step 1: Fetch newly created Neo4jUser nodes and extract their phone numbers
        five_minutes_ago = datetime.now() - timedelta(minutes=5)
        five_minutes_ago_unix = int(five_minutes_ago.timestamp())
        logger.info(f"five minutes ago : {five_minutes_ago_unix}, datetime now {datetime.now().timestamp()}")

        # Constructing the Cypher query
        cypher_query = '''
                   MATCH (u:Neo4jUser) 
                   WHERE u.created_date >= $five_minutes_ago 
                   RETURN u
               '''
        parameters = {
            "five_minutes_ago": five_minutes_ago_unix
        }
        logger.info("cyper query to fetch new users started")

        # Execute the Cypher query
        result, _ = db.cypher_query(cypher_query, parameters)

        if result:
            # Extracting the nodes from the result
            new_users = [Neo4jUser.inflate(row[0]) for row in result]
        else:
            # Handle the case where no new users were found
            new_users = []
        logger.info("No new users found in the last 5 minutes.")
        logger.info(f"New users in last 5 min: {list(new_users)}. count = {len(new_users)}")

        # Step 2: Fetch Neo4jUnrUser nodes with the extracted phone numbers
        for user in new_users:
            phonenumber = user.phonenumber
            try:
                unr_user = Neo4jUnrUser.nodes.get(phonenumber=phonenumber)

                following_entities = []

                # Step 3: Get previous 'follows' relationships
                if unr_user:
                    following_entities.extend(unr_user.followed_by.all())

                # Step 4: Create 'follow' relationships and handle notifications
                if following_entities:
                    for entity in following_entities:
                        entity.follow(user)
                        notified_to = user.reference
                        image = entity.icon if entity.icon else None
                        notify_about = entity.reference
                        notification_type = Notifications.Notifications_Type.SOMEONE_FOLLOWED_USER

                        try:
                            notification_handler = NotificationHandler(
                                notified_user=notified_to,
                                notification_type=notification_type,
                                notification_about=notify_about,
                                image=image,
                            )
                            notification_handler.create_notification(notification_handler)
                            logger.info(f"'someone followed user' notification sent!!")
                        except Exception as e:
                            logger.error(f"'someone followed user' notification failed: {str(e)}")

                unr_user.delete()
                logger.info(
                    f"Relationships of Unregistered User with phone number {phonenumber} successfully transferred its user node.")

            except Neo4jUnrUser.DoesNotExist:
                logger.warning(f"Neo4jUnrUser with phone number {phonenumber} does not exist.")


class DeleteUnrUser(APIView):
    def get(self,request,):
        api = delete_unr_users()
        if api:
            return Response(
            {
                "message": f"successfully deleted {api} unr users"
            }, status=status.HTTP_200_OK)
        else:
            return Response(
                {
                    "message": "no unr users present"
                }, status=status.HTTP_200_OK)
