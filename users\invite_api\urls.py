from django.urls import path
from .views import (
    CreateInviteCode,
    UpdateInviteBalance,
    ApplyInviteCode,
    CreateInviteCodeDetails,
    GetAllInvitesCreatedByOneUser,
    ListOfUserContacts,
    GetUserRewardData,
    GetUserRewardsHistory,
    GetInvitedCounts,
    GetInviteCode,
    GetInviteRewardsInfo,
    GetUserAffiliateBalanceDetails,
)


urlpatterns = [
    path("create_invite_code/", CreateInviteCode.as_view(), name="create-invite-code"),
    path(
            "create_invite_code_details/<str:invite_code>/",
            CreateInviteCodeDetails.as_view(),
            name="create-invite-code-details",
        ),
    path("apply_invite_code/", ApplyInviteCode.as_view(), name="apply-invite-code"),
    path(
            "update_invite_balance/",
            UpdateInviteBalance.as_view(),
            name="update-invite-balance",
        ),
    path(
            "list_of_invites_sent/<int:user_id>/",
            GetAllInvitesCreatedByOneUser.as_view({"get": "list"}),
            name="list-of-invites-sent",
        ),
    path("list_of_contact/", ListOfUserContacts.as_view(), name="list-of-contacts"),
    path("reward_data/", GetUserRewardData.as_view(), name="get-user-reward-data"),
    path("rewards_history/", GetUserRewardsHistory.as_view(), name="get-user-rewards-history"),
    path("invited_counts/", GetInvitedCounts.as_view(), name="get-user-invited-counts"),
    path("get_invite_code/", GetInviteCode.as_view(), name="get-invite-code"),
    path("get_invite_rewards_info/", GetInviteRewardsInfo.as_view(), name="get-invite-rewards-info"),
    path("get_user_affiliate_balance/", GetUserAffiliateBalanceDetails.as_view(), name="get-user-affiliate-balance"),
]
