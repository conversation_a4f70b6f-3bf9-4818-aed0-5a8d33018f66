from stores.store_api.models import StoreConfig
from stores.store_settings_api.models import TrustCenter, RefundAndWarranty, DeliverySettings
import time
from GraphDB.queries import dbqueries
from general.views import BuyStatusCheckerBatch
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def prepare_context_data(store_references, products, visitor_reference=None, user_pincode=None, get_neo4j_interactions = True):
    product_references = [p.product_reference for p in products]

    # 1. Trust Centers
    start_trust = time.perf_counter()
    trust_centers = {
        tc.store_reference.store_reference: tc
        for tc in TrustCenter.objects.filter(store_reference__in=store_references)
        if tc.store_reference and tc.store_reference.store_reference
    }
    logger.info(f"Trust center fetch in {time.perf_counter() - start_trust:.4f} seconds")

    # 2. Refund & Warranty
    start_return = time.perf_counter()
    refund_warranties = {}
    product_rs = RefundAndWarranty.objects.filter(product_reference__in=product_references, is_deleted=False)
    store_rs = RefundAndWarranty.objects.filter(store_reference__in=store_references, product_reference__isnull=True, is_deleted=False)

    for rs in product_rs:
        refund_warranties[rs.product_reference] = rs
    for rs in store_rs:
        if rs.store_reference and rs.store_reference.store_reference:
            refund_warranties[rs.store_reference.store_reference] = rs
    logger.info(f"Return settings fetch in {time.perf_counter() - start_return:.4f} seconds")

    # 3. Delivery Settings
    start_delivery = time.perf_counter()
    delivery_settings = {}
    product_ds = DeliverySettings.objects.filter(product_reference__in=product_references, is_deleted=False)
    store_ds = DeliverySettings.objects.filter(store_reference__in=store_references, product_reference__isnull=True, is_deleted=False)

    for ds in product_ds:
        delivery_settings[ds.product_reference] = ds
    for ds in store_ds:
        if ds.store_reference and ds.store_reference.store_reference:
            delivery_settings[ds.store_reference.store_reference] = ds
    logger.info(f"Delivery settings fetch in {time.perf_counter() - start_delivery:.4f} seconds")

    # 4. Store Configs
    start_config = time.perf_counter()
    store_configs = {
        sc.store_reference.store_reference: sc
        for sc in StoreConfig.objects.filter(store_reference__in=store_references)
        if sc.store_reference and sc.store_reference.store_reference
    }
    logger.info(f"Store config fetch in {time.perf_counter() - start_config:.4f} seconds")

    # 5. Neo4j Interaction Data
    if get_neo4j_interactions:
        neo4j_data = {}
        if visitor_reference:
            start_neo4j = time.perf_counter()
            try:
                neo4j_data = dbqueries.get_batched_interaction_details(visitor_reference, product_references)
            except Exception as e:
                logger.error(f"Error in batch Neo4j processing: {str(e)}")
            logger.info(f"Neo4j fetch in {time.perf_counter() - start_neo4j:.4f} seconds")
    else: 
        neo4j_data = {}
        
    # 6. Buy Status Check
    buy_status_data = {}
    if user_pincode:
        start_buy = time.perf_counter()
        try:
            checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
            buy_status_data = checker.get_buy_status_map()
        except Exception as e:
            logger.error(f"Error in batch buy status processing: {str(e)}")
        logger.info(f"Buy status check in {time.perf_counter() - start_buy:.4f} seconds")

    # Final context
    return {
        'trust_centers': trust_centers,
        'refund_warranties': refund_warranties,
        'delivery_settings': delivery_settings,
        'store_configs': store_configs,
        'neo4j_data': neo4j_data,
        'buy_status_data': buy_status_data,
    }