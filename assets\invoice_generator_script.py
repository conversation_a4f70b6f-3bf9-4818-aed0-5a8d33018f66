import base64
from jinja2 import Environment, BaseLoader, TemplateSyntaxError, Template
from xhtml2pdf import pisa
from io import BytesIO
import requests
import os

"""     
        invoice_data = {
            'storehandle': '@swadesic_store',
            'supplied_company_name': 'Organic Supplier Ltd.',
            'supplied_address': '123 Organic Farms,\nGreenfield Lane,\nNatural City, NC 12345',
            'company_name': 'Store Pvt Ltd.',
            'registered_address': '456 Market Street,\nTech Park,\nInnovation Hub,\nBengaluru,\nKarnataka, 560001',
            'pan_no': '**********',
            'gstin_no': '29**********1Z1',
            'buyer_name': '<PERSON><PERSON>',
            'billing_address': '789 Residential Area,\nSunshine Apartments,\nFlat 12B,\nHyderabad,\nTelangana, 500032',
            'shipping_address': '789 Residential Area,\nSunshine Apartments,\nFlat 12B,\nHyderabad,\nTelangana, 500032',
            'phone_number': '+91 98765-43210',
            'order_number': 'ORD67890',
            'order_date': '2023-07-25',
            'invoice_number': 'INV67890',
            'invoice_details': 'Invoice for organic products purchased on 2023-07-25',
            'invoice_date': '2023-07-25',
            'products': [
                {'brand': 'Organic Farms', 'name': 'Organic Apples', 'unit_price': 200, 'quantity': 3, 'total': 600},
                {'brand': 'Eco Goods', 'name': 'Bamboo Toothbrush', 'unit_price': 50, 'quantity': 5, 'total': 250},
                {'brand': 'Green Life', 'name': 'Reusable Shopping Bag', 'unit_price': 100, 'quantity': 2, 'total': 200},
                {'brand': 'Eco Goods', 'name': 'Stainless Steel Straw', 'unit_price': 150, 'quantity': 2, 'total': 300},
            ],
            'sub_total': '1350.00',
            'shipping_fee': '50.00',
            'total_amount_paid': '1400.00',
            'amount_in_words': 'One Thousand Four Hundred Only',
            'store_contact_name': 'Amit Sharma',
            'store_phone': '+91 91234-56789',
            'store_email': '<EMAIL>'
        }
        """
class InvoiceGenerator:
    def __init__(self):

        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.template_file = os.path.join(current_dir, 'invoice-template.html')
        self.logo_file = os.path.join(current_dir, 'swadesic_logo.png')

    def create_invoice_html(self, invoice_data):
        try:
            with open(self.template_file, 'r', encoding='utf-8') as file:
                template_content = file.read()
            
            template = Template(template_content)
            
            # Generate product rows
            product_rows = []
            for i, product in enumerate(invoice_data['products'], start=1):
                product_rows.append({
                    'index': i,
                    'brand': product['brand'],
                    'name': product['name'],
                    'unit_price': product['unit_price'],
                    'quantity': product['quantity'],
                    'total': product['total']
                })

            # Render the template
            html_output = template.render(
                logo=self.logo_file,
                signature_image=invoice_data["store_signature"],
                storehandle=invoice_data['storehandle'],
                supplied_company_name=invoice_data['supplied_company_name'],
                supplied_address=invoice_data['supplied_address'],
                company_name=invoice_data['company_name'],
                registered_address=invoice_data['registered_address'],
                pan_no=invoice_data['pan_number'],
                gstin_no=invoice_data['gst_number'],
                buyer_name=invoice_data.get('buyer_name', ''),  # This field is not in the provided list, using get() with default
                billing_address=invoice_data['billing_address'],
                shipping_address=invoice_data['shipping_address'],
                phone_number=invoice_data.get('phone_number', ''),  # This field is not in the provided list, using get() with default
                order_number=invoice_data['order_number'],
                order_date=invoice_data['order_date'],
                invoice_number=invoice_data.get('invoice_number', ''),  # This field is not in the provided list, using get() with default
                invoice_details=invoice_data.get('invoice_details', ''),  # This field is not in the provided list, using get() with default
                invoice_date=invoice_data.get('invoice_date', ''),  # This field is not in the provided list, using get() with default
                product_rows=product_rows,
                sub_total=invoice_data['sub_total'],
                shipping_fee=invoice_data['shipping_fee'],
                refund_handling_fee=invoice_data['refund_handling_fee'],
                total_amount_paid=invoice_data['total_amount_paid'],
                amount_in_words=invoice_data['amount_in_words'],
                store_contact_name=invoice_data['store_contact_name'],
                store_phone=invoice_data['store_phone'],
                store_email=invoice_data['store_email'],
                delivery_person_name=invoice_data['delivery_person_name'],
                delivery_person_contact=invoice_data['delivery_person_contact'],
                store_address=invoice_data['store_address'],
                category_name=invoice_data['category_name']
            )
            return html_output
        except TemplateSyntaxError as e:
            error_message = f"Template syntax error: {str(e)}"
            print(error_message)
            raise ValueError(f"Failed to parse invoice template: {error_message}")
        except Exception as e:
            error_message = f"Unexpected error in create_invoice_html: {str(e)}"
            print(error_message)
            raise ValueError(f"Failed to create invoice HTML: {error_message}")

    def generate_pdf_with_xhtml2pdf(self, html_content, output_filename):
        # Create a file-like buffer to receive PDF data
        pdf_buffer = BytesIO()

        # Convert HTML to PDF
        pisa_status = pisa.CreatePDF(
            html_content,                # the HTML to convert
            dest=pdf_buffer,             # file handle to receive result
            encoding='utf-8'
        )

        # If successful, write the PDF to a file
        if not pisa_status.err:
            with open(output_filename, "wb") as f:
                pdf_buffer.seek(0)
                f.write(pdf_buffer.getvalue())
            print(f"PDF has been generated and saved as '{output_filename}'")
        else:
            print("Error: PDF generation failed")

    def get_invoice_html_from_api(self, api_url, invoice_data):
        try:
            response = requests.post(api_url, json=invoice_data)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"Error fetching invoice HTML from API: {e}")
            return None
