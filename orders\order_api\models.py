from django.db import models
import datetime
from stores.store_api.models import Store
from users.user_api.models import User
from products.models import Product
from ..payout_api.models import OrderPayout
from common.util.support_helper import DeleteManager
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Create your models here.


def create_order_number():
    now = datetime.datetime.now()
    today = datetime.date.today()
    count = Order.objects.filter(date=today).count() + 1
    code = now.strftime("%y%m%d%H%M%S")
    digit = str(count).zfill(4)
    my_code = ("O", code, digit)
    return "".join(my_code)


class Order(models.Model):
    """"
    This table holds information for orders at store level with unique order_number for each store, order_request_number is the parent for individual order_number
    """
    class Order_Status(models.TextChoices):
        ORDER_INITIATED = "ORDER_INITIATED", _("Order initiated")
        PAYMENT_INITIATED = "PAYMENT_INITIATED", _("Payment initiated")
        PAYMENT_SUCCESS = "PAYMENT_SUCCESS", _("Payment success")
        PAYMENT_PENDING = "PAYMENT_PENDING", _("Payment pending")
        PAYMENT_FAILED = "PAYMENT_FAILED", _("Payment failed")
        ORDER_CANCELLED = "ORDER_CANCELLED", _("Order cancelled")

    class SwadesicFeeStatus(models.TextChoices):
        GIVEN = "GIVEN", _("Given")
        TAKEN = "TAKEN", _("Taken")
        UNPROCESSED = "UNPROCESSED", _("Unprocessed")

    orderid = models.AutoField(primary_key=True)
    order_number = models.CharField(max_length=20, unique=True)
    userid = models.CharField(max_length=20, null=True, blank=True)
    # user_reference = models.CharField(max_length=20, null=True, blank=True)
    user_reference = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        max_length=20,
        null=True,
        blank=True,
        related_name="customer_name",
        to_field="user_reference",
        db_column="user_reference",
    )
    storeid = models.CharField(max_length=20, null=True, blank=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        on_delete=models.DO_NOTHING,
        to_field="store_reference",
        related_name="store",
        db_column="store_reference",
        null=True,
        blank=True,
    )
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now=True)
    date = models.DateField(default=datetime.date.today)
    order_status = models.CharField(
        max_length=30,
        choices=Order_Status.choices,
        default=Order_Status.ORDER_INITIATED,
    )
    is_deleted = models.BooleanField(default=False)
    cancelled_by = models.CharField(max_length=500, null=True, blank=True) # how aggregation happens?
    cancellation_reason = models.CharField(max_length=500, null=True, blank=True) # how aggregation happens?
    delivery_note = models.CharField(max_length=5000, null=True, blank=True)
    seller_note = models.CharField(max_length=5000, null=True, blank=True)
    order_request_number = models.CharField(max_length=500, null=True)
    billing_address_id = models.ForeignKey(
        "users.UserAddress",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="user_addresses",
        db_column="billing_address_id",
    )
    order_phone_number = models.CharField(max_length=15, null=True, blank=True)
    # product amount is price x quantity, product order amount is product amount + product delivery fee, order amount is product order amount + product delivery fee + store delivery fee
    total_product_amount = models.IntegerField(null=True, default=0) # product amounts
    total_delivery_fee = models.IntegerField(null=True, default=0) # product delivery fees + store delivery fee
    store_delivery_fee = models.IntegerField(null=True, default=0)
    total_order_amount = models.IntegerField(null=True, default=0) # product amounts + product delivery fees + store delivery fee

    txn_token = models.CharField(max_length=50, null=True)
    transaction_id = models.CharField(max_length=100, null=True)
    bank_transaction_id = models.CharField(max_length=100, null=True)
    payment_mode = models.CharField(max_length=50, null=True)
    payment_channel = models.CharField(max_length=30, null=True)
    transaction_date = models.CharField(max_length=50, null=True)

    transaction_fee = models.FloatField(null=True, default=0)
    transaction_fee_tax = models.FloatField(null=True, default=0)
    pg_transctionfee_with_tax_perc = models.FloatField(null=True, default=0)
    pg_transctionfee_perc = models.FloatField(null=True, default=0)
    pg_gst_tax_perc = models.FloatField(null=True, default=0)
    razorpay_payment_id = models.CharField(max_length=50, null=True)

    expected_swadesic_fee = models.IntegerField(null=True, default=0)
    swadesic_fee = models.IntegerField(null=True, default=0)
    swadesic_fee_status = models.CharField(max_length=25, choices=SwadesicFeeStatus.choices,
            null=True,
            blank=True
        )
    order_delivery_pincode = models.CharField(max_length=10, null=True, blank=True)
    objects = models.Manager()  # The default manager.
    new_objects = DeleteManager()  # delete manager.

    @property
    def orderitems(self):
        return self.suborderitems

    @property
    def order_items(self):
        return self.suborderitems

    def save(self, *args, **kwargs):
        if self.orderid is None:
            self.order_number = create_order_number()
            store = Store.objects.get(storeid=self.storeid)
            self.store_reference = store
            user = User.objects.get(userid=self.userid)
            self.user_reference = user
        super(Order, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "order"
        db_table = '"order"."order"'


class ShippingHistory(models.Model):
    shipping_update_id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=225, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    order_number = models.ForeignKey(
        "orders.Order",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="order_number",
        db_column="order_number",
    )
    shipping_reference = models.CharField(max_length=50)
    created_date = models.DateTimeField(auto_now_add=True)
    is_editable = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "shipping updates"
        db_table = '"order"."shipping_updates"'


class SubOrder(models.Model):
    class Cancelled_By(models.TextChoices):
        BUYER = "buyer", _("buyer")
        SELLER = "seller", _("seller")
        AUTO_CANCELLED = "auto_cancelled", _("auto_cancelled")

    class Suborder_Status(models.TextChoices):

        ORDER_INITIATED = "ORDER_INITIATED", _("Order initiated")
        PAYMENT_INITIATED = "PAYMENT_INITIATED", _("Payment initiated")
        PAYMENT_SUCCESS = "PAYMENT_SUCCESS", _("Payment success")
        WAITING_FOR_CONFIRMATION = "WAITING_FOR_CONFIRMATION", _(
            "waiting for confirmation"
        )
        PAYMENT_PENDING = "PAYMENT_PENDING", _("Payment pending")
        PAYMENT_FAILED = "PAYMENT_FAILED", _("Payment failed")
        ORDER_CONFIRMED = "ORDER_CONFIRMED", _("Order confirmed")
        SCHEDULED_FOR_PICKUP = "SCHEDULED_FOR_PICKUP", _("Scheduled for pickup")
        DELIVERY_IN_PROGRESS = "DELIVERY_IN_PROGRESS", _("Delivery in progress")
        ORDER_DELIVERED = "ORDER_DELIVERED", _("Order delivered")
        ORDER_CANCELLED = "ORDER_CANCELLED", _("Order cancelled")
        ORDER_CANCELLED_BY_BUYER = "ORDER_CANCELLED_BY_BUYER", _(
            "Order cancelled by buyer"
        )
        ORDER_CANCELLED_BY_SELLER = "ORDER_CANCELLED_BY_SELLER", _(
            "Order cancelled by seller"
        )
        CANCELLED_IN_TRANSIT = "CANCELLED_IN_TRANSIT", _("Cancelled in transit")
        DELIVERY_FAILED = "DELIVERY_FAILED", _("Delivery failed")
        RETURN_REQUESTED = "RETURN_REQUESTED", _("Return requested")
        RETURN_REQUEST_CANCELLED = "RETURN_REQUEST_CANCELLED", _("Return request cancelled")
        RETURN_CONFIRMED = "RETURN_CONFIRMED", _("Return confirmed")
        RETURN_INITIATED = "RETURN_INITIATED", _("Return initiated")
        RETURN_IN_PROGRESS = "RETURN_IN_PROGRESS", _("Return in progress")
        RETURNED_TO_SELLER = "RETURNED_TO_SELLER", _("Return to seller")
        RETURN_FAILED = "RETURN_FAILED", _("Return failed")
        REFUND_HOLD = "REFUND_HOLD", _("Refund hold")
        REFUNDED = "REFUNDED", _("Refunded")
        ORDER_AUTO_CANCELLED = "ORDER_AUTO_CANCELLED", _("Order auto cancelled")

    class Secondary_Suborder_Status(models.TextChoices):

        ORDER_INITIATED = "ORDER_INITIATED", _("Order initiated")
        PAYMENT_INITIATED = "PAYMENT_INITIATED", _("Payment initiated")
        PAYMENT_SUCCESS = "PAYMENT_SUCCESS", _("Payment success")
        WAITING_FOR_CONFIRMATION = "WAITING_FOR_CONFIRMATION", _(
            "waiting for confirmation"
        )
        PAYMENT_PENDING = "PAYMENT_PENDING", _("Payment pending")
        PAYMENT_FAILED = "PAYMENT_FAILED", _("Payment failed")
        ORDER_CONFIRMED = "ORDER_CONFIRMED", _("Order confirmed")
        SCHEDULED_FOR_PICKUP = "SCHEDULED_FOR_PICKUP", _("Scheduled for pickup")
        DELIVERY_IN_PROGRESS = "DELIVERY_IN_PROGRESS", _("Delivery in progress")
        ORDER_DELIVERED = "ORDER_DELIVERED", _("Order delivered")
        ORDER_CANCELLED = "ORDER_CANCELLED", _("Order cancelled")
        ORDER_CANCELLED_BY_BUYER = "ORDER_CANCELLED_BY_BUYER", _(
            "Order cancelled by buyer"
        )
        ORDER_CANCELLED_BY_SELLER = "ORDER_CANCELLED_BY_SELLER", _(
            "Order cancelled by seller"
        )
        CANCELLED_IN_TRANSIT = "CANCELLED_IN_TRANSIT", _("Cancelled in transit")
        DELIVERY_FAILED = "DELIVERY_FAILED", _("Delivery failed")
        RETURN_REQUESTED = "RETURN_REQUESTED", _("Return requested")
        RETURN_REQUEST_CANCELLED = "RETURN_REQUEST_CANCELLED", _("Return request cancelled")
        RETURN_CONFIRMED = "RETURN_CONFIRMED", _("Return confirmed")
        RETURN_IN_PROGRESS = "RETURN_IN_PROGRESS", _("Return in progress")
        RETURNED_TO_SELLER = "RETURNED_TO_SELLER", _("Return to seller")
        RETURN_FAILED = "RETURN_FAILED", _("Return failed")
        REFUND_HOLD = "REFUND_HOLD", _("Refund hold")
        REFUNDED = "REFUNDED", _("Refunded")
        ORDER_AUTO_CANCELLED = "ORDER_AUTO_CANCELLED", _("Order auto cancelled")

    suborderid = models.AutoField(primary_key=True)
    orderid = models.ForeignKey(
        Order, on_delete=models.CASCADE, related_name="suborderitems", db_column="orderid"
    )
    order_number = models.CharField(max_length=50)
    suborder_number = models.CharField(max_length=50, unique=True)
    package_number = models.CharField(max_length=50, null=True, blank=True)
    display_package_number = models.CharField(max_length=50, null=True, blank=True)

    user_reference = models.CharField(max_length=20, null=True, blank=True)
    storeid = models.CharField(max_length=50, null=True, blank=True)
    store_reference = models.CharField(max_length=50, null=True, blank=True)
    store_name = models.CharField(max_length=500, null=True, blank=True)
    store_category = models.CharField(max_length=500, null=True, blank=True)
    store_description = models.CharField(max_length=5000, null=True, blank=True)
    store_image = models.ImageField(upload_to="store_image_cart", null=True, blank=True)

    trust_score = models.CharField(max_length=500, null=True, blank=True)
    seller_level = models.CharField(max_length=500, null=True, blank=True)
    return_and_warranty_description = models.CharField(
        max_length=255, null=True, blank=True
    )

    productid = models.IntegerField(null=True, blank=True)
    product_reference = models.CharField(max_length=5000, null=True, blank=True)
    product_name = models.CharField(max_length=5000, null=True, blank=True)
    product_description = models.CharField(max_length=5000, null=True, blank=True)
    product_brand = models.CharField(max_length=500, null=True, blank=True)
    product_image = models.ImageField(
        upload_to="product_image_cart", null=True, blank=True
    )
    product_quantity = models.PositiveIntegerField()
    mrp_price = models.PositiveIntegerField()
    selling_price = models.PositiveIntegerField()
    product_version = models.CharField(max_length=50, null=True, blank=True)

    suborder_status = models.CharField(
        max_length=50,
        choices=Suborder_Status.choices,
        default=Suborder_Status.ORDER_INITIATED,
    )
    secondary_suborder_status = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        choices=Secondary_Suborder_Status.choices,
    )

    is_deleted = models.BooleanField(default=False)
    cancelled_by = models.CharField(
        max_length=50, choices=Cancelled_By.choices, null=True, blank=True
    )
    cancellation_reason = models.CharField(max_length=500, null=True, blank=True)

    confirmation_date = models.CharField(max_length=100, null=True, blank=True)
    cancelled_date = models.CharField(max_length=100, null=True, blank=True)
    delivered_date = models.CharField(max_length=100, null=True, blank=True)
    estimated_delivery_date = models.CharField(max_length=100, null=True, blank=True)
    
    # self delivery 
    self_delivery_by_store = models.BooleanField(default=False)
    delivery_person_name = models.CharField(max_length=200, null=True, blank=True)
    delivery_person_contact = models.CharField(max_length=20, null=True, blank=True)

    # logistic partner
    delivery_by_logistic_partner = models.BooleanField(default=False)
    logistic_partner = models.CharField(max_length=255, null=True, blank=True)
    tracking_number = models.CharField(max_length=255, null=True, blank=True)
    tracking_link = models.CharField(max_length=200, null=True, blank=True)

    # Swadesic shipping values are fetched from SwadesicShippingPackageDetails table while sending API response
    delivery_by_swadesic = models.BooleanField(default=False)
    
    additional_notes = models.TextField(null=True, blank=True)

    return_initiate_date = models.CharField(max_length=100, null=True, blank=True)
    return_reason = models.CharField(max_length=500, null=True, blank=True)
    return_confirmation_date = models.CharField(max_length=100, null=True, blank=True)
    estimated_pickup_date = models.CharField(max_length=100, null=True, blank=True)

    self_return_by_store = models.BooleanField(default=False)
    return_person_name = models.CharField(max_length=200, null=True, blank=True)
    return_person_contact = models.CharField(max_length=20, null=True, blank=True)

    return_by_logistic_partner = models.BooleanField(default=False)
    return_tracking_link = models.CharField(max_length=200, null=True, blank=True)
    return_pickup_logistic_partner = models.CharField(
        max_length=255, null=True, blank=True
    )
    return_tracking_number = models.CharField(max_length=255, null=True, blank=True)
    return_estimated_pickup_date = models.CharField(max_length=100, null=True, blank=True)
    return_estimated_delivery_date = models.CharField(max_length=100, null=True, blank=True)

    additional_return_notes = models.TextField(null=True, blank=True)
    returned_date = models.CharField(max_length=100, null=True, blank=True)

    return_package_number = models.CharField(max_length=50, null=True, blank=True)
    display_return_package_number = models.CharField(
        max_length=50, null=True, blank=True
    )
    refund_warranty_version = models.CharField(max_length=5, null=True, blank=True)
    refund_warranty_settings_type = models.CharField(
        max_length=30, null=True, blank=True
    )
    refund_warranty_id = models.IntegerField(null=True, blank=True)
    delivery_settings_version = models.CharField(max_length=5, null=True, blank=True)
    delivery_settings_type = models.CharField(max_length=30, null=True, blank=True)
    delivery_settings_id = models.IntegerField(null=True, blank=True)

    transaction_fee = models.IntegerField(default=0)

    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now=True)

    return_conditions_json = models.JSONField(null=True, blank=True)

    __suborder_status = None

    objects = models.Manager()  # The default manager.
    new_objects = DeleteManager()  # delete manager.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__suborder_status = self.suborder_status

    def save(self, *args, **kwargs):
        if self.suborderid is None:
            store = Store.objects.get(storeid=self.storeid)
            self.store_reference = store.store_reference
            order = Order.objects.get(order_number=self.order_number)
            self.user_reference = order.user_reference.user_reference
        product_version = Product.objects.values_list("product_version", flat=True).get(
            productid=self.productid
        )
        self.product_version = product_version

        super(SubOrder, self).save(*args, **kwargs)

        # if current status is REFUND_HOLD, and it's going back to RETURN_TO_SELLER then change the refund_status back
        # to UNPROCESSED.
        if (
            self.suborder_status == self.Suborder_Status.RETURNED_TO_SELLER
            and self.__suborder_status == self.Suborder_Status.REFUND_HOLD
        ):
            RefundedAmount.objects.filter(
                suborder_reference=self.suborder_number
            ).update(refund_status=RefundedAmount.Status.UNPROCESSED)
        return_cancelled_condition = (
            self.suborder_status == self.Suborder_Status.ORDER_DELIVERED
            and self.__suborder_status == self.Suborder_Status.RETURN_REQUESTED
        )
        suborder_refund_instance = RefundedAmount.objects.filter(
            suborder_reference=self.suborder_number
        ).exists()
        if return_cancelled_condition and suborder_refund_instance:
            RefundedAmount.objects.filter(
                suborder_reference=self.suborder_number
            ).update(refund_status=RefundedAmount.Status.CANCELLED)
        if (
            self.suborder_status != self.__suborder_status
            or self.suborder_status == self.Suborder_Status.ORDER_INITIATED
        ):
            OrderLifeCycle.objects.create(
                orderid=self.orderid,
                suborderid_id=self.suborderid,
                created_by=self.orderid.userid,
                suborder_status=self.suborder_status,
                suborder_number=self.suborder_number,
                order_number=self.order_number,
            )
        self.__suborder_status = self.suborder_status

    class Meta:
        verbose_name_plural = "sub orders"
        db_table = '"order"."sub_order"'


class OrderLifeCycle(models.Model):
    orderlifecycleid = models.AutoField(primary_key=True)
    orderid = models.ForeignKey(
        Order, null=True, on_delete=models.SET_NULL, db_column="orderid"
    )
    suborderid = models.ForeignKey(
        SubOrder, null=True, on_delete=models.SET_NULL, db_column="suborderid"
    )
    created_by = models.CharField(max_length=5000, null=True, blank=True)
    suborder_status = models.CharField(max_length=100, null=True, blank=True)
    order_updated_date = models.DateTimeField(auto_now_add=True)
    suborder_number = models.CharField(max_length=100, null=True)
    order_number = models.CharField(max_length=100, null=True)

    txn_token = models.CharField(max_length=50, null=True)
    transaction_id = models.CharField(max_length=100, null=True)
    bank_transaction_id = models.CharField(max_length=100, null=True)
    payment_mode = models.CharField(max_length=50, null=True)
    payment_channel = models.CharField(max_length=30, null=True)
    transaction_date = models.CharField(max_length=50, null=True)
    razorpay_payment_id = models.CharField(max_length=50, null=True)

    confirmation_date = models.CharField(max_length=100, null=True, blank=True)
    cancelled_date = models.CharField(max_length=100, null=True, blank=True)
    delivered_date = models.CharField(max_length=100, null=True, blank=True)
    estimated_delivery_date = models.CharField(max_length=100, null=True, blank=True)

    self_delivery_by_store = models.BooleanField(default=False)
    delivery_person_name = models.CharField(max_length=200, null=True, blank=True)
    delivery_person_contact = models.CharField(max_length=20, null=True, blank=True)

    delivery_by_logistic_partner = models.BooleanField(default=False)
    logistic_partner = models.CharField(max_length=255, null=True, blank=True)
    tracking_number = models.CharField(max_length=255, null=True, blank=True)
    tracking_link = models.CharField(max_length=200, null=True, blank=True)

    delivery_by_swadesic = models.BooleanField(default=False)

    additional_notes = models.TextField(null=True, blank=True)

    return_initiate_date = models.CharField(max_length=100, null=True, blank=True)
    estimated_pickup_date = models.CharField(max_length=100, null=True, blank=True)
    return_request_cancelled_date = models.CharField(max_length=100, null=True, blank=True)

    return_estimated_pickup_date = models.CharField(max_length=100, null=True, blank=True)
    return_estimated_delivery_date = models.CharField(max_length=100, null=True, blank=True)

    self_return_by_store = models.BooleanField(default=False)
    return_person_name = models.CharField(max_length=200, null=True, blank=True)
    return_person_contact = models.CharField(max_length=20, null=True, blank=True)

    return_by_logistic_partner = models.BooleanField(default=False)
    return_pickup_logistic_partner = models.CharField(
        max_length=255, null=True, blank=True
    )
    return_tracking_number = models.CharField(max_length=255, null=True, blank=True)
    return_tracking_link = models.CharField(max_length=200, null=True, blank=True)

    returned_date = models.CharField(max_length=100, null=True, blank=True)
    additional_return_notes = models.TextField(null=True, blank=True)

    return_package_number = models.CharField(max_length=50, null=True, blank=True)
    display_return_package_number = models.CharField(
        max_length=50, null=True, blank=True
    )

    package_number = models.CharField(max_length=50, null=True, blank=True)
    display_package_number = models.CharField(max_length=50, null=True, blank=True)

    created_date = models.DateTimeField(auto_now_add=True, null=True, blank=True)

    return_conditions_json = models.JSONField(null=True, blank=True)

    class Meta:
        verbose_name_plural = "order life cycle "
        db_table = '"order"."order_life_cycle"'


class OrderRating(models.Model):
    class Review_Type(models.TextChoices):
        BUYER = "BUYER", _("buyer review")
        SELLER = "SELLER", _("seller review")

    order_rating_id = models.AutoField(primary_key=True)
    order_id = models.CharField(max_length=12, null=True, blank=True)
    order_reference = models.CharField(max_length=20, null=True, blank=True)
    order_status = models.CharField(max_length=20, null=True, blank=True)
    review_type = models.CharField(
        max_length=10, choices=Review_Type.choices, null=True, blank=True
    )
    user_reference = models.CharField(max_length=20, null=True, blank=True)
    order_rate = models.PositiveIntegerField(default=0)
    easiness_level = models.PositiveIntegerField(default=0)
    improvement_note = models.TextField()
    like_note = models.TextField()
    problems_facing = models.TextField()
    experience_level = models.PositiveIntegerField(default=0)
    experience_note = models.TextField()

    class Meta:
        verbose_name_plural = "order rating"
        db_table = '"order"."order_rating"'


class RefundedAmount(models.Model):
    class Status(models.TextChoices):
        UNPROCESSED = "UNPROCESSED", _("Unprocessed")
        CANCELLED = "CANCELLED", _("Cancelled")
        REFUNDED = "REFUNDED", _("Refunded")
        PENDING = "PENDING", _("Pending")
        HOLD = "HOLD", _("Hold")
        REFUND_INITIATED = "REFUND_INITIATED", _("Refund initiated")
        REFUND_INITIATE_FAILED = "REFUND_INITIATE_FAILED", _("Initiate failed")
        REFUND_FAILED = "REFUND_FAILED", _("Refund failed")

    refunded_amount_id = models.AutoField(primary_key=True)
    user_reference = models.CharField(max_length=20, null=True, blank=True)
    order_reference = models.CharField(max_length=20, null=True, blank=True)
    suborder_reference = models.ForeignKey(
        SubOrder,
        on_delete=models.CASCADE,
        related_name="refund_details",
        to_field="suborder_number",
        db_column="suborder_number",
        null=True,
        blank=True,
    )
    refunded_amount = models.FloatField(default=0)
    refund_id = models.CharField(max_length=50, null=True, blank=True)
    refund_hold_reason = models.TextField(null=True, blank=True)
    refund_status = models.CharField(
        max_length=40, choices=Status.choices, null=True, blank=True
    )

    suborder_status = models.CharField(max_length=30, null=True, blank=True)
    return_cost_on = models.CharField(max_length=20, null=True, blank=True)
    is_shipped = models.BooleanField(null=True, blank=True)
    df_settings = models.CharField(max_length=20, null=True, blank=True)
    df_type = models.CharField(max_length=20, null=True, blank=True)
    product_price = models.FloatField(default=0)
    product_df = models.FloatField(default=0)
    df_reversal = models.FloatField(default=0)
    transaction_fee = models.FloatField(default=0)

    refund_scheduled_datetime = models.DateTimeField(null=True, blank=True)
    refund_initiated_datetime = models.DateTimeField(null=True, blank=True)
    refunded_datetime = models.DateTimeField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    modified_date = models.DateTimeField(auto_now=True, null=True, blank=True)

    initiate_refund_status = models.CharField(
        max_length=20, null=True, blank=True
    )
    initiate_refund_message = models.TextField(null=True, blank=True)
    razorpay_order_payment_id = models.CharField(max_length=25, blank=True, null=True)
    razorpay_refund_id = models.CharField(max_length=25, blank=True, null=True)
    refund_transaction_id = models.CharField(max_length=25, blank=True, null=True)

    class Meta:
        verbose_name_plural = "refunded amounts"
        db_table = '"order"."refunded_amounts"'


class OrderConfiguration(models.Model):
    order_configuration_id = models.AutoField(primary_key=True)
    transaction_fee = models.IntegerField(null=True, blank=True)
    min_cart_value_for_commission_fee = models.IntegerField(
        null=True, blank=True, default=500)
    commission_fee = models.IntegerField(null=True, blank=True)
    commission_fee_discount = models.IntegerField(null=True, blank=True)
    return_refund_wait_time = models.IntegerField(null=True, blank=True)
    cancel_refund_wait_time = models.IntegerField(null=True, blank=True)
    fulfillment_window = models.IntegerField(null=True, blank=True)
    grace_period = models.IntegerField(null=True, blank=True)
    auto_cancel_time = models.IntegerField(null=True, blank=True)
    

    class Meta:
        verbose_name_plural = "order configuration"
        db_table = '"order"."order_configuration"'


class LogisticPartner(models.Model):
    courier_id = models.AutoField(primary_key=True)
    courier_name = models.CharField(max_length=50, null=True, blank=True)
    tracking_link = models.CharField(max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = "logistic partners"
        db_table = '"order"."logistic_partner"'


class OrderOtp(models.Model):
    class Status(models.TextChoices):
        USED = "USED", _("Used")
        EXPIRED = "EXPIRED", _("Expired")
        UNUSED = "UNUSED", _("Unused")

    order_otp_id = models.AutoField(primary_key=True)
    user_reference = models.CharField(max_length=20)
    package_number = models.CharField(max_length=75)
    otp = models.CharField(max_length=8)
    generated_time = models.DateTimeField(auto_now_add=True)
    otp_status = models.CharField(
        max_length=20, choices=Status.choices, default=Status.UNUSED
    )

    class Meta:
        verbose_name_plural = "order otp"
        db_table = '"order"."order_otp"'
