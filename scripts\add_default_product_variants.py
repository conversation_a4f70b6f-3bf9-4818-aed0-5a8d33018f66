import django
import os
import sys
from django.utils.text import slugify
import random
import string

# Add the parent directory (one level above `swadesic`) to sys.path
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(BASE_DIR)

# Set the correct Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "swadesic.settings")

# Initialize Django
django.setup()
from products.models import Product, ProductVariants

def populate_product_variants():
    products = Product.objects.all()

    for product in products:
        ProductVariants.objects.create(
            product_reference=product,
            mrp_price=product.mrp_price,
            selling_price=product.selling_price,
            stock=product.in_stock,
            is_active= False if product.deleted == True else True,
            )

    print(f"Updated {products.count()} products with variants.")

if __name__ == "__main__":
    populate_product_variants()
