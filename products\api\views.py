from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework import generics
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from django.db.models.functions import Upper
import json

from ..models import (
    Product,
    ProductImages,
    ProductVersion,
    ProductVersionManagement,
    ProductVariants,
    ProductVariantHistory,
    ProductVariantVersionManagement,
    calculate_product_variant_version
)
from .serializers import (
    GetProductSerializer,
    ProductSerializer,
    ProductDetailsSerializer,
    ProductImagesSerializer,
    ProductVersionSerializer,
    ProductHistorySerializer,
    ProductVariantsSerializer,
)
from stores.store_settings_api.models import DeliveryLocations, DeliverySettings
import uuid
from common.util.support_helper import GenerateNotifications
from users.user_api.models import User, UserProduct
from users.notification_api.models import Notifications
from stores.store_api.models import Store
from GraphDB.models import Neo4jEntity, Neo4jProduct, Neo4jStore
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from neomodel import db
from django.forms.models import model_to_dict
from django.core.exceptions import ObjectDoesNotExist
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
# PRODUCT MANAGEMENT


class ProductlistAV(APIView):  # Add and get product api view.
    @swagger_auto_schema(
        operation_summary="get list of all the products",
        operation_description="All the products irrespective of store, this api is not use anywhere in project as of now",
    )
    def get(self, request):
        products = Product.objects.filter(deleted=False)
        serializer = GetProductSerializer(products, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Add product details",
        operation_description="Add a new product by passing all the details by default "
        "'product_version' is 1.0.0 ",
        request_body=ProductSerializer,
    )
    def post(self, request):
        mrp_price = request.data['mrp_price']
        selling_price = request.data['selling_price']
        if selling_price > mrp_price:
            context = {"details": "selling price should less than mrp."}
            return Response(
                {"message": "error", "data": context},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = ProductSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            data = serializer.data
            product = Product.objects.get(productid=data["productid"])
            product_reference = product.product_reference
            data["product_reference"] = product_reference
            try:
                neo4j_store = Neo4jStore.nodes.get(reference=data["store_reference"])
                with db.transaction:
                    neo4j_product = Neo4jProduct(
                        reference=product_reference,
                        created_date=product.created_date
                    )
                    neo4j_product.save()
                    neo4j_store.listed.connect(neo4j_product)
            except Neo4jEntity.DoesNotExist:
                return Response(
                    {"message": "error", "data": "The store node does not exist"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                {"message": "success", "data": data}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ProductDetailsAV(APIView):  # get, put and delete products

    @swagger_auto_schema(operation_summary="get a particular product's details")
    def get_deliverability(self, product_reference, store_reference, user_pincode):
        deliverability = False

        if DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
        ).exists():
            delivery_settings_instance = DeliverySettings.objects.filter(
                product_reference=product_reference, store_reference=store_reference, is_deleted=False
            ).last()
        else:
            delivery_settings_instance = DeliverySettings.objects.filter(
                store_reference=store_reference, product_reference__isnull=True, is_deleted=False
            ).last()
        delivery_locations = delivery_settings_instance.delivery_locations

        if delivery_settings_instance.fulfillment_options == 'IN_STORE_PICKUP':
            return deliverability

        if delivery_locations:
            list_of_pincode = self.convert_delivery_locations_to_pincode(delivery_locations)
            if user_pincode in list_of_pincode:
                deliverability = True
        return deliverability

    @staticmethod
    def convert_delivery_locations_to_pincode(delivery_locations):
        queryset = DeliveryLocations.objects.all()
        all_states = set(queryset.values_list(Upper('state'), flat=True).distinct())
        all_cities = set(queryset.values_list(Upper('city'), flat=True).distinct())

        list_of_locations = delivery_locations.split("|")
        lst = []

        for elem in list_of_locations:
            elem_upper = elem.upper()
            if elem_upper in all_states:
                pincode = list(queryset.filter(state__iexact=elem).values_list("pincode", flat=True))
                lst.extend(pincode)
            elif elem_upper in all_cities:
                pincode = list(queryset.filter(city__iexact=elem).values_list("pincode", flat=True))
                lst.extend(pincode)
            else:
                lst.append(elem)

        return lst

    def get(self, request, product_reference, user_pincode=None, product_version=None):

        """
        By passing the product_reference this method will provide product details of the latest version
        and by passing product_reference and product_version on url, return the product details of
        specified version
        """
        logger.info("Entered product details api")
        visitor_reference = request.query_params.get("visitor_reference")
        if product_version:
            logger.info("Inside product version condition")
            try:
                product_version_instance = ProductVersion.objects.filter(
                    product_reference=product_reference, product_version=product_version
                ).first()
            except ObjectDoesNotExist:
                return Response(
                    {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            latest_product_version = Product.objects.values_list(
                "product_version", flat=True
            ).get(product_reference=product_reference)
            serializer = ProductVersionSerializer(product_version_instance,  context={"visitor_reference": visitor_reference})
            logger.info("Existed product version condition")
            return Response(
                {
                    "message": "success",
                    "latest_product_version": latest_product_version,
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            logger.info("Inside product details alone.")
            try:
                product = Product.objects.get(
                    product_reference=product_reference
                )
            except ObjectDoesNotExist:
                return Response({"message": "error"}, status=status.HTTP_404_NOT_FOUND)

            context = {
                "visitor_reference": visitor_reference,
                "user_pincode": user_pincode
            } if user_pincode else {"visitor_reference": visitor_reference}

            serializer = ProductDetailsSerializer(product, context=context)
            serializer_copy = serializer.data
            if user_pincode:
                product_deliverability = self.get_deliverability(product_reference, product.store_reference.store_reference, user_pincode)
                serializer_copy["deliverability"] = product_deliverability
                # serializer_copy["store_name"] = product.store_reference.store_name
            logger.info("Exited product details alone.")
            return Response(
                {"message": "success", "data": serializer_copy},
                status=status.HTTP_200_OK,
            )

    @swagger_auto_schema(
        operation_summary="Edit product's details",
        operation_description="Edit product's details by passing product_reference",
        request_body=ProductSerializer,
    )
    def put(self, request, product_reference):
        logger.info("Entered product update api")
        try:
            product = Product.objects.get(
                product_reference=product_reference, deleted=False
            )
        except ObjectDoesNotExist:
            return Response(
                {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Store the original values of affiliate fields
        promotion_amount = product.promotion_amount
        is_promotion_enabled = product.is_promotion_enabled

        serializer = ProductSerializer(product, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            
            # Check if affiliate-related fields were updated
            affiliate_fields_updated = []
            if 'promotion_amount' in request.data and request.data['promotion_amount'] != promotion_amount:
                affiliate_fields_updated.append('promotion_amount')
            if 'is_promotion_enabled' in request.data and request.data['is_promotion_enabled'] != is_promotion_enabled:
                affiliate_fields_updated.append('is_promotion_enabled')
            
            # If affiliate fields were updated, create version entry
            if affiliate_fields_updated:
                # Get the change type for these fields from ProductVersionManagement
                change_types = []
                for field in affiliate_fields_updated:
                    try:
                        version_mgmt = ProductVersionManagement.objects.get(
                            product_column_name=field,
                            operations='UPDATE'
                        )
                        change_types.append(version_mgmt.change_type)
                    except ProductVersionManagement.DoesNotExist:
                        change_types.append('MINOR')  # Default to MINOR if not found
                
                # Use the most significant change type (MAJOR > MINOR > IDLE)
                final_change_type = 'IDLE'
                if 'MAJOR' in change_types:
                    final_change_type = 'MAJOR'
                elif 'MINOR' in change_types:
                    final_change_type = 'MINOR'
                
                # Calculate new version based on the change type
                current_version = product.product_version
                new_version = calculate_product_version(current_version, final_change_type)
                
                # Create ProductVersion entry
                ProductVersion.objects.create(
                    productid=product.productid,
                    product_reference=product_reference,
                    storeid=product.storeid,
                    store_reference=product.store_reference.store_reference if product.store_reference else None,
                    product_name=product.product_name,
                    product_description=product.product_description,
                    brand_name=product.brand_name,
                    mrp_price=product.mrp_price,
                    selling_price=product.selling_price,
                    product_version=new_version,
                    change_type=final_change_type,
                    changed_fields=','.join(affiliate_fields_updated),
                    is_affiliate_promotion_enabled=product.is_affiliate_promotion_enabled,
                    affiliate_commission_amount=product.affiliate_commission_amount
                )
                
                # Update product version
                product.product_version = new_version
                product.save(update_fields=['product_version'])
            
            logger.info("Exited product update api successfully")
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            logger.info("some error in product update api ")
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(operation_summary="Delete a product")
    def delete(self, request, product_reference):

        """
        Delete a product by passing product_reference. product and product images deletes are soft delete.
        deleting products will also delete the corresponding product images
        """

        try:
            product = Product.objects.get(
                product_reference=product_reference, deleted=False
            )
        except ObjectDoesNotExist:
            return Response(
                {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        product_id = product.productid
        product.deleted = True
        product.save(update_fields=["deleted"])
        ProductImages.objects.filter(productid=product_id).update(is_deleted=True)
        try:
            product_node = Neo4jProduct.nodes.get(reference=product_reference)
            product_node.is_deleted = True
            product_node.save()
        except Neo4jProduct.DoesNotExist:
            return Response(
                {"message": "Product node with given product_reference does not exist "},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        return Response({"message": "success"}, status=status.HTTP_200_OK)


class UpdateProductStock(generics.UpdateAPIView):
    queryset = Product.objects.filter(deleted=False)
    serializer_class = ProductSerializer
    lookup_field = 'product_reference'
    def patch(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer (instance, data=request.data, partial=True)
        if serializer.is_valid():
            self.perform_update (serializer)
            return Response (
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response (
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ProductHistory(generics.RetrieveAPIView):
    @swagger_auto_schema(operation_summary="Get product history")
    def get(self, request, *args, **kwargs):
        """
        get all the previous versions and some other details of a product

        By passing the product_reference in url gets a dict which contain message, latest_product_version and a list of
        product's previous versions

        """
        try:
            product_history = ProductVersion.objects.filter(
                product_reference=kwargs["product_reference"]
            ).order_by("-created_date")
        except ObjectDoesNotExist:
            return Response(
                {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        latest_version = product_history.first().product_version
        logger.info(latest_version)
        serializer = ProductHistorySerializer(product_history, many=True)
        return Response(
            {
                "message": "success",
                "latest_version": latest_version,
                "previous_versions": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class ProductimagelistAV(APIView):
    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        operation_summary="Get all images of a product",
        operation_description="get all images of a product by passing product_referecne",
    )
    def get(
        self, request, product_reference, product_version=None
    ):  # get all the images of a product by product_id

        if product_version:
            try:
                product_version_instance = ProductVersion.objects.filter(
                    product_reference=product_reference, product_version=product_version
                ).first()
            except ObjectDoesNotExist:
                return Response(
                    {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            latest_product_version = Product.objects.values_list(
                "product_version", flat=True
            ).get(product_reference=product_reference)
            product_image_ids = product_version_instance.product_images_ids
            image_ids_convert_to_int = map(int, product_image_ids.split("|"))
            list_of_image_ids = list(image_ids_convert_to_int)
            product_images = ProductImages.objects.filter(
                productimageid__in=list_of_image_ids
            ).order_by("reorder")
            serializer = ProductImagesSerializer(product_images, many=True)

            return Response(
                {
                    "message": "success",
                    "latest_product_version": latest_product_version,
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            try:
                product_images = ProductImages.objects.filter(
                    product_reference=product_reference,
                    is_deleted=False
                ).select_related('product_reference').order_by("reorder")
            except ObjectDoesNotExist:
                return Response(
                    {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            serializer = ProductImagesSerializer(product_images, many=True)
            return Response({"message": "success", "data": serializer.data})

    @swagger_auto_schema(
        operation_summary="Add product images", request_body=ProductImagesSerializer
    )
    def post(self, request, *args, **kwargs):

        logger.info("Entered post method of image adding api")
        product_reference = self.kwargs["product_reference"]
        product = Product.objects.get(product_reference=product_reference)
        # neo4j_product = Neo4jProduct.nodes.get(reference=product_reference)
        existing_product_images = ProductImages.objects.values_list(
            "productimageid", flat=True
        ).filter(product_reference=product_reference)
        logger.info(
            "List of existing product image ids %s", list(existing_product_images)
        )

        created_by = request.data["created_by"]
        modified_by = request.data["modified_by"]
        if not list(existing_product_images) and product.product_version == "1.0.0":
            logger.info("Adding first image for the given product")
            # create an empty dictionary and add product_id, created_by,modified_by to the dictionary
            form_data = {}
            form_data["product_reference"] = product_reference
            form_data["created_by"] = created_by
            form_data["modified_by"] = modified_by
            success = True
            image_list = []  # to keep mutliple images

            image = request.FILES.getlist(
                "product_image"
            )  # get list of images from request.
            if not image:
                return Response(
                    {"message": "enter product_image"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            media_properties = []
            # for idx, images in enumerate(image):
            for images in image:
                # media_id = str(uuid.uuid4())
                form_data[
                    "product_image"
                ] = images  # store product_image to the dictionary
                serializer = ProductImagesSerializer(data=form_data)
                if serializer.is_valid():
                    serializer.save()
                    # media_properties.append(
                    #     {'media_id': media_id,
                    #      'media_type': 'IMAGE',
                    #      'media_path': serializer.data["product_image"],
                    #      'order': idx + 1})
                    # image_list.append(serializer.data)
                    # media_node = Neo4jMedia(media=media_properties)
                    # media_node.save()
                    # neo4j_product.media.connect(media_node)
                else:
                    success = False

            # update image ids in product version table which has product version 1.0.0, stored as pipe.
            newly_added_product_images = list(
                ProductImages.objects.values_list("productimageid", flat=True).filter(
                    product_reference=product_reference, is_deleted=False
                )
            )
            piped_product_images_ids = "|".join(map(str, newly_added_product_images))
            ProductVersion.objects.filter(
                product_reference=product_reference, product_version="1.0.0"
            ).update(product_images_ids=piped_product_images_ids)
        else:
            logger.info("Adding images for the given product, not first time.")
            # create an empty dictionary and add product_id, created_by,modified_by to the dictionary
            form_data = {}
            form_data["product_reference"] = product_reference
            form_data["created_by"] = created_by
            form_data["modified_by"] = modified_by
            success = True
            image_list = []  # to keep mutliple images

            image = request.FILES.getlist(
                "product_image"
            )  # get list of images from request.
            if not image:
                return Response(
                    {"message": "enter product_image"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            for images in image:
                form_data[
                    "product_image"
                ] = images  # store product_images to the dictionary
                serializer = ProductImagesSerializer(data=form_data)
                if serializer.is_valid():
                    serializer.save()
                    image_list.append(serializer.data)
                else:
                    success = False

            # create a new entry in product version table with newly added product_image_ids, change type and changed field.
            available_product_images = list(
                ProductImages.objects.values_list("productimageid", flat=True).filter(
                    product_reference=product_reference, is_deleted=False
                )
            )
            piped_product_images_ids = "|".join(map(str, available_product_images))

            # find the change type of product_field from ProductVersionManagement table, each time a new image added,
            # should calculate a new product_version by passing current product_version and change_type assinged to the
            # field (here product_image field).
            change_type = ProductVersionManagement.objects.values_list(
                "change_type", flat=True
            ).get(product_column_name="product_image", operations="INSERT")

            product_version = Product.objects.values_list(
                "product_version", flat=True
            ).get(product_reference=product_reference)
            new_product_version = calculate_product_version(
                product_version, change_type
            )

            # update new product_version in product table.
            Product.objects.filter(product_reference=product_reference).update(
                product_version=new_product_version
            )
            prod_image = ProductImages.objects.filter(product_reference=product_reference, is_deleted=False).first()
            if prod_image.product_image:
                image = prod_image.product_image
            else:
                image = None
            notification_gen = GenerateNotifications()
            notification_gen.create_notifications(
                notified_user=None,
                notification_type=Notifications.Notifications_Type.PRODUCT_VERSION_CHANGED,
                notification_about=product_reference,
                product_id=product.productid,
                image=image
            )
            # create new entry in product version table with all the current product details with image ids
            ProductVersion.objects.create(
                productid=product.productid,
                product_reference=product.product_reference,
                storeid=product.storeid,
                store_reference=product.store_reference.store_reference,
                product_name=product.product_name,
                product_description=product.product_description,
                product_images_ids=piped_product_images_ids,
                brand_name=product.brand_name,
                selling_price=product.selling_price,
                mrp_price=product.mrp_price,
                in_stock=product.in_stock,
                promotion_link=product.promotion_link,
                hashtags=product.hashtags,
                product_version=new_product_version,
                change_type=change_type,
                changed_fields="product_image",
            )

        if success:
            logger.info("Exiting post method of image adding api")
            return Response(
                {"message": "success", "Data": image_list}, status=status.HTTP_200_OK
            )
        return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


def calculate_product_version(product_version, severity):

    current_product_version = product_version
    major_part = int(current_product_version.split(".")[0])
    minor_part = int(current_product_version.split(".")[1])
    idle_part = int(current_product_version.split(".")[2])

    if severity == "MAJOR":
        major_part += 1
        new_product_version = str(major_part) + "." + "0" + "." + "0"
    elif severity == "MINOR":
        minor_part += 1
        new_product_version = str(major_part) + "." + str(minor_part) + "." + "0"
    elif severity == "IDLE":
        idle_part += 1
        new_product_version = (
            str(major_part) + "." + str(minor_part) + "." + str(idle_part)
        )
    logger.info(
        "product_version chagnged from %s to %s",
        current_product_version,
        new_product_version,
    )
    return new_product_version


class ProductimageDetailsAV(APIView):
    @swagger_auto_schema(auto_schema=None, operation_summary="get a product image")
    def get(self, request, *args, **kwargs):  # get product image
        """
        get product_image by passing productimageid , this method is not using anywhere in project as of now
        """
        try:
            product_images = ProductImages.objects.get(
                pk=self.kwargs["product_image_id"], is_deleted=False
            )
        except ProductImages.DoesNotExist:
            return Response(
                {"Error": "product image not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = ProductImagesSerializer(product_images)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(operation_summary="Delete a product image")
    def delete(self, request, *args, **kwargs):
        """
        Delete a product image by passing product_reference. Deleting images will be soft delete.

        Whenever there is a deletion, it will affect the product_version, and it will get updated in
        both Product and productVersion table to keep track of the changes.
        """
        logger.info("Entered product deletion api")
        try:
            product_image_id = self.kwargs["product_image_id"]
            product_image_instance = ProductImages.objects.get(
                pk=product_image_id, is_deleted=False
            )
        except ObjectDoesNotExist:
            return Response(
                {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        product_image_instance.is_deleted = True
        product_image_instance.save(
            update_fields=["is_deleted"]
        )  # save using update_fields helps to update the specified alone.
        logger.info("product image id %s has deleted", product_image_id)

        #  Deleting an image should be updated in ProductVersion table to keep track of any changes in product.

        product_reference = product_image_instance.product_reference.product_reference
        product = Product.objects.get(
            product_reference=product_reference, deleted=False
        )

        # Get all currently available image ids
        currently_available_image_ids = list(
            ProductImages.objects.values_list("productimageid", flat=True).filter(
                product_reference=product_reference, is_deleted=False
            )
        )
        piped_product_images_ids = "|".join(
            map(str, currently_available_image_ids)
        )  # convert product image list to string separated by pipe

        #  once an image deleted, calculate the new product version according to the
        #  change_type(MAJOR, MINOR, IDLE) assigned to field.
        change_type = ProductVersionManagement.objects.values_list(
            "change_type", flat=True
        ).get(product_column_name="product_image", operations="DELETE")
        product_version = Product.objects.values_list("product_version", flat=True).get(
            product_reference=product_reference
        )

        new_product_version = calculate_product_version(product_version, change_type)

        #  update new product version in Product table
        Product.objects.filter(product_reference=product_reference).update(
            product_version=new_product_version
        )

        #  create a new entry in the ProductVersion table with all the current product details
        #  and currently available image_ids (piped format eg:(1|2|3)),
        #  it excludes the deleted image id of that product.

        ProductVersion.objects.create(
            productid=product.productid,
            product_reference=product.product_reference,
            storeid=product.storeid,
            store_reference=product.store_reference.store_reference,
            product_name=product.product_name,
            product_description=product.product_description,
            product_images_ids=piped_product_images_ids,
            brand_name=product.brand_name,
            selling_price=product.selling_price,
            mrp_price=product.mrp_price,
            in_stock=product.in_stock,
            promotion_link=product.promotion_link,
            hashtags=product.hashtags,
            product_version=new_product_version,
            change_type=change_type,
            changed_fields="product_image",
        )
        logger.info("Exited product deletion api")
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class UpdateProductImageOrder(APIView):
    @swagger_auto_schema(
        operation_summary="Reorder the product images",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["product_reference", "data"],
            properties={
                "product_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "data": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        required=["productImageId", "reorder"],
                        properties={
                            "productImageId": openapi.Schema(type=openapi.TYPE_INTEGER),
                            "reorder": openapi.Schema(type=openapi.TYPE_INTEGER),
                        },
                    ),
                ),
            },
        ),
    )
    def post(self, request):
        """
        Reorder images that are added to a product. This method will call after images are added or edited.

        input will be a list of productimage id and reorder number.
        While getting images it will fetch according to this order.
        """
        try:
            product_reference = request.data["product_reference"]
            product_instance = Product.objects.get(
                product_reference=product_reference, deleted=False
            )
        except ObjectDoesNotExist:
            return Response(
                {"message": "error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        product_id = product_instance.productid
        input_data = request.data["data"]
        for inp in input_data:
            image = ProductImages.objects.get(
                productid=product_id,
                productimageid=inp["productImageId"],
                is_deleted=False,
            )
            image.reorder = inp["reorder"]
            image.save(update_fields=["reorder"])
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class HideProducts(APIView):
    @swagger_auto_schema(
        operation_summary="Hide a product",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["product_reference"],
            properties={"product_reference": openapi.Schema(type=openapi.TYPE_STRING)},
        ),
    )
    def post(self, request):
        product_reference = request.data["product_reference"]
        try:
            product = Product.objects.get(
                product_reference=product_reference, deleted=False
            )
        except Product.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        product.hide = True
        product.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetHiddenProducts(APIView):
    @swagger_auto_schema(operation_summary="Get hidden products")
    def get(self, request, store_reference):
        try:
            product = Product.objects.filter(
                store_reference=store_reference, hide=True, deleted=False
            ).order_by("-created_date")
        except Product.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        serializer = GetProductSerializer(product, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class UnHideProducts(APIView):
    @swagger_auto_schema(
        operation_summary="Expose a hidden products",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["product_reference"],
            properties={"product_reference": openapi.Schema(type=openapi.TYPE_STRING)},
        ),
    )
    def post(self, request):
        product_reference = request.data["product_reference"]
        try:
            product = Product.objects.get(
                product_reference=product_reference, deleted=False
            )
        except Product.DoesNotExist:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        product.hide = False
        product.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class VisitProductAV(APIView):
    @swagger_auto_schema(
        operation_summary="Visit a product",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["product_reference", "userid"],
            properties={
                "product_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "userid": openapi.Schema(type=openapi.TYPE_INTEGER),
            },
        ),
    )
    def post(self, request):
        """Whenever user visit a product it will create a new entry in UserProduct table by making is_visited field as
        true. It will help to keep track of all the products that visited by each user."""
        try:
            userid = request.data["userid"]
            product_reference = request.data["product_reference"]
            user = User.objects.get(userid=userid, deleted=False)
            product = Product.objects.get(
                product_reference=product_reference, deleted=False
            )

            if UserProduct.objects.filter(
                userid=user, productid=product.productid
            ).exists():
                user_product_instance = UserProduct.objects.get(
                    userid=user, productid=product
                )
                user_product_instance.is_visited = True
                user_product_instance.save()
                return Response(
                    {"message": "success", "visited": user_product_instance.is_visited},
                    status=status.HTTP_200_OK,
                )
            else:
                UserProduct.objects.create(
                    userid=user, productid=product, is_visited=True
                )
                is_visited = True
                return Response(
                    {"message": "success", "visited": is_visited},
                    status=status.HTTP_200_OK,
                )
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class CheckProductSavedOrNot(APIView):
    @swagger_auto_schema(
        operation_summary="check a product saved or not",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["product_reference", "userid"],
            properties={
                "product_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "userid": openapi.Schema(type=openapi.TYPE_INTEGER),
            },
        ),
    )
    def post(self, request):
        """
        Whenever user visit a product page, It should check whether that product is already saved by the user or not.\n
        This will help to get the flag. This api should call only after 'VisitProductAV', because this entry should be\n
        in UserProduct table."""

        user_id = request.data["userid"]
        product_reference = request.data["product_reference"]
        # product_id = Product.objects.values_list("productid", flat=True).get(
        #     product_reference=product_reference
        # )
        user_reference = User.objects.get(user_id=user_id).user_reference

        try:
            neo4j_user = Neo4jEntity.nodes.get(reference=user_reference)
            neo4j_product = Neo4jProduct.nodes.get(reference=product_reference)
        except Neo4jEntity.DoesNotExist or Neo4jProduct.DoesNotExist:
            logger.error("Nodes Matching the given user and product does not exist ")

        # try:
        #     user_product_instance = UserProduct.objects.get(
        #         userid_id=user_id, productid_id=product_id
        #     )
        # except ObjectDoesNotExist:
        #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

        saved = Neo4jEntity.saves.is_connected(neo4j_product)
        return Response(
            {"message": "success", "saved": saved}, status=status.HTTP_200_OK
        )


class SaveProductAV(APIView):
    @swagger_auto_schema(
        operation_summary="Save product by user",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["product_reference", "userid"],
            properties={
                "product_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "userid": openapi.Schema(type=openapi.TYPE_INTEGER),
            },
        ),
    )
    def post(self, request):
        """By using the given product_id and user_id save that product, for that mark is_saved field in UserProduct
        table as true. \nThis same api will unsaved the product on toggle."""
        logger.info("Entered save product api")
        user_id = request.data["userid"]
        product_reference = request.data["product_reference"]
        try:
            product_id = Product.objects.values_list("productid", flat=True).get(
                product_reference=product_reference
            )

            user = User.objects.get(userid=user_id, deleted=False)
            user_reference = user.user_reference
            product = Product.objects.get(productid=product_id, deleted=False)

            neo4j_user = Neo4jEntity.nodes.get(reference= user_reference)
            neo4j_product = Neo4jProduct.nodes.get(reference=product_reference)

            try:
                if neo4j_user.saves.is_connected(neo4j_product):
                    neo4j_user.saves.disconnect(neo4j_product)
                else:
                    neo4j_user.saves.connect(neo4j_product)
            except:
                logger.error("User saved product relationship could not be created")

            if UserProduct.objects.filter(userid=user, productid=product).exists():
                user_product = UserProduct.objects.get(userid=user, productid=product)
                if user_product.is_saved:
                    user_product.is_saved = False
                    user_product.save(update_fields=["is_saved"])
                    logger.info("Product unsaved")
                else:
                    user_product.is_saved = True
                    user_product.save(update_fields=["is_saved"])
                    logger.info("Product saved")
                logger.info("Exited save product api")
                return Response(
                    {"message": "success", "saved": user_product.is_saved},
                    status=status.HTTP_200_OK,
                )
            else:
                UserProduct.objects.create(
                    userid=user, productid=product, is_saved=True
                )
                is_saved = True
                logger.info("Product saved")
                logger.info("Exited save product api")
                return Response(
                    {"message": "success", "saved": is_saved}, status=status.HTTP_200_OK
                )
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class WishlistProducts(generics.CreateAPIView):
    def post(self, request, *args, **kwargs):
        user_reference = request.data['user_reference']
        user = User.objects.get(user_reference=user_reference)
        neo4j_user = Neo4jEntity.nodes.get(reference=user_reference)
        product_list = request.data['product_list']
        for product in product_list:
            #neo4j code
            neo4j_product = Neo4jProduct.nodes.get(reference=product)
            try:
                if not neo4j_user.saves.is_connected(neo4j_product):
                    neo4j_user.saves.connect(neo4j_product)
                else:
                    pass
            except:
                logger.error("User saved product relationship could not be created")

            product_instance = Product.objects.get(product_reference=product, deleted=False)
            if UserProduct.objects.filter(userid=user, productid=product_instance).exists():
                instance = UserProduct.objects.get(userid=user, productid=product_instance)
                if not instance.is_saved:
                    instance.is_saved = True
            else:
                UserProduct.objects.create(userid=user, productid=product_instance, is_saved=True)
        return Response({"message": "success", "data": "products whishlisted"})


class UpdateInventory(APIView):
    @swagger_auto_schema(
        operation_summary="update inventory of store",
        request_body=openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                required=["product_reference", "store_reference", "product_quantity"],
                properties={
                    "product_reference": openapi.Schema(type=openapi.TYPE_STRING),
                    "store_reference": openapi.Schema(type=openapi.TYPE_STRING),
                    "product_quantity": openapi.Schema(type=openapi.TYPE_STRING),
                },
            ),
        ),
    )
    def post(self, request, *args, **kwargs):
        """
        Passes list of product_reference, store_reference and product_quantity. when passing more than one item in list
        try it on postman
        It will update the product stock (product quantity will reduce from product stock and update it.
        when ever there is a product purchased this api will use to update  stock.).
        """
        input_data = request.data

        for data in input_data:
            product_instance = Product.objects.get(
                productid=data["productid"], storeid=data["storeid"], deleted=False
            )
            if product_instance.in_stock > 0:
                product_instance.in_stock -= data["product_quantity"]
                product_instance.save(update_fields=["in_stock"])
        return Response(
            {"message": "success", "data": "quantity updated successfully"},
            status=status.HTTP_200_OK,
        )


class RecommendedProducts(APIView):
    def get(self, request, *args, **kwargs):
        visitor_reference = request.query_params.get("visitor_reference")
        user_pincode = request.query_params.get("user_pincode")
        limit = int(request.query_params.get("limit"))
        offset = int(request.query_params.get("offset"))

        all_test_store_references = Store.objects.filter(storehandle__startswith='test_').values_list("store_reference",
                                                                                                      flat=True)
        try:
            if visitor_reference.startswith('U'):
                visitor_id = User.objects.get(user_reference=visitor_reference, deleted=False).userid
            elif visitor_reference.startswith('S'):
                visitor_id = Store.objects.get(store_reference=visitor_reference, deleted=False).created_by
            user_test_stores = Store.objects.filter(created_by=visitor_id, is_active=True, storehandle__startswith='test_')
            user_test_stores_references = user_test_stores.values_list('store_reference', flat=True)
        except Exception as e:
            user_test_stores_references = []
            logger.info(f"An exception occured :{e}")

        master_query_product = Product.objects.filter(
            deleted=False,
            store_reference__is_active=True
        ).exclude(store_reference__in=all_test_store_references)

        test_store_products = Product.objects.filter(store_reference__in=user_test_stores_references, deleted=False)
        master_query_product = master_query_product | test_store_products
        products_to_show = master_query_product.order_by('-created_date')
        if limit or offset:
            all_products = products_to_show[offset:offset + limit]
        return Response(
            {"messsage": "success",
             "product": GetProductSerializer(
                 all_products, many=True, context={"user_pincode": user_pincode}
             ).data,

             },
            status=status.HTTP_200_OK,
        )
    

class ProductSlugCodeAvailability(APIView):
    def get(self, request):
        store_reference = request.query_params.get("store_reference")
        search_type = request.query_params.get("search_type")
        search_query = request.query_params.get("search_query")
        if search_type == "product_slug":
            is_product_slug_exists = Product.objects.filter(product_slug=search_query, store_reference=store_reference, deleted=False).exists()
            if is_product_slug_exists:
                return Response({"message": "success", "available": "false"}, status=status.HTTP_200_OK)
            else:
                return Response({"message": "success", "available": "true"}, status=status.HTTP_200_OK)
        if search_type == "product_code":
            is_product_code_exists = Product.objects.filter(product_code= search_query, store_reference=store_reference, deleted=False).exists()
            if is_product_code_exists:
                return Response({"message": "success", "available": "false"}, status=status.HTTP_200_OK)
            else:
                return Response({"message": "success", "available": "true"}, status=status.HTTP_200_OK)
        

class GetProductReferenceFromSlug(APIView):
    def get(self, request):
        product_slug = request.query_params.get("product_slug")
        store_reference = request.query_params.get("store_reference")
        try:
            product_instance = Product.objects.get(product_slug=product_slug, store_reference=store_reference, deleted=False)
            product_reference = product_instance.product_reference
            return Response({"message": "success", "product_reference": product_reference}, status=status.HTTP_200_OK)
        except:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)
        
class ProductVariantsView(APIView):
    def normalize_dict(self, d):
        return json.dumps(d, sort_keys=True)

    def get(self, request):
        product_reference = request.query_params.get("product_reference")
        product_variants = ProductVariants.objects.filter(product_reference=product_reference, is_active=True)
        return Response({"message": "success", "product_variants": ProductVariantsSerializer(product_variants, many=True).data}, status=status.HTTP_200_OK)

    def post(self, request):
        product_reference = request.data.get("product_reference")
        mrp_price = request.data.get("mrp_price")
        selling_price = request.data.get("selling_price")
        stock = request.data.get("stock")
        combinations = request.data.get("combinations")

        normalized_combinations = self.normalize_dict(combinations)
        product_instance = Product.objects.get(product_reference=product_reference, deleted=False)

        variants = ProductVariants.objects.filter(product_reference=product_instance)

        for variant in variants:
            if self.normalize_dict(variant.combinations) == normalized_combinations:
                # Update existing variant - this will trigger version tracking
                logger.info(f"Updating existing variant {variant.variant_reference}")

                # Update the variant
                variant.mrp_price = mrp_price
                variant.selling_price = selling_price
                variant.stock = stock

                # Save will automatically handle version tracking through ModelDiffMixin
                variant.save()

                logger.info(f"Variant {variant.variant_reference} updated successfully with version {variant.variant_version}")

                return Response({
                    "message": "Variant updated",
                    "product_variant": ProductVariantsSerializer(variant).data
                }, status=status.HTTP_200_OK)

        # If not found, create new variant
        logger.info(f"Creating new variant for product {product_reference}")
        new_variant = ProductVariants.objects.create(
            product_reference=product_instance,
            mrp_price=mrp_price,
            selling_price=selling_price,
            stock=stock,
            combinations=combinations
        )

        logger.info(f"New variant {new_variant.variant_reference} created with version {new_variant.variant_version}")

        return Response({
            "message": "Variant created",
            "product_variant": ProductVariantsSerializer(new_variant).data
        }, status=status.HTTP_201_CREATED)