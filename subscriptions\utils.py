import requests
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from decouple import config
from datetime import datetime
from decimal import Decimal
from .models import SubscriptionPayment
from stores.store_api.models import StoreSubscription
from users.user_api.models import UserSubscription

def get_razorpay_auth():
    return HTTPBasicAuth(config('RAZORPAY_ID'), config('RAZORPAY_KEY'))

def fetch_and_process_subscription_payments(subscription_id):
    """
    Fetches all invoices for a subscription and processes their payments
    """
    try:
        # First determine if this is a store or user subscription
        store_sub = StoreSubscription.objects.filter(razorpay_subscription_id=subscription_id).first()
        user_sub = UserSubscription.objects.filter(razorpay_subscription_id=subscription_id).first()
        
        if not store_sub and not user_sub:
            raise ValueError(f"No subscription found for ID: {subscription_id}")
            
        # Fetch all invoices
        auth = get_razorpay_auth()
        response = requests.get(
            'https://api.razorpay.com/v1/invoices',
            params={'subscription_id': subscription_id},
            auth=auth
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to fetch invoices: {response.text}")
            
        invoices = response.json().get('items', [])
        
        for invoice in invoices:
            payment_id = invoice.get('payment_id')
            if not payment_id:
                continue
                
            # Skip if payment already processed
            if SubscriptionPayment.objects.filter(payment_reference=payment_id).exists():
                continue
                
            # Fetch payment details
            payment_response = requests.get(
                f'https://api.razorpay.com/v1/payments/{payment_id}',
                auth=auth
            )
            
            if payment_response.status_code != 200:
                continue
                
            payment_details = payment_response.json()
            
            # Calculate fee and tax percentages
            amount = Decimal(payment_details['amount']) / 100  # Convert from paise to rupees
            fee = Decimal(payment_details['fee']) / 100
            tax = Decimal(payment_details['tax']) / 100
            
            fee_percentage = (fee / amount * 100).quantize(Decimal('0.01'))
            tax_percentage = (tax / amount * 100).quantize(Decimal('0.01'))
            
            # Create payment record
            payment = SubscriptionPayment(
                payment_reference=payment_id,
                subscription_id=subscription_id,
                invoice_id=invoice['id'],
                store_reference=store_sub.store_reference if store_sub else None,
                user_reference=user_sub.user_reference if user_sub else None,
                amount=amount,
                currency=payment_details['currency'],
                status=SubscriptionPayment.PaymentStatus.SUCCESS if payment_details['status'] == 'captured' else SubscriptionPayment.PaymentStatus.FAILED,
                payment_method=payment_details['method'],
                fee_amount=fee,
                tax_amount=tax,
                fee_percentage=fee_percentage,
                tax_percentage=tax_percentage,
                payment_date=datetime.fromtimestamp(payment_details['created_at']),
                billing_period_start=datetime.fromtimestamp(invoice['billing_start']) if invoice.get('billing_start') else None,
                billing_period_end=datetime.fromtimestamp(invoice['billing_end']) if invoice.get('billing_end') else None
            )
            payment.save()
            
        return True
        
    except Exception as e:
        # Log error
        print(f"Error processing subscription payments: {str(e)}")
        return False


