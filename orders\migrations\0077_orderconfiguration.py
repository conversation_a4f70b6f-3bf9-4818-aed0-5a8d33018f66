# Generated by Django 3.2.13 on 2023-05-08 08:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0076_orderpayout_order_value_after_reduction'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderConfiguration',
            fields=[
                ('order_configuration_id', models.AutoField(primary_key=True, serialize=False)),
                ('transaction_fee', models.IntegerField(blank=True, null=True)),
                ('commission_fee', models.IntegerField(blank=True, null=True)),
                ('early_commission_fee', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'order configuration',
                'db_table': '"order"."order_configuration"',
            },
        ),
    ]
