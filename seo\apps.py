from django.apps import AppConfig
from django.db.models.signals import post_migrate

class SeoConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'seo'

    def ready(self):
        from django.conf import settings
        from django.contrib.sites.models import Site

        def configure_sites_domain(sender, **kwargs):
            Site.objects.update_or_create(
                id=settings.SITE_ID,
                defaults={
                    'domain': settings.SITE_DOMAIN,
                    'name': 'Swadesic'
                }
            )

        post_migrate.connect(configure_sites_domain, sender=self)
