from django.db import models
from datetime import datetime
import random
import string
import time
from django.utils import timezone
from common.util.support_helper import compress
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.fields import ArrayField

# Create your models here.


class Category(models.Model):

    categoryid = models.AutoField(primary_key=True)
    category_name = models.CharField(max_length=100, unique=True)
    active = models.BooleanField(default=True)
    created_by = models.Char<PERSON>ield(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_by = models.CharField(max_length=100)
    modified_date = models.DateField(auto_now=True)

    def __str__(self):
        return self.category_name

    class Meta:
        verbose_name_plural = "categories"
        db_table = '"store"."category"'


class StoreConfig(models.Model):
    store_config_id = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        related_name="store_configurations",
        db_column="store_reference",
    )
    enable_orders = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "configurations"
        db_table = '"store"."store_config"'


class Store(models.Model):

    class Verification_Type(models.TextChoices):

        BUSINESS = "BUSINESS", _("Business")
        INDIVIDUAL = "INDIVIDUAL", _("Individual")
    class SubscriptionType(models.TextChoices):
        FREE = "FREE", _("Free")
        PREMIUM = "PREMIUM", _("Premium")

    storeid = models.AutoField(primary_key=True)
    store_reference = models.CharField(max_length=20, unique=True)
    store_name = models.CharField(max_length=40)
    store_desc = models.CharField(max_length=1000, null=True, blank=True)
    business_description = models.CharField(max_length=500, null=True, blank=True)
    is_active = models.BooleanField(default=False)
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="created",
        db_column="created_by",
        null=True,
        blank=True,
    )
    created_date = models.DateTimeField(auto_now_add=True)
    activated_date = models.DateTimeField(blank=True, null=True)
    modified_by = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="modified",
        db_column="modified_by",
        null=True,
        blank=True,
    )
    modified_date = models.DateTimeField(auto_now=True)
    invite_code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    icon = models.ImageField(upload_to="store_icons", null=True, blank=True)
    cover_image = models.ImageField(upload_to="store_cover_images", null=True, blank=True)
    store_signature = models.ImageField(upload_to="store_signatures", null=True, blank=True)
    storehandle = models.CharField(max_length=35, null=True, blank=True)
    category_name = models.CharField(max_length=50, null=True, blank=True)
    value_added_to_whitelabel = models.BooleanField(default=False)
    deleted = models.BooleanField(default=False)

    trustcenter_detail = models.BooleanField(default=False)
    add_products = models.BooleanField(default=False)

    open_for_order = models.BooleanField(default=False)
    # config_receive_orders = models.BooleanField(default=False)
    is_test_store = models.BooleanField(default=False)

    gst_number = models.CharField(max_length=50, null=True, blank=True)
    is_gst_verified = models.BooleanField(default=False)
    gst_verified_time = models.DateTimeField(blank=True, null=True)
    gst_business_name = models.CharField(max_length=100, null=True, blank=True)

    pan_number = models.CharField(max_length=50, null=True, blank=True)
    pan_name = models.CharField(max_length=50, null=True, blank=True)
    is_pan_verified = models.BooleanField(default=False)
    pan_verified_time = models.DateTimeField(blank=True, null=True)
    verification_requested_time = models.DateTimeField(blank=True, null=True)
    verification_type = models.CharField(
        max_length=30, choices=Verification_Type.choices, null=True, blank=True
    )
    is_verification_completed = models.BooleanField(default=False)
    first_verified_date = models.DateTimeField(blank=True, null=True)
    ID_verification_requested = ArrayField(
        models.CharField(max_length=3, choices=[('GST', 'GST'), ('PAN', 'PAN')]),
        default=list,
        blank=True
    )
    is_auto_withdrawal_enabled = models.BooleanField(default=False)

    xmpp_jid = models.CharField(max_length=255, unique=True, null=True, blank=True)
    xmpp_password = models.CharField(max_length=255, null=True, blank=True)
    js_messaging_token = models.CharField(max_length=255, null=True, blank=True)

    subscription_type = models.CharField(max_length=10, choices=SubscriptionType.choices, default=SubscriptionType.FREE)
    #new messaging fields
    new_messaging_token = models.CharField(max_length=300, null=True, blank=True)
    new_messaging_user_id = models.CharField(max_length=200,unique=True, null=True, blank=True)

    #storeAI messaging fields
    store_ai_reference = models.CharField(max_length=25, unique=True, null=True, blank=True)
    store_ai_messaging_token = models.CharField(max_length=300, null=True, blank=True)
    store_ai_messaging_user_id = models.CharField(max_length=200, unique=True, null=True, blank=True)

    #Store Valuation fields
    store_valuation = models.FloatField(default=0)
    store_level = models.CharField(max_length=5, blank=True, null=True, default="1")

    #store count fields
    store_supporters_count = models.IntegerField(default=0)
    store_sales_count = models.IntegerField(default=0)
    store_order_count = models.IntegerField(default=0)
    store_review_count = models.IntegerField(default=0)
    store_avg_rating = models.FloatField(default=0)
    store_product_review_count = models.IntegerField(default=0)
    store_product_avg_rating = models.FloatField(default=0)

    #analytics fields
    analytics_view_count = models.IntegerField(default=0)

    def __str__(self):
        return self.store_name

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__icon = self.icon
        self.__cover_image = self.cover_image
        self.__gst_number = self.gst_number
        self.__pan_number = self.pan_number

    def save(self, *args, **kwargs):
        if self.storeid is None:
            self.store_reference = get_store_reference()
        if self.__icon is None and self.icon:
            new_image = compress(self.icon, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
            self.icon = new_image
        if self.__cover_image is None and self.cover_image:
            new_image = compress(self.cover_image, min_image_length=2000, desired_min_size_in_KB=120, desired_max_size_in_KB=140)
            self.cover_image = new_image
        if self.__gst_number != self.gst_number:
            self.is_gst_verified = False
            self.verification_requested_time = datetime.now()
        if self.__pan_number != self.pan_number:
            self.is_pan_verified = False
            self.verification_requested_time = datetime.now()

        super(Store, self).save(*args, **kwargs)

        if self.is_test_store:
            store_config = StoreConfig.objects.filter(store_reference=self).first()
            if store_config:
                store_config.enable_orders = True
            else:
                StoreConfig.objects.create(store_reference=self)
        else:
            store_config = StoreConfig.objects.filter(store_reference=self).first()
            if store_config:
                store_config.enable_orders = False
            else:
                StoreConfig.objects.create(store_reference=self)

        self.__icon = self.icon
        self.__cover_image = self.cover_image
        self.__gst_number = self.gst_number
        self.__pan_number = self.pan_number

    class Meta:
        db_table = '"store"."store"'

#TODO change the way the UID is created for better optimisation
def get_store_reference():
    # random_digits = "".join(random.choices(string.digits, k=7))
    # store_reference = "S" + random_digits
    store_reference = f'S{int(time.time() * 1000)}'
    if Store.objects.filter(store_reference=store_reference).exists():
        store_reference = get_store_reference()
        return store_reference
    return store_reference


class Storelink(models.Model):
    storelinkid = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        related_name="storelinks",
        db_column="store_reference",
    )
    storeid = models.IntegerField()
    storelink_name = models.CharField(max_length=100)
    store_link = models.CharField(max_length=255)

    def __str__(self):
        return self.storelink_name

    class Meta:
        verbose_name_plural = "store links"
        db_table = '"store"."store_link"'


class Cities(models.Model):
    city_id = models.AutoField(primary_key=True)
    city = models.CharField(max_length=30)

    class Meta:
        verbose_name_plural = "cities"
        db_table = '"store"."cities"'


class BusinessTypes(models.Model):
    business_type_id = models.AutoField(primary_key=True)
    business_type = models.CharField(max_length=40)

    class Meta:
        verbose_name_plural = "business types"
        db_table = '"store"."business_types"'


class StoreSubscription(models.Model):
    class Subscription_Status(models.TextChoices):
        ACTIVE = "ACTIVE", _("Active")
        INACTIVE = "INACTIVE", _("Inactive")
        EXPIRED = "EXPIRED", _("Expired")
    store_subscription_id = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        related_name="store_subscriptions",
        db_column="store_reference",
    )
    subscription_plan_reference = models.ForeignKey(
        "swadesic_admin.SubscriptionPlan",
        to_field="plan_reference",
        on_delete=models.CASCADE,
        related_name="store_subscriptions",
        db_column="subscription_plan_reference",
        null=True,
        blank=True,
    )
    subscription_start_date = models.DateTimeField(auto_now_add=True)
    subscription_end_date = models.DateTimeField(blank=True, null=True)
    subscription_status = models.CharField(max_length=100, choices=Subscription_Status.choices, default=Subscription_Status.INACTIVE)
    razorpay_payment_id = models.CharField(max_length=100, blank=True, null=True)
    razorpay_subscription_id = models.CharField(max_length=100, blank=True, null=True)
    auto_renew = models.BooleanField(default=False)
    is_scheduled_for_cancellation = models.BooleanField(default=False)
    scheduled_cancellation_date = models.DateTimeField(null=True, blank=True)
    cancellation_reason = models.TextField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = '"store"."store_subscription"'

