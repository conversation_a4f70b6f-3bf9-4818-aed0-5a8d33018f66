from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import generics, mixins
from rest_framework.mixins import UpdateModelMixin
from .models import Posts, PostImages, Story, ReviewRequest
from .serializers import PostsCreateSerializer, PostsSerializer, PostImagesSerializer, StorySerializer, StoryViewSerializer
from rest_framework.parsers import MultiPartParser, FormParser
from django.db import transaction
from users.user_api.models import User, UserFollow, UserStore
from stores.store_api.models import Store
from GraphDB.models import Neo4jStore, Neo4jUser, Neo4jPost, Neo4jStory, Neo4jEntity, Neo4jProduct, Neo4jContent
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .models import PostLikes
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from common.util.support_helper import compress
from products.models import Product
import logging
from django.shortcuts import get_object_or_404
from GraphDB.queries import dbqueries
from rest_framework.parsers import <PERSON>PartParser, FormParser
from django.conf import settings
import os
from datetime import datetime
from django.utils import timezone
import uuid
from datetime import timedelta
import re
import logging
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.core.files.storage import default_storage



logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class PostsCreateView(APIView):
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        serializer = PostsCreateSerializer(data=request.data)

        if serializer.is_valid():
            # Manually set the post_reference and save
            post_instance = serializer.save(post_reference=Posts.get_post_reference(serializer.instance))
            post_reference = post_instance.post_reference
            posted_by_user = post_instance.user_reference
            post_images = request.FILES.getlist('post_images')

            neo4j_post = Neo4jPost(
                post_reference=post_reference,
                post_text=post_instance.post_text,
            )
            neo4j_post.save()
            try:
                if posted_by_user:
                    neo4j_user = Neo4jUser.nodes.get(reference=posted_by_user.user_reference)
                    neo4j_post.posted_by_user.connect(neo4j_user)
                else:
                    neo4j_store = Neo4jStore.nodes.get(reference=post_instance.store_reference.store_reference)
                    neo4j_post.posted_by_store.connect(neo4j_store)
            except:
                logger.info("Post Created connection Failed")

            if neo4j_post.post_reference:  # Checking if the node was created successfully
                logger.info(f"Neo4jPost node created successfully: {neo4j_post.post_reference}")
            else:
                logger.warning("Failed to create Neo4jUser node")


            post_images_data = []
            form_data = {}

            # Check if there is an image file in the request
            if post_images:
                form_data["post_reference"] = post_reference
            for image in post_images:
                compressed_image = compress(image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
                form_data["post_image"] = compressed_image
                post_images_serializer = PostImagesSerializer(data=form_data)
                if post_images_serializer.is_valid():
                    post_images_serializer.save()


            post_serializer = PostsSerializer(post_instance, context={
                'visitor_reference': post_instance.user_reference.user_reference if posted_by_user
                else post_instance.store_reference.store_reference})

            return Response(
                {"post_data": post_serializer.data,
                 }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, *args, **kwargs):
        post_reference = request.data.get('post_reference')
        post_text = request.data.get('post_text')
        post_images = request.FILES.getlist('post_images')
        post_instance = Posts.objects.get(post_reference=post_reference)
        posted_by_user = post_instance.user_reference

        if not post_text and not post_images:
            return Response(
                {"message": "please enter atleast one key value"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            post_instance = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        except Posts.DoesNotExist:
            return Response(
                {"message": f"Entry with post reference {post_reference} does not exist."},
                status=status.HTTP_404_NOT_FOUND
            )

        if post_text:
            # If post_text is provided, update using PostsCreateSerializer
            post_text_serializer = PostsCreateSerializer(instance=post_instance, data={'post_text': post_text},
                                                      partial=True)
            post_text_serializer.is_valid(raise_exception=True)
            post_text_serializer.save()

        if post_images:
            # If post_images are provided, update using PostImagesSerializer
            post_images_data = [{'post_reference': post_instance.post_reference, 'post_image': image}
                                for image in post_images]

            # Use a transaction for efficient update
            with transaction.atomic():
                # Add new post images to the existing ones
                post_images_serializer = PostImagesSerializer(
                    data=post_images_data,
                    many=True,
                )
                if post_images_serializer.is_valid():
                    # Save new post images
                    post_images_serializer.save()
                    # Retrieve all post images after the update

        post_serializer = PostsSerializer(post_instance, context={
            'visitor_reference': post_instance.user_reference.user_reference if posted_by_user
            else post_instance.store_reference.store_reference})

        return Response({"post_data": post_serializer.data},
        status=status.HTTP_200_OK)

    def delete(self, request, *args, **kwargs):
        post_reference = request.data.get('post_reference')

        try:
            post_instance = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        except Posts.DoesNotExist:
            return Response(
                {"message": f"Entry with post reference {post_reference} does not exist."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Soft delete post by setting is_deleted to True
        post_instance.is_deleted = True
        post_instance.save()

        post_images_queryset = PostImages.objects.filter(post_reference=post_instance.post_reference, is_deleted=False)

        if post_images_queryset.exists():
            post_images_queryset.update(is_deleted=True)

        return Response({"message": "Post and associated images deleted successfully"}, status=status.HTTP_204_NO_CONTENT)


class GetPostsAV(UpdateModelMixin,generics.GenericAPIView):

    queryset = Posts.objects.all()
    serializer_class = PostsSerializer

    def get(self, request, *args, **kwargs):
        reference = self.request.query_params.get("creator")
        visitor_reference = self.request.query_params.get("visitor")
        if reference.startswith('U'):
            user_posts = Posts.objects.filter(user_reference=reference, is_deleted=False)
            posts_serializer = PostsSerializer(user_posts, many=True, context={'visitor_reference': visitor_reference})
        else:
            store_posts = Posts.objects.filter(store_reference=reference, is_deleted=False)
            posts_serializer = PostsSerializer(store_posts, many=True, context={'visitor_reference': visitor_reference})

        for post_data in posts_serializer.data:
            if 'post_images' in post_data:
                post_data['post_images'] = [image for image in post_data['post_images'] if image is not None]

        return Response(
            {
                "message": "success",
                "data": posts_serializer.data
            }, status=status.HTTP_200_OK
        )


class LikeUnlikePost(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Like or unlike a post",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["post_reference", "liked_by_user", "liked_by_store"],
            properties={
                "post_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "liked_by_user": openapi.Schema(type=openapi.TYPE_STRING),
                "liked_by_store": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        post_reference = request.data.get('post_reference')
        liked_by_user = request.data.get('liked_by_user')
        liked_by_store = request.data.get('liked_by_store')

        post_instance = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        posted_by = post_instance.user_reference if post_instance.user_reference else post_instance.store_reference

        if not post_reference:
            return Response({"message": "error", "detail": "Missing post_reference"}, status=status.HTTP_400_BAD_REQUEST)

        if not (liked_by_user or liked_by_store):
            return Response({"message": "error", "detail": "Missing liked_by_user or liked_by_store"}, status=status.HTTP_400_BAD_REQUEST)

        image = None

        if liked_by_user:
            liked_by_store = None
            store_instance = None
            user_instance = User.objects.get(user_reference=liked_by_user)
            post_serializer = PostsSerializer(post_instance,context={'visitor_reference': liked_by_user})

            if not user_instance:
                return Response({"message": "error", "detail": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
            if user_instance.icon:
                image = user_instance.icon

        if liked_by_store:
            liked_by_user = None
            user_instance = None
            store_instance = Store.objects.get(store_reference=liked_by_store)
            post_serializer = PostsSerializer(post_instance,context={'visitor_reference': liked_by_store})

            if not store_instance:
                return Response({"message": "error", "detail": "Store not found"}, status=status.HTTP_400_BAD_REQUEST)
            if store_instance.icon:
                image = store_instance.icon

        # Try to find an existing PostLikes record
        if PostLikes.objects.filter(post_reference=post_reference, liked_by_user=liked_by_user, liked_by_store=liked_by_store).exists():
            instance = PostLikes.objects.get(post_reference=post_reference, liked_by_user=liked_by_user, liked_by_store=liked_by_store)
            if instance.is_liked:
                instance.is_liked = False
                instance.save(update_fields=['is_liked'])
                return Response({"message": "success", "data": post_serializer.data}, status=status.HTTP_200_OK)
            else:
                instance.is_liked = True
                instance.save(update_fields=['is_liked'])
                # Create a notification
                notification_handler = NotificationHandler(
                    notified_user= posted_by,
                    notification_type=Notifications.Notifications_Type.POST_LIKED,
                    notification_about=liked_by_user if liked_by_user else liked_by_store,
                    image=image,
                )
                notification_handler.create_notification(notification_handler)
                return Response({"message": "success", "data": post_serializer.data}, status=status.HTTP_200_OK)
        else:
            PostLikes.objects.create(
                post_reference=post_instance,
                liked_by_user=user_instance,
                liked_by_store=store_instance,
                is_liked=True,
            )
            # Create a notification
            notification_handler = NotificationHandler(
                notified_user=posted_by,
                notification_type=Notifications.Notifications_Type.POST_LIKED,
                notification_about=liked_by_user if liked_by_user else liked_by_store,
                image=image,
            )
            notification_handler.create_notification(notification_handler)
            return Response({"message": "success", "data": post_serializer.data}, status=status.HTTP_200_OK)


class DeletePostImagesView(APIView):
    def delete(self, request, *args, **kwargs):
        post_image_id = kwargs.get('post_image_id')

        try:
            post_image_instance = PostImages.objects.get(post_image_id=post_image_id, is_deleted=False)

            # Soft delete post images by setting is_deleted to True
            post_image_instance.is_deleted = True
            post_image_instance.save()

            return Response({"message": "Post image soft deleted successfully"}, status=status.HTTP_200_OK)

        except PostImages.DoesNotExist:
            return Response(
                {"message": f"No post image found for post image ID {post_image_id}."},
                status=status.HTTP_404_NOT_FOUND
            )

        except Exception as e:
            return Response(
                {"message": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetSinglePostAV(generics.RetrieveAPIView):
    def get(self, request, *args, **kwargs):
        post_reference = self.request.query_params.get("post_reference")
        visitor_reference = self.request.query_params.get("visitor")

        post_instance = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        post_serializer = PostsSerializer(post_instance, context={'visitor_reference': visitor_reference})

        if 'post_images' in post_serializer.data:
            post_serializer.data['post_images'] = [image for image in post_serializer.data['post_images'] if image is not None]

        return Response(
            {
                "message": "success",
                "data": post_serializer.data
            }, status=status.HTTP_200_OK
        )


class GetFollowersOfPostCreator(APIView):
    def get(self,request, *args, **kwargs):
        post_reference=request.data.get("post_reference")
        post_instance = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        follower_references = []
        posted_by = post_instance.user_reference if post_instance.user_reference else post_instance.store_reference

        if isinstance(posted_by, User):
            followers = UserFollow.objects.filter(user_reference=posted_by,
                                                  is_following=True)  # this has user follower and store follower
            for entry in followers:
                follower = (
                    entry.user_follower
                    if entry.user_follower
                    else entry.store_follower
                )
                follower_reference = follower.user_reference if entry.user_follower else follower.store_reference
                follower_references.append(follower_reference)
        else:
            followers = UserStore.objects.filter(store_reference=posted_by,
                                                 is_following=True)  # this has user supporter and store supporter
            for entry in followers:
                follower = (
                    entry.user_supporter
                    if entry.user_supporter
                    else entry.store_supporter
                )
                follower_reference = follower.user_reference if entry.user_supporter else follower.store_reference
                follower_references.append(follower_reference)

        # if post_instance.user_reference:
        #     followers = UserFollow.objects.filter(user_reference=posted_by.user_reference, is_following=True)
        #     user_follower_references = followers.exclude(store_follower__isnull=True).values_list(
        #         'user_follower__user_reference', flat=True)
        #
        #     store_follower_references = followers.exclude(user_follower__isnull=True).values_list(
        #         'store_follower__store_reference', flat=True)
        #     follower_references.extend(user_follower_references)
        #     follower_references.extend(store_follower_references)
        #
        # else:
        #     followers = UserStore.objects.filter(store_reference=posted_by.store_reference, is_following=True)
        #     user_supporter_references = followers.exclude(store_supporter__isnull=True).values_list(
        #         'user_supporter__user_reference', flat=True)
        #     store_supporter_references = followers.exclude(user_supporter__isnull=True).values_list(
        #         'store_supporter__store_reference', flat=True)
        #     follower_references.extend(user_supporter_references)
        #     follower_references.extend(store_supporter_references)

        return Response(
            {
                "message": "success",
                "data": follower_references
            }, status=status.HTTP_200_OK
        )


class StoryAPIView(APIView):
    def get(self, request):
        story_reference = request.query_params.get('story_reference')
        if story_reference:
            # Get single story
            story = get_object_or_404(Story, story_reference=story_reference)
            serializer = StorySerializer(story)
            return Response({"message": "success", "data": serializer.data}, status=status.HTTP_200_OK)
        else:
            return Response({"message": "story_reference is required"}, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = StorySerializer(data=request.data)
        if serializer.is_valid():
            with transaction.atomic():
                try:
                    # Get references from validated data
                    entity_reference = serializer.validated_data.pop('entity_reference')
                    if serializer.validated_data.get('product_reference'):
                        product_reference = serializer.validated_data.pop('product_reference')
                    else:
                        product_reference = None
                    tagged_products = serializer.validated_data.get('tagged_content')

                    # Save the story with the remaining validated data
                    story = serializer.save()
                    
                    # Neo4j operations
                    neo4j_entity = Neo4jEntity.nodes.get(reference=entity_reference)
                    max_order = 0
                    if product_reference is not None:
                        neo4j_product = Neo4jProduct.nodes.get(reference=product_reference)
                    
                        # Get the maximum order of existing stories for this product
                        existing_stories, max_order = neo4j_product.get_stories()
                    # Create Neo4j Story node
                    neo4j_story = Neo4jStory(
                        reference=story.story_reference,
                        created_date=story.created_at,
                    )
                    neo4j_story.save()
                    
                    # Create relationships
                    neo4j_story.posted_by.connect(neo4j_entity)
                    
                    # Create relationship with incremented order
                    if product_reference is not None:
                        neo4j_story.has_story.connect(neo4j_product, {
                            'order': max_order + 1
                        })
                    else:
                        neo4j_entity.has_story.connect(neo4j_story, {
                            'order': max_order + 1
                        })

                    if tagged_products:
                        taggable_products, untaggable_products = dbqueries.check_products_taggability(tagged_products)
                        if untaggable_products:
                            return Response({"message": "error", "detail": f"Products {', '.join(untaggable_products)} cannot be tagged in stories"}, status=status.HTTP_400_BAD_REQUEST)
                        product_nodes = Neo4jProduct.nodes.filter(reference__in=taggable_products)
                        for product_node in product_nodes:
                            neo4j_story.has_tag.connect(product_node)
                    
                    logger.info(f"Neo4jStory node created successfully: {neo4j_story.reference}")
                    return Response({"message": "success", "data": serializer.data}, status=status.HTTP_201_CREATED)
                except Exception as e:
                    logger.error(f"Neo4jStory node creation failed: {str(e)}")
                    raise  # This will trigger rollback due to transaction.atomic()

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request):
        story_reference = request.query_params.get('story_reference')
        story = get_object_or_404(Story, story_reference=story_reference)
        neo4j_story = Neo4jStory.nodes.get(reference=story_reference)
        with transaction.atomic():
            tagged_products = request.data.get('tagged_content')    
            if sorted(tagged_products) != sorted(story.tagged_content):
                taggable_products, untaggable_products = dbqueries.check_products_taggability(tagged_products)
                if untaggable_products:
                    return Response({"message": "error", "detail": f"Products {', '.join(untaggable_products)} cannot be tagged in stories"}, status=status.HTTP_400_BAD_REQUEST)
                product_nodes = Neo4jProduct.nodes.filter(reference__in=taggable_products)
                for product_node in product_nodes:
                    neo4j_story.has_tag.connect(product_node)
            for key, value in request.data.items():
                setattr(story, key, value)
            story.save()
            serializer = StorySerializer(story)
            return Response({"message": "success", "data": serializer.data}, status=status.HTTP_200_OK)

    def delete(self, request):
        story_reference = request.query_params.get('story_reference')
        with transaction.atomic():
            try:
                # Get the story from PostgreSQL
                story = get_object_or_404(Story, story_reference=story_reference)
                
                # Get and delete Neo4j story node
                try:
                    neo4j_story = Neo4jStory.nodes.get(reference=story_reference)
                    
                    # Get the product and current order before deletion
                    product = neo4j_story.has_story.all()[0]  # Get the connected product
                    current_order = neo4j_story.has_story.relationship(product).order
                    
                    # Delete the Neo4j node
                    neo4j_story.delete()
                    
                    # Reorder remaining stories
                    dbqueries.reorder_stories_after_deletion(product, current_order)
                    
                    logger.info(f"Neo4j story node deleted: {story_reference}")
                except Neo4jStory.DoesNotExist:
                    logger.warning(f"Neo4j story node not found: {story_reference}")
                
                # Delete PostgreSQL story
                story.delete()
                
                return Response({"message": "success"}, status=status.HTTP_200_OK)
                
            except Exception as e:
                logger.error(f"Failed to delete story: {str(e)}")
                raise


class GetStoriesOfProduct(APIView):
    def get(self, request):
        product_reference = request.query_params.get('product_reference')
        stories, _ = dbqueries.get_product_stories(product_reference=product_reference)
        story_instances = Story.objects.filter(story_reference__in=stories)
        serializer = StorySerializer(story_instances, many=True)
        return Response({"message": "success", "data": serializer.data}, status=status.HTTP_200_OK) 


class StoryImageUploadView(APIView):
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request):
        try:
            story_reference = request.data.get('story_reference')
            image_hash = request.data.get('image_hash')
            image_file = request.FILES.get('image_file')
            compressed_image_file = compress(image_file, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)

            if not all([story_reference, image_hash, image_file]):
                return Response(
                    {"error": "story_reference, image_hash, and image file are required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the story
            story = get_object_or_404(Story, story_reference=story_reference)
            sections = story.sections
            image_updated = False

            # Generate file path
            file_extension = os.path.splitext(compressed_image_file.name)[1]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"story_{story_reference}_{timestamp}{file_extension}"
            relative_path = f"story_images/{story_reference}/{filename}"
            full_path = os.path.join(settings.MEDIA_ROOT, relative_path)

            # Ensure directory exists
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # Update sections data
            for section in sections:
                if section['type'] == 'image' and 'images' in section:
                    for image in section['images']:
                        if image.get('image_url') == image_hash:
                            # Save the file
                            with open(full_path, 'wb+') as destination:
                                for chunk in image_file.chunks():
                                    destination.write(chunk)

                            # Update the image URL
                            image['image_url'] = f"/media/{relative_path}"
                            image_updated = True
                            break
                    if image_updated:
                        break

            if not image_updated:
                return Response(
                    {"error": "Image hash not found in story sections"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Update story with new sections data
            story.sections = sections
            story.save()

            return Response({
                "message": "Image uploaded successfully",
                "image_url": f"/media/{relative_path}"
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error uploading image: {str(e)}")
            return Response(
                {"error": "Failed to upload image"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DeleteStoryImageView(APIView):
    def delete(self, request):
        try:
            # Get parameters
            story_reference = request.query_params.get('story_reference')
            section_order = request.query_params.get('section_order')
            image_order = request.query_params.get('image_order')

            if not all([story_reference, section_order, image_order]):
                return Response({
                    "error": "story_reference, section_order, and image_order are required"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Convert to integers
            try:
                section_order = int(section_order)
                image_order = int(image_order)
            except ValueError:
                return Response({
                    "error": "section_order and image_order must be integers"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the story
            story = get_object_or_404(Story, story_reference=story_reference)
            sections = story.sections
            image_deleted = False
            image_url_to_delete = None

            # Find and update the section
            for section in sections:
                if section.get('order') == section_order and section.get('type') == 'image':
                    images = section.get('images', [])
                    
                    # Find the image to delete
                    for i, image in enumerate(images):
                        if image.get('order') == image_order:
                            image_url_to_delete = image.get('image_url')
                            images.pop(i)
                            image_deleted = True
                            break
                    
                    if image_deleted:
                        # Reorder remaining images
                        for new_order, image in enumerate(images, 1):
                            image['order'] = new_order
                        
                        # Update section with reordered images
                        section['images'] = images
                        break

            if not image_deleted:
                return Response({
                    "error": "Image not found in specified section"
                }, status=status.HTTP_404_NOT_FOUND)

            # Delete the actual image file if it exists on the server
            if image_url_to_delete and image_url_to_delete.startswith('/media/'):
                try:
                    file_path = os.path.join(settings.MEDIA_ROOT, 
                                           image_url_to_delete.replace('/media/', '', 1))
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    logger.warning(f"Failed to delete image file: {str(e)}")

            # Update story with modified sections
            story.sections = sections
            story.save()

            return Response({
                "message": "Image deleted successfully",
                "updated_sections": sections
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error deleting image: {str(e)}")
            return Response({
                "error": "Failed to delete image"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _validate_section_structure(self, section):
        """Validate section structure after modification"""
        if section['type'] == 'image':
            if not section.get('images'):
                return False
            orders = [img.get('order') for img in section['images']]
            return len(orders) == len(set(orders)) and all(isinstance(o, int) for o in orders)
        return True


class GetStoriesofEntity(APIView):
    def get(self, request):
        entity_reference = request.query_params.get('entity_reference')
        stories = dbqueries.get_entity_stories(entity_reference=entity_reference)
        story_instances = Story.objects.filter(story_reference__in=stories)
        serializer = StoryViewSerializer(story_instances, many=True)
        return Response({"message": "success", "data": serializer.data}, status=status.HTTP_200_OK) 


class TagContent(APIView):
    def post(self, request):
        content_reference = request.data.get('content_reference')
        tagged_content = request.data.get('tagged_content')

        if not all([content_reference, tagged_content]):
            return Response({"error": "content_reference and tagged_content are required"}, status=status.HTTP_400_BAD_REQUEST)
        
        neo4j_content = Neo4jContent.nodes.get(reference=content_reference)

        taggable_content, untaggable_content = dbqueries.check_contents_taggability(tagged_content)
        if untaggable_content:
            return Response({"error": f"Content {', '.join(untaggable_content)} cannot be tagged"}, status=status.HTTP_400_BAD_REQUEST)
        
        taggable_nodes = Neo4jContent.nodes.filter(reference__in=taggable_content)
        for content_node in taggable_nodes:
            neo4j_content.has_tag.connect(content_node)

        return Response({"message": "Content tagged successfully"}, status=status.HTTP_200_OK)

    def delete(self, request):
        content_reference = request.data.get('content_reference')
        tagged_content = request.data.get('tagged_content')

        if not all([content_reference, tagged_content]):
            return Response(
                {"error": "content_reference and tagged_content are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            neo4j_content = Neo4jContent.nodes.get(reference=content_reference)
            
            # Get all nodes that need to be untagged
            tagged_nodes = Neo4jContent.nodes.filter(reference__in=tagged_content)
            
            # Remove the tag relationships
            for content_node in tagged_nodes:
                neo4j_content.has_tag.disconnect(content_node)

            return Response(
                {"message": "Content untagged successfully"}, 
                status=status.HTTP_200_OK
            )
            
        except Neo4jContent.DoesNotExist:
            return Response(
                {"error": "Content reference not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CreateExternalReviewRequest(APIView):
    def validate_user_identifier(self, user_identifier):
        """
        Validate and categorize user identifier
        Returns: (type, normalized_identifier)
        """
        # User Reference
        if re.match(r'^U\d{13}$', user_identifier):
            return 'user_reference', user_identifier

        # Phone Number
        phone_match = re.match(r'^\+?(\d{12})$', user_identifier)
        if phone_match:
            # Normalize phone number: remove non-digit characters
            normalized_phone = re.sub(r'\D', '', user_identifier)
            return 'phone', '+' + normalized_phone

        # user Name
        if re.match(r'^[a-zA-Z0-9_.-]+$', user_identifier):
            return 'user_name', user_identifier

        # Email
        try:
            validate_email(user_identifier)
            return 'email', user_identifier.lower()
        except ValidationError:
            logger.warning(f"Invalid user identifier format: {user_identifier}")
            return None, None


    def post(self, request):
        # Input validation
        product_reference = request.data.get('product_reference')
        user_identifier = request.data.get('user_identifier')

        if not product_reference or not user_identifier:
            logger.warning("Missing product reference or user identifier")
            return Response({
                'error': 'Product reference and user identifier must be provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate identifier
        identifier_type, normalized_identifier = self.validate_user_identifier(user_identifier)
        if not identifier_type:
            return Response({
                'error': 'Invalid user identifier format'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Product existence check
            product = Product.objects.get(
                product_reference=product_reference, 
                deleted=False
            )

            # User lookup based on identifier type
            user_query_kwargs = {
                'deleted': False
            }
            if identifier_type == 'user_reference':
                user_query_kwargs['user_reference'] = normalized_identifier
            elif identifier_type == 'phone':
                user_query_kwargs['phonenumber'] = normalized_identifier
            elif identifier_type == 'user_name':
                user_query_kwargs['user_name'] = normalized_identifier
            else:
                user_query_kwargs['email'] = normalized_identifier

            user = User.objects.filter(**user_query_kwargs).first()

            if user:
                # Collect user identifiers
                user_identifiers = [
                    user.user_reference or "",
                    user.email or "",
                    user.phonenumber or "",
                    user.user_name or ""
                ]
            else:
                # If user not found, use the normalized identifier
                user_identifiers = [normalized_identifier]

            # Check for existing used review request
            existing_used_request = ReviewRequest.objects.filter(
                product_reference=product.product_reference,
                user_identifier__in=user_identifiers,
                is_used=True,
            ).first()

            if existing_used_request:
                logger.info(f"Used review request found for product {product_reference}")
                return Response({
                    'message': 'Already used review request found',
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check for existing unused expired review request
            existing_unused_expired_request = ReviewRequest.objects.filter(
                product_reference=product.product_reference,
                user_identifier__in=user_identifiers,
                is_used=False,
                expires_at__lt=timezone.now()
            ).first()

            if existing_unused_expired_request:
                logger.info(f"Extending expired review request for product {product_reference}")
                existing_unused_expired_request.expires_at = timezone.now() + timedelta(days=7)
                existing_unused_expired_request.save()
                return Response({
                    'message': 'Existing review request found',
                    'token': existing_unused_expired_request.token,
                    'expires_at': existing_unused_expired_request.expires_at,
                    'user_identifier': existing_unused_expired_request.user_identifier
                }, status=status.HTTP_200_OK)

            # Check for existing unused review request
            existing_unused_request = ReviewRequest.objects.filter(
                product_reference=product.product_reference,
                user_identifier__in=user_identifiers,
                is_used=False,
                expires_at__gt=timezone.now()
            ).first()

            if existing_unused_request:
                logger.info(f"Active review request found for product {product_reference}")
                return Response({
                    'message': 'Existing review request found',
                    'token': existing_unused_request.token,
                    'expires_at': existing_unused_request.expires_at,
                    'user_identifier': existing_unused_request.user_identifier
                }, status=status.HTTP_200_OK)

            # Create new review request
            new_request = ReviewRequest.objects.create(
                product_reference=product.product_reference,
                token=uuid.uuid4(),
                user_identifier=user_identifier,
                expires_at=timezone.now() + timedelta(days=7),
                store_reference=""
            )

            logger.info(f"New review request created for product {product_reference}")
            return Response({
                'token': new_request.token,
                'expires_at': new_request.expires_at,
                'user_identifier': new_request.user_identifier
            }, status=status.HTTP_201_CREATED)

        except Product.DoesNotExist:
            logger.warning(f"Product not found: {product_reference}")
            return Response({
                'error': 'Product not found or has been deleted'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Unexpected error in review request: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckExternalReviewRequestExists(APIView):
    def post(self, request):
        product_reference = request.data.get('product_reference')
        user_identifier = request.data.get('user_identifier')

        if not product_reference or not user_identifier:
            return Response({
                'error': 'Product reference and user identifier must be provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        user_instance = User.objects.filter(
            Q(user_name=user_identifier) |
            Q(phonenumber=user_identifier) |
            Q(email=user_identifier)
        ).first()

        user_identifiers = [
            user_instance.user_reference if user_instance else "",
            user_instance.email if user_instance else "",
            user_instance.phonenumber if user_instance else "",
            user_instance.user_name if user_instance else ""
        ]

        review_exists = ReviewRequest.objects.filter(
            product_reference=product.product_reference,
            user_identifier__in=user_identifiers,
            is_used=True).exists()

        if review_exists:
            return Response({
                'success': True,
                'message': 'User already used the review request',
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': 'User does not have a used review request',
            }, status=status.HTTP_200_OK)




class CreateExternalStoreReviewRequest(APIView):
    def validate_user_identifier(self, user_identifier):
        """
        Validate and categorize user identifier
        Returns: (type, normalized_identifier)
        """
        # User Reference
        if re.match(r'^U\d{13}$', user_identifier):
            return 'user_reference', user_identifier

        # Phone Number
        phone_match = re.match(r'^\+?(\d{12})$', user_identifier)
        if phone_match:
            # Normalize phone number: remove non-digit characters
            normalized_phone = re.sub(r'\D', '', user_identifier)
            return 'phone', '+' + normalized_phone

        # user Name
        if re.match(r'^[a-zA-Z0-9_.-]+$', user_identifier):
            return 'user_name', user_identifier

        # Email
        try:
            validate_email(user_identifier)
            return 'email', user_identifier.lower()
        except ValidationError:
            logger.warning(f"Invalid user identifier format: {user_identifier}")
            return None, None


    def post(self, request):
        # Input validation
        store_reference = request.data.get('store_reference')
        user_identifier = request.data.get('user_identifier')

        if not store_reference or not user_identifier:
            logger.warning("Missing store reference or user identifier")
            return Response({
                'error': 'Product reference and user identifier must be provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate identifier
        identifier_type, normalized_identifier = self.validate_user_identifier(user_identifier)
        if not identifier_type:
            return Response({
                'error': 'Invalid user identifier format'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Product existence check
            store = Store.objects.get(
                store_reference=store_reference, 
                deleted=False
            )

            # User lookup based on identifier type
            user_query_kwargs = {
                'deleted': False
            }
            if identifier_type == 'user_reference':
                user_query_kwargs['user_reference'] = normalized_identifier
            elif identifier_type == 'phone':
                user_query_kwargs['phonenumber'] = normalized_identifier
            elif identifier_type == 'user_name':
                user_query_kwargs['user_name'] = normalized_identifier
            else:
                user_query_kwargs['email'] = normalized_identifier

            user = User.objects.filter(**user_query_kwargs).first()

            if user:
                # Collect user identifiers
                user_identifiers = [
                    user.user_reference or "",
                    user.email or "",
                    user.phonenumber or "",
                    user.user_name or ""
                ]
            else:
                # If user not found, use the normalized identifier
                user_identifiers = [normalized_identifier]

            # Check for existing used review request
            existing_used_request = ReviewRequest.objects.filter(
                store_reference=store.store_reference,
                user_identifier__in=user_identifiers,
                is_used=True,
            ).first()

            if existing_used_request:
                logger.info(f"Used review request found for product {product_reference}")
                return Response({
                    'message': 'Already used review request found',
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check for existing unused expired review request
            existing_unused_expired_request = ReviewRequest.objects.filter(
                store_reference=store.store_reference,
                user_identifier__in=user_identifiers,
                is_used=False,
                expires_at__lt=timezone.now()
            ).first()

            if existing_unused_expired_request:
                logger.info(f"Extending expired review request for product {product_reference}")
                existing_unused_expired_request.expires_at = timezone.now() + timedelta(days=7)
                existing_unused_expired_request.save()
                return Response({
                    'message': 'Existing review request found',
                    'token': existing_unused_expired_request.token,
                    'expires_at': existing_unused_expired_request.expires_at,
                    'user_identifier': existing_unused_expired_request.user_identifier
                }, status=status.HTTP_200_OK)

            # Check for existing unused review request
            existing_unused_request = ReviewRequest.objects.filter(
                store_reference=store.store_reference,
                user_identifier__in=user_identifiers,
                is_used=False,
                expires_at__gt=timezone.now()
            ).first()

            if existing_unused_request:
                logger.info(f"Active review request found for store {store_reference}")
                return Response({
                    'message': 'Existing review request found',
                    'token': existing_unused_request.token,
                    'expires_at': existing_unused_request.expires_at,
                    'user_identifier': existing_unused_request.user_identifier
                }, status=status.HTTP_200_OK)

            # Create new review request
            new_request = ReviewRequest.objects.create(
                store_reference=store.store_reference,
                product_reference="",
                token=uuid.uuid4(),
                user_identifier=user_identifier,
                expires_at=timezone.now() + timedelta(days=7),
            )

            logger.info(f"New review request created for store {store_reference}")
            return Response({
                'token': new_request.token,
                'expires_at': new_request.expires_at,
                'user_identifier': new_request.user_identifier
            }, status=status.HTTP_201_CREATED)

        except Store.DoesNotExist:
            logger.warning(f"Store not found: {store_reference}")
            return Response({
                'error': 'Store not found or has been deleted'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Unexpected error in review request: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckExternalStoreReviewRequestExists(APIView):
    def post(self, request):
        store_reference = request.data.get('store_reference')
        user_identifier = request.data.get('user_identifier')

        if not store_reference or not user_identifier:
            return Response({
                'error': 'Store reference and user identifier must be provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        user_instance = User.objects.filter(
            Q(user_name=user_identifier) |
            Q(phonenumber=user_identifier) |
            Q(email=user_identifier)
        ).first()

        user_identifiers = [
            user_instance.user_reference if user_instance else "",
            user_instance.email if user_instance else "",
            user_instance.phonenumber if user_instance else "",
            user_instance.user_name if user_instance else ""
        ]

        review_exists = ReviewRequest.objects.filter(
            store_reference=store_reference,
            user_identifier__in=user_identifiers,
            is_used=True).exists()

        if review_exists:
            return Response({
                'success': True,
                'message': 'User already used the review request',
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': 'User does not have a used review request',
            }, status=status.HTTP_200_OK)






# class UpdateStoryImageView(APIView):
#     parser_classes = (MultiPartParser, FormParser)
#
#     def put(self, request):
#         try:
#             # Get parameters
#             story_reference = request.data.get('story_reference')
#             section_order = request.data.get('section_order')
#             image_order = request.data.get('image_order')
#             image_file = request.FILES.get('image')
#             new_caption = request.data.get('caption')  # Optional new caption
#
#             # Validate inputs
#             if not all([story_reference, section_order, image_order, image_file]):
#                 return Response({
#                     "error": "story_reference, section_order, image_order, and image file are required"
#                 }, status=status.HTTP_400_BAD_REQUEST)
#
#             try:
#                 section_order = int(section_order)
#                 image_order = int(image_order)
#             except ValueError:
#                 return Response({
#                     "error": "section_order and image_order must be integers"
#                 }, status=status.HTTP_400_BAD_REQUEST)
#
#             # Get the story
#             story = get_object_or_404(Story, story_reference=story_reference)
#             sections = story.sections
#             image_updated = False
#             old_image_url = None
#
#             # Generate new file path
#             file_extension = os.path.splitext(image_file.name)[1].lower()
#             if file_extension not in ['.jpg', '.jpeg', '.png', '.gif']:
#                 return Response({
#                     "error": "Invalid file type. Only jpg, jpeg, png, and gif are allowed."
#                 }, status=status.HTTP_400_BAD_REQUEST)
#
#             timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
#             filename = f"story_{story_reference}_{timestamp}{file_extension}"
#             relative_path = f"story_images/{story_reference}/{filename}"
#             full_path = os.path.join(settings.MEDIA_ROOT, relative_path)
#
#             # Ensure directory exists
#             os.makedirs(os.path.dirname(full_path), exist_ok=True)
#
#             # Find and update the section
#             for section in sections:
#                 if section.get('order') == section_order and section.get('type') == 'image':
#                     for image in section.get('images', []):
#                         if image.get('order') == image_order:
#                             # Store old image URL for deletion
#                             old_image_url = image.get('image_url')
#
#                             # Save new image
#                             with open(full_path, 'wb+') as destination:
#                                 for chunk in image_file.chunks():
#                                     destination.write(chunk)
#
#                             # Update image data
#                             image['image_url'] = f"/media/{relative_path}"
#                             if new_caption:
#                                 image['caption'] = new_caption
#
#                             image_updated = True
#                             break
#                     break
#
#             if not image_updated:
#                 return Response({
#                     "error": "Image not found in specified section"
#                 }, status=status.HTTP_404_NOT_FOUND)
#
#             # Delete old image file if it exists
#             if old_image_url and old_image_url.startswith('/media/'):
#                 try:
#                     old_file_path = os.path.join(settings.MEDIA_ROOT,
#                                                old_image_url.replace('/media/', '', 1))
#                     if os.path.exists(old_file_path):
#                         os.remove(old_file_path)
#                 except Exception as e:
#                     logger.warning(f"Failed to delete old image file: {str(e)}")
#
#             # Update story with modified sections
#             story.sections = sections
#             story.save()
#
#             # Update Neo4j if needed
#             try:
#                 neo4j_story = Neo4jStory.nodes.get(reference=story_reference)
#                 neo4j_story.save()
#             except Exception as e:
#                 logger.error(f"Failed to update Neo4j story: {str(e)}")
#
#             return Response({
#                 "message": "Image updated successfully",
#                 "new_image_url": f"/media/{relative_path}",
#                 "updated_sections": sections
#             }, status=status.HTTP_200_OK)
#
#         except Exception as e:
#             logger.error(f"Error updating image: {str(e)}")
#             return Response({
#                 "error": "Failed to update image"
#             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
#
#     def _validate_image_file(self, image_file):
#         """Validate image file size and type"""
#         # Maximum file size (5MB)
#         MAX_FILE_SIZE = 5 * 1024 * 1024
#
#         if image_file.size > MAX_FILE_SIZE:
#             return False, "File size too large. Maximum size is 5MB."
#
#         # Check file extension
#         valid_extensions = ['.jpg', '.jpeg', '.png', '.gif']
#         ext = os.path.splitext(image_file.name)[1].lower()
#         if ext not in valid_extensions:
#             return False, f"Invalid file type. Allowed types are: {', '.join(valid_extensions)}"
#
#         return True, None