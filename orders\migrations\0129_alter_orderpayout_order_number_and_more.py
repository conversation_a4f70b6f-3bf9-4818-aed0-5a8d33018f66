# Generated by Django 4.2.7 on 2024-08-23 14:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0066_storebystate_storesbycategory"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("orders", "0128_alter_orderpayout_promotional_value_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="orderpayout",
            name="order_number",
            field=models.ForeignKey(
                blank=True,
                db_column="order_number",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="order_payout_items",
                to="orders.order",
                to_field="order_number",
            ),
        ),
        migrations.AlterField(
            model_name="orderpayout",
            name="store_reference",
            field=models.ForeignKey(
                blank=True,
                db_column="store_reference",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="order_payout_items",
                to="stores.store",
                to_field="store_reference",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="orderpayout",
            name="suborder_number",
            field=models.ForeignKey(
                blank=True,
                db_column="suborder_number",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="order_payout_items",
                to="orders.suborder",
                to_field="suborder_number",
            ),
        ),
        migrations.AlterField(
            model_name="orderpayout",
            name="user_reference",
            field=models.ForeignKey(
                blank=True,
                db_column="user_reference",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="order_payout_items",
                to=settings.AUTH_USER_MODEL,
                to_field="user_reference",
            ),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="orderid",
            field=models.ForeignKey(
                db_column="orderid",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="suborderitems",
                to="orders.order",
            ),
        ),
        migrations.AlterField(
            model_name="suborderpaymentdetails",
            name="order_number",
            field=models.ForeignKey(
                blank=True,
                db_column="order_number",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="suborder_payment_detail_items",
                to="orders.order",
                to_field="order_number",
            ),
        ),
        migrations.AlterField(
            model_name="suborderpaymentdetails",
            name="suborder_number",
            field=models.ForeignKey(
                blank=True,
                db_column="suborder_number",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="suborder_payment_detail_items",
                to="orders.suborder",
                to_field="suborder_number",
            ),
        ),
    ]
