# Generated by Django 3.2.13 on 2022-12-13 05:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0053_auto_20221208_1910"),
    ]

    operations = [
        migrations.AlterField(
            model_name="order",
            name="order_status",
            field=models.CharField(
                choices=[
                    ("ORDER_INITIATED", "Order initiated"),
                    ("PAYMENT_INITIATED", "Payment initiated"),
                    ("PAYMENT_SUCCESS", "Payment success"),
                    ("PAYMENT_PENDING", "Payment pending"),
                    ("PAYMENT_FAILED", "Payment failed"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                ],
                default="ORDER_INITIATED",
                max_length=30,
            ),
        ),
        migrations.AlterField(
            model_name="orderpaymentdetails",
            name="payment_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("PAYMENT_INITIATED", "Payment initiated"),
                    ("PAYMENT_SUCCESS", "Payment success"),
                    ("REFUND_INITIATED", "Refund initiated"),
                    ("REFUND_PENDING", "Refund pending"),
                    ("REFUND_SUCCESS", "Refund success"),
                    ("REFUND_FAILED", "Refund failed"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="cancelled_by",
            field=models.CharField(
                blank=True,
                choices=[("buyer", "buyer"), ("seller", "seller")],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="suborder_status",
            field=models.CharField(
                choices=[
                    ("ORDER_INITIATED", "Order initiated"),
                    ("PAYMENT_INITIATED", "Payment initiated"),
                    ("PAYMENT_SUCCESS", "Payment success"),
                    ("PAYMENT_PENDING", "Payment pending"),
                    ("PAYMENT_FAILED", "Payment failed"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("DELIVERY_IN_PROGRESS", "Delivery in progress"),
                    ("ORDER_DELIVERED", "Order delivered"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("ORDER_CANCELLED_BY_BUYER", "Order cancelled by buyer"),
                    ("ORDER_CANCELLED_BY_SELLER", "Order cancelled by seller"),
                    ("DELIVERY_FAILED", "Delivery failed"),
                    ("RETURN_REQUESTED", "Return requested"),
                    ("RETURN_CONFIRMED", "Return confirmed"),
                    ("RETURN_IN_PROGRESS", "Return in progress"),
                    ("RETURNED_TO_SELLER", "Return to seller"),
                    ("RETURN_FAILED", "Return failed"),
                ],
                default="ORDER_INITIATED",
                max_length=50,
            ),
        ),
    ]
