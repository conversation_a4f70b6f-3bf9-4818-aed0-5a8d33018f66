from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from rest_framework.permissions import AllowAny
from django.db import transaction
import logging

from .models import FaqCategory, FaqItem, FaqItemImages
from .serializers import (
    FaqCategorySerializer,
    FaqItemImageSerializer
)
from common.util.support_helper import compress

logger = logging.getLogger(__name__)


class FaqDataAPIView(APIView):
    """
    API view to get all FAQ data in the specified format
    """
    permission_classes = [AllowAny]  # Make it public, adjust as needed

    def get(self, request):
        """
        Get all active FAQ categories with their active items
        """
        try:
            # Get all active categories ordered by their order field
            categories = FaqCategory.objects.filter(is_active=True).order_by('order')

            # Serialize the data
            serializer = FaqCategorySerializer(
                categories,
                many=True,
                context={'request': request}
            )

            # Return in the specified format
            response_data = {
                "faq_categories": serializer.data
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching FAQ data: {str(e)}")
            return Response(
                {"error": "Failed to fetch FAQ data", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FaqImageUploadAPIView(APIView):
    """
    API view to upload images for FAQ items
    """
    parser_classes = [MultiPartParser, FormParser]
    permission_classes = [AllowAny]  # Adjust permissions as needed

    def post(self, request):
        """
        Upload an image for a specific FAQ item

        Expected data:
        - item_key: The unique key of the FAQ item
        - image: The image file to upload
        - order: (optional) Order of the image within the item
        """
        try:
            item_key = request.data.get('item_key')
            image_file = request.FILES.get('image')
            order = request.data.get('order', 0)

            # Validate required fields
            if not item_key:
                return Response(
                    {"error": "item_key is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not image_file:
                return Response(
                    {"error": "image file is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the FAQ item
            try:
                faq_item = FaqItem.objects.get(item_key=item_key, is_active=True)
            except FaqItem.DoesNotExist:
                return Response(
                    {"error": f"FAQ item with key '{item_key}' not found or inactive"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Auto-generate order if not provided or is 0
            if not order or order == 0:
                last_image = FaqItemImages.objects.filter(item=faq_item).order_by('-order').first()
                order = (last_image.order + 1) if last_image else 1

            # Compress the image (following project pattern)
            compressed_image = compress(
                image_file,
                min_image_length=1000,
                desired_min_size_in_KB=60,
                desired_max_size_in_KB=70
            )

            # Create the image record
            with transaction.atomic():
                faq_image = FaqItemImages.objects.create(
                    item=faq_item,
                    image=compressed_image,
                    order=order
                )

            # Serialize and return the created image
            serializer = FaqItemImageSerializer(faq_image, context={'request': request})

            return Response({
                "message": "Image uploaded successfully",
                "image_data": serializer.data
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error uploading FAQ image: {str(e)}")
            return Response(
                {"error": "Failed to upload image", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def delete(self, request):
        """
        Delete a specific FAQ item image

        Expected data:
        - image_id: The ID of the image to delete
        """
        try:
            image_id = request.data.get('image_id')

            if not image_id:
                return Response(
                    {"error": "image_id is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get and delete the image
            try:
                faq_image = FaqItemImages.objects.get(id=image_id)
                faq_image.delete()

                return Response(
                    {"message": "Image deleted successfully"},
                    status=status.HTTP_200_OK
                )

            except FaqItemImages.DoesNotExist:
                return Response(
                    {"error": f"Image with ID '{image_id}' not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        except Exception as e:
            logger.error(f"Error deleting FAQ image: {str(e)}")
            return Response(
                {"error": "Failed to delete image", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FaqCategoryReorderAPIView(APIView):
    """
    API view to reorder FAQ categories
    """
    permission_classes = [AllowAny]  # Adjust permissions as needed

    def post(self, request):
        """
        Reorder categories - move a category from one position to another

        Expected data:
        - category_key: The key of the category to move
        - new_order: The new order position (1-based)
        """
        try:
            category_key = request.data.get('category_key')
            new_order = request.data.get('new_order')

            # Validate required fields
            if not category_key:
                return Response(
                    {"error": "category_key is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if new_order is None:
                return Response(
                    {"error": "new_order is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                new_order = int(new_order)
                if new_order < 1:
                    raise ValueError("Order must be positive")
            except (ValueError, TypeError):
                return Response(
                    {"error": "new_order must be a positive integer"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the category to move
            try:
                category_to_move = FaqCategory.objects.get(category_key=category_key)
            except FaqCategory.DoesNotExist:
                return Response(
                    {"error": f"Category with key '{category_key}' not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get all categories ordered by current order
            all_categories = list(FaqCategory.objects.all().order_by('order'))
            total_categories = len(all_categories)

            # Validate new_order is within bounds
            if new_order > total_categories:
                return Response(
                    {"error": f"new_order ({new_order}) cannot be greater than total categories ({total_categories})"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Find current position (1-based)
            current_position = None
            for i, cat in enumerate(all_categories):
                if cat.id == category_to_move.id:
                    current_position = i + 1
                    break

            if current_position == new_order:
                return Response(
                    {"message": "Category is already at the specified position"},
                    status=status.HTTP_200_OK
                )

            # Perform reordering
            with transaction.atomic():
                # Remove the category from its current position
                all_categories.pop(current_position - 1)

                # Insert it at the new position
                all_categories.insert(new_order - 1, category_to_move)

                # Update order values for all categories
                for i, cat in enumerate(all_categories):
                    cat.order = i + 1
                    cat.save(update_fields=['order'])

            return Response({
                "message": f"Category '{category_key}' moved from position {current_position} to {new_order}",
                "category_key": category_key,
                "old_order": current_position,
                "new_order": new_order
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error reordering FAQ category: {str(e)}")
            return Response(
                {"error": "Failed to reorder category", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FaqCategorySwitchAPIView(APIView):
    """
    API view to switch positions of two FAQ categories
    """
    permission_classes = [AllowAny]  # Adjust permissions as needed

    def post(self, request):
        """
        Switch positions of two categories

        Expected data:
        - category_key_1: The key of the first category
        - category_key_2: The key of the second category
        """
        try:
            category_key_1 = request.data.get('category_key_1')
            category_key_2 = request.data.get('category_key_2')

            # Validate required fields
            if not category_key_1:
                return Response(
                    {"error": "category_key_1 is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not category_key_2:
                return Response(
                    {"error": "category_key_2 is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if category_key_1 == category_key_2:
                return Response(
                    {"error": "Cannot switch a category with itself"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get both categories
            try:
                category_1 = FaqCategory.objects.get(category_key=category_key_1)
                category_2 = FaqCategory.objects.get(category_key=category_key_2)
            except FaqCategory.DoesNotExist as e:
                return Response(
                    {"error": f"One or both categories not found: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Switch their order values
            with transaction.atomic():
                temp_order = category_1.order
                category_1.order = category_2.order
                category_2.order = temp_order

                category_1.save(update_fields=['order'])
                category_2.save(update_fields=['order'])

            return Response({
                "message": f"Successfully switched positions of '{category_key_1}' and '{category_key_2}'",
                "category_1": {
                    "key": category_key_1,
                    "new_order": category_1.order
                },
                "category_2": {
                    "key": category_key_2,
                    "new_order": category_2.order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error switching FAQ categories: {str(e)}")
            return Response(
                {"error": "Failed to switch categories", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FaqItemReorderAPIView(APIView):
    """
    API view to reorder FAQ items within a category
    """
    permission_classes = [AllowAny]  # Adjust permissions as needed

    def post(self, request):
        """
        Reorder items - move an item from one position to another within its category

        Expected data:
        - item_key: The key of the item to move
        - new_order: The new order position (1-based within the category)
        """
        try:
            item_key = request.data.get('item_key')
            new_order = request.data.get('new_order')

            # Validate required fields
            if not item_key:
                return Response(
                    {"error": "item_key is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if new_order is None:
                return Response(
                    {"error": "new_order is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                new_order = int(new_order)
                if new_order < 1:
                    raise ValueError("Order must be positive")
            except (ValueError, TypeError):
                return Response(
                    {"error": "new_order must be a positive integer"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the item to move
            try:
                item_to_move = FaqItem.objects.get(item_key=item_key)
            except FaqItem.DoesNotExist:
                return Response(
                    {"error": f"Item with key '{item_key}' not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get all items in the same category ordered by current order
            category_items = list(
                FaqItem.objects.filter(category=item_to_move.category)
                .order_by('order')
            )
            total_items = len(category_items)

            # Validate new_order is within bounds
            if new_order > total_items:
                return Response(
                    {"error": f"new_order ({new_order}) cannot be greater than total items in category ({total_items})"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Find current position (1-based)
            current_position = None
            for i, item in enumerate(category_items):
                if item.id == item_to_move.id:
                    current_position = i + 1
                    break

            if current_position == new_order:
                return Response(
                    {"message": "Item is already at the specified position"},
                    status=status.HTTP_200_OK
                )

            # Perform reordering
            with transaction.atomic():
                # Remove the item from its current position
                category_items.pop(current_position - 1)

                # Insert it at the new position
                category_items.insert(new_order - 1, item_to_move)

                # Update order values for all items in the category
                for i, item in enumerate(category_items):
                    item.order = i + 1
                    item.save(update_fields=['order'])

            return Response({
                "message": f"Item '{item_key}' moved from position {current_position} to {new_order} in category '{item_to_move.category.name}'",
                "item_key": item_key,
                "category_key": item_to_move.category.category_key,
                "old_order": current_position,
                "new_order": new_order
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error reordering FAQ item: {str(e)}")
            return Response(
                {"error": "Failed to reorder item", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FaqItemSwitchAPIView(APIView):
    """
    API view to switch positions of two FAQ items within the same category
    """
    permission_classes = [AllowAny]  # Adjust permissions as needed

    def post(self, request):
        """
        Switch positions of two items within the same category

        Expected data:
        - item_key_1: The key of the first item
        - item_key_2: The key of the second item
        """
        try:
            item_key_1 = request.data.get('item_key_1')
            item_key_2 = request.data.get('item_key_2')

            # Validate required fields
            if not item_key_1:
                return Response(
                    {"error": "item_key_1 is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not item_key_2:
                return Response(
                    {"error": "item_key_2 is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if item_key_1 == item_key_2:
                return Response(
                    {"error": "Cannot switch an item with itself"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get both items
            try:
                item_1 = FaqItem.objects.get(item_key=item_key_1)
                item_2 = FaqItem.objects.get(item_key=item_key_2)
            except FaqItem.DoesNotExist as e:
                return Response(
                    {"error": f"One or both items not found: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if both items are in the same category
            if item_1.category.id != item_2.category.id:
                return Response(
                    {"error": "Items must be in the same category to switch positions"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Switch their order values
            with transaction.atomic():
                temp_order = item_1.order
                item_1.order = item_2.order
                item_2.order = temp_order

                item_1.save(update_fields=['order'])
                item_2.save(update_fields=['order'])

            return Response({
                "message": f"Successfully switched positions of '{item_key_1}' and '{item_key_2}' in category '{item_1.category.name}'",
                "category_key": item_1.category.category_key,
                "item_1": {
                    "key": item_key_1,
                    "new_order": item_1.order
                },
                "item_2": {
                    "key": item_key_2,
                    "new_order": item_2.order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error switching FAQ items: {str(e)}")
            return Response(
                {"error": "Failed to switch items", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
