# Generated by Django 3.2.13 on 2022-12-07 10:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0049_auto_20221206_1814"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShippingHistory",
            fields=[
                (
                    "shipping_update_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("title", models.CharField(blank=True, max_length=225, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("shipping_reference", models.CharField(max_length=50)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "order_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="order_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.order",
                        to_field="order_number",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "shipping updates",
                "db_table": '"order"."shipping_updates"',
            },
        ),
    ]
