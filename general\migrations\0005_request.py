# Generated by Django 3.2.13 on 2023-04-28 11:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('general', '0004_delete_request'),
    ]

    operations = [
        migrations.CreateModel(
            name='Request',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.CharField(max_length=100, null=True)),
                ('response_status_code', models.PositiveSmallIntegerField()),
                ('request_method', models.CharField(max_length=10, null=True)),
                ('remote_address', models.CharField(max_length=20, null=True)),
                ('exec_time', models.IntegerField(null=True)),
                ('date', models.DateTimeField(auto_now=True)),
                ('response_body', models.TextField()),
                ('request_body', models.TextField()),
                ('http_user_agent', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50, null=True)),
                ('query_string', models.<PERSON><PERSON><PERSON><PERSON>(max_length=20, null=True)),
            ],
            options={
                'verbose_name_plural': 'requests',
                'db_table': '"public"."requests"',
            },
        ),
    ]
