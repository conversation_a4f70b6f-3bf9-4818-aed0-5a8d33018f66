# Generated by Django 3.2.13 on 2023-05-16 22:12

from django.db import migrations

def adding_delivery_fees_in_order_payout_table(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in product_reference column, where
    # productid of CartItem model is same as productid in Product table.

    OrderPayout = apps.get_model("orders", "OrderPayout")
    OrderPaymentDetails = apps.get_model("orders", "OrderPaymentDetails")
    for item in OrderPayout.objects.all():
        if OrderPaymentDetails.objects.filter(suborder_number=item.suborder_number).exists():
            instance = OrderPaymentDetails.objects.get(suborder_number=item.suborder_number)
            item.product_level_delivery_fee = instance.delivery_fee
            item.store_level_delivery_fee = instance.store_level_fee
            item.save(update_fields=["product_level_delivery_fee", "store_level_delivery_fee"])

            if item.product_level_delivery_fee != 0:
                item.total_order_value = item.order_value + item.product_level_delivery_fee
            elif item.product_level_delivery_fee == 0 and item.store_level_delivery_fee != 0:
                item.total_order_value = item.order_value + item.store_level_delivery_fee
            elif item.product_level_delivery_fee == 0 and item.store_level_delivery_fee == 0:
                item.total_order_value = item.order_value
            item.save (update_fields=["total_order_value"])

def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    OrderPayout = apps.get_model("orders", "OrderPayout")
    for item in OrderPayout.objects.all():
        item.product_level_delivery_fee = 0
        item.store_level_delivery_fee = 0
        item.total_order_value = 0
        item.save(update_fields=["product_level_delivery_fee", "store_level_delivery_fee", "total_order_value"])

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0080_auto_20230512_1725'),
    ]

    operations = [migrations.RunPython(adding_delivery_fees_in_order_payout_table, reverse_func)
    ]
