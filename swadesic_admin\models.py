from django.db import models
import time

# Create your models here.


class SubscriptionPlan(models.Model):
    PERIOD_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ]

    PLAN_TYPE_CHOICES = [
        ('user', 'User Plan'),
        ('store', 'Store Plan'),
    ]
    plan_reference = models.CharField(max_length=25, unique=True)
    plan_id = models.AutoField(primary_key=True)
    plan_name = models.CharField(max_length=100)
    plan_description = models.TextField(blank=True, null=True)
    plan_details = models.JSONField(blank=True, null=True)
    plan_period = models.Char<PERSON>ield(max_length=10, choices=PERIOD_CHOICES)
    plan_amount = models.DecimalField(max_digits=10, decimal_places=2)
    razorpay_plan_id = models.CharField(max_length=100, unique=True)
    plan_type = models.CharField(max_length=5, choices=PLAN_TYPE_CHOICES)
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Subscription Plan"
        db_table = '"public"."subscription_plan"'

    def save(self, *args, **kwargs):
        if not self.plan_reference:
            self.plan_reference = "PLAN" + str(int(time.time()*1000))
        super().save(*args, **kwargs)
