import logging
from rest_framework import serializers
from rest_framework.response import Response
from rest_framework import status
from rest_framework.validators import UniqueValidator
from rest_framework.exceptions import ValidationError
from ..user_api.models import User, UserAddress, User<PERSON>ollow, UserDeviceDetails, UnregisteredUser
from phonenumber_field.modelfields import PhoneNumberField
from stores.store_api.models import Store


class UserSerializer(serializers.ModelSerializer):
    phonenumber = serializers.CharField(max_length=13)
    def validate_phonenumber(self, value):
        if len(value) != 13:
            raise ValidationError({"message": "The phone number entered is not valid."})
        # Check if the phone number starts with 5, 6, 7, 8, or 9
        if not str(value[3:]).startswith(('5', '6', '7', '8', '9')):
            raise ValidationError({"message": "The phone number entered is not valid."})
        return value

    class Meta:
        model = User
        fields = ["userid", "phonenumber", "email"]


class UserCheckSerializer(serializers.Serializer):
    otp = serializers.CharField(max_length=6)

    class Meta:
        Model = User
        fields = ["phonenumber", "otp"]


class NewUserCheckSerializer(serializers.Serializer):
    phonenumber = serializers.CharField(max_length=15)  # Adjust max_length as per your requirement
    email = serializers.EmailField(required=False, allow_null=True)
    phonenumber_otp = serializers.CharField(max_length=6, required=False, allow_null=True)
    email_otp = serializers.CharField(max_length=6, required=False, allow_null=True)

    def validate(self, attrs):
        phonenumber = attrs.get('phonenumber')
        email = attrs.get('email')
        phonenumber_otp = attrs.get('phonenumber_otp')
        email_otp = attrs.get('email_otp')

        # Check if either phone number or email is provided
        if not phonenumber and not email:
            raise ValidationError({"message": "Either phone number or email is required"})

        # Check if phone number OTP or email OTP is provided when corresponding field is provided
        if not phonenumber_otp and (email and not email_otp):
            raise ValidationError({"message": "Email OTP is required for the provided email"})

        if not phonenumber_otp and (not email and not email_otp):
            raise ValidationError({"message": "Phonenumber OTP is required for the provided phonenumber"})
        return attrs

# class UserCheckSerializer(serializers.Serializer):
#     otp = serializers.CharField(max_length=6)
#     phonenumber = serializers.CharField(max_length=15)  # Add phonenumber field
#
#     def validate(self, data):
#         request = self.context.get('request')
#         phonenumber = data.get('phonenumber')
#         otp = data.get('otp')
#
#         # Retrieve the user from the request using the token
#         user = authenticate(request=request)
#
#         if user:
#             # Now you can access the authenticated user
#             # For example, you can check if the user has a valid OTP
#
#             # Your existing OTP verification logic goes here
#             # For example, if you have a User model instance associated with the phone number
#             try:
#                 mobile_user = User.objects.get(phonenumber=phonenumber)
#             except User.DoesNotExist:
#                 raise serializers.ValidationError("User not found")
#
#             if mobile_user.is_verified:
#                 # Verify the OTP
#                 if your_otp_verification_logic(otp, mobile_user.counter):
#                     # Your existing logic for a successful verification goes here
#
#                     return data
#                 else:
#                     raise serializers.ValidationError("Invalid OTP")
#             else:
#                 raise serializers.ValidationError("User not verified")
#         else:
#             raise serializers.ValidationError("Authentication failed")
#
#         return data


class UserProfilePictureSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["icon"]


class UserProfileSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(
        max_length=50,
        validators=[
            UniqueValidator(
                queryset=User.objects.all(), message="This username already exist."
            )
        ],
    )

    class Meta:
        model = User
        fields = [
            "userid",
            "user_reference",
            "first_name",
            "last_name",
            "display_name",
            "about_user",
            "profession",
            "website_link",
            "date_of_birth",
            "user_name",
            "user_location",
            "pincode",
            "email",
            "gender",
            "age",
            "user_roles",
            "invited_by_code",
            "subscription_type",
            "xmpp_jid",
            "xmpp_password",
            "js_messaging_token",
            "new_messaging_token",
            "new_messaging_user_id"
        ]


class PrivateUserDetailsSerializer(serializers.ModelSerializer):
    role = serializers.CharField(source="invite_type")
    display_name = serializers.SerializerMethodField("get_display_name")

    class Meta:
        model = User
        fields = [
            "userid",
            "user_reference",
            "user_name",
            "icon",
            "first_name",
            "last_name",
            "display_name",
            "about_user",
            "profession",
            "website_link",
            "date_of_birth",
            "phonenumber",
            "email",
            "gender",
            "age",
            "icon",
            "user_location",
            "pincode",
            "role",
            "subscription_type",
            "invite_code",
            "seller_invite_balance",
            "member_invite_balance",
            "search_and_view",
            "view_stores",
            "follow_stores",
            "view_store_contact",
            "report_stores",
            "view_store_documents",
            "view_product",
            "save_product",
            "buy_product",
            "report_product",
            "view_comment_question_review",
            "add_comment",
            "add_question",
            "add_review",
            "report_comment_question_review",
            "clap_comment_question_review",
            "follow_people",
            "report_people",
            "message_people",
            "create_store",
            "create_test_store",
            "store_live",
            "add_products",
            "receive_orders",
            "receive_payments",
            "send_member_invites",
            "send_seller_invites",
            "vote_for_membership",
            "giving_suggestions",
            "create_promotion",
            "view_community_forum",
            "edit_community_forum",
            "include_in_rating_calculation",
            "include_in_analytics",
            "included_in_others_trust_score",
            "include_in_self_trust_score",
            "js_messaging_token",
            "new_messaging_token",
            "new_messaging_user_id",
            "is_email_verified",
            "is_phonenumber_verified",
            "user_level",
            "user_support_score",
            "user_roles"
        ]

    def get_display_name(self, obj):
        return obj.display_name if obj.display_name else obj.user_name


class GetUserDetailsSerializer(serializers.ModelSerializer):
    role = serializers.CharField(source="invite_type")
    display_name = serializers.SerializerMethodField("get_display_name")

    class Meta:
        model = User
        fields = [
            "userid",
            "user_reference",
            "icon",
            "user_name",
            "first_name",
            "last_name",
            "display_name",
            "about_user",
            "profession",
            "website_link",
            "date_of_birth",
            "email",
            "gender",
            "age",
            "icon",
            "user_location",
            "pincode",
            "role",
            "subscription_type",
            "is_email_verified",
            "is_phonenumber_verified",
            "user_level",
            "user_support_score",
            "user_roles",
            "new_messaging_user_id",
        ]
    def get_display_name(self, obj):
        return obj.display_name if obj.display_name else obj.user_name



class UpdatePincodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["pincode"]


class UserAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserAddress
        fields = [
            "useraddressid",
            "userid",
            "address",
            "city",
            "pincode",
            "state",
            "name",
            "phone_number",
            "address_type",
        ]


class UserFollowSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserFollow
        fields = '__all__'


class UserFollowerListSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = ['user_reference', 'user_name', 'icon']


class UserDeviceDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDeviceDetails
        fields = '__all__'


class UnregisteredUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = UnregisteredUser
        fields = '__all__'
