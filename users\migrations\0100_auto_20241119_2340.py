# Generated by Django 4.2.7 on 2024-11-19 18:10

from django.db import migrations
from users.user_api.utils import create_messaging_server_user
from stores.store_api.utils import create_store_messaging_user
import logging

logger = logging.getLogger(__name__)


def create_messaging_accounts(apps, schema_editor):
    """
    Create messaging accounts for all verified users and stores
    """
    User = apps.get_model('users', 'User')
    Store = apps.get_model('stores', 'Store')

    # Create messaging accounts for verified users
    verified_users = User.objects.filter(
        xmpp_jid__isnull=True,
        deleted=False
    ).select_related()  # Add select_related for better performance

    logger.info(f"Found {verified_users.count()} verified users without messaging accounts")

    success_count_users = 0
    for user in verified_users:
        try:
            username, password = create_messaging_server_user(user)
            if username and password:
                user.xmpp_jid = username
                user.xmpp_password = password
                user.save(update_fields=['xmpp_jid', 'xmpp_password'])
                success_count_users += 1
                if success_count_users % 100 == 0:  # Log progress every 100 users
                    logger.info(f"Created {success_count_users} user messaging accounts so far")
        except Exception as e:
            logger.error(f"Error creating messaging account for user {user.user_reference}: {str(e)}")

    logger.info(f"Successfully created {success_count_users} user messaging accounts")

    # Create messaging accounts for stores
    active_stores = Store.objects.filter(
        xmpp_jid__isnull=True,
        deleted=False
    ).select_related()  # Add select_related for better performance

    logger.info(f"Found {active_stores.count()} active stores without messaging accounts")

    success_count_stores = 0
    for store in active_stores:
        try:
            username, password = create_store_messaging_user(store)
            if username and password:
                store.xmpp_jid = username
                store.xmpp_password = password
                store.save(update_fields=['xmpp_jid', 'xmpp_password'])
                success_count_stores += 1
                if success_count_stores % 100 == 0:  # Log progress every 100 stores
                    logger.info(f"Created {success_count_stores} store messaging accounts so far")
        except Exception as e:
            logger.error(f"Error creating messaging account for store {store.store_reference}: {str(e)}")

    logger.info(f"Successfully created {success_count_stores} store messaging accounts")


def reverse_messaging_accounts(apps, schema_editor):
    """
    Reverse migration - clear messaging accounts
    Note: This won't delete the accounts from the messaging server
    """
    User = apps.get_model('users', 'User')
    Store = apps.get_model('stores', 'Store')

    # Clear messaging credentials for users
    users_updated = User.objects.filter(xmpp_jid__isnull=False).update(
        xmpp_jid=None,
        xmpp_password=None
    )
    logger.info(f"Cleared messaging credentials for {users_updated} users")

    # Clear messaging credentials for stores
    stores_updated = Store.objects.filter(xmpp_jid__isnull=False).update(
        xmpp_jid=None,
        xmpp_password=None
    )
    logger.info(f"Cleared messaging credentials for {stores_updated} stores")


class Migration(migrations.Migration):
    dependencies = [
        ('users', '0099_rewardshistory_bank_account_name_and_more'),
    ]

    operations = [
        migrations.RunPython(
            create_messaging_accounts,
            reverse_messaging_accounts
        ),
    ]