# Generated by Django 4.2.7 on 2024-11-01 10:09

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("content", "0025_story_comment_count_story_like_count_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="story",
            name="tagged_content",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=255), default=list, size=None
            ),
        ),
    ]
