# Generated by Django 3.2.13 on 2023-06-20 09:55
from django.db import migrations


def data_population(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in category_name column, of store table

    Store = apps.get_model("stores", "Store")
    for item in Store.objects.all():
        if item.categoryid.category_name:
            item.category_name = item.categoryid.category_name
        else:
            item.category_name = None
        item.save(update_fields=["category_name"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    Store = apps.get_model("stores", "Store")
    for item in Store.objects.all():
        item.category_name = None
        item.save(update_fields=["category_name"])

class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0034_store_category_name'),
    ]

    operations = [migrations.RunPython(data_population, reverse_func)]

