# Generated by Django 3.2.13 on 2023-05-01 07:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('general', '0005_request'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='request',
            name='response_body',
        ),
        migrations.AlterField(
            model_name='request',
            name='http_user_agent',
            field=models.TextField(null=True),
        ),
        migrations.AlterField(
            model_name='request',
            name='query_string',
            field=models.TextField(null=True),
        ),
        migrations.AlterField(
            model_name='request',
            name='remote_address',
            field=models.Char<PERSON>ield(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='request',
            name='request_body',
            field=models.TextField(null=True),
        ),
    ]
