from django.db import models
from users.notification_api.models import Notifications
from stores.store_api.models import Store
from products.models import Product
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django.contrib.postgres.fields import ArrayField

class TrustCenter(models.Model):
    trustcenterid = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        related_name="store_details",
        db_column="store_reference",
    )
    storeid = models.IntegerField(null=True, blank=True)
    joiningdate = models.DateTimeField(auto_now=True)
    trustscore = models.IntegerField(null=True, blank=True, default=0)
    sellerlevel = models.CharField(max_length=100, null=True, blank=True)
    phonenumber = models.CharField(max_length=100000, null=True, blank=True)
    emailid = models.CharField(max_length=100000, null=True, blank=True)
    primarycontacttype = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    isphysicalstore = models.Bo<PERSON>anField(null=True, blank=True)
    address = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    pincode = models.CharField(max_length=6, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    latitude = models.FloatField(null=True, blank=True)
    trust_center_note = models.TextField(null=True, blank=True)
    class Swadeshi_Owned_Labels(models.TextChoices):
        NOT_SWADESHI_OWNED = "NOT_SWADESHI_OWNED", _("Not Swadeshi Owned")
        PARTIALLY_SWADESHI_OWNED = "PARTIALLY_SWADESHI_OWNED", _("Partially Swadeshi Owned")
        MAINLY_SWADESHI_OWNED = "MAINLY_SWADESHI_OWNED", _("Mainly Swadeshi Owned")
        FULLY_SWADESHI_OWNED = "FULLY_SWADESHI_OWNED", _("Fully Swadeshi Owned")

    swadeshi_owned = models.CharField(
        max_length=50,
        choices=Swadeshi_Owned_Labels.choices,
        null=True,
        blank=True,

    )

    def save(self, *args, **kwargs):
        store_instance = Store.objects.get(
            store_reference=self.store_reference.store_reference
        )
        if self.trustcenterid is None:
            # Get store_id from store_instance and save it in current instance only for the first time
            # trust center creation
            self.storeid = store_instance.storeid
        super(TrustCenter, self).save(*args, **kwargs)

        # when trust center is completed, make trustcenter_detail field in store as True. This will
        # indicate that trust center has completed for that store.

        # To check if trust completed TrustCenterChecker class is used.
        is_trust_completed = TrustCenterChecker(self)

        # If this true, change the trust center details value in store.
        if is_trust_completed.check_trust_completed(self):
            store_instance.trustcenter_detail = True
            store_instance.save(update_fields=["trustcenter_detail"])

        else:
            if store_instance.trustcenter_detail is True:
                store_instance.trustcenter_detail = False
                store_instance.save(update_fields=["trustcenter_detail"])

        # if self.pincode:
        #     notification_gen = GenerateNotifications()
        #     notification_gen.create_notifications(
        #         notified_user=None,
        #         notification_type=Notifications.Notifications_Type.STORE_CREATED,
        #         notification_about=self.store_reference.store_reference,
        #         store_pincode=self.pincode,
        #         image=self.store_reference.icon
        #     )

    class Meta:
        verbose_name_plural = "trustcenter"
        db_table = '"store"."trust_center"'


class TrustCenterChecker:
    def __init__(self, instance):
        self.instance = instance

    def check_trust_completed(self, instance):

        # this function check for important fields, that will make sure that trust is completed
        contact = getattr(self.instance, "phonenumber")
        address = getattr(self.instance, "address")
        ownership = getattr(self.instance, "swadeshi_owned")

        store_instance = self.instance.store_reference

        # Check if the store has either GST number or PAN number
        has_gst_or_pan = bool(store_instance.gst_number or store_instance.pan_number)

        # if all four fields has not null value, indicates that it is completed and return true.
        if contact and address and ownership and has_gst_or_pan:
            return True
        else:
            return False


class Documents(models.Model):
    documentid = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        db_column="store_reference",
    )
    storeid = models.IntegerField()
    document_file = models.FileField(upload_to="documents")
    documentname = models.CharField(max_length=100, null=True)
    show_to_public = models.BooleanField(default=False)
    upload_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "documents"
        db_table = '"store"."documents"'


class DeliverySettings(models.Model):
    deliverysettingid = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        related_name="deliverysettings",
        db_column="store_reference",
    )
    storeid = models.IntegerField(null=True, blank=True)
    productid = models.IntegerField(null=True, blank=True)
    product_reference = models.CharField(max_length=255, null=True, blank=True)
    deliverymethod_swadesic = models.BooleanField(default=False)
    deliverymethod_logistics = models.BooleanField(default=False)
    deliverymethod_self = models.BooleanField(default=False)
    delivery_locations = models.CharField(max_length=5000, blank=True, null=True)
    selected_location_count = models.IntegerField(default=0)
    deliveryfeetype_all_free = models.BooleanField(default=False)
    deliveryfeetype_standard = models.BooleanField(default=False)
    deliveryfeetype_distance = models.BooleanField(default=False)
    deliveryfee_value = models.IntegerField(blank=True, null=True, default=0)
    deliveryfee_valuetype = models.CharField(max_length=255, blank=True, null=True)
    distance_based_max_deliveryfee = models.IntegerField(blank=True, null=True, default=0)
    no_deliveryfee_maxvalue = models.IntegerField(null=True, blank=True, default=0)
    no_deliveryfee_maxvalue_enabled = models.BooleanField(default=False)
    no_deliveryfee_products = models.IntegerField(null=True, blank=True, default=0)
    no_deliveryfee_products_enabled = models.BooleanField(default=False)
    no_deliveryfee_samepincode_enabled = models.BooleanField(default=False)
    no_deliveryfee_samecity_enabled = models.BooleanField(default=False)
    created_by = models.CharField(max_length=100, blank=True, null=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_by = models.CharField(max_length=100, blank=True, null=True)
    modified_date = models.DateTimeField(auto_now=True)
    delivery_personal_name = models.CharField(max_length=300, null=True, blank=True)
    delivery_personal_phone = models.CharField(max_length=300, null=True, blank=True)
    default_logistic_partner = models.CharField(max_length=300, null=True, blank=True)
    delivery_tracking_link = models.CharField(max_length=300, null=True, blank=True)
    time_to_prepare = models.PositiveIntegerField(default=0)
    time_to_deliver = models.PositiveIntegerField(default=0)
    delivery_settings_version = models.CharField(max_length=5, null=True, blank=True)
    is_deleted = models.BooleanField(default=False)

    # pickup location related fields
    class FulfillmentTypeChoices(models.TextChoices):
        DELIVERY = "DELIVERY", "Delivery"
        IN_STORE_PICKUP = "IN_STORE_PICKUP", "In Store Pickup"
        DELIVERY_AND_IN_STORE_PICKUP = "DELIVERY_AND_IN_STORE_PICKUP", "Delivery and In Store Pickup"

    fulfillment_options = models.CharField(
        max_length=30,
        choices=FulfillmentTypeChoices.choices,
        default=FulfillmentTypeChoices.DELIVERY,
        null=True,
        blank=True,
    )
    pickup_location_ids = models.JSONField(default=list)

    def save(self, *args, **kwargs):
        store_instance = Store.objects.filter(store_reference=self.store_reference.store_reference).first()
        if not store_instance:
            raise ValueError("Invalid store reference provided.")

        is_store_level = self.productid is None and self.product_reference is None

        existing_settings_query = DeliverySettings.objects.filter(
            store_reference=self.store_reference.store_reference,
            productid__isnull=is_store_level,
            product_reference__isnull=is_store_level
        )

        latest_existing_setting = existing_settings_query.order_by("-created_date").first()

        COMPARISON_FIELDS = [
            'deliverymethod_swadesic', 'deliverymethod_logistics', 'deliverymethod_self',
            'delivery_locations', 'selected_location_count',
            'deliveryfeetype_all_free', 'deliveryfeetype_standard', 'deliveryfeetype_distance',
            'deliveryfee_value', 'deliveryfee_valuetype',
            'distance_based_max_deliveryfee', 'no_deliveryfee_maxvalue',
            'no_deliveryfee_maxvalue_enabled', 'no_deliveryfee_products',
            'no_deliveryfee_products_enabled', 'no_deliveryfee_samepincode_enabled',
            'no_deliveryfee_samecity_enabled', 'delivery_personal_name',
            'delivery_personal_phone', 'default_logistic_partner',
            'delivery_tracking_link', 'time_to_prepare', 'time_to_deliver',
            'fulfillment_options', 'pickup_location_ids'
        ]

        if latest_existing_setting:
            is_different = any(
                getattr(self, field) != getattr(latest_existing_setting, field)
                for field in COMPARISON_FIELDS
            )

            if not is_different:
                self.deliverysettingid = latest_existing_setting.deliverysettingid
                return  # Avoid returning an object

        if not self.deliverysettingid:
            self.storeid = store_instance.storeid

            if self.productid:
                product_instance = Product.objects.filter(productid=self.productid).first()
                if product_instance:
                    self.product_reference = product_instance.product_reference

            # Soft-delete any existing active product-level settings for same product
            DeliverySettings.objects.filter(
                store_reference=self.store_reference,
                productid=self.productid,
                product_reference=self.product_reference,
                is_deleted=False
            ).exclude(pk=self.pk).update(is_deleted=True)

            if latest_existing_setting and latest_existing_setting.delivery_settings_version:
                version = latest_existing_setting.delivery_settings_version.split('.')[
                    0] if '.' in latest_existing_setting.delivery_settings_version else latest_existing_setting.delivery_settings_version
                self.delivery_settings_version = f"{int(version) + 1}.0"
            else:
                self.delivery_settings_version = "1.0"

        super().save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "delivery settings"
        db_table = '"store"."delivery_settings"'


class DeliveryLocations(models.Model):
    deliverylocationid = models.AutoField(primary_key=True)
    pincode = models.CharField(max_length=6)
    city = models.CharField(max_length=200)
    state = models.CharField(max_length=200)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_by = models.CharField(max_length=100, null=True, blank=True)
    modified_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "delivery locations"
        db_table = '"store"."delivery_locations"'


class RefundAndWarranty(models.Model):
    refundandwarrantyid = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        db_column="store_reference",
    )
    storeid = models.IntegerField(null=True, blank=True)
    productid = models.IntegerField(null=True, blank=True)
    product_reference = models.CharField(max_length=255, null=True, blank=True)
    has_warranty = models.BooleanField(default=False)
    warranty_period_no = models.IntegerField(null=True, blank=True, default=0)
    warranty_period = models.CharField(max_length=100, null=True, blank=True)
    warranty_card_available = models.BooleanField(default=False)
    return_type = models.BooleanField(default=False)
    replacement_type = models.BooleanField(default=False)
    return_period = models.IntegerField(null=True, blank=True, default=0)
    return_conditions = models.TextField(null=True, blank=True)
    return_pickup = models.CharField(max_length=400, null=True, blank=True)
    return_cost_on_customer = models.BooleanField(default=False)
    return_cost_on_seller = models.BooleanField(default=False)
    delivery_fee_reevaluation_on_seller = models.BooleanField(default=False)
    delivery_fee_reevaluation_on_customer = models.BooleanField(default=False)
    return_delivery_address_id = models.ForeignKey(
        "stores.Address",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_column="return_delivery_address_id",
    )
    refund_warranty_version = models.CharField(max_length=5, null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    created_by = models.CharField(max_length=100, blank=True, null=True)
    created_date = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    modified_by = models.CharField(max_length=100, blank=True, null=True)
    modified_date = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        store_instance = Store.objects.filter(store_reference=self.store_reference.store_reference).first()
        if not store_instance:
            raise ValueError("Invalid store reference provided.")

        is_store_level = self.productid is None and self.product_reference is None

        existing_settings_query = RefundAndWarranty.objects.filter(
            store_reference=self.store_reference.store_reference,
            productid__isnull=is_store_level,
            product_reference__isnull=is_store_level
        )

        latest_existing_setting = existing_settings_query.order_by("-created_date").first()

        COMPARISON_FIELDS = [
            'has_warranty', 'warranty_period_no', 'warranty_period', 'warranty_card_available',
            'return_type', 'replacement_type', 'return_period', 'return_conditions',
            'return_pickup', 'return_cost_on_customer', 'return_cost_on_seller',
            'delivery_fee_reevaluation_on_seller', 'delivery_fee_reevaluation_on_customer',
            'return_delivery_address_id'
        ]

        if latest_existing_setting:
            is_different = any(
                getattr(self, field) != getattr(latest_existing_setting, field)
                for field in COMPARISON_FIELDS
            )

            if not is_different:
                self.refundandwarrantyid = latest_existing_setting.refundandwarrantyid
                return  # Avoid returning an object

        if not self.refundandwarrantyid:
            self.storeid = store_instance.storeid

            if self.productid:
                product_instance = Product.objects.filter(productid=self.productid).first()
                if product_instance:
                    self.product_reference = product_instance.product_reference
            elif self.product_reference:
                product_instance = Product.objects.filter(product_reference=self.product_reference).first()
                if product_instance:
                    self.productid = product_instance.productid

            # Soft-delete any existing active product-level settings for same product
            RefundAndWarranty.objects.filter(
                store_reference=self.store_reference,
                productid=self.productid,
                product_reference=self.product_reference,
                is_deleted=False
            ).exclude(pk=self.pk).update(is_deleted=True)

            if latest_existing_setting and latest_existing_setting.refund_warranty_version:
                version = latest_existing_setting.refund_warranty_version.split('.')[
                    0] if '.' in latest_existing_setting.refund_warranty_version else latest_existing_setting.refund_warranty_version
                self.refund_warranty_version = f"{int(version) + 1}.0"
            else:
                self.refund_warranty_version = "1.0"

        super().save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "refund and warranty"
        db_table = '"store"."refund_and_warranty"'


class Address(models.Model):
    addressid = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        db_column="store_reference",
    )
    storeid = models.IntegerField()
    address = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    pincode = models.CharField(max_length=6, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    default_address = models.BooleanField(default=False)

    # pickup location related fields
    is_pickup_location = models.BooleanField(default=False)
    latitude = models.CharField(max_length=20, null=True, blank=True)
    longitude = models.CharField(max_length=20, null=True, blank=True)
    location_link = models.CharField(max_length=150, null=True, blank=True)
    pickup_timings = models.JSONField(null=True, blank=True)

    #swadesic shipping related fields
    store_address_type = ArrayField(models.CharField(max_length=100, blank=True), null=True, blank=True, default=list)
    # can be list like ["SWADESIC_SHIPPING_PICKUP", "IN_STORE_PICKUP", "NORMAL" ]
    pickup_location_in_shiprocket = models.CharField(max_length=100, null=True, blank=True)
    class Meta:
        verbose_name_plural = "address"
        db_table = '"store"."address"'


class StoreByState(models.Model):
    state = models.CharField(max_length=100)
    icons = models.CharField(max_length=1000)
    total_number_of_stores = models.IntegerField()

    class Meta:
        verbose_name_plural = "stores_by_state"
        db_table = '"store"."stores_by_state"'


class StoresByCategory(models.Model):
    category_name = models.CharField(max_length=100)
    icons = models.CharField(max_length=1000)
    total_number_of_stores = models.IntegerField()

    class Meta:
        verbose_name_plural = "stores_by_category"
        db_table = '"store"."stores_by_category"'
