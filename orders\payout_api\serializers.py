from rest_framework import serializers
from django.db.models import Sum, Q
from .models import OrderPayout, PayoutTransactions, encrypt_string, decrypt_string, BankDetails
import math


class PayoutDetailsSerializer(serializers.ModelSerializer):
    release_date = serializers.SerializerMethodField("get_payout_release_date")

    class Meta:
        model = OrderPayout
        fields = ["suborder_number", "product_total", "payout_status", "release_date"]

    def get_payout_release_date(self, obj):
        date = obj.payout_release_date
        return date.strftime("%d:%m:%Y")


class AccountPayoutDetailsSerializer(serializers.ModelSerializer):
    details = serializers.SerializerMethodField("get_details")
    ordered_date = serializers.SerializerMethodField("get_ordered_date")
    release_dates = serializers.SerializerMethodField("get_release_dates")
    total_order_value = serializers.SerializerMethodField("get_total_order_value")
    total_receivable = serializers.SerializerMethodField("get_total_receivable")
    payout_status = serializers.SerializerMethodField("get_payout_status")
    customer_name = serializers.CharField(source="user_reference.user_name")
    customer_image = serializers.ImageField(source="user_reference.icon")

    class Meta:
        model = OrderPayout
        fields = [
            "order_number",
            "ordered_date",
            "customer_name",
            "customer_image",
            "user_reference",
            "total_order_value",
            "total_receivable",
            "payout_status",
            "release_dates",
            "details",
        ]

    def get_payout_status(self, obj):
        all_payout_status = OrderPayout.objects.filter(
            order_number=obj.order_number
        ).values_list("payout_status", flat=True)

        if all(
            status == OrderPayout.Payout_Status.AMOUNT_RELEASED
            for status in all_payout_status
        ):
            payout_status = OrderPayout.Payout_Status.AMOUNT_RELEASED
        elif any(
            status == OrderPayout.Payout_Status.WAITING_FOR_RELEASE
            for status in all_payout_status
        ):
            payout_status = OrderPayout.Payout_Status.WAITING_FOR_RELEASE
        else:
            payout_status = OrderPayout.Payout_Status.IN_PROCESS
        return payout_status

    def get_total_receivable(self, obj):
        total_receivable = OrderPayout.objects.filter(
            order_number=obj.order_number).aggregate(payout_amount=Sum("payout_amount"))["payout_amount"]
        if not total_receivable:
            total_receivable = 0
        return total_receivable

    def get_total_order_value(self, obj):
        total_order_value = OrderPayout.objects.filter(
            order_number=obj.order_number
        ).aggregate(expected_payout_amount=Sum("expected_payout_amount"))["expected_payout_amount"]
        if not total_order_value:
            total_order_value = 0
        return total_order_value

    def get_release_dates(self, obj):
        release_dates = (
            OrderPayout.objects.filter(order_number=obj.order_number)
            .values_list("payout_release_date", flat=True)
            .distinct()
        )
        k = [date.strftime("%d:%m:%Y") for date in release_dates]
        return k

    def get_ordered_date(self, obj):
        date = obj.order_date
        return date.strftime("%d:%m:%Y")

    def get_details(self, obj):
        instance = OrderPayout.objects.filter(order_number=obj.order_number)
        serializer = PayoutDetailsSerializer(instance, many=True)
        return serializer.data


class PayoutTransactionsSerializer(serializers.ModelSerializer):
    date = serializers.SerializerMethodField("get_date")
    bank_account_number = serializers.SerializerMethodField("get_bank_account_number")
    bank_ifsc_code = serializers.SerializerMethodField("get_bank_ifsc_code")
    class Meta:
        model = PayoutTransactions
        fields = [
            "payout_transaction_id",
            "transaction_type",
            "transaction_status",
            "store_reference",
            "order_number",
            "suborder_number",
            "payout_amount",
            "date",
            "bank_account_number",
            "bank_account_name",
            "bank_ifsc_code",
            "bank_name",
            "bank_branch",
            "notes",
        ]

    def get_date(self, obj):
        date = obj.payout_release_date
        return date.strftime("%d:%m:%Y") if date else None

    def get_bank_account_number(self, obj):
        if not obj.bank_account_number:
            return None
        return self.mask_middle_third(obj.bank_account_number)

    def get_bank_ifsc_code(self, obj):
        if not obj.bank_ifsc_code:
            return None
        return self.mask_middle_third(obj.bank_ifsc_code)

    def mask_middle_third(self, string):
        length = len(string)
        third = math.ceil(length / 3)
        
        start = string[:third]
        middle = '*' * third
        end = string[-third:]
        
        return start + middle + end
        

class PayoutWithdrawalSerializer(serializers.ModelSerializer):
    storehandle = serializers.CharField(source="store_reference.storehandle")
    current_account_balance = serializers.SerializerMethodField()

    class Meta:
        model = PayoutTransactions
        fields = [
            "storehandle",
            "store_reference",
            "transaction_reference",
            "transaction_type",
            "transaction_status",
            "payout_amount",
            "current_account_balance",
            "payout_release_date",
            "bank_account_number",
            "bank_account_name",
            "bank_ifsc_code",
            "bank_name",
            "bank_branch"
        ]

    def get_current_account_balance(self, obj):
        try:
            return obj.store_reference.account_payout_balance.first().current_balance
        except AttributeError:
            return None


class OrderPayoutCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderPayout
        fields = [
            "store_reference",
            "suborder_number",
            "order_number",
            "user_reference",
            "product_total",
            "product_df",
            "store_order_df",
            "order_date",
            "payout_status",
            "payout_release_date",
        ]


class GetBankDetailsSerializer(serializers.ModelSerializer):
    account_number = serializers.SerializerMethodField("get_decrypted_account_number")
    class Meta:
        model = BankDetails
        fields = [
            "bank_detail_reference",
            "account_name",
            "account_number",
            "is_primary",
        ]

    def get_decrypted_account_number(self, obj):
        decrypted_account_number = decrypt_string(obj.account_number)
        return self.mask_middle_third(decrypted_account_number)

    
    def mask_middle_third(self, string):
        length = len(string)
        third = math.ceil(length / 3)
        
        start = string[:third]
        middle = '*' * third
        end = string[-third:]
        
        return start + middle + end


class BankDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankDetails
        fields = [
            "entity_reference",
            "account_name",
            "account_holder_name",
            "ifsc_code",
            "account_number",
            "bank_branch",
            "bank_name",
            "is_primary",
        ]
