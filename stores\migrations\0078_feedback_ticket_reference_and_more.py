# Generated by Django 4.2.7 on 2024-10-17 16:26 
# User this if asked for a one off value for the field 
#import time; return f"TKT{int(time.time() * 1000)}"

from django.db import migrations, models
import time


def generate_ticket_reference():
    prefix = 'TKT'
    timestamp = int(time.time() * 1000)
    return f"{prefix}{timestamp}"


def populate_ticket_references(apps, schema_editor):
    FeedBack = apps.get_model('stores', 'FeedBack')
    db_alias = schema_editor.connection.alias
    
    feedbacks = FeedBack.objects.using(db_alias).filter(ticket_reference__isnull=True)
    
    for feedback in feedbacks:
        while True:
            new_reference = generate_ticket_reference()
            if not FeedBack.objects.using(db_alias).filter(ticket_reference=new_reference).exists():
                feedback.ticket_reference = new_reference
                feedback.save()
                break
            time.sleep(0.001)  # Small delay to ensure unique timestamp


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0077_rename_is_autopay_enabled_store_is_auto_withdrawal_enabled"),
    ]

    operations = [
        migrations.AddField(
            model_name="feedback",
            name="ticket_reference",
            field=models.CharField(
                blank=True, editable=False, max_length=20, null=True, unique=True
            ),
        ),
        migrations.AddIndex(
            model_name="feedback",
            index=models.Index(
                fields=["ticket_reference"], name="feed_back_ticket__6b7b6c_idx"
            ),
        ),
        migrations.RunPython(populate_ticket_references),
    ]
