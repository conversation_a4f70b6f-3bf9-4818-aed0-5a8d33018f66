import graphene
import neo4j
from neomodel import db
import datetime
from datetime import datetime, timedelta
import logging
from itertools import chain
import psycopg2
import psycopg2.extras
from decouple import config
import json


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class dbqueries():

    def get_references_of_connected_entities(phonenumbers):
        query = """
        MATCH (entity:Neo4jEntity)
        WHERE entity.phonenumber IN $phonenumbers
        RETURN entity.reference, entity.phonenumber

        UNION

        MATCH (entity:Neo4jUnrUser)
        WHERE entity.phonenumber IN $phonenumbers
        RETURN entity.reference, entity.phonenumber

        """

        parameters = {'phonenumbers': phonenumbers}
        result, _ = db.cypher_query(query, parameters)
        if len(result)==0:
            return {},{}
        # Extract references and phone numbers separately
        references, phone_numbers = zip(*result)

        return set(references), set(phone_numbers)

    def create_unregistered_nodes(phonenumbers):
        # this creates duplicate unregistered nodes if same phone numbers are given again. So make sure non-existing phone numbers are passed.
        query = """
            UNWIND $phonenumbers AS phonenumber
            CREATE(u: Neo4jUnrUser {phonenumber: phonenumber, is_deleted: FALSE})
            WITH u
            SET u.reference = 'UR' + toString(id(u) % 1000000000)
            RETURN collect(u.reference) as references
        """

        parameters = {'phonenumbers': phonenumbers}
        result, _ = db.cypher_query(query, parameters)

        # Extract references and phone numbers separately
        # references,_ = zip(*result)
        references = set(result[0][0])

        return set(references)

    def get_references_of_has_contacts(asking_reference):

        query = """
        MATCH (asking: Neo4jEntity {reference:$asking_reference})
        WITH asking
        MATCH (asking)-[:HAS_CONTACT]->(contact_user_store:Neo4jEntity)
        WITH contact_user_store
        RETURN contact_user_store.reference as references

        UNION

        MATCH (asking: Neo4jEntity {reference:$asking_reference})
        WITH asking
        MATCH (asking)-[:HAS_CONTACT]->(contact_unruser:Neo4jUnrUser)
        WITH contact_unruser
        RETURN contact_unruser.reference as references
        """

        parameters = {'asking_reference': asking_reference}
        logger.info(f"cypher querying started @ {datetime.now()}")

        result, _ = db.cypher_query(query, parameters)
        logger.info(f"cypher querying ended @ {datetime.now()}")

        # Extract references from the result
        references = list(chain(*result))

        return set(references)

    def create_or_remove_has_contacts(asking_reference, references_to_create, references_to_remove):
        create_query = """
            MATCH (asking:Neo4jEntity {reference: $asking_reference})
            UNWIND $references_to_create AS reference
            OPTIONAL MATCH (user:Neo4jUser {reference: reference}) WHERE user IS NOT NULL
            OPTIONAL MATCH (store:Neo4jStore {reference: reference}) WHERE store IS NOT NULL
            OPTIONAL MATCH (unr_user:Neo4jUnrUser {reference: reference}) WHERE unr_user IS NOT NULL

            WITH asking, reference, user, store, unr_user
            WHERE user IS NOT NULL OR store IS NOT NULL OR unr_user IS NOT NULL

            WITH asking, reference, CASE
                WHEN user IS NOT NULL THEN user
                WHEN store IS NOT NULL THEN store
                ELSE unr_user
                END AS matched_entity

            MERGE (asking)-[:HAS_CONTACT]->(matched_entity)

            UNION

            MATCH (asking:Neo4jEntity {reference: $asking_reference})
            UNWIND $references_to_remove AS reference
            OPTIONAL MATCH (user:Neo4jUser {reference: reference}) WHERE user IS NOT NULL
            OPTIONAL MATCH (store:Neo4jStore {reference: reference}) WHERE store IS NOT NULL
            OPTIONAL MATCH (unr_user:Neo4jUnrUser {reference: reference}) WHERE unr_user IS NOT NULL

            WITH asking, reference, user, store, unr_user
            WHERE user IS NOT NULL OR store IS NOT NULL OR unr_user IS NOT NULL

            WITH asking, reference, CASE
                WHEN user IS NOT NULL THEN user
                WHEN store IS NOT NULL THEN store
                ELSE unr_user
                END AS matched_entity

            MATCH (asking)-[rel:HAS_CONTACT]->(matched_entity)
            DELETE rel
            """

        parameters = {'asking_reference': asking_reference,
                      'references_to_create': references_to_create,
                      'references_to_remove': references_to_remove}
        result, _ = db.cypher_query(create_query , parameters)

    def get_has_contact_entities(asking_reference):

        query = """
        MATCH (asking: Neo4jEntity {reference:$asking_reference})
        WITH asking
        MATCH (asking)-[:HAS_CONTACT]->(contact_user:Neo4jUser)
        WITH asking, contact_user
        RETURN contact_user AS entity, CASE
                WHEN (contact_user)-[:FOLLOWS]->(asking) THEN 'Follow back'
                WHEN (asking)-[:FOLLOWS]->(contact_user) THEN 'Following'
                ELSE 'Follow'
            END AS follow_status,
            'USER' AS entity_type

        UNION

        MATCH (asking: Neo4jEntity {reference:$asking_reference})
        WITH asking
        MATCH (asking)-[:HAS_CONTACT]->(contact_unruser:Neo4jUnrUser)
        WITH asking, contact_unruser
        RETURN contact_unruser AS entity, CASE
                WHEN (asking)-[:FOLLOWS]->(contact_unruser) THEN 'Pending'
                ELSE 'Follow'
            END AS follow_status,
            'UNREGISTERED' AS entity_type

        UNION

        MATCH (asking: Neo4jEntity {reference:$asking_reference})
        WITH asking
        MATCH (asking)-[:HAS_CONTACT]->(contact_store:Neo4jStore)
        with asking, contact_store
        RETURN contact_store AS entity, CASE
            WHEN (contact_store)-[:FOLLOWS]->(asking) THEN 'Support back'
            WHEN (asking)-[:FOLLOWS]->(contact_store) THEN 'Supporting'
            ELSE 'Support'
        END AS follow_status,
        'STORE' AS entity_type
        """

        parameters = {'asking_reference': asking_reference}
        logger.info(f"cypher querying started @ {datetime.now()}")

        result, _ = db.cypher_query(query, parameters)
        logger.info(f"cypher querying ended @ {datetime.now()}")

        def add_node_property(x):
            x[0]._properties['follow_status'] = x[1]
            x[0]._properties['entity_type'] = x[2]
            return x[0]

        final_results_with_status = [add_node_property(x) for x in result]
        return final_results_with_status

    def follow_or_unfollow_all(asking_reference, entity_type, action, time_window_in_minutes):
        user_query = """
                MATCH (asking: Neo4jEntity {reference:$asking_reference})
                WITH asking

                MATCH (asking)-[:HAS_CONTACT]->(contact_user:Neo4jUser)
                WHERE NOT EXISTS((asking)-[:FOLLOWS]->(contact_user))
                WITH asking, contact_user


                CREATE (asking)-[:FOLLOWS{datetimestamp:$datetimestamp}]->(contact_user)
                RETURN contact_user AS entity, CASE
                        WHEN (contact_user)-[:FOLLOWS]->(asking) THEN 'Follow back'
                        WHEN (asking)-[:FOLLOWS]->(contact_user) THEN 'Following'
                        ELSE 'Follow'
                    END AS follow_status,
                    'USER' AS entity_type

                UNION

                MATCH (asking: Neo4jEntity {reference:$asking_reference})
                WITH asking

                MATCH (asking)-[:HAS_CONTACT]->(contact_unruser:Neo4jUnrUser)
                WHERE NOT EXISTS((asking)-[:FOLLOWS]->(contact_unruser))
                WITH asking, contact_unruser

                CREATE (asking)-[:FOLLOWS{datetimestamp:$datetimestamp}]->(contact_unruser)
                RETURN contact_unruser AS entity, CASE
                        WHEN (asking)-[:FOLLOWS]->(contact_unruser) THEN 'Pending'
                        ELSE 'Follow'
                END AS follow_status,
                'UNREGISTERED' AS entity_type
                """

        store_query = """
                MATCH (asking: Neo4jEntity {reference:$asking_reference})
                WITH asking

                MATCH (asking)-[:HAS_CONTACT]->(contact_store:Neo4jStore)
                WHERE NOT EXISTS ((asking)-[:FOLLOWS]->(contact_store))
                with asking, contact_store

                CREATE (asking)-[:FOLLOWS{datetimestamp:$datetimestamp}]->(contact_store)
                RETURN contact_store AS entity, CASE
                    WHEN (contact_store)-[:FOLLOWS]->(asking) THEN 'Support back'
                    WHEN (asking)-[:FOLLOWS]->(contact_store) THEN 'Supporting'
                    ELSE 'Support'
                END AS follow_status,
                'STORE' AS entity_type
                """

        def add_node_property(x):
            x[0]._properties['follow_status'] = x[1]
            x[0]._properties['entity_type'] = x[2]
            return x[0]

        if action == 'FOLLOW':
            if entity_type == 'USER':
                query = user_query
            elif entity_type == 'STORE':
                query = store_query
            elif entity_type == 'BOTH':
                query = user_query + "UNION" + store_query

            parameters = {'datetimestamp': datetime.now(),
                          'asking_reference': asking_reference}

            logger.info(f"cypher querying started @ {datetime.now()}")
            result, _ = db.cypher_query(query, parameters)
            logger.info(f"cypher querying ended @ {datetime.now()}")
            final_results_with_status = [add_node_property(x) for x in result]

        else:
            unfollow_query = """
                    MATCH (asking: Neo4jEntity {reference:$asking_reference})-[follow_rel:FOLLOWS]->()
                    WHERE follow_rel.datetimestamp >= $cutoff_time
                    DELETE follow_rel
                    """
            cutoff_time = datetime.now() - timedelta(minutes=time_window_in_minutes)
            parameters = {'asking_reference': asking_reference,
                          'cutoff_time': cutoff_time}
            result, _ = db.cypher_query(unfollow_query, parameters)
            final_results_with_status = dbqueries.get_has_contact_entities(asking_reference=asking_reference)

        return final_results_with_status

    def get_follower_following_entities(creator_reference, visitor_reference, required_list, entity_type, limit=None, offset=None):

        d1, d2 = '-', '->'
        if required_list == 'FOLLOWERS':
            d1, d2 = '<-', '-'

        user_query = '''
            MATCH (creator: Neo4jEntity {reference:$creator_reference})
            MATCH (visitor: Neo4jEntity {reference:$visitor_reference})
            WITH creator, visitor
            MATCH (creator)%s[:FOLLOWS]%s(contact_user:Neo4jUser)
            WHERE contact_user.is_deleted = false
            WITH visitor, contact_user
            RETURN contact_user AS entity, CASE
                    WHEN (visitor)-[:FOLLOWS]->(contact_user) THEN 'Following'
                    WHEN (contact_user)-[:FOLLOWS]->(visitor) THEN 'Follow back'
                    ELSE 'Follow'
                END AS follow_status,
                'USER' AS entity_type
            ''' % (d1, d2)

        store_query = '''
            MATCH (creator: Neo4jEntity {reference:$creator_reference})
            MATCH (visitor: Neo4jEntity {reference:$visitor_reference})
            WITH creator, visitor
            MATCH (creator)%s[:FOLLOWS]%s(contact_store:Neo4jStore)
            WHERE contact_store.is_deleted = false
            WITH visitor, contact_store
            RETURN contact_store AS entity, CASE
                WHEN (visitor)-[:FOLLOWS]->(contact_store) THEN 'Supporting'
                WHEN (contact_store)-[:FOLLOWS]->(visitor) THEN 'Support back'
                ELSE 'Support'
            END AS follow_status,
            'STORE' AS entity_type
            ''' % (d1, d2)

        unr_query = '''
            MATCH (creator: Neo4jEntity {reference:$creator_reference})
            MATCH (visitor: Neo4jEntity {reference:$visitor_reference})
            WITH creator, visitor
            MATCH (creator)%s[:FOLLOWS]%s(contact_unruser:Neo4jUnrUser)
            WITH visitor, contact_unruser
            RETURN contact_unruser AS entity, CASE
                    WHEN (visitor)-[:FOLLOWS]->(contact_unruser) THEN 'Pending'
                    ELSE 'Follow'
                END AS follow_status, 'UNREGISTERED' AS entity_type
            ''' % (d1, d2)

        if entity_type == 'USER':
            query = user_query
        if entity_type == 'STORE':
            query = store_query
        if entity_type == 'UNREGISTERED':
            query = unr_query

        if limit or offset:
            query = query + " SKIP $offset LIMIT $limit"
            parameters = {
                'creator_reference': creator_reference,
                'visitor_reference': visitor_reference,
                'd1': d1,
                'd2': d2,
                'limit': limit,
                'offset': offset
            }
        else:
            parameters = {
                'creator_reference': creator_reference,
                'visitor_reference': visitor_reference,
                'd1': d1,
                'd2': d2
            }
        logger.info(f"cypher querying started @ {datetime.now()}")

        result, _ = db.cypher_query(query, parameters)
        logger.info(f"cypher querying ended @ {datetime.now()}")

        def add_node_property(x):
            x[0]._properties['follow_status'] = x[1]
            x[0]._properties['entity_type'] = x[2]
            return x[0]

        final_results_with_status = [add_node_property(x) for x in result]
        return final_results_with_status

    @staticmethod
    def get_follow_status(visitor_reference, references):
        query = '''
            MATCH (visitor:Neo4jEntity {reference: $visitorReference})
            MATCH (visited:Neo4jEntity) WHERE visited.reference IN $references
            WITH visitor, visited,
                CASE WHEN (visited:Neo4jUser) THEN 'USER'
                    WHEN (visited:Neo4jStore) THEN 'STORE'
                    ELSE 'UNKNOWN'
                END AS entityType
            RETURN visited.reference AS visitedReference,
                CASE
                    WHEN entityType = 'USER' THEN
                        CASE
                            WHEN (visitor)-[:FOLLOWS]->(visited) THEN 'Following'
                            WHEN (visited)-[:FOLLOWS]->(visitor) THEN 'Follow Back'
                            ELSE 'Follow'
                        END
                    WHEN entityType = 'STORE' THEN
                        CASE
                            WHEN (visitor)-[:FOLLOWS]->(visited) THEN 'Supporting'
                            WHEN (visited)-[:FOLLOWS]->(visitor) THEN 'Support Back'
                            ELSE 'Support'
                        END
                    ELSE 'Unknown Entity Type'
                END AS followStatus
        '''
        parameters = {'visitorReference': visitor_reference,
                      'references': references}

        result, _ = db.cypher_query(query, parameters)
        data = [{"reference": reference, "follow_status": follow_status} for reference, follow_status in result]
        return data

    def get_recently_visited_store_references(asking_reference, limit, offset):
        query = '''
        MATCH (visitor:Neo4jEntity {reference: $asking_reference})-[rel:VISITED]->(store:Neo4jStore)
        WHERE store.is_deleted = false
        RETURN store.reference AS store_reference
        ORDER BY rel.datetimestamp DESC
        '''
        if limit or offset:
            query = query + 'SKIP $offset LIMIT $limit'
            parameters = {
                'asking_reference': asking_reference,
                'offset': offset,
                'limit': limit
            }
        else:
            parameters = {
                'asking_reference': asking_reference
            }

        result, _ = db.cypher_query(query, parameters)
        data = list(chain.from_iterable(result))
        return data

    def get_following_store_references(asking_reference, limit, offset):
        query = '''
        MATCH (visitor:Neo4jEntity {reference: $asking_reference})-[rel:FOLLOWS]->(store:Neo4jStore)
        WHERE store.is_deleted = false
        RETURN store.reference AS store_reference
        ORDER BY rel.datetimestamp DESC
        '''
        if limit and offset:
            query = query + 'SKIP $offset LIMIT $limit'
            parameters = {
                'asking_reference': asking_reference,
                'offset': offset,
                'limit': limit
            }
        else:
            parameters = {
                'asking_reference': asking_reference
            }

        result, _ = db.cypher_query(query, parameters)
        data = list(chain.from_iterable(result))
        return data

    def follow_or_unfollow_entity(visitor_reference, entity_reference):
        query = '''
        MATCH (visitor:Neo4jEntity {reference: $visitor_reference})
        WITH visitor
        MATCH (entity)
        WHERE $entity_reference STARTS WITH 'UR' AND entity:Neo4jUnrUser AND entity.reference = $entity_reference
              OR
              NOT($entity_reference STARTS WITH 'UR') AND entity:Neo4jEntity AND entity.reference = $entity_reference
        MERGE (visitor)-[rel:FOLLOWS]->(entity)
        ON CREATE SET rel.datetimestamp = $datetimestamp
        RETURN rel
        '''
        parameters = {
                'visitor_reference': visitor_reference,
                'entity_reference': entity_reference,
                'datetimestamp': datetime.now().timestamp()
            }
        result, _ = db.cypher_query(query, parameters)
        return result

    def unsync_contacts(asking_reference):
        query = '''
        MATCH (entity:Neo4jEntity {reference: $asking_reference})-[r:HAS_CONTACT]->()
        WITH r ,count(r) as removed_contact_count
        DETACH DELETE r

        WITH SUM(removed_contact_count) as removed_contact_count

        MATCH (unregistered:Neo4jUnrUser)
        WHERE NOT (unregistered)-[]-()
        WITH unregistered, removed_contact_count, count(unregistered) as deleted_unregistered_count
        DETACH DELETE unregistered

        WITH removed_contact_count, SUM(deleted_unregistered_count) as deleted_unregistered_count
        RETURN removed_contact_count , deleted_unregistered_count;
        '''

        parameters = {
            'asking_reference': asking_reference
        }
        result, _ = db.cypher_query(query, parameters)
        return result

    def get_comment_headers(comment_reference):
        query = '''MATCH path = (cn:Neo4jContent{reference:$comment_reference})<-[:HAS_COMMENT*]-(c:Neo4jContent)<-[:COMMENTED]-(e:Neo4jEntity)
                return distinct e.handle , e.reference
                '''
        parameters = {
            'comment_reference': comment_reference
        }
        result, _ = db.cypher_query(query, parameters)

        return result

    def get_content_headers(content_reference, visitor_reference):
        query = '''
        MATCH (n:Neo4jEntity {reference: $visitor_reference})
        OPTIONAL MATCH (n)-[:REPOSTED]->(c:Neo4jContent {reference: $content_reference})
        WITH n, c
        OPTIONAL MATCH (n)-[:FOLLOWS]->(e:Neo4jEntity)-[:REPOSTED]->(c)
        WITH n, c, e
        WHERE c IS NOT NULL OR e IS NOT NULL
        RETURN DISTINCT
          CASE WHEN c IS NOT NULL THEN n.handle ELSE e.handle END AS handle,
          CASE WHEN c IS NOT NULL THEN n.reference ELSE e.reference END AS reference
        '''
        parameters = {
            'visitor_reference': visitor_reference,
            'content_reference': content_reference
        }
        result, _ = db.cypher_query(query, parameters)

        return result

    def get_references_followed_by_entity(entity_reference):
        query = '''
        MATCH (u:Neo4jEntity {reference: $entity_reference})-[:FOLLOWS]->(f:Neo4jEntity)
        WHERE f.is_deleted = false
        RETURN COLLECT(f.reference) AS followerReferences
        '''
        parameters = {
            'entity_reference': entity_reference
        }

        result, _ = db.cypher_query(query, parameters)
        return result[0][0]

    def get_feed_posts(reference, offset, limit):
        query = '''
        MATCH (n:Neo4jEntity {reference: $reference})-[:FEED]->(f:Neo4jFeed)-[:CONTAINS]->(post:Neo4jPost)
        RETURN post
        SKIP $offset LIMIT $limit

        '''
        parameters = {
            'reference': reference,
            'offset': offset,
            'limit': limit
        }
        result, _ = db.cypher_query(query, parameters)
        return result

    def get_content_references(limit,offset):

        query = '''
        MATCH (content:Neo4jContent)<-[:POSTED|LISTED]-(creator:Neo4jEntity)
        WHERE NOT creator.handle STARTS WITH 'test_'
            AND creator.is_deleted = False
            AND (
                (creator:Neo4jStore AND creator.is_active = True) OR
                NOT creator:Neo4jStore
            )
            AND (content:Neo4jPost OR content:Neo4jProduct)
        WITH content
        ORDER BY content.created_date DESC
        WITH collect(content)[$offset..($offset + $limit)] AS selectedContent
        RETURN
                [c IN selectedContent | c.reference] AS references,
                [c IN selectedContent WHERE c:Neo4jPost | c.reference] AS post_references,
                [c IN selectedContent WHERE c:Neo4jProduct | c.reference] AS product_references,
                [c IN selectedContent WHERE c:Neo4jComment | c.reference] AS comment_references
        '''

        parameters = {
            'offset': offset,
            'limit': limit
        }

        result, _ = db.cypher_query(query, parameters)
        keys = ['references', 'post_references', 'product_references', 'comment_references']
        return dict(zip(keys, result[0]))

    def get_content_references_old(limit,offset):

        query = '''
        MATCH (content:Neo4jContent)<-[:POSTED|LISTED]-(creator:Neo4jEntity)
        WHERE NOT creator.handle STARTS WITH 'test_'
            AND creator.is_deleted = False
            AND (
                (creator:Neo4jStore AND creator.is_active = True) OR
                NOT creator:Neo4jStore
            )
            AND (content:Neo4jPost OR content:Neo4jProduct)
        WITH content
        ORDER BY content.created_date DESC
        RETURN content.reference
        SKIP $offset LIMIT $limit
        '''

        parameters = {
            'offset': offset,
            'limit': limit
        }

        result, _ = db.cypher_query(query, parameters)
        data = list(chain.from_iterable(result))
        return data

    def get_required_posts_in_search(search_query,references_to_omit, offset, limit):
        query = '''
        MATCH (p:Neo4jPost)
        WHERE p.post_text CONTAINS $search_text AND NOT p.reference IN $references_to_omit AND p.is_deleted = false
        RETURN p.reference SKIP $offset LIMIT $limit
        '''
        parameters = {
            'references_to_omit': references_to_omit,
            'search_text': search_query,
            'offset': offset,
            'limit': limit
        }

        result, _ = db.cypher_query(query, parameters)
        data = list(chain.from_iterable(result))
        return data

    def check_previous_review(entity_reference, product_reference):
        query = '''
            MATCH (e:Neo4jEntity {reference: $entity_reference})-[:COMMENTED]->(c:Neo4jComment {comment_type: 'REVIEW'})<-[:HAS_COMMENT]-(:Neo4jContent {reference: $product_reference})
            RETURN COUNT(c) > 0 AS hasCommentedReview
           '''
        parameters = {
            'entity_reference': entity_reference,
            'product_reference': product_reference,
        }

        result, _ = db.cypher_query(query, parameters)
        data = result[0][0]
        return data

    def get_content_creator_handle(content_reference):
        query = '''
            MATCH (c:Neo4jContent {reference: $content_reference})<-[r:POSTED|COMMENTED|LISTED]-(entity:Neo4jEntity)
            RETURN entity.handle AS handle

            '''
        parameters = {
            'content_reference': content_reference,
        }

        result, _ = db.cypher_query(query, parameters)
        data = result[0][0]
        return data

    def get_reposts_of_entity(reference, limit, offset):
        query = '''
        MATCH (n:Neo4jEntity {reference: $reference})-[rel:REPOSTED]->(content:Neo4jContent)
        WITH content
        ORDER BY rel.datetimestamp DESC
        RETURN content.reference SKIP $offset LIMIT $limit
        '''

        parameters = {
            'reference': reference,
            'offset': offset,
            'limit': limit
        }
        result, _ = db.cypher_query(query, parameters)
        data = list(chain.from_iterable(result))
        return data

    def get_sorted_feed_content_references(entity_reference, limit, offset):
        query = '''
            MATCH (entity:Neo4jEntity {reference: $entity_reference})-[:FEED]->(feedNode: Neo4jFeed)-[contains:CONTAINS]->(contentNode:Neo4jContent)
            WHERE NOT EXISTS((contentNode)<-[:LISTED|POSTED]-(:Neo4jEntity {is_active: False}))
            AND NOT EXISTS((contentNode)<-[:LISTED|POSTED]-(:Neo4jEntity {is_deleted: True}))
            WITH contentNode, contains.date AS containsDate,
                CASE WHEN NOT EXISTS((entity)-[:VISITED]->(contentNode)) THEN 0 ELSE 1 END AS visitedOrder
            ORDER BY visitedOrder, containsDate DESC
            WITH collect(contentNode)[$offset..($offset + $limit)] AS selectedContent
            RETURN
                [c IN selectedContent | c.reference] AS references,
                [c IN selectedContent WHERE c:Neo4jPost | c.reference] AS post_references,
                [c IN selectedContent WHERE c:Neo4jProduct | c.reference] AS product_references,
                [c IN selectedContent WHERE c:Neo4jComment | c.reference] AS comment_references
        '''
        parameters = {
            'entity_reference': entity_reference,
            'offset': offset,
            'limit': limit
        }
        result, _ = db.cypher_query(query, parameters)
        keys = ['references', 'post_references', 'product_references', 'comment_references']
        return dict(zip(keys, result[0]))

    def get_sorted_feed_content_references_old(entity_reference, limit, offset):
        query = '''
            MATCH (entity:Neo4jEntity {reference: $entity_reference})-[:FEED]->(feedNode: Neo4jFeed)-[contains:CONTAINS]->(contentNode:Neo4jContent)
            WHERE NOT EXISTS((contentNode)<-[:LISTED|POSTED]-(:Neo4jEntity {is_active: False}))
            AND NOT EXISTS((contentNode)<-[:LISTED|POSTED]-(:Neo4jEntity {is_deleted: True}))
            WITH contentNode, contains.date AS containsDate,
                CASE WHEN NOT EXISTS((entity)-[:VISITED]->(contentNode)) THEN 0 ELSE 1 END AS visitedOrder
            ORDER BY visitedOrder, containsDate DESC
            RETURN DISTINCT contentNode.reference AS reference,
                CASE
                    WHEN contentNode:Neo4jPost THEN 'post'
                    WHEN contentNode:Neo4jProduct THEN 'product'
                    ELSE 'content'
                END AS type
            SKIP $offset LIMIT $limit
        '''
        parameters = {
            'entity_reference': entity_reference,
            'offset': offset,
            'limit': limit
        }
        result, _ = db.cypher_query(query, parameters)
        return result

    def get_liked_entities(content_reference, limit, offset):
        query = '''
        MATCH (c:Neo4jContent {reference: $content_reference})<-[:LIKED]-(entity:Neo4jEntity)
        WHERE NOT entity.handle STARTS WITH 'test_'
        RETURN COLLECT (entity.reference) as liked_entities SKIP $offset LIMIT $limit
        '''
        parameters = {
            'content_reference': content_reference,
            'offset': offset,
            'limit': limit
        }
        result, _ = db.cypher_query(query, parameters)
        return result[0][0]

    def remove_content_from_feed(follower_reference, unfollowed_reference):
        query = '''
        MATCH (follower:Neo4jEntity {reference: $follower_reference})
        MATCH (unfollowed:Neo4jEntity {reference: $unfollowed_reference})
        MATCH (follower)-[:FEED]->(feed:Neo4jFeed)
        MATCH (feed)-[r:CONTAINS]->(content:Neo4jContent)
        WHERE EXISTS((content)<-[:POSTED|REPOSTED|LISTED]-(unfollowed))
        DELETE r
        RETURN count(r) as removed_count
        '''
        parameters = {
            'follower_reference': follower_reference,
            'unfollowed_reference': unfollowed_reference
        }

        result, _ = db.cypher_query(query, parameters)

        removed_count = result[0][0] if result else 0
        return removed_count

    def get_follower_counts(references):
        '''
        Get follower counts for a list of references
        references: list of user or store references
        '''
        query = """
        MATCH (s:Neo4jEntity)
        WHERE s.reference IN $references
        OPTIONAL MATCH (s)<-[:FOLLOWS]-(f:Neo4jEntity)
        WHERE NOT f.is_deleted
        WITH s, COUNT(f) AS follower_count
        RETURN COLLECT({reference: s.reference, follower_count: follower_count}) AS result
        """
        parameters = {
            'references': references
        }
        result, _ = db.cypher_query(query, parameters)
        return result[0][0] if result else []

    def get_product_stories(product_reference):
        query = """
            MATCH (product:Neo4jProduct {reference: $product_reference})-[r:HAS_STORY]->(story:Neo4jStory)
            WITH COLLECT(story.reference) as stories, MAX(r.order) as max_order
            RETURN stories, max_order
        """
        parameters = {'product_reference': product_reference}
        result, _ = db.cypher_query(query, parameters)

        # Return stories list and max_order, default to empty list and 0 if no results
        stories = result[0][0] if result and result[0][0] else []
        max_order = result[0][1] if result and result[0][1] is not None else 0

        return stories, max_order

    def reorder_stories_after_deletion(product, deleted_order):
        """Reorder remaining stories after deletion using a Cypher query"""
        query = """
        MATCH (product:Neo4jProduct {reference: $ref})-[r:HAS_STORY]->(story:Neo4jStory)
        WHERE r.order > $deleted_order
        SET r.order = r.order - 1
        """
        parameters = {
            'ref': product.reference,
            'deleted_order': deleted_order
        }
        result, _ = db.cypher_query(query, parameters)
        return result

    def get_entity_stories(entity_reference):
        query = """
        MATCH (entity:Neo4jEntity {reference: $entity_reference})-[r:POSTED]->(story:Neo4jStory)
        RETURN COLLECT(story.reference) as stories
        """
        parameters = {'entity_reference': entity_reference}
        result, _ = db.cypher_query(query, parameters)
        return result[0][0] if result and result[0][0] else []

    def check_products_taggability(product_references):
        """
        Check which products can be tagged in stories based on maximum tag limit.
        Returns two lists: taggable and untaggable product references.

        Args:
            product_references (list): List of product references to check

        Returns:
            tuple: (taggable_products, untaggable_products)
        """
        query = """
        MATCH (p:Neo4jProduct)
        WHERE p.reference IN $product_references
        OPTIONAL MATCH (p)<-[:TAGGED]-(s:Neo4jStory)
        WITH p, COUNT(s) as tag_count
        RETURN p.reference as reference,
               CASE WHEN tag_count < 4 THEN true ELSE false END as is_taggable
        """

        parameters = {'product_references': product_references}
        result, _ = db.cypher_query(query, parameters)

        taggable = []
        untaggable = []

        for reference, is_taggable in result:
            if is_taggable:
                taggable.append(reference)
            else:
                untaggable.append(reference)

        return taggable, untaggable

    def check_contents_taggability(content_references):
        """
        Check which contents can be tagged in stories.
        For products: maximum 4 story tags allowed
        For other content types: no limit on tags

        Args:
            content_references (list): List of content references to check

        Returns:
            tuple: (taggable_contents, untaggable_contents)
        """
        query = """
        MATCH (c:Neo4jContent)
        WHERE c.reference IN $content_references
        OPTIONAL MATCH (c)<-[:TAGGED]-(s:Neo4jStory)
        WITH c, COUNT(s) as tag_count,
             CASE WHEN c:Neo4jProduct THEN true ELSE false END as is_product
        RETURN c.reference as reference,
               CASE
                   WHEN is_product AND tag_count >= 4 THEN false
                   ELSE true
               END as is_taggable
        """

        parameters = {'content_references': content_references}
        result, _ = db.cypher_query(query, parameters)

        taggable = []
        untaggable = []

        for reference, is_taggable in result:
            if is_taggable:
                taggable.append(reference)
            else:
                untaggable.append(reference)

        return taggable, untaggable

    def add_visit_relationship(visitor_reference, visited_reference):
        """
        Create or update a VISITED relationship between a visitor entity and any visited node.
        This simplified version directly fetches both nodes by reference and creates the relationship.

        Args:
            visitor_reference (str): Reference of the visitor entity
            visited_reference (str): Reference of the visited node (entity or content)

        Returns:
            bool: True if the relationship was created/updated successfully, False otherwise
        """
        query = """
        // Match the visitor entity
        MATCH (visitor {reference: $visitor_reference, is_deleted: false})
        MATCH (visited {reference: $visited_reference, is_deleted: false})

        // Create or update the VISITED relationship
        MERGE (visitor)-[r:VISITED]->(visited)
        ON CREATE SET r.datetimestamp = datetime()
        ON MATCH SET r.datetimestamp = datetime()

        RETURN count(r) > 0 as success
        """

        parameters = {
            'visitor_reference': visitor_reference,
            'visited_reference': visited_reference
        }

        try:
            result, _ = db.cypher_query(query, parameters)
            return len(result) > 0 and result[0][0]
        except Exception as e:
            logger.error(f"Error in add_visit_relationship: {e}")
            return False

    def get_saved_content(entity_reference,content_type, limit, offset):
        if content_type == 'PRODUCT':
            neo4j_content_type = 'Neo4jProduct'
        elif content_type == 'POST':
            neo4j_content_type = 'Neo4jPost'
        else:
            neo4j_content_type = 'Neo4jContent'
        query = f'''
        MATCH (entity:Neo4jEntity {{reference: $entity_reference}})-[:SAVED]->(content:{neo4j_content_type})
        RETURN COLLECT (content.reference) AS saved_content SKIP $offset LIMIT $limit
        '''
        parameters = {
            'entity_reference': entity_reference,
            'offset': offset,
            'limit': limit,
        }
        result, _ = db.cypher_query(query, parameters)
        data = result[0][0] if result and result[0][0] else []
        return data

    def get_batched_interaction_details(visitor_ref, content_refs):
        """
        Get interaction details between a visitor and multiple content items in batch.

        Args:
            visitor_ref: Reference of the visitor entity
            content_refs: List of content references to check interactions for

        Returns:
            Dictionary mapping content references to their interaction details
        """
        if not content_refs:
            return {}

        query = """
        UNWIND $refs AS ref
        MATCH (visitor:Neo4jEntity {reference: $visitor_ref})
        MATCH (content:Neo4jContent {reference: ref})
        OPTIONAL MATCH (visitor)-[rel]->(content)
        WITH visitor, content, ref, COLLECT(TYPE(rel)) AS rel_types
        WITH visitor, content, ref,
            'LIKED' IN rel_types AS liked,
            'SAVED' IN rel_types AS saved,
            'REPOSTED' IN rel_types AS reposted
        OPTIONAL MATCH (content)<-[:CREATED]-(creator:Neo4jEntity)
        OPTIONAL MATCH (visitor)-[:FOLLOWS]->(creator)
        WITH visitor, content, ref, liked, saved, reposted,
            creator IS NOT NULL AS follows_creator
        OPTIONAL MATCH (visitor)-[:FOLLOWS]->(follower:Neo4jEntity)-[r:REPOSTED]->(content)
        WITH visitor, content, ref, liked, saved, reposted, follows_creator,
            COUNT { (follower)-[r:REPOSTED]->(content) WHERE content.created_date > datetime($cutoff_date) } > 0 AS recent_follower_repost

        // First collect visitor headers if they reposted the content
        WITH visitor, content, ref, liked, saved, reposted, follows_creator, recent_follower_repost,
            CASE WHEN reposted AND visitor.handle IS NOT NULL
                THEN [{handle: visitor.handle, reference: visitor.reference}]
                ELSE []
            END AS visitor_headers

        // Then collect followed entities that reposted in a separate step
        OPTIONAL MATCH (visitor)-[:FOLLOWS]->(e:Neo4jEntity)-[:REPOSTED]->(content)
        WITH ref, liked, saved, reposted, follows_creator, recent_follower_repost, visitor_headers,
            COLLECT(DISTINCT {handle: e.handle, reference: e.reference}) AS followed_headers

        // Combine the headers in a separate step
        WITH ref, liked, saved, reposted, follows_creator, recent_follower_repost,
            visitor_headers + followed_headers AS combined_headers

        // Filter out nulls and determine category
        WITH ref, liked, saved, reposted, follows_creator, recent_follower_repost,
            [x IN combined_headers WHERE x IS NOT NULL AND x.handle IS NOT NULL] AS filtered_headers,
            CASE
                WHEN reposted THEN 'REPOST'
                WHEN follows_creator THEN 'POST'
                WHEN recent_follower_repost THEN 'REPOST'
                ELSE 'POST'
            END AS category

        // Only include headers if there are any (either visitor reposted or followed entities reposted)
        WITH ref, liked, saved, reposted, category,
            CASE WHEN size(filtered_headers) > 0 THEN filtered_headers ELSE [] END AS headers

        RETURN ref AS content_ref, liked, saved, reposted, category, headers
        """

        try:
            result, _ = db.cypher_query(query, {
                "visitor_ref": visitor_ref,
                "refs": content_refs,
                "cutoff_date": (datetime.now() - timedelta(days=30)).isoformat()
            })

            def build_header_text(headers):
                """
                Format header text to match the original get_content_header_text method.

                Args:
                    headers: List of dictionaries with 'handle' and 'reference' keys

                Returns:
                    Formatted header text string or None if no headers
                """
                if not headers:
                    return None

                if len(headers) == 1:
                    return f"reposted by @{headers[0].get('handle')}"
                elif len(headers) == 2:
                    return f"reposted by @{headers[0].get('handle')} and @{headers[1].get('handle')}"
                else:
                    others_count = len(headers) - 1
                    return f"reposted by @{headers[0].get('handle')} and {others_count} others"

            return {
                row[0]: {
                    "liked": row[1],
                    "saved": row[2],
                    "reposted": row[3],
                    "category": row[4],
                    "headers": row[5],
                    "header_text": build_header_text(row[5])
                } for row in result
            }
        except Exception as e:
            logger.error(f"Error in get_batched_interaction_details: {e}")
            return {}

    def get_batched_content_creator_details(content_refs):
        """
        Get creator details for multiple content items in batch.

        Args:
            content_refs: List of content references to get creator details for

        Returns:
            Dictionary mapping content references to their creator details
        """
        if not content_refs:
            return {}

        query = """
        UNWIND $refs AS ref
        MATCH (c:Neo4jContent {reference: ref})<-[:POSTED|COMMENTED|LISTED]-(creator:Neo4jEntity)
        RETURN
            ref AS key,
            {
                reference: creator.reference,
                icon: creator.icon,
                handle: creator.handle,
                name: creator.name
            } AS value

        """
        try:
            result, _ = db.cypher_query(query, {"refs": content_refs})
            return dict(result)
        except Exception as e:
            logger.error(f"Error in get_batched_content_creator_details: {e}")
            return {}

    def get_batched_feed_details_new(entity_reference, limit, offset):
        query = '''
            // Step 1: Fetch paginated content from feed
            MATCH (entity:Neo4jEntity {reference: $entity_reference})-[:FEED]->(:Neo4jFeed)-[contains:CONTAINS]->(content:Neo4jContent{is_deleted: false})
            WHERE NOT EXISTS((content)<-[:LISTED|POSTED]-(:Neo4jEntity {is_active: false}))
            AND NOT EXISTS((content)<-[:LISTED|POSTED]-(:Neo4jEntity {is_deleted: true}))
            WITH content, contains.date AS containsDate,
                CASE WHEN NOT EXISTS((entity)-[:VISITED]->(content)) THEN 0 ELSE 1 END AS visitedOrder
            ORDER BY visitedOrder, containsDate DESC
            WITH collect(content)[$offset..($offset + $limit)] AS selectedContent

            // Step 2: Prepare interaction context for current visitor
            MATCH (visitor:Neo4jEntity {reference: $entity_reference})
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(f:Neo4jEntity)-[r:REPOSTED]->(rc:Neo4jContent)
            WITH visitor, selectedContent, f, r, rc
            ORDER BY r.date DESC
            WITH visitor, selectedContent, collect(DISTINCT rc)[0..30] AS recentReposts

            // Step 3: Process each content item
            UNWIND selectedContent AS content

            // Step 4: Fetch creator of the content
            OPTIONAL MATCH (content)<-[:POSTED|COMMENTED|LISTED]-(creator:Neo4jEntity)

            // Step 5: Check visitor interactions with content
            OPTIONAL MATCH (visitor)-[rel]->(content)
            WITH visitor, content, creator, recentReposts, COLLECT(TYPE(rel)) AS rel_types
            WITH visitor, content, creator, recentReposts,
                'LIKED' IN rel_types AS liked,
                'SAVED' IN rel_types AS saved,
                'REPOSTED' IN rel_types AS reposted

            // Step 6: Determine if visitor follows the creator
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(creator)
            WITH visitor, content, creator, liked, saved, reposted, recentReposts,
                creator IS NOT NULL AS follows_creator

            // Step 7: Check if content is among last 30 reposts by followed entities
            WITH visitor, content, creator, liked, saved, reposted, follows_creator,
                ANY(x IN recentReposts WHERE x = content) AS recent_follower_repost

            // Step 8: Header info — if visitor or followed reposted
            WITH visitor, content, creator, liked, saved, reposted, follows_creator, recent_follower_repost,
                CASE WHEN reposted AND visitor.handle IS NOT NULL
                    THEN [{handle: visitor.handle, reference: visitor.reference}]
                    ELSE []
                END AS visitor_headers

            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(e:Neo4jEntity)-[:REPOSTED]->(content)
            WITH content, creator, liked, saved, reposted, follows_creator, recent_follower_repost, visitor_headers,
                COLLECT(DISTINCT {handle: e.handle, reference: e.reference}) AS followed_headers

            // Step 9: Build final fields
            WITH content, creator, liked, saved, reposted,
                [x IN (visitor_headers + followed_headers) WHERE x IS NOT NULL AND x.handle IS NOT NULL] AS headers,
                CASE
                    WHEN reposted THEN 'REPOST'
                    WHEN follows_creator THEN 'POST'
                    WHEN recent_follower_repost THEN 'REPOST'
                    ELSE 'POST'
                END AS category

            WITH content, creator, liked, saved, reposted, category,
                CASE WHEN size(headers) > 0 THEN headers ELSE [] END AS filtered_headers

            WITH content, creator, liked, saved, reposted, category, filtered_headers,
                CASE
                    WHEN content:Neo4jPost THEN 'POST'
                    WHEN content:Neo4jProduct THEN 'PRODUCT'
                    WHEN content:Neo4jComment THEN 'COMMENT'
                    ELSE 'UNKNOWN'
                END AS content_type

            WITH content, creator, liked, saved, reposted, category, filtered_headers, content_type,
            CASE
                WHEN size(filtered_headers) = 1 THEN
                    'reposted by @' + filtered_headers[0].handle
                WHEN size(filtered_headers) = 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and @' + filtered_headers[1].handle
                WHEN size(filtered_headers) > 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and ' + toString(size(filtered_headers) - 1) + ' others'
                ELSE ''
            END AS content_header_text


            // Step 10: Final aggregation
            WITH collect({
                reference: content.reference,
                creator: {
                    reference: creator.reference,
                    icon: creator.icon,
                    handle: creator.handle,
                    name: creator.name
                },
                interaction: {
                    like_status: liked,
                    save_status: saved,
                    repost_status: reposted,
                    content_category: category,
                    content_headers: filtered_headers,
                    content_type: content_type,
                    content_header_text: content_header_text
                }
            }) AS contentWithCreators,
            [selected IN collect(content) WHERE selected:Neo4jPost | selected.reference] AS post_references,
            [selected IN collect(content) WHERE selected:Neo4jProduct | selected.reference] AS product_references,
            [selected IN collect(content) WHERE selected:Neo4jComment | selected.reference] AS comment_references

            RETURN
                contentWithCreators AS references_with_creator_and_interaction,
                post_references,
                product_references,
                comment_references

        '''
        parameters = {
            'entity_reference': entity_reference,
            'offset': offset,
            'limit': limit,
        }
        try:
            result, _ = db.cypher_query(query, parameters)
            data = result[0] if result and result[0] else []
            return data
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

    def get_batched_all_feed_details_new(entity_reference, limit, offset):
        query = '''
            // Step 1: Fetch paginated content from DB
            MATCH (content:Neo4jContent{is_deleted: false})<-[:POSTED|LISTED]-(creator:Neo4jEntity)
            WHERE NOT creator.handle STARTS WITH 'test_'
            AND creator.is_deleted = False
            AND (
                    (creator:Neo4jStore AND creator.is_active = True) OR
                    NOT creator:Neo4jStore
                )
            AND (content:Neo4jPost OR content:Neo4jProduct)
            WITH content
            ORDER BY content.created_date DESC SKIP $offset LIMIT $limit
            WITH collect(content) AS selectedContent

            // Step 2: Prepare interaction context for current visitor
            MATCH (visitor:Neo4jEntity {reference: $entity_reference})
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(f:Neo4jEntity)-[r:REPOSTED]->(rc:Neo4jContent)
            WITH visitor, selectedContent, f, r, rc
            ORDER BY r.date DESC
            WITH visitor, selectedContent, collect(DISTINCT rc)[0..30] AS recentReposts

            // Step 3: Process each content item
            UNWIND selectedContent AS content

            // Step 4: Fetch creator of the content
            OPTIONAL MATCH (content)<-[:POSTED|COMMENTED|LISTED]-(creator:Neo4jEntity)

            // Step 5: Check visitor interactions with content
            OPTIONAL MATCH (visitor)-[rel]->(content)
            WITH visitor, content, creator, recentReposts, COLLECT(TYPE(rel)) AS rel_types
            WITH visitor, content, creator, recentReposts,
                'LIKED' IN rel_types AS liked,
                'SAVED' IN rel_types AS saved,
                'REPOSTED' IN rel_types AS reposted

            // Step 6: Determine if visitor follows the creator
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(creator)
            WITH visitor, content, creator, liked, saved, reposted, recentReposts,
                creator IS NOT NULL AS follows_creator

            // Step 7: Check if content is among last 30 reposts by followed entities
            WITH visitor, content, creator, liked, saved, reposted, follows_creator,
                ANY(x IN recentReposts WHERE x = content) AS recent_follower_repost

            // Step 8: Header info — if visitor or followed reposted
            WITH visitor, content, creator, liked, saved, reposted, follows_creator, recent_follower_repost,
                CASE WHEN reposted AND visitor.handle IS NOT NULL
                    THEN [{handle: visitor.handle, reference: visitor.reference}]
                    ELSE []
                END AS visitor_headers

            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(e:Neo4jEntity)-[:REPOSTED]->(content)
            WITH content, creator, liked, saved, reposted, follows_creator, recent_follower_repost, visitor_headers,
                COLLECT(DISTINCT {handle: e.handle, reference: e.reference}) AS followed_headers

            // Step 9: Build final fields
            WITH content, creator, liked, saved, reposted,
                [x IN (visitor_headers + followed_headers) WHERE x IS NOT NULL AND x.handle IS NOT NULL] AS headers,
                CASE
                    WHEN reposted THEN 'REPOST'
                    WHEN follows_creator THEN 'POST'
                    WHEN recent_follower_repost THEN 'REPOST'
                    ELSE 'POST'
                END AS category

            WITH content, creator, liked, saved, reposted, category,
                CASE WHEN size(headers) > 0 THEN headers ELSE [] END AS filtered_headers

            WITH content, creator, liked, saved, reposted, category, filtered_headers,
                CASE
                    WHEN content:Neo4jPost THEN 'POST'
                    WHEN content:Neo4jProduct THEN 'PRODUCT'
                    WHEN content:Neo4jComment THEN 'COMMENT'
                    ELSE 'UNKNOWN'
                END AS content_type

            WITH content, creator, liked, saved, reposted, category, filtered_headers, content_type,
            CASE
                WHEN size(filtered_headers) = 1 THEN
                    'reposted by @' + filtered_headers[0].handle
                WHEN size(filtered_headers) = 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and @' + filtered_headers[1].handle
                WHEN size(filtered_headers) > 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and ' + toString(size(filtered_headers) - 1) + ' others'
                ELSE ''
            END AS content_header_text


            // Step 10: Final aggregation
            WITH collect({
                reference: content.reference,
                creator: {
                    reference: creator.reference,
                    icon: creator.icon,
                    handle: creator.handle,
                    name: creator.name
                },
                interaction: {
                    like_status: liked,
                    save_status: saved,
                    repost_status: reposted,
                    content_category: category,
                    content_headers: filtered_headers,
                    content_type: content_type,
                    content_header_text: content_header_text
                }
            }) AS contentWithCreators,
            [selected IN collect(content) WHERE selected:Neo4jPost | selected.reference] AS post_references,
            [selected IN collect(content) WHERE selected:Neo4jProduct | selected.reference] AS product_references,
            [selected IN collect(content) WHERE selected:Neo4jComment | selected.reference] AS comment_references

            RETURN
                contentWithCreators AS references_with_creator_and_interaction,
                post_references,
                product_references,
                comment_references

        '''
        parameters = {
            'entity_reference': entity_reference,
            'offset': offset,
            'limit': limit,
        }
        try:
            result, _ = db.cypher_query(query, parameters)
            data = result[0] if result and result[0] else []
            return data
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

    def get_store_product_interaction_details(visitor_reference, store_reference, limit, offset):
        """
        Get interaction details between a visitor and multiple content items in batch.

        Args:
            visitor_ref: Reference of the visitor entity
            content_refs: List of content references to check interactions for

        Returns:
            Dictionary mapping content references to their interaction details
        """

        query = """
            // Step 1: Fetch paginated content from feed
            MATCH (entity:Neo4jEntity {reference: $store_reference})-[:LISTED]->(content:Neo4jContent{is_deleted: false})
            WHERE NOT EXISTS((content)<-[:LISTED]-(:Neo4jEntity {is_active: false}))
            AND NOT EXISTS((content)<-[:LISTED]-(:Neo4jEntity {is_deleted: true}))

            WITH content
            ORDER BY content.created_date DESC
            WITH collect(content)[$offset..($offset + $limit)] AS selectedContent

            // Step 2: Prepare interaction context for current visitor
            MATCH (visitor:Neo4jEntity {reference: $visitor_reference})
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(f:Neo4jEntity)-[r:REPOSTED]->(rc:Neo4jContent)
            WITH visitor, selectedContent, f, r, rc
            ORDER BY r.date DESC
            WITH visitor, selectedContent, collect(DISTINCT rc)[0..30] AS recentReposts

            // Step 3: Process each content item
            UNWIND selectedContent AS content

            // Step 4: Fetch creator of the content
            OPTIONAL MATCH (content)<-[:POSTED|COMMENTED|LISTED]-(creator:Neo4jEntity)

            // Step 5: Check visitor interactions with content
            OPTIONAL MATCH (visitor)-[rel]->(content)
            WITH visitor, content, creator, recentReposts, COLLECT(TYPE(rel)) AS rel_types
            WITH visitor, content, creator, recentReposts,
                'LIKED' IN rel_types AS liked,
                'SAVED' IN rel_types AS saved,
                'REPOSTED' IN rel_types AS reposted

            // Step 6: Determine if visitor follows the creator
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(creator)
            WITH visitor, content, creator, liked, saved, reposted, recentReposts,
                creator IS NOT NULL AS follows_creator

            // Step 7: Check if content is among last 30 reposts by followed entities
            WITH visitor, content, creator, liked, saved, reposted, follows_creator,
                ANY(x IN recentReposts WHERE x = content) AS recent_follower_repost

            // Step 8: Header info — if visitor or followed reposted
            WITH visitor, content, creator, liked, saved, reposted, follows_creator, recent_follower_repost,
                CASE WHEN reposted AND visitor.handle IS NOT NULL
                    THEN [{handle: visitor.handle, reference: visitor.reference}]
                    ELSE []
                END AS visitor_headers

            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(e:Neo4jEntity)-[:REPOSTED]->(content)
            WITH content, creator, liked, saved, reposted, follows_creator, recent_follower_repost, visitor_headers,
                COLLECT(DISTINCT {handle: e.handle, reference: e.reference}) AS followed_headers

            // Step 9: Build final fields
            WITH content, creator, liked, saved, reposted,
                [x IN (visitor_headers + followed_headers) WHERE x IS NOT NULL AND x.handle IS NOT NULL] AS headers,
                CASE
                    WHEN reposted THEN 'REPOST'
                    WHEN follows_creator THEN 'POST'
                    WHEN recent_follower_repost THEN 'REPOST'
                    ELSE 'POST'
                END AS category

            WITH content, creator, liked, saved, reposted, category,
                CASE WHEN size(headers) > 0 THEN headers ELSE [] END AS filtered_headers

            WITH content, creator, liked, saved, reposted, category, filtered_headers,
                CASE
                    WHEN content:Neo4jPost THEN 'POST'
                    WHEN content:Neo4jProduct THEN 'PRODUCT'
                    WHEN content:Neo4jComment THEN 'COMMENT'
                    ELSE 'UNKNOWN'
                END AS content_type

            WITH content, creator, liked, saved, reposted, category, filtered_headers, content_type,
            CASE
                WHEN size(filtered_headers) = 1 THEN
                    'reposted by @' + filtered_headers[0].handle
                WHEN size(filtered_headers) = 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and @' + filtered_headers[1].handle
                WHEN size(filtered_headers) > 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and ' + toString(size(filtered_headers) - 1) + ' others'
                ELSE ''
            END AS content_header_text


            // Step 10: Final aggregation
            WITH collect({
                reference: content.reference,
                creator: {
                    reference: creator.reference,
                    icon: creator.icon,
                    handle: creator.handle,
                    name: creator.name
                },
                interaction: {
                    like_status: liked,
                    save_status: saved,
                    repost_status: reposted,
                    content_category: category,
                    content_headers: filtered_headers,
                    content_type: content_type,
                    content_header_text: content_header_text
                }
            }) AS contentWithCreators,
            [selected IN collect(content) WHERE selected:Neo4jProduct | selected.reference] AS product_references

            RETURN
                contentWithCreators AS references_with_creator_and_interaction,
                product_references
        """

        parameters = {
            "visitor_reference": visitor_reference,
            "store_reference": store_reference,
            "limit": limit,
            "offset":offset
        }

        try:
            result, _ = db.cypher_query(query, parameters)
            data = result[0] if result and result[0] else []
            return data
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

    def get_content_interaction_details(visitor_reference, content_references):
        """
        Get interaction details between a visitor and multiple content items in batch.

        Args:
            visitor_ref: Reference of the visitor entity
            content_refs: List of content references to check interactions for

        Returns:
            Dictionary mapping content references to their interaction details
        """

        query = """
            // Step 1: Fetch paginated content from feed
            MATCH (content:Neo4jContent) WHERE content.reference IN $content_references
            AND NOT EXISTS((content)<-[:LISTED]-(:Neo4jEntity {is_active: false}))
            AND NOT EXISTS((content)<-[:LISTED]-(:Neo4jEntity {is_deleted: true}))

            WITH content
            ORDER BY content.created_date DESC
            WITH collect(content) AS selectedContent

            // Step 2: Prepare interaction context for current visitor
            MATCH (visitor:Neo4jEntity {reference: $visitor_reference})
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(f:Neo4jEntity)-[r:REPOSTED]->(rc:Neo4jContent)
            WITH visitor, selectedContent, f, r, rc
            ORDER BY r.date DESC
            WITH visitor, selectedContent, collect(DISTINCT rc)[0..30] AS recentReposts

            // Step 3: Process each content item
            UNWIND selectedContent AS content

            // Step 4: Fetch creator of the content
            OPTIONAL MATCH (content)<-[:POSTED|COMMENTED|LISTED]-(creator:Neo4jEntity)

            // Step 5: Check visitor interactions with content
            OPTIONAL MATCH (visitor)-[rel]->(content)
            WITH visitor, content, creator, recentReposts, COLLECT(TYPE(rel)) AS rel_types
            WITH visitor, content, creator, recentReposts,
                'LIKED' IN rel_types AS liked,
                'SAVED' IN rel_types AS saved,
                'REPOSTED' IN rel_types AS reposted

            // Step 6: Determine if visitor follows the creator
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(creator)
            WITH visitor, content, creator, liked, saved, reposted, recentReposts,
                creator IS NOT NULL AS follows_creator

            // Step 7: Check if content is among last 30 reposts by followed entities
            WITH visitor, content, creator, liked, saved, reposted, follows_creator,
                ANY(x IN recentReposts WHERE x = content) AS recent_follower_repost

            // Step 8: Header info — if visitor or followed reposted
            WITH visitor, content, creator, liked, saved, reposted, follows_creator, recent_follower_repost,
                CASE WHEN reposted AND visitor.handle IS NOT NULL
                    THEN [{handle: visitor.handle, reference: visitor.reference}]
                    ELSE []
                END AS visitor_headers

            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(e:Neo4jEntity)-[:REPOSTED]->(content)
            WITH content, creator, liked, saved, reposted, follows_creator, recent_follower_repost, visitor_headers,
                COLLECT(DISTINCT {handle: e.handle, reference: e.reference}) AS followed_headers

            // Step 9: Build final fields
            WITH content, creator, liked, saved, reposted,
                [x IN (visitor_headers + followed_headers) WHERE x IS NOT NULL AND x.handle IS NOT NULL] AS headers,
                CASE
                    WHEN reposted THEN 'REPOST'
                    WHEN follows_creator THEN 'POST'
                    WHEN recent_follower_repost THEN 'REPOST'
                    ELSE 'POST'
                END AS category

            WITH content, creator, liked, saved, reposted, category,
                CASE WHEN size(headers) > 0 THEN headers ELSE [] END AS filtered_headers

            WITH content, creator, liked, saved, reposted, category, filtered_headers,
                CASE
                    WHEN content:Neo4jPost THEN 'POST'
                    WHEN content:Neo4jProduct THEN 'PRODUCT'
                    WHEN content:Neo4jComment THEN 'COMMENT'
                    ELSE 'UNKNOWN'
                END AS content_type

            WITH content, creator, liked, saved, reposted, category, filtered_headers, content_type,
            CASE
                WHEN size(filtered_headers) = 1 THEN
                    'reposted by @' + filtered_headers[0].handle
                WHEN size(filtered_headers) = 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and @' + filtered_headers[1].handle
                WHEN size(filtered_headers) > 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and ' + toString(size(filtered_headers) - 1) + ' others'
                ELSE ''
            END AS content_header_text


            // Step 10: Final aggregation
            WITH collect({
                reference: content.reference,
                creator: {
                    reference: creator.reference,
                    icon: creator.icon,
                    handle: creator.handle,
                    name: creator.name
                },
                interaction: {
                    like_status: liked,
                    save_status: saved,
                    repost_status: reposted,
                    content_category: category,
                    content_headers: filtered_headers,
                    content_type: content_type,
                    content_header_text: content_header_text
                }
            }) AS contentWithCreators,
            [selected IN collect(content) WHERE selected:Neo4jProduct | selected.reference] AS product_references,
            [selected IN collect(content) WHERE selected:Neo4jPost | selected.reference] AS post_references
            RETURN
                contentWithCreators AS references_with_creator_and_interaction,
                product_references,
                post_references
        """

        parameters = {
            "visitor_reference": visitor_reference,
            "content_references": content_references
        }

        try:
            result, _ = db.cypher_query(query, parameters)
            data = result[0] if result and result[0] else []
            return data
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

    def get_user_or_store_post_interaction_details(visitor_reference, entity_reference, limit, offset):
        """
        Get interaction details between a visitor and multiple content items in batch.

        Args:
            visitor_ref: Reference of the visitor entity
            content_refs: List of content references to check interactions for

        Returns:
            Dictionary mapping content references to their interaction details
        """

        query = """
            // Step 1: Fetch paginated content from feed
            MATCH (entity:Neo4jEntity {reference: $entity_reference})-[:POSTED]->(content:Neo4jContent{is_deleted: false})
            WHERE NOT EXISTS((content)<-[:POSTED]-(:Neo4jEntity {is_active: false}))
            AND NOT EXISTS((content)<-[:POSTED]-(:Neo4jEntity {is_deleted: true}))

            WITH content
            ORDER BY content.created_date DESC
            WITH collect(content)[$offset..($offset + $limit)] AS selectedContent

            // Step 2: Prepare interaction context for current visitor
            MATCH (visitor:Neo4jEntity {reference: $visitor_reference})
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(f:Neo4jEntity)-[r:REPOSTED]->(rc:Neo4jContent)
            WITH visitor, selectedContent, f, r, rc
            ORDER BY r.date DESC
            WITH visitor, selectedContent, collect(DISTINCT rc)[0..30] AS recentReposts

            // Step 3: Process each content item
            UNWIND selectedContent AS content

            // Step 4: Fetch creator of the content
            OPTIONAL MATCH (content)<-[:POSTED|COMMENTED|LISTED]-(creator:Neo4jEntity)

            // Step 5: Check visitor interactions with content
            OPTIONAL MATCH (visitor)-[rel]->(content)
            WITH visitor, content, creator, recentReposts, COLLECT(TYPE(rel)) AS rel_types
            WITH visitor, content, creator, recentReposts,
                'LIKED' IN rel_types AS liked,
                'SAVED' IN rel_types AS saved,
                'REPOSTED' IN rel_types AS reposted

            // Step 6: Determine if visitor follows the creator
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(creator)
            WITH visitor, content, creator, liked, saved, reposted, recentReposts,
                creator IS NOT NULL AS follows_creator

            // Step 7: Check if content is among last 30 reposts by followed entities
            WITH visitor, content, creator, liked, saved, reposted, follows_creator,
                ANY(x IN recentReposts WHERE x = content) AS recent_follower_repost

            // Step 8: Header info — if visitor or followed reposted
            WITH visitor, content, creator, liked, saved, reposted, follows_creator, recent_follower_repost,
                CASE WHEN reposted AND visitor.handle IS NOT NULL
                    THEN [{handle: visitor.handle, reference: visitor.reference}]
                    ELSE []
                END AS visitor_headers

            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(e:Neo4jEntity)-[:REPOSTED]->(content)
            WITH content, creator, liked, saved, reposted, follows_creator, recent_follower_repost, visitor_headers,
                COLLECT(DISTINCT {handle: e.handle, reference: e.reference}) AS followed_headers

            // Step 9: Build final fields
            WITH content, creator, liked, saved, reposted,
                [x IN (visitor_headers + followed_headers) WHERE x IS NOT NULL AND x.handle IS NOT NULL] AS headers,
                CASE
                    WHEN reposted THEN 'REPOST'
                    WHEN follows_creator THEN 'POST'
                    WHEN recent_follower_repost THEN 'REPOST'
                    ELSE 'POST'
                END AS category

            WITH content, creator, liked, saved, reposted, category,
                CASE WHEN size(headers) > 0 THEN headers ELSE [] END AS filtered_headers

            WITH content, creator, liked, saved, reposted, category, filtered_headers,
                CASE
                    WHEN content:Neo4jPost THEN 'POST'
                    WHEN content:Neo4jProduct THEN 'PRODUCT'
                    WHEN content:Neo4jComment THEN 'COMMENT'
                    ELSE 'UNKNOWN'
                END AS content_type

            WITH content, creator, liked, saved, reposted, category, filtered_headers, content_type,
            CASE
                WHEN size(filtered_headers) = 1 THEN
                    'reposted by @' + filtered_headers[0].handle
                WHEN size(filtered_headers) = 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and @' + filtered_headers[1].handle
                WHEN size(filtered_headers) > 2 THEN
                    'reposted by @' + filtered_headers[0].handle + ' and ' + toString(size(filtered_headers) - 1) + ' others'
                ELSE ''
            END AS content_header_text


            // Step 10: Final aggregation
            WITH collect({
                reference: content.reference,
                creator: {
                    reference: creator.reference,
                    icon: creator.icon,
                    handle: creator.handle,
                    name: creator.name
                },
                interaction: {
                    like_status: liked,
                    save_status: saved,
                    repost_status: reposted,
                    content_category: category,
                    content_headers: filtered_headers,
                    content_type: content_type,
                    content_header_text: content_header_text
                }
            }) AS contentWithCreators,
            [selected IN collect(content) WHERE selected:Neo4jPost | selected.reference] AS post_references

            RETURN
                contentWithCreators AS references_with_creator_and_interaction,
                post_references
        """

        parameters = {
            "visitor_reference": visitor_reference,
            "entity_reference": entity_reference,
            "limit": limit,
            "offset":offset
        }

        try:
            result, _ = db.cypher_query(query, parameters)
            data = result[0] if result and result[0] else []
            return data
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

    def get_tagged_objects_count(content_reference, item_type):
        if item_type not in ['PRODUCT', 'STORE', 'USER']:
            raise ValueError("Invalid item_type. Must be one of: 'PRODUCT', 'STORE', 'USER'.")

        label_map = {
            'PRODUCT': 'Neo4jProduct',
            'STORE': 'Neo4jStore',
            'USER': 'Neo4jUser',
        }

        label = label_map[item_type]

        query = f'''
        MATCH (c:Neo4jContent {{reference: $content_reference}})
        OPTIONAL MATCH (c)-[:TAGGED]->(n:{label})
        RETURN COUNT(n) AS tagged_objects_count
        '''

        parameters = {
            'content_reference': content_reference
        }
        result, _ = db.cypher_query(query, parameters)
        data = result[0][0]
        return data


    def get_comment_interaction_details(visitor_reference, comment_references):
        """
        Get interaction details between a visitor and multiple content items in batch.

        Args:
            visitor_ref: Reference of the visitor entity
            content_refs: List of content references to check interactions for

        Returns:
            Dictionary mapping content references to their interaction details
        """

        query = """
            MATCH (content:Neo4jComment)
            WHERE content.reference IN $content_references
            AND NOT EXISTS((content)<-[:COMMENTED]-(:Neo4jEntity {is_active: false}))
            AND NOT EXISTS((content)<-[:COMMENTED]-(:Neo4jEntity {is_deleted: true}))
            WITH content
            ORDER BY content.created_date DESC
            WITH collect(content) AS selectedContent

            MATCH (visitor:Neo4jEntity {reference: $visitor_reference})
            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(:Neo4jEntity)-[r:REPOSTED]->(rc:Neo4jContent)
            WITH visitor, selectedContent, collect(DISTINCT rc)[0..30] AS recentReposts

            UNWIND selectedContent AS content
            OPTIONAL MATCH (content)<-[:POSTED|COMMENTED|LISTED]-(creator:Neo4jEntity)
            OPTIONAL MATCH (visitor)-[rel]->(content)
            WITH visitor, content, creator, recentReposts, COLLECT(TYPE(rel)) AS rel_types

            WITH visitor, content, creator, recentReposts,
                'LIKED' IN rel_types AS liked,
                'SAVED' IN rel_types AS saved,
                'REPOSTED' IN rel_types AS reposted

            OPTIONAL MATCH (visitor)-[:FOLLOWS]->(creator)
            WITH visitor, content, liked, saved, reposted, creator, 
                creator IS NOT NULL AS follows_creator,
                ANY(x IN recentReposts WHERE x = content) AS recent_follower_repost

            WITH content, liked, saved, reposted, creator,
                CASE
                    WHEN reposted THEN 'REPOST'
                    WHEN follows_creator THEN 'POST'
                    WHEN recent_follower_repost THEN 'REPOST'
                    ELSE 'POST'
                END AS category,
                CASE
                    WHEN content:Neo4jPost THEN 'POST'
                    WHEN content:Neo4jProduct THEN 'PRODUCT'
                    WHEN content:Neo4jComment THEN 'COMMENT'
                    ELSE 'UNKNOWN'
                END AS content_type

            RETURN collect({
                reference: content.reference,
                creator: {
                    reference: creator.reference,
                    icon: creator.icon,
                    handle: creator.handle,
                    name: creator.name
                },
                interaction: {
                    like_status: liked,
                    save_status: saved,
                    repost_status: reposted,
                    content_category: category,
                    content_type: content_type
                }
            }) AS references_with_interaction

        """

        parameters = {
            "visitor_reference": visitor_reference,
            "content_references": comment_references
        }

        try:
            result, _ = db.cypher_query(query, parameters)
            data = result[0] if result and result[0] else []
            return data
        except Exception as e:
            print(f"Error executing query: {e}")
            return []

class sqlqueries():
    def __init__(self):
        self.db_config = {
            "dbname": config('DB_NAME'),
            "user": config('DB_USER'),
            "password": config('DB_PASSWORD'),
            "host": config('DB_HOST'),
            "port": 5432
        }

    def get_store_products_full(self, store_ref):
        query = '''
        WITH latest_refund_warranty AS (
        SELECT
            store_reference,
            product_reference,
            return_type,
            return_period,
            return_conditions,
            return_pickup,
            has_warranty,
            warranty_period_no,
            warranty_period,
            warranty_card_available,
            ROW_NUMBER() OVER (PARTITION BY COALESCE(product_reference, store_reference)
                            ORDER BY refundandwarrantyid DESC) as rn
        FROM store.refund_and_warranty
        WHERE store_reference = %(store_ref)s
            AND is_deleted = false
        ),
        latest_delivery_settings AS (
            SELECT
                store_reference,
                product_reference,
                deliverymethod_swadesic,
                deliverymethod_logistics,
                deliverymethod_self,
                delivery_locations,
                deliveryfeetype_all_free,
                deliveryfeetype_standard,
                deliveryfeetype_distance,
                deliveryfee_value,
                time_to_deliver,
                time_to_prepare,
                ROW_NUMBER() OVER (PARTITION BY COALESCE(product_reference, store_reference)
                                ORDER BY deliverysettingid DESC) as rn
            FROM store.delivery_settings
            WHERE store_reference = %(store_ref)s
                AND is_deleted = false
        ),
        trust_center_details as (
            SELECT
                trustcenterid,
                joiningdate,
                trustscore,
                sellerlevel,
                phonenumber,
                emailid,
                primarycontacttype,
                isphysicalstore,
                address,
                city,
                pincode,
                state,
                longitude,
                latitude,
                store_reference,
                storeid,
                swadeshi_owned,
                trust_center_note
            FROM store.trust_center
            where store_reference = %(store_ref)s
        )
        SELECT
            p.product_reference,
            p.product_name,
            p.brand_name,
            p.product_description,
            p.promotion_link,
            p.hashtags,
            p.mrp_price,
            p.selling_price,
            p.product_category,
            p.targeted_gender,
            p.swadeshi_brand,
            p.swadeshi_made,
            p.active,
            p.in_stock,
            p.created_date,
            p.modified_date,
            p.product_version,
            p.comment_count,
            p.like_count,
            p.orders_count,
            p.returns_count,
            p.rating,
            p.count_of_ratings,
            p.analytics_view_count,
            p.is_promotion_enabled,
            p.promotion_amount,
            p.product_slug,
            p.product_code,
            -- Product specific refund & warranty settings (if exists)
            prw.return_type as product_return_type,
            prw.return_period as product_return_period,
            prw.return_conditions as product_return_conditions,
            prw.return_pickup as product_return_pickup,
            prw.has_warranty as product_has_warranty,
            prw.warranty_period_no as product_warranty_period_no,
            prw.warranty_period as product_warranty_period,
            prw.warranty_card_available as product_warranty_card_available,
            -- Store default refund & warranty settings
            srw.return_type as store_return_type,
            srw.return_period as store_return_period,
            srw.return_conditions as store_return_conditions,
            srw.return_pickup as store_return_pickup,
            srw.has_warranty as store_has_warranty,
            srw.warranty_period_no as store_warranty_period_no,
            srw.warranty_period as store_warranty_period,
            srw.warranty_card_available as store_warranty_card_available,
            -- Product specific delivery settings (if exists)
            pds.deliverymethod_swadesic as product_delivery_by_swadesic,
            pds.deliverymethod_logistics as product_delivery_by_logistics,
            pds.deliverymethod_self as product_delivery_by_seller,
            pds.delivery_locations as product_delivery_locations,
            pds.deliveryfeetype_all_free as product_delivery_all_free,
            pds.deliveryfeetype_standard as product_delivery_standard_fee,
            pds.deliveryfeetype_distance as product_delivery_distance_based,
            pds.deliveryfee_value as product_delivery_fee,
            pds.time_to_deliver as product_time_to_deliver,
            pds.time_to_prepare as product_time_to_prepare,
            -- Store default delivery settings
            sds.deliverymethod_swadesic	 as store_delivery_by_swadesic,
            sds.deliverymethod_logistics as store_delivery_by_logistics,
            sds.deliverymethod_self as store_delivery_by_seller,
            sds.delivery_locations as store_delivery_locations,
            sds.deliveryfeetype_all_free as store_delivery_all_free,
            sds.deliveryfeetype_standard as store_delivery_standard_fee,
            sds.deliveryfeetype_distance as store_delivery_distance_based,
            sds.deliveryfee_value as store_delivery_fee,
            sds.time_to_deliver as store_time_to_deliver,
            sds.time_to_prepare as store_time_to_prepare,
            -- Trustcenter Details --
            stc.swadeshi_owned as swadeshi_owned
        FROM
            product.product p
            -- Get product specific refund & warranty settings
            LEFT JOIN latest_refund_warranty prw ON
                p.product_reference = prw.product_reference
                AND prw.rn = 1
            -- Get store default refund & warranty settings
            LEFT JOIN latest_refund_warranty srw ON
                srw.product_reference IS NULL
                AND srw.rn = 1
            -- Get product specific delivery settings
            LEFT JOIN latest_delivery_settings pds ON
                p.product_reference = pds.product_reference
                AND pds.rn = 1
            -- Get store default delivery settings
            LEFT JOIN latest_delivery_settings sds ON
                sds.product_reference IS NULL
                AND sds.rn = 1
            -- Get Trustcenter Details
            LEFT JOIN trust_center_details stc ON
                stc.store_reference = p.store_reference
        WHERE
            p.store_reference = %(store_ref)s
            AND p.deleted = false
        ORDER BY p.created_date DESC
        '''

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # Bind store_ref securely
            cursor.execute(query, {"store_ref": store_ref})
            # print(cursor.mogrify(query, {"store_ref": store_ref}).decode())
            results = cursor.fetchall()

            cursor.close()
            conn.close()
            json_results = json.dumps(results, default=str)  # Convert to JSON

            return json_results

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])


    def get_products_full(self, product_refs):
        query = '''
            WITH latest_refund_warranty AS (
                SELECT
                    store_reference,
                    product_reference,
                    return_type,
                    return_period,
                    return_conditions,
                    return_pickup,
                    has_warranty,
                    warranty_period_no,
                    warranty_period,
                    warranty_card_available,
                    ROW_NUMBER() OVER (PARTITION BY COALESCE(product_reference, store_reference)
                                    ORDER BY refundandwarrantyid DESC) as rn
                FROM store.refund_and_warranty
                WHERE is_deleted = false
            ),
            latest_delivery_settings AS (
                SELECT
                    store_reference,
                    product_reference,
                    deliverymethod_swadesic,
                    deliverymethod_logistics,
                    deliverymethod_self,
                    delivery_locations,
                    deliveryfeetype_all_free,
                    deliveryfeetype_standard,
                    deliveryfeetype_distance,
                    deliveryfee_value,
                    time_to_deliver,
                    time_to_prepare,
                    fulfillment_options,
                    ROW_NUMBER() OVER (PARTITION BY COALESCE(product_reference, store_reference)
                                    ORDER BY deliverysettingid DESC) as rn
                FROM store.delivery_settings
                WHERE is_deleted = false
            ),
            trust_center_details as (
                SELECT
                    trustcenterid,
                    joiningdate,
                    trustscore,
                    sellerlevel,
                    phonenumber,
                    emailid,
                    primarycontacttype,
                    isphysicalstore,
                    address,
                    city,
                    pincode,
                    state,
                    longitude,
                    latitude,
                    store_reference,
                    storeid,
                    swadeshi_owned,
                    trust_center_note
                FROM store.trust_center
            )
            SELECT
                p.product_reference,
                p.product_name,
                p.brand_name,
                p.product_description,
                p.promotion_link,
                p.hashtags,
                p.mrp_price,
                p.selling_price,
                p.product_category,
                p.targeted_gender,
                p.swadeshi_brand,
                p.swadeshi_made,
                p.active,
                p.in_stock,
                p.created_date,
                p.modified_date,
                p.product_version,
                p.comment_count,
                p.like_count,
                p.orders_count,
                p.returns_count,
                p.rating,
                p.count_of_ratings,
                p.analytics_view_count,
                p.is_promotion_enabled,
                p.promotion_amount,
                p.product_slug,
                p.product_code,
                -- Product specific refund & warranty settings (if exists)
                prw.return_type as product_return_type,
                prw.return_period as product_return_period,
                prw.return_conditions as product_return_conditions,
                prw.return_pickup as product_return_pickup,
                prw.has_warranty as product_has_warranty,
                prw.warranty_period_no as product_warranty_period_no,
                prw.warranty_period as product_warranty_period,
                prw.warranty_card_available as product_warranty_card_available,
                -- Store default refund & warranty settings
                srw.return_type as store_return_type,
                srw.return_period as store_return_period,
                srw.return_conditions as store_return_conditions,
                srw.return_pickup as store_return_pickup,
                srw.has_warranty as store_has_warranty,
                srw.warranty_period_no as store_warranty_period_no,
                srw.warranty_period as store_warranty_period,
                srw.warranty_card_available as store_warranty_card_available,
                -- Product specific delivery settings (if exists)
                pds.deliverymethod_swadesic as product_delivery_by_swadesic,
                pds.deliverymethod_logistics as product_delivery_by_logistics,
                pds.deliverymethod_self as product_delivery_by_seller,
                pds.delivery_locations as product_delivery_locations,
                pds.deliveryfeetype_all_free as product_delivery_all_free,
                pds.deliveryfeetype_standard as product_delivery_standard_fee,
                pds.deliveryfeetype_distance as product_delivery_distance_based,
                pds.deliveryfee_value as product_delivery_fee,
                pds.time_to_deliver as product_time_to_deliver,
                pds.time_to_prepare as product_time_to_prepare,
                pds.fulfillment_options as product_fulfillment_options,
                -- Store default delivery settings
                sds.deliverymethod_swadesic	 as store_delivery_by_swadesic,
                sds.deliverymethod_logistics as store_delivery_by_logistics,
                sds.deliverymethod_self as store_delivery_by_seller,
                sds.delivery_locations as store_delivery_locations,
                sds.deliveryfeetype_all_free as store_delivery_all_free,
                sds.deliveryfeetype_standard as store_delivery_standard_fee,
                sds.deliveryfeetype_distance as store_delivery_distance_based,
                sds.deliveryfee_value as store_delivery_fee,
                sds.time_to_deliver as store_time_to_deliver,
                sds.time_to_prepare as store_time_to_prepare,
                sds.fulfillment_options as store_fulfillment_options,
                COALESCE(images.images, '[]'::json) AS product_images,


                -- Trustcenter Details --
                stc.swadeshi_owned as swadeshi_owned
            FROM
                product.product p
                -- Get product specific refund & warranty settings
                LEFT JOIN latest_refund_warranty prw ON
                    p.product_reference = prw.product_reference
                    AND prw.rn = 1
                -- Get store default refund & warranty settings
                LEFT JOIN latest_refund_warranty srw ON
                    srw.product_reference IS NULL
                    AND srw.rn = 1
                -- Get product specific delivery settings
                LEFT JOIN latest_delivery_settings pds ON
                    p.product_reference = pds.product_reference
                    AND pds.rn = 1
                -- Get store default delivery settings
                LEFT JOIN latest_delivery_settings sds ON
                    sds.product_reference IS NULL
                    AND sds.rn = 1
                -- Get Trustcenter Details
                LEFT JOIN trust_center_details stc ON
                    stc.store_reference = p.store_reference

                LEFT JOIN LATERAL (
                SELECT json_agg(json_build_object(
                    'product_image_id', pi.productimageid,
                    'product_image', pi.product_image,
                    'created_date', pi.created_date,
                    'is_deleted', pi.is_deleted,
                    'reorder', pi.reorder
                ) ORDER BY pi.reorder ASC) AS images
                FROM "product"."product_images" pi
                WHERE pi.product_reference = p.product_reference AND pi.is_deleted = false
            ) images ON true

            WHERE
                p.product_reference = ANY(%(product_refs)s)
                AND p.deleted = false
            ORDER BY p.created_date DESC

        '''

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # Bind store_ref securely
            cursor.execute(query, {"product_refs": product_refs})
            results = cursor.fetchall()

            cursor.close()
            conn.close()
            json_results = json.dumps(results, default=str)
            return json_results

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])


    def get_posts_full(self, post_refs):
        query = '''
            SELECT
                p.post_reference,
                p.post_id,
                p.quote_parent_id,
                p.user_reference,
                p.store_reference,
                p.post_text,
                p.created_date,
                p.is_deleted,
                p.like_count,
                p.comment_count,
                p.repost_count,
                p.repost_plus_count,
                p.save_count,
                p.share_count,
                p.analytics_view_count,
                COALESCE(p.tagged_references_json, '[]'::jsonb) AS tagged_references_json,

                -- Tagged type-specific counts
                COALESCE(tagged_counts.tagged_users_count, 0) AS tagged_users_count,
                COALESCE(tagged_counts.tagged_stores_count, 0) AS tagged_stores_count,
                COALESCE(tagged_counts.tagged_products_count, 0) AS tagged_products_count,

                -- Images as JSON array
                COALESCE(images.images, '[]'::json) AS post_images,

                -- Unified actor level
                COALESCE(u.user_level, s.store_level) AS level
            FROM "content"."posts" p

            -- Images join
            LEFT JOIN LATERAL (
                SELECT json_agg(json_build_object(
                    'post_image_id', pi.post_image_id,
                    'post_image', pi.post_image,
                    'created_date', pi.created_date,
                    'is_deleted', pi.is_deleted,
                    'reorder', pi.reorder
                ) ORDER BY pi.reorder ASC) AS images
                FROM "content"."post_images" pi
                WHERE pi.post_reference = p.post_reference AND pi.is_deleted = false
            ) images ON true

            -- User join
            LEFT JOIN "user"."user" u ON u.user_reference = p.user_reference

            -- Store join
            LEFT JOIN "store"."store" s ON s.store_reference = p.store_reference

            -- Tagged object type counts
            LEFT JOIN LATERAL (
                SELECT
                    COUNT(*) FILTER (WHERE t.type = 'USER') AS tagged_users_count,
                    COUNT(*) FILTER (WHERE t.type = 'STORE') AS tagged_stores_count,
                    COUNT(*) FILTER (WHERE t.type = 'PRODUCT') AS tagged_products_count
                FROM jsonb_to_recordset(COALESCE(p.tagged_references_json, '[]'::jsonb)) 
                    AS t(reference TEXT, type TEXT, "order" INTEGER)
            ) tagged_counts ON true

            -- Filters
            WHERE
                p.post_reference = ANY(%(post_refs)s)
                AND p.is_deleted = false

            ORDER BY p.created_date DESC;

        '''
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # Bind store_ref securely
            # print(cursor.mogrify(query, {"post_refs": post_refs}).decode('utf-8'))
            cursor.execute(query, {"post_refs": post_refs})
            results = cursor.fetchall()

            cursor.close()
            conn.close()

            json_results = json.dumps(results, default=str)  # Convert to JSON

            return json_results

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])


    def get_comments_full_old(self, store_reference, comment_types, limit, offset):
        
        query = '''
        SELECT
            -- Core content metadata
            c.comment_reference AS reference,
            c.comment_text,
            c.created_date,
            c.is_deleted,
            c.like_count,
            c.comment_count,
            c.repost_count,
            c.repost_plus_count,
            c.save_count,
            c.share_count,
            c.analytics_view_count,
            c.rating_count,
            c.level,
            c.comment_type,
            c.main_parent_id,
            -- New fields based on commenter_reference prefix
            CASE 
                WHEN LEFT(c.commenter_reference, 1) = 'U' THEN c.commenter_reference
                ELSE NULL
            END AS user_reference,
            CASE 
                WHEN LEFT(c.commenter_reference, 1) = 'S' THEN c.commenter_reference
                ELSE NULL
            END AS store_reference,

            -- JSON tagged references
            COALESCE(c.tagged_references_json, '[]'::jsonb) AS tagged_references_json,
            -- Count by tagged type
            COALESCE(tagged_counts.tagged_users_count, 0) AS tagged_users_count,
            COALESCE(tagged_counts.tagged_stores_count, 0) AS tagged_stores_count,
            COALESCE(tagged_counts.tagged_products_count, 0) AS tagged_products_count,
            -- Images associated with the comment
            COALESCE(images.images, '[]'::json) AS comment_images,
            -- Content header logic
            CASE 
                WHEN c.comment_type = 'REVIEW' THEN 
                    'verified review @' || COALESCE(s.storehandle, '') || '/' || COALESCE(pr.product_slug, '')
                WHEN c.comment_type = 'EXTERNAL_REVIEW' THEN 
                    'external review @' || COALESCE(s.storehandle, '') || '/' || COALESCE(pr.product_slug, '')
                ELSE NULL
            END AS content_header_text,
            -- reviewed_reference_json for reviews
            CASE 
                WHEN c.comment_type IN ('REVIEW', 'EXTERNAL_REVIEW') AND pr.product_reference IS NOT NULL THEN 
                    jsonb_build_array(jsonb_build_object(
                        'reference', pr.product_reference,
                        'type', 'PRODUCT',
                        'order', 1
                    ))
                ELSE NULL
            END AS reviewed_reference_json
        FROM "content"."comments" c
        -- Images join (comment images)
        LEFT JOIN LATERAL (
            SELECT json_agg(json_build_object(
                'comment_image_id', pi.comment_image_id,
                'comment_image', pi.comment_image,
                'created_date', pi.created_date,
                'is_deleted', pi.is_deleted,
                'reorder', pi.reorder
            ) ORDER BY pi.reorder ASC) AS images
            FROM "content"."comment_images" pi
            WHERE pi.comment_reference = c.comment_reference AND pi.is_deleted = false
        ) images ON true
        -- Tagged object type counts
        LEFT JOIN LATERAL (
            SELECT
                COUNT(*) FILTER (WHERE t.type = 'USER') AS tagged_users_count,
                COUNT(*) FILTER (WHERE t.type = 'STORE') AS tagged_stores_count,
                COUNT(*) FILTER (WHERE t.type = 'PRODUCT') AS tagged_products_count
            FROM jsonb_to_recordset(COALESCE(c.tagged_references_json, '[]'::jsonb)) 
                AS t(reference TEXT, type TEXT, "order" INTEGER)
        ) tagged_counts ON true
        -- Join to product and store to get product_slug and store_handle
        LEFT JOIN "product"."product" pr ON pr.product_reference = c.main_parent_id
        LEFT JOIN "store"."store" s ON s.store_reference = pr.store_reference
        -- Filters
        WHERE c.is_deleted = false
        AND c.comment_type = ANY(%(comment_types)s)
        AND c.level = 1
        AND (
            EXISTS (
                SELECT 1 FROM "content"."posts" p
                WHERE p.post_reference = c.main_parent_id
                AND p.store_reference = %(store_reference)s
                AND p.is_deleted = false
            )
            OR EXISTS (
                SELECT 1 FROM "product"."product" pr2
                WHERE pr2.product_reference = c.main_parent_id
                AND pr2.store_reference =  %(store_reference)s
                AND pr2.deleted = false
            )
        )
        ORDER BY c.created_date DESC
        LIMIT %(limit)s OFFSET  %(offset)s;
        '''

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            cursor.execute(query, {
                "store_reference": store_reference,
                "comment_types": comment_types,
                "limit": limit,
                "offset": offset
            })
            results = cursor.fetchall()

            cursor.close()
            conn.close()
            comment_references = [row['reference'] for row in results]
            json_results = json.dumps(results, default=str)

            return json_results, comment_references

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])

    def get_comments_full(self, store_reference, comment_types, limit, offset, review_type=None):
        query = '''
        SELECT
            -- Core content metadata
            c.comment_reference,
            c.comment_text,
            c.created_date,
            c.is_deleted,
            c.like_count,
            c.comment_count,
            c.repost_count,
            c.repost_plus_count,
            c.save_count,
            c.share_count,
            c.analytics_view_count,
            c.rating_count,
            c.level,
            c.comment_type,
            c.main_parent_id,
            c.commenter_reference,
            -- User/Store reference
            CASE 
                WHEN LEFT(c.commenter_reference, 1) = 'U' THEN c.commenter_reference
                ELSE NULL
            END AS user_reference,
            CASE 
                WHEN LEFT(c.commenter_reference, 1) = 'S' THEN c.commenter_reference
                ELSE NULL
            END AS store_reference,
            -- JSON tagged references
            COALESCE(c.tagged_references_json, '[]'::jsonb) AS tagged_references_json,
            -- Count by tagged type
            COALESCE(tagged_counts.tagged_users_count, 0) AS tagged_users_count,
            COALESCE(tagged_counts.tagged_stores_count, 0) AS tagged_stores_count,
            COALESCE(tagged_counts.tagged_products_count, 0) AS tagged_products_count,
            -- Images associated with the comment
            COALESCE(images.images, '[]'::json) AS comment_images,
            -- Content header text
            CASE 
                WHEN c.comment_type = 'REVIEW' THEN 
                    'verified review @' || COALESCE(s.storehandle, '') || '/' || COALESCE(pr.product_slug, '')
                WHEN c.comment_type = 'EXTERNAL_REVIEW' THEN 
                    'external review @' || COALESCE(s.storehandle, '') || '/' || COALESCE(pr.product_slug, '')
                ELSE NULL
            END AS content_header_text,
            -- Content headers array for product or store
            CASE 
                WHEN c.comment_type IN ('REVIEW', 'EXTERNAL_REVIEW') AND pr.product_reference IS NOT NULL THEN 
                    jsonb_build_array(jsonb_build_object(
                        'handle', '@' || COALESCE(s.storehandle, '') || '/' || COALESCE(pr.product_slug, ''),
                        'reference', pr.product_reference
                    ))
                WHEN c.comment_type IN ('REVIEW', 'EXTERNAL_REVIEW') AND c.main_parent_id = s.store_reference THEN 
                    jsonb_build_array(jsonb_build_object(
                        'handle', '@' || COALESCE(s.storehandle, ''),
                        'reference', s.store_reference
                    ))
                ELSE '[]'::jsonb
            END AS content_headers,
            CASE 
                WHEN c.comment_type IN ('REVIEW', 'EXTERNAL_REVIEW') AND pr.product_reference IS NOT NULL THEN 
                    jsonb_build_array(jsonb_build_object(
                        'type', 'PRODUCT',
                        'order', 1,
                        'reference', pr.product_reference,
                        'icon', (
                            SELECT product_image FROM "product"."product_images" 
                            WHERE product_reference = pr.product_reference AND is_deleted = false 
                            ORDER BY reorder ASC LIMIT 1
                        ),
                        'name', CONCAT(pr.product_name, '-', pr.brand_name),
                        'handle', '@' || COALESCE(s.storehandle, '')
                    ))
                WHEN c.comment_type IN ('REVIEW', 'EXTERNAL_REVIEW') AND c.main_parent_id = s.store_reference THEN 
                    jsonb_build_array(jsonb_build_object(
                        'type', 'STORE',
                        'order', 1,
                        'reference', s.store_reference,
                        'icon', s.icon,
                        'name', s.store_name,
                        'handle', '@' || COALESCE(s.storehandle, '')
                    ))
                ELSE '[]'::jsonb
            END AS reviewed_reference_json
        FROM "content"."comments" c
        -- Images join (comment images)
        LEFT JOIN LATERAL (
            SELECT json_agg(json_build_object(
                'comment_image_id', pi.comment_image_id,
                'comment_image', pi.comment_image,
                'created_date', pi.created_date,
                'is_deleted', pi.is_deleted,
                'reorder', pi.reorder
            ) ORDER BY pi.reorder ASC) AS images
            FROM "content"."comment_images" pi
            WHERE pi.comment_reference = c.comment_reference AND pi.is_deleted = false
        ) images ON true
        -- Tagged object type counts
        LEFT JOIN LATERAL (
            SELECT
                COUNT(*) FILTER (WHERE t.type = 'USER') AS tagged_users_count,
                COUNT(*) FILTER (WHERE t.type = 'STORE') AS tagged_stores_count,
                COUNT(*) FILTER (WHERE t.type = 'PRODUCT') AS tagged_products_count
            FROM jsonb_to_recordset(COALESCE(c.tagged_references_json, '[]'::jsonb)) 
                AS t(reference TEXT, type TEXT, "order" INTEGER)
        ) tagged_counts ON true
        -- Join to product and store to get product_slug and store_handle
        LEFT JOIN "product"."product" pr ON pr.product_reference = c.main_parent_id
        LEFT JOIN "store"."store" s ON s.store_reference = %(store_reference)s
        -- Filters
        WHERE c.is_deleted = false
        AND c.comment_type = ANY(%(comment_types)s)
        AND c.level = 1
        AND (
            EXISTS (
                SELECT 1 FROM "content"."posts" p
                WHERE p.post_reference = c.main_parent_id
                AND p.store_reference = %(store_reference)s
                AND p.is_deleted = false
            )
            OR EXISTS (
                SELECT 1 FROM "product"."product" pr2
                WHERE pr2.product_reference = c.main_parent_id
                AND pr2.store_reference = %(store_reference)s
                AND pr2.deleted = false
            )
            OR c.main_parent_id = %(store_reference)s
        )
        AND (
            (%(review_type)s = 'STORE' AND LEFT(c.main_parent_id, 1) = 'S' AND LEFT(c.main_parent_id, 2) NOT IN ('ST'))
            OR (%(review_type)s = 'PRODUCT' AND LEFT(c.main_parent_id, 1) = 'P' AND LEFT(c.main_parent_id, 2) NOT IN ('PO'))
            OR (%(review_type)s = '')
        )
        ORDER BY c.created_date DESC
        LIMIT %(limit)s OFFSET %(offset)s;
        '''

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            cursor.execute(query, {
                "store_reference": store_reference,
                "comment_types": comment_types,
                "limit": limit,
                "offset": offset,
                "review_type": review_type
            })
            results = cursor.fetchall()

            cursor.close()
            conn.close()
            comment_references = [row['comment_reference'] for row in results]
            json_results = json.dumps(results, default=str)

            return json_results, comment_references

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])


    def get_products_hybrid(self, product_refs, store_ref, limit, offset):
        query = '''
            WITH base_product AS (
                SELECT
                    p.productid,
                    p.product_reference,
                    p.product_name,
                    p.product_description,
                    p.product_category,
                    p.product_version,
                    TO_CHAR(p.modified_date, 'DD:MM:YYYY HH24:MI:SS') AS updated_date,
                    TO_CHAR(p.created_date, 'DD:MM:YYYY HH24:MI:SS') AS created_date,
                    p.promotion_link,
                    p.brand_name,
                    p.mrp_price,
                    p.selling_price,
                    p.count_of_ratings,
                    p.rating,
                    p.in_stock,
                    p.hashtags,
                    p.store_reference,
                    p.storeid,
                    p.targeted_gender,
                    p.swadeshi_brand,
                    p.swadeshi_made,
                    p.deleted,
                    p.like_count,
                    p.comment_count,
                    p.repost_count,
                    p.repost_plus_count,
                    p.save_count,
                    p.share_count,
                    p.orders_count,
                    p.returns_count,
                    p.cancels_count,
                    p.modified_date,
                    p.promotion_amount,
                    p.is_promotion_enabled,
                    p.analytics_view_count,
                    p.product_slug,
                    p.product_code
                FROM product.product p
                WHERE p.product_reference = ANY(%(product_refs)s::text[]) OR p.store_reference = %(store_ref)s
            ),

            store_data AS (
                SELECT
                    s.storeid,
                    s.icon AS store_icon,
                    s.store_name,
                    s.storehandle,
                    s.is_test_store,
                    s.store_reference,
                    s.subscription_type,
                    s.open_for_order,
                    s.store_valuation,
                    s.store_level,
                    s.analytics_view_count
                FROM store.store s
                WHERE NOT s.deleted
            ),

            trust_center_data AS (
                SELECT
                    t.storeid,
                    t.city,
                    t.swadeshi_owned
                FROM store.trust_center t
            )


            SELECT
                bp.*,
                sd.store_icon,
                sd.store_name,
                sd.storehandle,
                sd.is_test_store,
                sd.subscription_type,
                sd.open_for_order,
                sd.store_valuation,
                sd.store_level,
                sd.analytics_view_count AS store_analytics_view_count,
                tc.city AS location,
                tc.swadeshi_owned,
                COALESCE(pi.prod_images, '[]'::json) AS prod_images,

                -- Delivery settings fields
                delivery.delivery_settings_type,
                delivery.time_to_deliver AS delivery_by,
                delivery.delivery_partner,
                delivery.delivery_fee,
                delivery.logistic_partner_name,
                delivery.fulfillment_options,

                -- Return/Refund settings fields
                refund.return_settings_type,
                refund.return_period,
                refund.return_conditions,
                refund.return_pickup AS return_pick_up,
                refund.return_cost_on,

                jsonb_build_array(
                    jsonb_build_object(
                        'item_heading', 'Refund policy',
                        'item_text', 'Full refund based on conditions',
                        'item_subtext', ''
                    ),
                    jsonb_build_object(
                        'item_heading', 'If the seller cancels or if the order is auto-canceled',
                        'item_text', 'Full Refund',
                        'item_subtext', ''
                    ),
                    jsonb_build_object(
                        'item_heading', 'If the customer cancels before order confirmation',
                        'item_text', 'Partial Refund',
                        'item_subtext', 'Transaction fee charged by payment gateway (usually 2%% + 18%% GST if any) will be reduced from the refund amount'
                    ),
                    jsonb_build_object(
                        'item_heading', 'On product returns',
                        'item_text', 'Full Refund',
                        'item_subtext', ''
                    )
                ) AS refund_responsibility

            FROM base_product bp
            LEFT JOIN store_data sd ON bp.storeid = sd.storeid
            LEFT JOIN trust_center_data tc ON bp.storeid = tc.storeid

            -- Delivery settings prioritizing product-level first
            LEFT JOIN LATERAL (
                SELECT
                    CASE
                        WHEN ds.product_reference IS NOT NULL THEN 'product_settings'
                        ELSE 'store_settings'
                    END AS delivery_settings_type,
                    ds.time_to_deliver,
                    CASE
                        WHEN ds.deliverymethod_logistics THEN 'logistics'
                        WHEN ds.deliverymethod_swadesic THEN 'swadesic'
                        WHEN ds.deliverymethod_self THEN 'self'
                        ELSE NULL
                    END AS delivery_partner,
                    CASE
                        WHEN ds.deliveryfeetype_all_free THEN 'free'
                        WHEN ds.deliveryfeetype_standard THEN CONCAT('₹', ds.deliveryfee_value, ' per ',
                            CASE WHEN ds.product_reference IS NOT NULL THEN 'product' ELSE 'store' END, ' order')
                        WHEN ds.deliveryfeetype_distance THEN 'distance_based'
                        ELSE NULL
                    END AS delivery_fee,
                    ds.default_logistic_partner AS logistic_partner_name,
                    ds.fulfillment_options
                FROM store.delivery_settings ds
                WHERE ds.is_deleted = false AND (
                    ds.product_reference = bp.product_reference
                    OR (ds.product_reference IS NULL AND ds.store_reference = bp.store_reference)
                )
                ORDER BY
                    CASE WHEN ds.product_reference IS NOT NULL THEN 0 ELSE 1 END -- product-specific first
                LIMIT 1
            ) AS delivery ON TRUE

            LEFT JOIN LATERAL (
                SELECT
                    CASE
                        WHEN rw.product_reference IS NOT NULL THEN 'product_settings'
                        ELSE 'store_settings'
                    END AS return_settings_type,
                    rw.return_period,
                    string_to_array(rw.return_conditions, '|') AS return_conditions,
                    rw.return_pickup,
                    CASE
                        WHEN rw.return_cost_on_customer THEN 'CUSTOMER'
                        WHEN rw.return_cost_on_seller THEN 'SELLER'
                        ELSE NULL
                    END AS return_cost_on
                FROM store.refund_and_warranty rw
                WHERE rw.is_deleted = false AND (
                    rw.product_reference = bp.product_reference
                    OR (rw.product_reference IS NULL AND rw.store_reference = bp.store_reference)
                )
                ORDER BY
                    CASE WHEN rw.product_reference IS NOT NULL THEN 0 ELSE 1 END -- prioritize product-level
                LIMIT 1
            ) AS refund ON TRUE

            LEFT JOIN LATERAL (
                SELECT json_agg(json_build_object(
                    'productimageid', pi.productimageid,
                    'product_image', pi.product_image,
                    'created_date', pi.created_date,
                    'is_deleted', pi.is_deleted,
                    'reorder', pi.reorder
                ) ORDER BY pi.reorder ASC) AS prod_images
                FROM product.product_images pi
                WHERE pi.productid = bp.productid AND pi.is_deleted = false
                ) pi ON TRUE

            ORDER BY bp.modified_date DESC
            LIMIT %(limit)s OFFSET %(offset)s;

        '''
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # Bind store_ref securely
            # print(cursor.mogrify(query, {"store_ref": store_ref, "product_refs": product_refs, "limit": limit, "offset": offset}).decode('utf-8'))
            cursor.execute(query, {"store_ref": store_ref, "product_refs": product_refs, "limit": limit, "offset":offset})
            results = cursor.fetchall()

            cursor.close()
            conn.close()

            json_results = json.dumps(results, default=str)  # Convert to JSON

            return json_results

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])


    def get_products_hybrid_without_pagination(self, product_refs, store_ref):
        query = '''
            WITH base_product AS (
                SELECT
                    p.productid,
                    p.product_reference,
                    p.product_name,
                    p.product_description,
                    p.product_category,
                    p.product_version,
                    TO_CHAR(p.modified_date, 'DD:MM:YYYY HH24:MI:SS') AS updated_date,
                    TO_CHAR(p.created_date, 'DD:MM:YYYY HH24:MI:SS') AS created_date,
                    p.promotion_link,
                    p.brand_name,
                    p.mrp_price,
                    p.selling_price,
                    p.count_of_ratings,
                    p.rating,
                    p.in_stock,
                    p.hashtags,
                    p.store_reference,
                    p.storeid,
                    p.targeted_gender,
                    p.swadeshi_brand,
                    p.swadeshi_made,
                    p.deleted,
                    p.like_count,
                    p.comment_count,
                    p.repost_count,
                    p.repost_plus_count,
                    p.save_count,
                    p.share_count,
                    p.orders_count,
                    p.returns_count,
                    p.cancels_count,
                    p.modified_date,
                    p.promotion_amount,
                    p.is_promotion_enabled,
                    p.analytics_view_count,
                    p.product_slug,
                    p.product_code
                FROM product.product p
                WHERE p.product_reference = ANY(%(product_refs)s::text[]) OR p.store_reference = %(store_ref)s
            ),

            store_data AS (
                SELECT
                    s.storeid,
                    s.icon AS store_icon,
                    s.store_name,
                    s.storehandle,
                    s.is_test_store,
                    s.store_reference,
                    s.subscription_type,
                    s.open_for_order,
                    s.store_valuation,
                    s.store_level,
                    s.analytics_view_count
                FROM store.store s
                WHERE NOT s.deleted
            ),

            trust_center_data AS (
                SELECT
                    t.storeid,
                    t.city,
                    t.swadeshi_owned
                FROM store.trust_center t
            )


            SELECT
                bp.*,
                sd.store_icon,
                sd.store_name,
                sd.storehandle,
                sd.is_test_store,
                sd.subscription_type,
                sd.open_for_order,
                sd.store_valuation,
                sd.store_level,
                sd.analytics_view_count AS store_analytics_view_count,
                tc.city AS location,
                tc.swadeshi_owned,
                COALESCE(pi.prod_images, '[]'::json) AS prod_images,

                -- Delivery settings fields
                delivery.delivery_settings_type,
                delivery.time_to_deliver AS delivery_by,
                delivery.delivery_partner,
                delivery.delivery_fee,
                delivery.logistic_partner_name,
                delivery.fulfillment_options,

                -- Return/Refund settings fields
                refund.return_settings_type,
                refund.return_period,
                refund.return_conditions,
                refund.return_pickup AS return_pick_up,
                refund.return_cost_on,

                jsonb_build_array(
                    jsonb_build_object(
                        'item_heading', 'Refund policy',
                        'item_text', 'Full refund based on conditions',
                        'item_subtext', ''
                    ),
                    jsonb_build_object(
                        'item_heading', 'If the seller cancels or if the order is auto-canceled',
                        'item_text', 'Full Refund',
                        'item_subtext', ''
                    ),
                    jsonb_build_object(
                        'item_heading', 'If the customer cancels before order confirmation',
                        'item_text', 'Partial Refund',
                        'item_subtext', 'Transaction fee charged by payment gateway (usually 2%% + 18%% GST if any) will be reduced from the refund amount'
                    ),
                    jsonb_build_object(
                        'item_heading', 'On product returns',
                        'item_text', 'Full Refund',
                        'item_subtext', ''
                    )
                ) AS refund_responsibility

            FROM base_product bp
            LEFT JOIN store_data sd ON bp.storeid = sd.storeid
            LEFT JOIN trust_center_data tc ON bp.storeid = tc.storeid

            -- Delivery settings prioritizing product-level first
            LEFT JOIN LATERAL (
                SELECT
                    CASE
                        WHEN ds.product_reference IS NOT NULL THEN 'product_settings'
                        ELSE 'store_settings'
                    END AS delivery_settings_type,
                    ds.time_to_deliver,
                    CASE
                        WHEN ds.deliverymethod_logistics THEN 'logistics'
                        WHEN ds.deliverymethod_swadesic THEN 'swadesic'
                        WHEN ds.deliverymethod_self THEN 'self'
                        ELSE NULL
                    END AS delivery_partner,
                    CASE
                        WHEN ds.deliveryfeetype_all_free THEN 'free'
                        WHEN ds.deliveryfeetype_standard THEN CONCAT('₹', ds.deliveryfee_value, ' per ',
                            CASE WHEN ds.product_reference IS NOT NULL THEN 'product' ELSE 'store' END, ' order')
                        WHEN ds.deliveryfeetype_distance THEN 'distance_based'
                        ELSE NULL
                    END AS delivery_fee,
                    ds.default_logistic_partner AS logistic_partner_name,
                    ds.fulfillment_options
                FROM store.delivery_settings ds
                WHERE ds.is_deleted = false AND (
                    ds.product_reference = bp.product_reference
                    OR (ds.product_reference IS NULL AND ds.store_reference = bp.store_reference)
                )
                ORDER BY
                    CASE WHEN ds.product_reference IS NOT NULL THEN 0 ELSE 1 END -- product-specific first
                LIMIT 1
            ) AS delivery ON TRUE

            LEFT JOIN LATERAL (
                SELECT
                    CASE
                        WHEN rw.product_reference IS NOT NULL THEN 'product_settings'
                        ELSE 'store_settings'
                    END AS return_settings_type,
                    rw.return_period,
                    string_to_array(rw.return_conditions, '|') AS return_conditions,
                    rw.return_pickup,
                    CASE
                        WHEN rw.return_cost_on_customer THEN 'CUSTOMER'
                        WHEN rw.return_cost_on_seller THEN 'SELLER'
                        ELSE NULL
                    END AS return_cost_on
                FROM store.refund_and_warranty rw
                WHERE rw.is_deleted = false AND (
                    rw.product_reference = bp.product_reference
                    OR (rw.product_reference IS NULL AND rw.store_reference = bp.store_reference)
                )
                ORDER BY
                    CASE WHEN rw.product_reference IS NOT NULL THEN 0 ELSE 1 END -- prioritize product-level
                LIMIT 1
            ) AS refund ON TRUE

            LEFT JOIN LATERAL (
                SELECT json_agg(json_build_object(
                    'productimageid', pi.productimageid,
                    'product_image', pi.product_image,
                    'created_date', pi.created_date,
                    'is_deleted', pi.is_deleted,
                    'reorder', pi.reorder
                ) ORDER BY pi.reorder ASC) AS prod_images
                FROM product.product_images pi
                WHERE pi.productid = bp.productid AND pi.is_deleted = false
                ) pi ON TRUE

            ORDER BY bp.modified_date DESC;

        '''
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # Bind store_ref securely
            # print(cursor.mogrify(query, {"store_ref": store_ref, "product_refs": product_refs, "limit": limit, "offset": offset}).decode('utf-8'))
            cursor.execute(query, {"store_ref": store_ref, "product_refs": product_refs})
            results = cursor.fetchall()

            cursor.close()
            conn.close()

            json_results = json.dumps(results, default=str)  # Convert to JSON

            return json_results

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])
        
    def get_product_suggestions(self, store_ref, limit, offset):

        query = '''
            WITH base_product AS (
                SELECT
                    'PRODUCT' AS type,
                    p.product_slug AS primary_text,
                    p.brand_name || ' ' || p.product_name AS secondary_text,
                    p.product_reference AS reference,
                    p.productid,
                    p.storeid,
                    p.modified_date
                FROM product.product p
                WHERE p.store_reference = %(store_ref)s
            ),
            store_data AS (
                SELECT
                    s.storeid,
                    s.store_name,
                    s.storehandle
                FROM store.store s
                WHERE NOT s.deleted
            )

            SELECT
                bp.type,
                bp.reference,
                bp.primary_text,
                bp.secondary_text,
                sd.store_name,
                sd.storehandle AS store_handle,
                pi.product_image AS image_url

            FROM base_product bp
            LEFT JOIN store_data sd ON bp.storeid = sd.storeid

            LEFT JOIN LATERAL (
                SELECT pi.product_image
                FROM product.product_images pi
                WHERE pi.productid = bp.productid AND pi.is_deleted = false
                ORDER BY pi.reorder ASC, pi.created_date ASC
                LIMIT 1
            ) pi ON TRUE

            ORDER BY bp.modified_date DESC
            LIMIT %(limit)s OFFSET %(offset)s;

        '''
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # Bind store_ref securely
            # print(cursor.mogrify(query, {"store_ref": store_ref, "product_refs": product_refs, "limit": limit, "offset": offset}).decode('utf-8'))
            cursor.execute(query, {"store_ref": store_ref, "limit": limit, "offset":offset})
            results = cursor.fetchall()

            cursor.close()
            conn.close()

            json_results = json.dumps(results, default=str)  # Convert to JSON

            return json_results

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])
    
    def get_post_tagged_object_details(self, post_reference):
        query='''
            SELECT
            p.post_reference,
            jsonb_build_array(
                jsonb_build_object('products', COALESCE(tagged_objects.products_details, '[]'::jsonb)),
                jsonb_build_object('stores', COALESCE(tagged_objects.stores_details, '[]'::jsonb)),
                jsonb_build_object('users', COALESCE(tagged_objects.users_details, '[]'::jsonb))
            ) AS tagged_objects_details
            FROM "content"."posts" p
            -- Lateral join for tagged object details
            LEFT JOIN LATERAL (
            SELECT
                jsonb_agg(
                    jsonb_build_object(
                        'reference', t_ref.reference,
                        'type', t_ref.type,
                        'order', t_ref.order,
                        'handle', product_store.storehandle,
                        'icon', product_image.product_image,
                        'name', CONCAT(product_data.product_name, ' - ', product_data.brand_name)
                    ) ORDER BY t_ref.order ASC
                ) FILTER (WHERE t_ref.type = 'PRODUCT') AS products_details,
                jsonb_agg(
                    jsonb_build_object(
                        'reference', t_ref.reference,
                        'type', t_ref.type,
                        'order', t_ref.order,
                        'handle', store_data.storehandle,
                        'icon', store_data.icon,
                        'name', store_data.store_name
                    ) ORDER BY t_ref.order ASC
                ) FILTER (WHERE t_ref.type = 'STORE') AS stores_details,
                jsonb_agg(
                    jsonb_build_object(
                        'reference', t_ref.reference,
                        'type', t_ref.type,
                        'order', t_ref.order,
                        'handle', user_data.user_name,
                        'icon', user_data.icon,
                        'name', user_data.first_name
                    ) ORDER BY t_ref.order ASC
                ) FILTER (WHERE t_ref.type = 'USER') AS users_details
            FROM jsonb_to_recordset(p.tagged_references_json) AS t_ref(reference TEXT, type TEXT, "order" INTEGER)
            LEFT JOIN "user"."user" user_data ON t_ref.type = 'USER' AND user_data.user_reference = t_ref.reference
            LEFT JOIN "store"."store" store_data ON t_ref.type = 'STORE' AND store_data.store_reference = t_ref.reference
            LEFT JOIN "product"."product" product_data ON t_ref.type = 'PRODUCT' AND product_data.product_reference = t_ref.reference
            LEFT JOIN "store"."store" product_store ON t_ref.type = 'PRODUCT' AND product_store.store_reference = product_data.store_reference
            LEFT JOIN LATERAL (
                SELECT pi.product_image
                FROM "product"."product_images" pi
                WHERE pi.product_reference = product_data.product_reference AND pi.is_deleted = false
                ORDER BY pi.reorder ASC
                LIMIT 1
            ) product_image ON t_ref.type = 'PRODUCT'
            WHERE p.tagged_references_json IS NOT NULL AND jsonb_array_length(p.tagged_references_json) > 0
            ) tagged_objects ON true
            WHERE p.post_reference = %(post_reference)s
            AND p.is_deleted = false;
        '''
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # Bind store_ref securely
            # print(cursor.mogrify(query, {"post_reference": post_reference}).decode('utf-8'))
            cursor.execute(query, {"post_reference": post_reference})
            results = cursor.fetchall()

            cursor.close()
            conn.close()

            json_results = json.dumps(results, default=str)  # Convert to JSON

            return json_results

        except Exception as e:
            print(f"Error executing query: {e}")
            return json.dumps([])


#------------------- POsts query with tagged details------------------
# SELECT
#     p.post_reference,
#     p.post_id,
#     p.quote_parent_id,
#     p.user_reference,
#     p.store_reference,
#     p.post_text,
#     p.created_date,
#     p.is_deleted,
#     p.like_count,
#     p.comment_count,
#     p.repost_count,
#     p.repost_plus_count,
#     p.save_count,
#     p.share_count,
#     p.analytics_view_count,
#     COALESCE(p.tagged_references_json, '[]'::jsonb) AS tagged_references_json,
#     COALESCE(jsonb_array_length(p.tagged_references_json), 0) AS tagged_objects_count,
#     -- Images as JSON array
#     COALESCE(images.images, '[]'::json) AS post_images,
#     -- Unified actor level from either user or store
#     COALESCE(u.user_level, s.store_level) AS level
# FROM "content"."posts" p
# -- Join for images
# LEFT JOIN LATERAL (
#     SELECT json_agg(json_build_object(
#         'post_image_id', pi.post_image_id,
#         'post_image', pi.post_image,
#         'created_date', pi.created_date,
#         'is_deleted', pi.is_deleted,
#         'reorder', pi.reorder
#     ) ORDER BY pi.reorder ASC) AS images
#     FROM "content"."post_images" pi
#     WHERE pi.post_reference = p.post_reference AND pi.is_deleted = false
# ) images ON true
# -- Join with user if applicable
# LEFT JOIN "user"."user" u ON u.user_reference = p.user_reference
# -- Join with store if applicable
# LEFT JOIN "store"."store" s ON s.store_reference = p.store_reference
# -- WHERE clause
# WHERE
#     p.post_reference = ANY(%(post_refs)s)
#     AND p.is_deleted = false
# ORDER BY p.created_date DESC;
