# Generated by Django 3.2.13 on 2022-09-15 04:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0011_auto_20220914_1424"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="address",
            name="storeid",
        ),
        migrations.RemoveField(
            model_name="deliverysettings",
            name="storeid",
        ),
        migrations.AddField(
            model_name="address",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                default="S3785476",
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.store",
                to_field="store_reference",
            ),
        ),
        migrations.AddField(
            model_name="deliverysettings",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                default="S3785476",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="deliverysettings",
                to="stores.store",
                to_field="store_reference",
            ),
        ),
        migrations.AlterField(
            model_name="documents",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.store",
                to_field="store_reference",
            ),
        ),
        migrations.AlterField(
            model_name="trustcenter",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="trustcenter",
                to="stores.store",
                to_field="store_reference",
            ),
        ),
    ]
