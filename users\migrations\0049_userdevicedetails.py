# Generated by Django 4.2.7 on 2023-11-24 18:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0048_auto_20231103_2115'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserDeviceDetails',
            fields=[
                ('user_device_details_id', models.AutoField(primary_key=True, serialize=False)),
                ('device_id', models.CharField(blank=True, max_length=30, null=True)),
                ('fcm_token', models.CharField(blank=True, max_length=300, null=True)),
                ('user_reference', models.ForeignKey(blank=True, db_column='user_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='device_details', to='users.user', to_field='user_reference')),
            ],
            options={
                'verbose_name': 'User Device Detail',
                'verbose_name_plural': 'User Device Details',
                'db_table': '"user"."user_device_details"',
            },
        ),
    ]
