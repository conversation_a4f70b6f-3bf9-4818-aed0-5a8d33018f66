from decouple import config
import datetime
import random
import string
import pytz
import requests
from datetime import datetime
from requests.auth import HTTPBasic<PERSON>uth
from django.db import transaction
from celery.utils.log import get_task_logger
from orders.order_api.models import Order, SubOrder, RefundedAmount
from orders.payment_api.models import SubOrderPaymentDetails
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from stores.store_settings_api.models import RefundAndWarranty

logger = get_task_logger(__name__)

M_ID = config("TEST_MERCHANT_ID")
M_KEY = config("TEST_MERCHANT_KEY")
SKIP_PAYTM_API = config("SKIP_PAYTM_API", cast=bool)

SKIP_RAZORPAY_API = config("SKIP_RAZORPAY_API", cast=bool)
RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")

AUTO_CANCEL_PERIOD_MINUTES = 2


class RefundCalculations():

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def calculate_refund_amount(self):
        # TODO: when a Positive delivery order is present store df should be sent to seller when all other are cancelled

        # self.order_payout_instance #= self.order_status_self.suborder.order_payout_items.first() # OrderPayout.objects.filter(suborder_number=self.order_status_self.suborder).first()
        delivery_group = self._get_delivery_status_group()
        self.refund_cost_on = self._get_refund_cost_on()

        refund_amount = self.order_payout_instance.order_amount
        delivery_deduction = self._calculate_delivery_deduction(delivery_group)
        transaction_fee_deduction_perc = self._calculate_transaction_fee_deduction()
        transaction_fee_and_tds_deduction_perc = transaction_fee_deduction_perc * (
                    1 + 0.01)  # tds is 1% of transaction fee
        refund_amount = (refund_amount - delivery_deduction) * (100 - transaction_fee_and_tds_deduction_perc) / 100
        return round(refund_amount, 2)

    def _calculate_delivery_deduction(self, delivery_group):
        if delivery_group == "AFTER_SHIPPING":
            return self.order_payout_instance.product_delivery_fee + self.order_payout_instance.store_delivery_fee
        return 0

    def _calculate_transaction_fee_deduction(self):
        if self.refund_cost_on in ['USER', 'STORE_TO_USER']:
            return self.order_payout_instance.transaction_fee_percentage
        return 0

    def _get_refund_cost_on(self) -> str:
        if self.store_df_record:
            return "USER" if self._get_delivery_status_group() == "BEFORE_SHIPPING" else 'STORE'
        else:
            refund_warranty = RefundAndWarranty.objects.filter(
                refundandwarrantyid=self.order_payout_instance.suborder_number.refund_warranty_id
            ).first()

            # Check if cancelled_by value is present, otherwise return "STORE"
            if not self.order_status_self.cancelled_by:
                return "STORE"

            # If customer cancels, they always bear the cost regardless of other conditions
            if self.order_status_self.cancelled_by == "buyer":
                return "USER"

            if self.order_status_self.new_return_cost_on:
                return self.order_status_self.new_return_cost_on
            if refund_warranty.return_cost_on_seller:
                return "STORE"
            elif refund_warranty.return_cost_on_customer:
                return "STORE" if self.order_status_self.cancelled_by in ["seller", "auto_cancelled"] else "USER"
            return "USER"

    def _get_delivery_status_group(self):
        before_shipping = [
            "ORDER_INITIATED",
            "PAYMENT_INITIATED",
            "PAYMENT_PENDING",
            "PAYMENT_SUCCESS",
            "PAYMENT_FAILED",
            "WAITING_FOR_CONFIRMATION",
            "ORDER_CONFIRMED",
            "ORDER_CANCELLED",
            "ORDER_CANCELLED_BY_BUYER",
            "ORDER_CANCELLED_BY_SELLER",
            "ORDER_AUTO_CANCELLED"
        ]

        after_shipping = [
            "DELIVERY_IN_PROGRESS",
            "ORDER_DELIVERED",
            "DELIVERY_FAILED",
            "RETURN_REQUESTED",
            "RETURN_CONFIRMED",
            "RETURN_IN_PROGRESS",
            "RETURNED_TO_SELLER",
            "RETURN_FAILED",
            "REFUND_HOLD",
            "REFUNDED"
        ]

        if self.order_status_self.current_status in before_shipping:
            return "BEFORE_SHIPPING"
        elif self.order_status_self.current_status in after_shipping:
            return "AFTER_SHIPPING"
        else:
            return "UNKNOWN"

        # Usage example:
        # Assuming 'self.current_status' contains the current status
        # group = self.get_status_group(self.current_status)



def initiate_refund(suborder_number_list):

    def create_refund_id(suborder_number):
        random_four_length_character = "".join(random.choices(string.ascii_uppercase, k=4))
        id_from_suborder = suborder_number.replace("O", "RE")
        final_code = (id_from_suborder, random_four_length_character)
        refund_id = "".join(final_code)
        if SubOrderPaymentDetails.objects.filter(refund_id=refund_id).exists():
            refund_id = create_refund_id(suborder_number)
            return refund_id
        return refund_id

    def get_date():
        date = datetime.now()
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%Y-%m-%d %H:%M:%S")

    def process_refund(suborder_number, razorpay_payment_id):
        order_number = suborder_number.split("-")[0]
        order = Order.new_objects.get(order_number=order_number)

        refund_instance = RefundedAmount.objects.get(
            suborder_reference=suborder_number,
            refund_status=RefundedAmount.Status.UNPROCESSED,
        )
        refund_amount = refund_instance.refunded_amount
        refund_id = create_refund_id(suborder_number)

        if SKIP_RAZORPAY_API:
            response = get_mock_response(order, refund_amount, refund_id)
        else:
            response = call_razorpay_api(razorpay_payment_id, refund_amount, refund_id, order)

        return handle_refund_response(response, suborder_number, order_number, refund_instance)

    def get_mock_response(order, refund_amount, refund_id):
        return {
            "body": {
                "txnTimestamp": get_date(),
                "orderId": order.order_request_number,
                "mid": RAZORPAY_ID,
                "refId": "123456789",
                "resultInfo": {
                    "resultStatus": "PENDING",
                    "resultCode": "601",
                    "resultMsg": "Refund request was raised for this transaction. But it is pending state",
                },
                "refundId": refund_id,
                "txnId": "arn_12345678",
                "refundAmount": str(refund_amount),
                "razorpayRefundId": "dummy_rfnd_qwert123",
                "acquirerData": {"utr": "123456789"}
            },
        }

    def get_refund_txn_id_from_acquirer_data(acquirer_data):
        if acquirer_data.get("rrn"):
            return acquirer_data["rrn"]
        elif acquirer_data.get("arn"):
            return acquirer_data["arn"]
        elif acquirer_data.get("utr"):
            return acquirer_data["utr"]
        else:
            return ""

    def call_razorpay_api(razorpay_payment_id, refund_amount, refund_id, order):
        url = f'https://api.razorpay.com/v1/payments/{razorpay_payment_id}/refund'
        payload = {
            'amount': refund_amount * 100,
            'receipt': refund_id,
            'notes': {
                'order_request_number': order.order_request_number,
                'order_number': order.order_number,
                'transaction_id': order.transaction_id,
            }
        }
        # logger.info(f"razorpay_url:{url}")
        # logger.info(f"razorpay_payload:{payload}")
        # logger.info(f"razorpay_auth:{HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY)}")

        response = requests.post(
            url,
            json=payload,
            auth=HTTPBasicAuth(RAZORPAY_ID, RAZORPAY_KEY),
            headers={'Content-Type': 'application/json'}
        )
        logger.info(f"razorpay_response:{response.content}")
        api_response = response.json()

        if api_response.get("error") is not None:
            logger.info(f"Razorpay API error: {api_response['error']['description']}")
            return None

        return {
            "body": {
                "txnTimestamp": api_response["created_at"],
                "orderId": api_response["notes"]["order_request_number"],
                "mid": RAZORPAY_ID,
                "refId": "123456789",
                "resultInfo": {
                    "resultStatus": api_response["status"],
                    "resultCode": "601",
                    "resultMsg": api_response["status"]
                },
                "refundId": api_response["receipt"],
                "txnId": get_refund_txn_id_from_acquirer_data(acquirer_data=api_response["acquirer_data"]),
                "refundAmount": str(api_response["amount"] / 100),
                "razorpayRefundId": api_response["id"],
                "acquirerData": api_response["acquirer_data"]
            },
        }

    def handle_refund_response(response, suborder_number, order_number, refund_instance):
        if response is not None:
            body = response["body"]
            initiate_response_status = body["resultInfo"]["resultStatus"]
            initiate_response_msg = body["resultInfo"]["resultMsg"]
            razorpay_refund_id = body["razorpayRefundId"]
            refund_transaction_id = body["txnId"]
            refund_id = body["refundId"]
            refund_amount = float(body["refundAmount"])
            refunded_date_unix = body["txnTimestamp"]
            refunded_date = datetime.datetime.fromtimestamp(refunded_date_unix)

            order_payment_details = SubOrderPaymentDetails.objects.get(
                suborder_number=SubOrder.objects.get(suborder_number=suborder_number)
            )

            status_mapping = {
                "processed": (RefundedAmount.Status.REFUNDED, SubOrderPaymentDetails.Payment_Status.REFUND_SUCCESS),
                "pending": (RefundedAmount.Status.PENDING, SubOrderPaymentDetails.Payment_Status.REFUND_PENDING),
                "failed": (RefundedAmount.Status.REFUND_FAILED, SubOrderPaymentDetails.Payment_Status.REFUND_FAILED),
            }

            refund_status, payment_status = status_mapping.get(
                initiate_response_status.lower(),
                (RefundedAmount.Status.REFUND_FAILED, SubOrderPaymentDetails.Payment_Status.REFUND_FAILED)
            )

            # Update RefundedAmount instance
            refund_instance.refund_status = refund_status
            refund_instance.refund_id = refund_id
            refund_instance.initiate_refund_status = initiate_response_status
            refund_instance.initiate_refund_message = initiate_response_msg
            refund_instance.refund_transaction_id = refund_transaction_id
            refund_instance.razorpay_refund_id = razorpay_refund_id
            refund_instance.refunded_date = refunded_date
            refund_instance.save(update_fields=[
                "refund_status", "refund_id", "initiate_refund_status",
                "initiate_refund_message", "refund_transaction_id", "razorpay_refund_id", "refunded_date"
            ])

            # Update OrderPaymentDetails instance
            order_payment_details.payment_status = payment_status
            order_payment_details.refund_id = refund_id
            order_payment_details.refund_amount = refund_amount
            order_payment_details.save(update_fields=["payment_status", "refund_id", "refund_amount"])

            if initiate_response_status.lower() == "processed":
                create_refund_notification(order_number, suborder_number)

            return {
                "suborder_number": suborder_number,
                "status": initiate_response_status.lower(),
                "message": initiate_response_msg
            }
        else:
            return {
                "message": "error in razorpay api"
            }

    def create_refund_notification(order_number, suborder_number):
        order = Order.objects.get(order_number=order_number)
        suborder = SubOrder.objects.get(suborder_number=suborder_number)

        notification_handler = NotificationHandler(
            notified_user=order.user_reference.user_reference,
            notification_type=Notifications.Notifications_Type.REFUND_INITIATED,
            notification_about=order_number,
            image=suborder.product_image,
        )
        notification_handler.create_notification(notification_handler)

    for suborder_number in suborder_number_list:
        results = []
        order_reference = suborder_number_list[0].split("-")[0]
        razorpay_payment_id = \
        Order.objects.filter(order_number=order_reference).values_list('razorpay_payment_id', flat=True)[0]

        try:
            with transaction.atomic():
                result = process_refund(suborder_number, razorpay_payment_id)
            results.append(result)
            logger.info({"refund_results": results})
            return "success"

        except Exception as e:
            logger.error(f"Error processing refund for suborder {suborder_number}: {str(e)}")
            results.append({"suborder_number": suborder_number, "status": "error", "message": str(e)})
            logger.info({"refund_results": results})
            return "error"
