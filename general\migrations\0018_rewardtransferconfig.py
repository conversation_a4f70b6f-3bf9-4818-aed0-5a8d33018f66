# Generated by Django 4.2.7 on 2024-07-13 11:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("general", "0017_rename_view_location_banner_banner_location_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="RewardTransferConfig",
            fields=[
                ("config_id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "transfer_type",
                    models.CharField(
                        choices=[
                            ("ONBOARDING_REFERRAL", "Onboarding Referral"),
                            ("U2U_TRANSFER", "U2U Transfer"),
                            ("S2U_TRANSFER", "S2U Transfer"),
                        ],
                        max_length=20,
                    ),
                ),
                ("sender_part", models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=10, null=True)),
                (
                    "receiver_part",
                    models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=10, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "Reward transfer configs",
                "db_table": '"public"."reward_transfer_config"',
            },
        ),
    ]
