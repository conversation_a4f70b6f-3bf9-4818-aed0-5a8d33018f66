# Generated by Django 4.2.7 on 2024-08-28 15:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "orders",
            "0132_rename_refund_requested_date_refundedamount_refund_initiated_datetime_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="orderpayout",
            name="payout_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("WAITING_FOR_CONFIRMATION", "Waiting for Confirmation"),
                    ("IN_PROCESS", "In process"),
                    ("WAITING_FOR_RELEASE", "Waiting for release"),
                    ("AMOUNT_RELEASED", "Amount released"),
                    ("NOTHING_TO_BE_ADDED", "Nothing to be added"),
                    ("AMOUNT_RELEASED_CORRECTED", "Amount released & corrected"),
                    ("PAYOUT_HOLD", "Payout hold"),
                ],
                max_length=50,
                null=True,
            ),
        ),
    ]
