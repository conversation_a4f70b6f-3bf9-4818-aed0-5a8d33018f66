from django.urls import path
from .views import (
    UpdateStoreVerificationStatus,
    StoresAwaitingVerification,
    GetPendingWithdrawalRequests,
    CompleteWithdrawalRequest,
    CreateSubscriptionPlanView
)

urlpatterns = [
    path(
        "update_store_verification/<str:store_handle>/",
        UpdateStoreVerificationStatus.as_view(),
        name="update-store-verification",
    ),
    path(
        "get_store_verification_requests/",
        StoresAwaitingVerification.as_view(),
        name="get-store-verification-requests",
    ),
    path(
        "get_pending_withdrawal_requests/",
        GetPendingWithdrawalRequests.as_view(),
        name="get-pending-withdrawal-requests",
    ),
    path(
        "complete_withdrawal_request/",
        CompleteWithdrawalRequest.as_view(),
        name="complete-withdrawal-request",
    ),
    path('subscription-plans/create/', CreateSubscriptionPlanView.as_view(), name='create-subscription-plan')
]

