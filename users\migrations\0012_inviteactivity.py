# Generated by Django 3.2.13 on 2022-09-20 06:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0011_alter_user_invite_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="InviteActivity",
            fields=[
                (
                    "invite_activity_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("invite_code", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "invite_status",
                    models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=200, null=True),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "invite_id",
                    models.ForeignKey(
                        db_column="invite_id",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.inviteuser",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "invite activities",
                "db_table": '"user"."invite_activity"',
            },
        ),
    ]
