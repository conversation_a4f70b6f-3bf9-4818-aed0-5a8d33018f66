from django.urls import path
from .views import (
    FaqDataAPIView,
    FaqImageUploadAPIView,
    FaqCategoryReorderAPIView,
    FaqCategorySwitchAPIView,
    FaqItemReorderAPIView,
    FaqItemSwitchAPIView
)

app_name = 'faq'

urlpatterns = [
    # Get all FAQ data
    path('data/', FaqDataAPIView.as_view(), name='faq-data'),

    # Upload/Delete FAQ item images
    path('images/', FaqImageUploadAPIView.as_view(), name='faq-image-upload'),

    # Category management
    path('categories/reorder/', FaqCategoryReorderAPIView.as_view(), name='faq-category-reorder'),
    path('categories/switch/', FaqCategorySwitchAPIView.as_view(), name='faq-category-switch'),

    # Item management
    path('items/reorder/', FaqItemReorderAPIView.as_view(), name='faq-item-reorder'),
    path('items/switch/', FaqItemSwitchAPIView.as_view(), name='faq-item-switch'),
]
