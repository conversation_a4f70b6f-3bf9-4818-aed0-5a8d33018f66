# Generated by Django 4.2.7 on 2024-08-26 16:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0130_orderpayout_order_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderpayout",
            name="remarks",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderpayout",
            name="payout_status",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("WAITING_FOR_CONFIRMATION", "Waiting for Confirmation"),
                    ("IN_PROCESS", "In process"),
                    ("WAITING_FOR_RELEASE", "Waiting for release"),
                    ("AMOUNT_RELEASED", "Amount released"),
                    ("NOTHING_TO_BE_ADDED", "Nothing to be added"),
                    ("AMOUNT_RELEASED_CORRECTED", "Amount released & corrected"),
                ],
                max_length=50,
                null=True,
            ),
        ),
    ]
