# Generated by Django 3.2.13 on 2022-11-11 17:57

from django.db import migrations


def enter_product_reference_on_suborder(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in product_reference column, where
    # productid of SubOrder model is same as productid in Product table.

    SubOrder = apps.get_model("orders", "SubOrder")
    Product = apps.get_model("products", "Product")
    for item in SubOrder.objects.all():
        if Product.objects.filter(productid=item.productid).exists():
            product_instance = Product.objects.get(productid=item.productid)
            item.product_reference = product_instance.product_reference
        else:
            item.product_reference = None
        item.save(update_fields=["product_reference"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    SubOrder = apps.get_model("orders", "SubOrder")
    for item in SubOrder.objects.all():
        item.product_reference = None
        item.save(update_fields=["product_reference"])


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0031_auto_20221111_2246"),
    ]

    operations = [
        migrations.RunPython(enter_product_reference_on_suborder, reverse_func)
    ]
