from neomodel import Q, db
import datetime
import random
import string
import uuid
from django.conf import settings
from django.core.files.storage import default_storage
from orders.order_api.models import SubOrder
import logging
from datetime import datetime
from .models import Neo4jPost, Neo4jProduct, Neo4jComment, Neo4jStory, Neo4jStore
from django.db.models import Q as DjangoQ
from content.models import Posts, Comments, Story
from products.models import Product

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def delete_media_file(media_url_list):
    for media_url in media_url_list:
        try:
            file_path = media_url.url.replace("/media", "", 1)
            absolute_file_path = settings.MEDIA_ROOT + file_path
            default_storage.delete(absolute_file_path)
            logger.info(f"Successfully deleted media file")
        except Exception as e:
            logger.error(f"Error while deleting image file: {e}")

def get_content_creator(content_reference):
    try:
        # Check if the content is a post
        post = Neo4jPost.nodes.get(reference=content_reference)
        # If it's a post, return the entity that posted it
        return post.get_post_creator()
    except Neo4jPost.DoesNotExist:
        pass

    try:
        # Check if the content is a product
        product = Neo4jProduct.nodes.get(reference=content_reference)
        # If it's a product, return the entity that listed it
        return product.listed_by.single()
    except Neo4jProduct.DoesNotExist:
        pass

    try:
        # Check if the content is a comment
        comment = Neo4jComment.nodes.get(reference=content_reference)
        # If it's a comment, return the entity that commented it
        return comment.commented_by.single()
    except Neo4jComment.DoesNotExist:
        pass

    try:
        # Check if the content is a comment
        story = Neo4jStory.nodes.get(reference=content_reference)
        # If it's a comment, return the entity that commented it
        return story.posted_by.single()
    except Neo4jStory.DoesNotExist:
        pass

    try:
        # Check if the content is a store
        store = Neo4jStore.nodes.get(reference=content_reference)
        # If it's a store, return the entity that posted it
        return store
    except Neo4jStore.DoesNotExist:
        pass

    # If the content reference does not belong to any known type, return None
    return None


def check_review_access(entity_reference, product_reference):
    lst = [
        SubOrder.Suborder_Status.ORDER_DELIVERED,
        SubOrder.Suborder_Status.RETURN_REQUESTED,
        SubOrder.Suborder_Status.RETURN_CONFIRMED,
        SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
        SubOrder.Suborder_Status.RETURNED_TO_SELLER,
        SubOrder.Suborder_Status.REFUNDED,
        SubOrder.Suborder_Status.REFUND_HOLD,
    ]
    # checking if this particular user has purchased this product or not, so that he can give review access.
    suborder_numbers = SubOrder.objects.filter(DjangoQ(user_reference=entity_reference)
                            & DjangoQ(product_reference=product_reference)
                            & DjangoQ(suborder_status__in=lst)).order_by('-created_date').values_list("suborder_number", flat=True)

    suborder_numbers_without_review = [suborder_number for suborder_number in suborder_numbers if not check_if_review_exists(product_reference, entity_reference, event_reference=suborder_number)]
    review_access = len(suborder_numbers_without_review) > 0

    return review_access, suborder_numbers_without_review[-1] if len(suborder_numbers_without_review) > 0 else None

def check_store_review_access(entity_reference, store_reference):
    lst = [
        SubOrder.Suborder_Status.ORDER_DELIVERED,
        SubOrder.Suborder_Status.RETURN_REQUESTED,
        SubOrder.Suborder_Status.RETURN_CONFIRMED,
        SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
        SubOrder.Suborder_Status.RETURNED_TO_SELLER,
        SubOrder.Suborder_Status.REFUNDED,
        SubOrder.Suborder_Status.REFUND_HOLD,
    ]
    # checking if this particular user has purchased this product or not, so that he can give review access.
    order_numbers = SubOrder.objects.filter(DjangoQ(user_reference=entity_reference)
                            & DjangoQ(store_reference=store_reference)
                            & DjangoQ(suborder_status__in=lst)).order_by('-created_date').values_list("order_number", flat=True)

    order_count = len(set(order_numbers))
    user_store_review_count = Comments.objects.filter(DjangoQ(commenter_reference=entity_reference)
                            & DjangoQ(store_reference=store_reference)
                            & DjangoQ(comment_type=Comments.CommentTypes.REVIEW)).count()
    review_access = order_count > user_store_review_count

    return review_access


def check_if_review_exists(product_reference, user_reference, event_reference):
    return Comments.objects.filter(
        main_parent_id=product_reference,
        commenter_reference=user_reference,
        event_reference=event_reference,
        comment_type=Comments.CommentTypes.REVIEW,
        is_deleted=False
    ).exists()


def check_question_access(entity_reference, product_reference):
    return True


def check_comment_access(entity_reference, product_reference):
    return True


def get_content_instance(content_reference):
    if content_reference.startswith('PO'):
        content_instance = Posts.objects.get(post_reference=content_reference, is_deleted=False)
    elif content_reference.startswith('P'):
        content_instance = Product.objects.get(product_reference=content_reference, deleted=False)
    elif content_reference.startswith('CO'):
        content_instance = Comments.objects.get(comment_reference=content_reference, is_deleted=False)
    elif content_reference.startswith('CO'):
        content_instance = Story.objects.get(story_reference=content_reference)
    else:
        content_instance = None

    return content_instance


def get_content_type(content_reference):
    if content_reference.startswith('C'):
        content_type = 'comment'
    elif content_reference.startswith('PO'):
        content_type = 'post'
    else:
        content_type = 'product'
    return content_type

# todo
# def get_unregistered_user_reference():
    # while True:
    #     current_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
    #     random_number = "".join(random.choices(string.digits, k=4))
    #     reference = f"UR{current_time_str}{random_number}"
    #     if not Neo4jUnrUser.nodes.filter(unregistered_user_reference=reference).all():
    #         break

    # return str(uuid.uuid4())


#
# def find_or_create_users(phonenumbers):
#     # Find existing users
#     existing_users = Neo4jUser.nodes.filter(phonenumber__in=phonenumbers, is_deleted=False)
#
#     # Find or create unregistered users
#     existing_phonenumbers = {user.phonenumber for user in existing_users}
#     unregistered_phonenumbers = set(phonenumbers) - existing_phonenumbers
#
#     # Use batch operations to create unregistered users
#     unregistered_users = [Neo4jUnrUser(
#         phonenumber=number,
#         is_deleted=False,
#         unregistered_user_reference=get_unregistered_user_reference()
#     ).save() for number in unregistered_phonenumbers]
#
#     return list(existing_users), unregistered_users
# def make_list(x):
#     if x:
#         x_list = list(x)
#     else:
#         x_list = []
#     return x_list


# def find_or_create_users(phonenumbers):
#     # Find existing users
#     logger.info(f"Fetching existing users started @ {datetime.now()}")

#     existing_users = Neo4jUser.nodes.filter(phonenumber__in=phonenumbers, is_deleted=False).values_list('reference', flat = True)


#     cypher_query = (
#         """MATCH (u:Neo4jUnrUser) 
#         WHERE u.phonenumber IN $phonenumbers AND u.is_deleted = False 
#         RETURN u.reference"""
#     )

#     # Execute the Cypher query
#     result, _ = db.cypher_query(cypher_query, {"phonenumbers": phonenumbers})

#     # Extract the nodes from the result
#     existing_unregistered_users = [Neo4jUnrUser.inflate(row[0]) for row in result]

#     logger.info(f"Fetching existing users ended @ {datetime.now()}")

#     logger.info(f"Fetching existing phone numbers started @ {datetime.now()}")

#     # Create a set of existing phonenumbers for both registered and unregistered users
#     existing_phonenumbers = {user.phonenumber for user in existing_users}
#     existing_unregistered_phonenumbers = {user.phonenumber for user in existing_unregistered_users}
#     all_existing_phonenumbers = existing_phonenumbers.union(existing_unregistered_phonenumbers)

#     # Find or create unregistered users for phonenumbers not linked to any User
#     unregistered_phonenumbers = list(set(phonenumbers).difference(all_existing_phonenumbers))

#     logger.info(f"Fetching existing phone numbers ended @ {datetime.now()}")

#     logger.info(f"creating new unregistered nodes started @ {datetime.now()}")

#     query = """
#     UNWIND $phonenumbers AS phonenumber
#     CREATE(u: Neo4jUnrUser {phonenumber: phonenumber, is_deleted: FALSE})
#     WITH u
#     SET u.reference = 'UR' + toString(id(u) % 1000000000)
#     RETURN u.reference
#     """

#     results, _ = db.cypher_query(query, {'phonenumbers': unregistered_phonenumbers})
#     unregistered_users = [Neo4jUnrUser.inflate(row[0]) for row in results]

#     logger.info(f"creating new unregistered nodes ended @ {datetime.now()}")

#     # Return both existing users and existing unregistered users
#     return make_list(existing_users), make_list(existing_unregistered_users), unregistered_users


# def get_stores_from_users(phonenumbers):
#     # user_references = [user.user_reference for user in users]
#     query = """
#       MATCH (u:Neo4jUser)-[:CREATED]->(s:Neo4jStore)
#       WHERE u.phonenumber IN $phonenumbers AND NOT s.is_deleted
#       RETURN s
#       """
#     results, _ = db.cypher_query(query, {'phonenumbers': phonenumbers})
#     return [Neo4jStore.inflate(row[0]) for row in results]


# todo using is_entity_user should be used in a better way

#
# def connect_entities_to_asking(asking_entity, final_list, is_entity_user):
#     if is_entity_user:
#         reference = "user_reference"
#     else:
#         reference = "store_reference"
#     query = """
#        WITH $final_list AS entities
#        UNWIND entities AS entity
#        CREATE (n:Neo4jEntity{$reference: $asking_entity})-[:HAS_CONTACT]->(entity)
#        """
#     db.cypher_query(query, {'reference': reference, 'asking_entity': asking_entity, 'final_list': final_list})
# def connect_entities_to_asking(asking_entity, user_store_list ,unregistered_users_list):
#
#     cypher_query = (
#         """MATCH (asking:Neo4jEntity), (contact:Neo4jEntity)
#         WHERE asking.reference = $asking_entity_reference AND contact.reference IN $user_store_list_references
#         CREATE (asking)-[:HAS_CONTACT]->(contact)"""
#     )
#
#     parameters = {'user_store_list_references': [node.reference for node in user_store_list],
#                   'asking_entity_reference': asking_entity.reference}
#     db.cypher_query(cypher_query, parameters)
#
#     cypher_query = (
#         """MATCH (asking:Neo4jEntity), (contact:Neo4jUnrUser)
#         WHERE asking.reference = $asking_entity_reference AND contact.reference IN $unregistered_list_references
#         CREATE (asking)-[:HAS_CONTACT]->(contact)"""
#     )
#
#     parameters = {'unregistered_list_references': [node.reference for node in unregistered_users_list],
#                   'asking_entity_reference': asking_entity.reference}
#     db.cypher_query(cypher_query, parameters)

def connect_entities_to_asking(asking_entity, user_store_list, unregistered_users_list):
    cypher_query = (
        """
        MATCH (asking:Neo4jEntity {reference: $asking_entity_reference})
        MATCH (contact:Neo4jEntity)
        WHERE contact.reference IN $user_store_list_references
        MERGE (asking)-[:HAS_CONTACT]->(contact)
        
        UNION
        
        MATCH (asking:Neo4jEntity {reference: $asking_entity_reference})
        MATCH (contact:Neo4jUnrUser)
        WHERE contact.reference IN $unregistered_list_references
        MERGE (asking)-[:HAS_CONTACT]->(contact)
        """
    )

    parameters = {
        'user_store_list_references': [node.reference for node in user_store_list],
        'unregistered_list_references': [node.reference for node in unregistered_users_list],
        'asking_entity_reference': asking_entity.reference
    }

    db.cypher_query(cypher_query, parameters)




