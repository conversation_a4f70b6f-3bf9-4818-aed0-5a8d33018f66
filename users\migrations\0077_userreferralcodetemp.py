# Generated by Django 4.2.7 on 2024-08-31 06:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0076_delete_storepoints"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserReferralCodeTemp",
            fields=[
                (
                    "user_referral_code_temp_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "user_device_id",
                    models.CharField(blank=True, max_length=30, null=True),
                ),
                (
                    "referral_code",
                    models.Char<PERSON>ield(blank=True, max_length=50, null=True),
                ),
            ],
            options={
                "verbose_name": "User Referral Code Temp",
                "verbose_name_plural": "User Referral Codes Temp",
                "db_table": '"user"."user_referral_code_temp"',
            },
        ),
    ]
