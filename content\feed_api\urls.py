from django.urls import path
from .views import FeedPostMarkAsSeenAPIView, GetFeedPostsAV

urlpatterns = [
    # path('feedposts/', FeedPostListCreateAPIView.as_view(), name='feedpost-list-create'),
    # path('feedposts/<int:pk>/',FeedPostRetrieveUpdateDestroyAPIView.as_view(),name='feedpost-retrieve-update-destroy'),
    path('feedposts/<int:pk>/mark_as_seen/', FeedPostMarkAsSeenAPIView.as_view(), name='feedpost-mark-as-seen'),
    path('get_feed_posts/', GetFeedPostsAV.as_view(), name='get-feed-posts')
]

