# Generated by Django 3.2.13 on 2023-08-04 10:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0105_auto_20230706_1604'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderOtp',
            fields=[
                ('order_otp_id', models.AutoField(primary_key=True, serialize=False)),
                ('user_reference', models.CharField(max_length=20)),
                ('package_number', models.CharField(max_length=20)),
                ('otp', models.CharField(max_length=8)),
                ('generated_time', models.DateTimeField(auto_now_add=True)),
                ('otp_status', models.CharField(choices=[('USED', 'Used'), ('EXPIRED', 'Expired'), ('UNUSED', 'Unused')], default='UNUSED', max_length=20)),
            ],
            options={
                'verbose_name_plural': 'order otp',
                'db_table': '"order"."order_otp"',
            },
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='refundedamount',
            name='refund_status',
            field=models.Char<PERSON>ield(blank=True, choices=[('UNPROCESSED', 'unprocessed'), ('CANCELLED', 'cancelled'), ('PROCESSED', 'processed')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='suborder',
            name='suborder_status',
            field=models.CharField(choices=[('ORDER_INITIATED', 'Order initiated'), ('PAYMENT_INITIATED', 'Payment initiated'), ('PAYMENT_SUCCESS', 'Payment success'), ('WAITING_FOR_CONFIRMATION', 'waiting for confirmation'), ('PAYMENT_PENDING', 'Payment pending'), ('PAYMENT_FAILED', 'Payment failed'), ('ORDER_CONFIRMED', 'Order confirmed'), ('DELIVERY_IN_PROGRESS', 'Delivery in progress'), ('ORDER_DELIVERED', 'Order delivered'), ('ORDER_CANCELLED', 'Order cancelled'), ('ORDER_CANCELLED_BY_BUYER', 'Order cancelled by buyer'), ('ORDER_CANCELLED_BY_SELLER', 'Order cancelled by seller'), ('DELIVERY_FAILED', 'Delivery failed'), ('RETURN_REQUESTED', 'Return requested'), ('RETURN_CONFIRMED', 'Return confirmed'), ('RETURN_IN_PROGRESS', 'Return in progress'), ('RETURN_HOLD', 'Return hold'), ('RETURNED_TO_SELLER', 'Return to seller'), ('RETURN_FAILED', 'Return failed'), ('ORDER_AUTO_CANCELLED', 'Order auto cancelled')], default='ORDER_INITIATED', max_length=50),
        ),
    ]
