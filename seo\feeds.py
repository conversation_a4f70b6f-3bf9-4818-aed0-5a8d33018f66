from django.contrib.syndication.views import Feed
from django.urls import reverse
from content.models import Posts, Story
from django.utils.feedgenerator import Atom1Feed

class LatestPostsFeed(Feed):
    title = "Swadesic Latest Posts"
    link = "/posts/"
    description = "Latest posts from Swadesic stores."
    feed_type = Atom1Feed

    def items(self):
        return Posts.objects.filter(is_deleted=False).order_by('-created_date')[:20]

    def item_title(self, item):
        return f"Post by {item.store_reference.store_name}"

    def item_description(self, item):
        return item.post_text[:200] if item.post_text else ""

    def item_link(self, item):
        return reverse('seo:post_detail', args=[item.post_reference])

    def item_pubdate(self, item):
        return item.created_date

class LatestStoriesFeed(Feed):
    title = "Swadesic Latest Stories"
    link = "/stories/"
    description = "Latest stories from Swadesic stores."
    feed_type = Atom1Feed

    def items(self):
        return Story.objects.filter(is_deleted=False).order_by('-created_at')[:20]

    def item_title(self, item):
        return item.title

    def item_description(self, item):
        return item.sections[0].get('content', '') if item.sections else ""

    def item_link(self, item):
        return reverse('seo:story_detail', args=[item.story_reference])

    def item_pubdate(self, item):
        return item.created_at
