import uuid

from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db import models
from products.models import Product
import datetime
from django.utils.translation import gettext_lazy as _
from django.db.models import <PERSON><PERSON><PERSON>ield
import logging
import time

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Create your models here.


class Posts(models.Model):
    post_id = models.AutoField(primary_key=True)
    quote_parent_id = models.CharField(max_length=25, blank=True, null=True)
    post_reference = models.CharField(max_length=20, unique=True)
    # A unique post_reference must be created for every entry so that
    # First two characters are 'PO' identifier for post, next 12 chars are DateTimestamp from YYYY to SS and
    # last 4 chars are random 4 digits
#TODO remove the fk constraints and add created_by as a Charfield
    user_reference = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        to_field="user_reference",
        null=True,
        blank=True,
        related_name="posted_by_user",
        db_column="user_reference",
    )  # Given as input while calling POST method
    store_reference = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        to_field="store_reference",
        null=True,
        blank=True,
        related_name="posted_by_store",
        db_column="store_reference",
    )     # Given by input while calling POST method

    post_text = models.TextField(blank=True, null=True)  # given as input while calling POST method
    created_date = models.DateTimeField()
    tagged_references_json = JSONField(default=list)
    is_deleted = models.BooleanField(default=False)

    like_count = models.IntegerField(default=0)
    comment_count = models.IntegerField(default=0)
    repost_count = models.IntegerField(default=0)
    repost_plus_count = models.IntegerField(default=0)
    save_count = models.IntegerField(default=0)
    share_count = models.IntegerField(default=0)

    #analytics fields
    analytics_view_count = models.IntegerField(default=0)

    # def get_post_reference(self):
    #     while True:
    #         current_time = datetime.datetime.now()
    #         current_time_str = current_time.strftime("%Y%m%d%H%M%S")
    #         random_number = "".join(random.choices(string.digits, k=4))
    #         final_code = ("PO", current_time_str, random_number)
    #         post_reference = "".join(final_code)
    #         if not Posts.objects.filter(post_reference=post_reference).exists():
    #             break
    #     return post_reference
    #
    # def save(self, *args, **kwargs):
    #     if not self.post_reference:
    #         self.post_reference = self.get_post_reference()
    #     super(Posts, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "posts"
        db_table = '"content"."posts"'


class PostImages(models.Model):
    post_image_id = models.TextField(primary_key=True)
    post_reference = models.ForeignKey(
        "content.Posts",
        on_delete=models.CASCADE,
        to_field="post_reference",
        null=True,
        blank=True,
        related_name="post_images",
        db_column="post_reference",
    )
    post_image = models.ImageField(upload_to='social_post_images', blank=True, null=True)
    created_date = models.DateTimeField(auto_now_add=True)
    is_deleted = models.BooleanField(default=False)
    reorder = models.IntegerField(default=0)

    # def save(self, *args, **kwargs):
    #     if self.post_image_id is None:
    #         count_of_product_image = PostImages.objects.filter(
    #             post_reference=self.post_reference
    #         ).count()
    #         logger.info("Count of existing product_image %s", count_of_product_image)
    #         self.reorder = count_of_product_image + 1
    #     new_image = compress(self.post_image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
    #     self.post_image = new_image
    #     super(PostImages, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "post images"
        db_table = '"content"."post_images"'


# todo remove the below model after checking any foreign dependencies and usages
class PostLikes(models.Model):
    post_likes_id = models.AutoField(primary_key=True)
    post_reference = models.ForeignKey(
        "content.Posts",
        on_delete=models.CASCADE,
        to_field="post_reference",
        null=True,
        blank=True,
        related_name="post_likes",
        db_column="post_reference",
    )
    liked_by_user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        to_field="user_reference",
        null=True,
        blank=True,
        related_name="liked_by_user",
        db_column="liked_by_user",
    )
    liked_by_store = models.ForeignKey(
        "stores.Store",
        on_delete=models.CASCADE,
        to_field="store_reference",
        null=True,
        blank=True,
        related_name="liked_by_store",
        db_column="liked_by_store",
    )
    is_liked = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "post likes"
        db_table = '"content"."post_likes"'


class Comments(models.Model):
    class CommentTypes(models.TextChoices):
        COMMENT = "COMMENT", _("Comment")
        QUESTION = "QUESTION", _("Question")
        REVIEW = "REVIEW", _("Review")
        EXTERNAL_REVIEW = "EXTERNAL_REVIEW", _("External Review")

    comment_id = models.AutoField(primary_key=True)
    comment_reference = models.CharField(max_length=20, unique=True)
    commenter_reference = models.CharField(max_length=25, blank=True, null=True)
    comment_text = models.TextField(blank=True, null=True)
    created_date = models.DateTimeField(default=datetime.datetime(2024,1,1,0,0,0))
    is_deleted = models.BooleanField(default=False)
    comment_type = models.CharField(max_length=20, choices=CommentTypes.choices, null=True, blank=True)
    rating_count = models.FloatField(null=True, blank=True)
    event_reference = models.CharField(max_length=25, blank=True, null=True)  #event for which the rating is given
    main_parent_id = models.CharField(max_length=25, blank=True, null=True) #post or product
    parent_comment_id = models.CharField(max_length=25, blank=True, null=True) # comments
    level = models.IntegerField(default=0)

    like_count = models.IntegerField(default=0)
    comment_count = models.IntegerField(default=0)
    repost_count = models.IntegerField(default=0)
    repost_plus_count = models.IntegerField(default=0)
    save_count = models.IntegerField(default=0)
    share_count = models.IntegerField(default=0)

    #analytics fields
    analytics_view_count = models.IntegerField(default=0)
    tagged_references_json = JSONField(default=list)


    class Meta:
        verbose_name_plural = "comments"
        db_table = '"content"."comments"'


class CommentImages(models.Model):
    comment_image_id = models.CharField(max_length=200, primary_key=True)
    comment_reference = models.ForeignKey(
        "content.Comments",
        on_delete=models.CASCADE,
        to_field="comment_reference",
        null=True,
        blank=True,
        related_name="comment_images",
        db_column="comment_reference",
    )
    comment_image = models.ImageField(upload_to='comment_images', blank=True, null=True)
    created_date = models.DateTimeField(auto_now_add=True)
    is_deleted = models.BooleanField(default=False)
    reorder = models.IntegerField(default=0)

    class Meta:
        verbose_name_plural = "comment images"
        db_table = '"content"."comment_images"'


class Story(models.Model):
    story_id = models.AutoField(primary_key=True)
    story_reference = models.CharField(max_length=20, unique=True)
    title = models.CharField(max_length=255)
    sections = JSONField()  # Store sections as a JSON object
    tagged_content = ArrayField(models.CharField(max_length=255), default=list)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    like_count = models.IntegerField(default=0)
    comment_count = models.IntegerField(default=0)
    repost_count = models.IntegerField(default=0)
    repost_plus_count = models.IntegerField(default=0)
    save_count = models.IntegerField(default=0)
    share_count = models.IntegerField(default=0)

    class Meta:
        verbose_name_plural = "stories"
        db_table = '"content"."stories"'
    def save(self, *args, **kwargs):
        if not self.story_reference:
            self.story_reference = self.get_story_reference()
        super(Story, self).save(*args, **kwargs)

    def get_story_reference(self):
        story_reference = f'ST{int(time.time() * 1000)}'
        if Story.objects.filter(story_reference=story_reference).exists():
            story_reference = self.get_story_reference()
            return story_reference
        return story_reference


class ReviewRequest(models.Model):
    product_reference = models.CharField(max_length=25, blank=True, null=True)
    store_reference = models.CharField(max_length=20, blank=True, null=True)
    token = models.UUIDField(default=uuid.uuid4, unique=True)
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    user_identifier = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        verbose_name_plural = "review requests"
        db_table = '"content"."review_request"'