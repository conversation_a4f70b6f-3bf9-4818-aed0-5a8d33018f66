# views.py
import logging
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from .serializers import StoreRewardsSerializer, StoreRewardsHistorySerializer, StoreRewardsMonthlyHistorySerializer
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from ..store_api.models import Store
from decouple import config
from django.db import transaction
from .utils import *
from stores.store_reward_api.models import StoreRewards, StoreRewardsHistory, StoreRewardsMonthlyHistory
from general.views import get_app_config


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def get_config():
    return get_app_config()

@api_view(['GET'])
def get_store_rewards(request):
    store_reference = request.query_params.get('store_reference')
    if not store_reference:
        return Response({"error": "store_reference is required"}, status=status.HTTP_400_BAD_REQUEST)
    try:
        store_instance = Store.objects.get(store_reference=store_reference, deleted=False)
    except Store.DoesNotExist:
        return Response({"error": "Store with given reference does not exist"}, status=status.HTTP_400_BAD_REQUEST)

    store_rewards, _ = StoreRewards.objects.get_or_create(store_reference=store_instance)
    serializer = StoreRewardsSerializer(store_rewards)
    data = serializer.data
    
    # Use get_infinity_rewards_breakup to get the breakup
    infinity_rewards_breakup = StoreRewards.get_infinity_rewards_breakup(store_reference)
    data.update(infinity_rewards_breakup)

    return Response({"data": data}, status=200)


@api_view(['GET'])
def get_store_rewards_history(request):
    store_reference = request.query_params.get('store_reference')
    limit = int(request.query_params.get("limit", 10))  # Default limit to 10 if not provided
    offset = int(request.query_params.get("offset", 0))  # Default offset to 0 if not provided

    if not store_reference:
        return Response({"error": "store_reference is required"}, status=status.HTTP_400_BAD_REQUEST)

    history = StoreRewardsHistory.objects.filter(store_reference=store_reference).order_by('-created_date')[offset:offset + limit]
    serializer = StoreRewardsHistorySerializer(history, many=True)
    return Response({"data": serializer.data}, status=200)


@api_view(['GET'])
def get_store_monthly_flash_points(request):
    store_reference = request.query_params.get('store_reference')
    if not store_reference:
        return Response({"error": "store_reference is required"}, status=status.HTTP_400_BAD_REQUEST)

    monthly_history = StoreRewardsMonthlyHistory.objects.filter(store_reference=store_reference)
    serializer = StoreRewardsMonthlyHistorySerializer(monthly_history, many=True)
    return Response({"data": serializer.data}, status=200)


class SingleStoreFlashRewardAPI(APIView):
    def post(self, request):
        
        store_reference = request.data.get("store_reference")
        if not store_reference:
            return Response({"message": "store_reference is required"}, status=status.HTTP_400_BAD_REQUEST)

        flash_promotion_period = get_config().flash_promotion_period

        current_date = timezone.now()
        promotion_period_benchmark = current_date - relativedelta(months=flash_promotion_period)

        # Fetch the store that is within the promotional period
        try:
            store = Store.objects.get(store_reference=store_reference,
                                      activated_date__gte=promotion_period_benchmark)
        except Store.DoesNotExist:
            return Response({"message": "Store not found or not within promotional period"},
                            status=status.HTTP_404_NOT_FOUND)

        # get previous, current and next month years
        current_month_year = current_date.strftime("%B-%Y")
        next_month_time = current_date + relativedelta(months=1)
        next_month_year = next_month_time.strftime("%B-%Y")

        try:
            with transaction.atomic():
                # Reset the store flash balance to 0
                store_rewards_instance, _ = StoreRewards.objects.get_or_create(store_reference=store)
                store_rewards_instance.flash_points = 0
                store_rewards_instance.save()

                # Add monthly credit to flash balance to the store
                monthly_flash_point_grant = get_config().monthly_flash_point_credit
                store_rewards_instance.flash_points += monthly_flash_point_grant
                store_rewards_instance.save()

                # Create history entry
                StoreRewardsHistory.objects.create(
                    reward_category='FLASH',
                    reward_value=monthly_flash_point_grant,
                    reward_type='MONTHLY_CREDIT',
                    transaction_type='CREDIT',
                    store_reference=store.store_reference,
                    reward_status='SUCCESS'
                )

                # Check if a monthly entry for the current month is already created or not
                # if it exists increment the monthly credit else create the monthly reward entry
                monthly_reward_instance = StoreRewardsMonthlyHistory.objects.filter(
                    store_reference=store.store_reference,
                    month_with_year=current_month_year
                ).first()

                if monthly_reward_instance:
                    store_rewards_instance.flash_points += monthly_reward_instance.reward_granted
                    monthly_reward_instance.reward_granted += monthly_flash_point_grant
                    store_rewards_instance.save()
                    monthly_reward_instance.save()
                else:
                    StoreRewardsMonthlyHistory.objects.create(
                        month_with_year=current_month_year,
                        reward_granted=monthly_flash_point_grant,
                        reward_used=0,
                        store_reference=store.store_reference
                    )

                # Simultaneously create an entry for the next month as well if it does not exist
                if not StoreRewardsMonthlyHistory.objects.filter(store_reference=store.store_reference,
                                                                 month_with_year=next_month_year).exists():
                    StoreRewardsMonthlyHistory.objects.create(
                        month_with_year=next_month_year,
                        reward_granted=0,
                        reward_used=0,
                        store_reference=store.store_reference
                    )

            return Response({"message": "success"}, status=status.HTTP_200_OK)

        except Exception as e:
            # Optionally log the exception or handle it as needed
            return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # #################################### scheduler task api #########################


class MonthlyFlashPointJob(APIView):
    def post(self, request):
        
        flash_promotion_period = get_config().flash_promotion_period
        current_date = timezone.now()
        promotion_period_benchmark = current_date - relativedelta(months=flash_promotion_period)

        # Fetch all stores that are within promotional period
        stores = Store.objects.filter(activated_date__gte=promotion_period_benchmark)

        # get previous, current and next month years
        current_month_year = current_date.strftime("%B-%Y")
        previous_month_time = current_date - relativedelta(months=1)
        previous_month_year = previous_month_time.strftime("%B-%Y")
        next_month_time = current_date + relativedelta(months=1)
        next_month_year = next_month_time.strftime("%B-%Y")

        try:
            with transaction.atomic():
                for store in stores:
                    # Reset the store flash balance to 0
                    store_rewards_instance, _ = StoreRewards.objects.get_or_create(store_reference=store)
                    store_rewards_instance.flash_points = 0
                    store_rewards_instance.save()

                    # Add monthly credit to flash balance to the store
                    monthly_flash_point_grant = get_config().monthly_flash_point_credit
                    store_rewards_instance.flash_points += monthly_flash_point_grant
                    store_rewards_instance.save()

                    # Create history entry
                    StoreRewardsHistory.objects.create(
                        reward_category='FLASH',
                        reward_value='500',
                        reward_type='MONTHLY_CREDIT',
                        transaction_type='CREDIT',
                        store_reference=store.store_reference,
                        reward_status='SUCCESS'
                    )

                    # Check if a monthly entry for the current month is already created or not
                    # if it exists increment the monthly credit else create the monthly reward entry
                    monthly_reward_instance = StoreRewardsMonthlyHistory.objects.filter(
                        store_reference=store.store_reference,
                        month_with_year=current_month_year
                    ).first()

                    if monthly_reward_instance:
                        store_rewards_instance.flash_points += monthly_reward_instance.reward_granted
                        monthly_reward_instance.reward_granted += monthly_flash_point_grant
                        store_rewards_instance.save()
                        monthly_reward_instance.save()
                    else:
                        StoreRewardsMonthlyHistory.objects.create(
                            month_with_year=current_month_year,
                            reward_granted=monthly_flash_point_grant,
                            reward_used=0,
                            store_reference=store.store_reference
                        )

                    # Simultaneously create an entry for the next month as well if it does not exist
                    if not StoreRewardsMonthlyHistory.objects.filter(store_reference=store.store_reference, month_with_year=next_month_year).exists():
                        StoreRewardsMonthlyHistory.objects.create(
                            month_with_year=next_month_year,
                            reward_granted=0,
                            reward_used=0,
                            store_reference=store.store_reference
                        )

            return Response({"message": "success"}, status=200)

        except Exception as e:
            # Optionally log the exception or handle it as needed
            return Response({"message": str(e)}, status=500)


# def deduct_flash_points(store_reference, points_to_deduct, event_reference):
#     try:
#         store_rewards_instance = StoreRewards.objects.get(store_reference=store_reference)
#         if store_rewards_instance.flash_points >= points_to_deduct:
#             store_rewards_instance.flash_points -= points_to_deduct
#             logger.info(f"Deducted {points_to_deduct} flash points from store {store_reference}")
#             store_rewards_instance.save()

#             # Create a history entry using StoreRewardsHistory.add_store_rewards_history
#             StoreRewardsHistory.add_store_rewards_history(
#                 store_reference=store_reference,
#                 reward_category='FLASH',
#                 reward_value=points_to_deduct,
#                 reward_type='DEDUCTION',
#                 transaction_type='DEBIT',
#                 event_reference=event_reference,
#                 reward_status='SUCCESS'
#             )
#             return True
#         else:
#             logging.info(f"Deduction failed for {store_reference}")
#             return False
#     except StoreRewards.DoesNotExist:
#         logging.info(f"Store Rewards entry is missing for store {store_reference}")
#         return False


# # create a method to check flash points of a store w.r.t points to deduct, if the points are sufficient then give true else false
# def check_flash_points(store_reference, points_to_deduct):
#     try:
#         store_rewards_instance = StoreRewards.objects.get(store_reference=store_reference)
#         if store_rewards_instance.flash_points >= points_to_deduct:
#             logger.info(f"Flash points available for store {store_reference}")
#             return True
#         else:
#             logging.info(f"Insufficient flash points for store {store_reference}")
#             return False
#     except StoreRewards.DoesNotExist:
#         logging.info(f"Store Rewards entry is missing for store {store_reference}")
#         return False

