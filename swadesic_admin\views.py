from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework import generics
from stores.store_api.models import Store
from stores.store_api.views import is_any_other_store_verified
from stores.store_reward_api.models import StoreRewardsHistory,StoreRewards
from users.invite_api.models import RewardsHistory, UserRewards
from stores.store_settings_api.models import TrustCenter
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from django.utils import timezone
from stores.store_api.serializers import (
    StoreVerificationSerializer
)
import logging
from stores.store_api.models import StoreConfig
from general.views import get_app_config
from orders.payout_api.models import PayoutTransactions, PayoutBalance
from orders.payout_api.serializers import PayoutWithdrawalSerializer
from django.db import transaction
from datetime import datetime
from .serializers import *
from decouple import config
import requests
from requests.auth import HTTPBasicAuth
import time
from rest_framework.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
from django.shortcuts import get_object_or_404
from users.user_api.models import User


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def get_config():
    return get_app_config()

# Create your views here.


class UpdateStoreVerificationStatus(generics.UpdateAPIView):

    def patch(self, request, *args, **kwargs):
        store_handle = kwargs.get('store_handle')
        verification_identity = request.data.get('verification_identity')
        value_string = request.data.get('value')
        value = True if value_string == 'TRUE' else False
        rejection_notes = request.data.get('rejection_notes')

        instance = Store.objects.get(storehandle=store_handle, deleted=False)
        trustcenter_instance = TrustCenter.objects.get(store_reference=instance.store_reference)

        self.process_rewards_and_notifications(instance)
        self.update_verification(instance=instance, verification_identity=verification_identity, value=value)

        if rejection_notes and not value:
            trustcenter_instance.trust_center_note = rejection_notes
        else:
            trustcenter_instance.trust_center_note = None
        trustcenter_instance.save()

        instance.save()
        serializer = StoreVerificationSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def process_rewards_and_notifications(self, instance):

        is_first_time = not is_any_other_store_verified(instance)
        is_within_promotion = self.is_within_promotion_period(instance)
        is_referred_store = bool(instance.created_by.invited_by_code)

        if is_first_time and is_within_promotion:
            reward = get_config().free_promotional_flash_point_reward
            notification_type = Notifications.Notifications_Type.STORE_VERIFICATION_PROMOTION_REWARD
        else:
            reward = get_config().normal_store_verification_flash_point_reward
            notification_type = Notifications.Notifications_Type.NORMAL_STORE_VERIFICATION_REWARD

        if reward:
            self.add_flash_reward(instance, reward, notification_type)

        if is_referred_store and is_first_time:
            self.process_referral_incentives(instance)
            self.send_notification(instance, Notifications.Notifications_Type.STORE_VERIFICATION_ONBOARDING_REWARD)

    def is_within_promotion_period(self, instance):

        is_promotion_active = get_config().is_promotion_active
        promotion_start_time = get_config().promotion_start_time
        promotion_end_time = get_config().promotion_end_time

        if is_promotion_active and instance.verification_requested_time:
            store_created_time = instance.created_date
            return (promotion_start_time <= store_created_time <= promotion_end_time and
                    promotion_start_time <= instance.verification_requested_time <= promotion_end_time)
        return False

    def add_flash_reward(self, instance, reward_points, notification_type):
        store_reward_instance, created = StoreRewards.objects.get_or_create(
            store_reference=instance,
            defaults={'flash_points': 0}
        )
        store_reward_instance.flash_points += reward_points
        store_reward_instance.save()

        StoreRewardsHistory.add_store_rewards_history(
            store_reference=instance.store_reference,
            reward_type=StoreRewardsHistory.RewardTypeChoices.VERIFICATION_PROMOTIONAL_REWARD,
            reward_value=reward_points,
            reward_category=StoreRewardsHistory.RewardCategoryChoices.FLASH,
            transaction_type=StoreRewardsHistory.TransactionTypeChoices.CREDIT,
            event_reference=instance.store_reference,
            reward_status=StoreRewardsHistory.RewardStatusChoices.SUCCESS
        )

        self.send_notification(instance, notification_type, points_count=reward_points)

    def process_referral_incentives(self, instance):
        reward_entries = RewardsHistory.objects.filter(
            event_reference=instance.store_reference,
            reward_type=RewardsHistory.RewardTypeChoices.STORE_VERIFICATION,
            transaction_type=RewardsHistory.TransactionTypeChoices.CREDIT,
            sender_reference='SWADESIC',
            reward_status=RewardsHistory.RewardStatusChoices.PENDING,
            invite_code=instance.created_by.invited_by_code
        )

        for reward in reward_entries:
            reward.reward_status = RewardsHistory.RewardStatusChoices.SUCCESS
            reward.save()
            self.update_infinity_points(reward)

        # Invalidate other pending rewards
        store_references = Store.objects.filter(created_by=instance.created_by).values_list('store_reference',
                                                                                            flat=True)
        RewardsHistory.objects.filter(
            event_reference__in=store_references,
            reward_type=RewardsHistory.RewardTypeChoices.STORE_VERIFICATION,
            transaction_type=RewardsHistory.TransactionTypeChoices.CREDIT,
            sender_reference='SWADESIC',
            reward_status=RewardsHistory.RewardStatusChoices.PENDING,
            invite_code=instance.created_by.invited_by_code
        ).update(reward_status=RewardsHistory.RewardStatusChoices.INVALID)

    def update_infinity_points(self, reward):
        if reward.receiver_reference.startswith('U'):
            user_reward_instance, created = UserRewards.objects.get_or_create(user_reference=reward.receiver_reference)
            user_reward_instance.infinity_points += int(reward.received_value)
            user_reward_instance.save()
        elif reward.receiver_reference.startswith('S'):
            store_reward_instance, created = StoreRewards.objects.get_or_create(
                store_reference=reward.receiver_reference)
            store_reward_instance.infinity_points += int(reward.received_value)
            store_reward_instance.save()

    def send_notification(self, instance, notification_type, points_count=None):
        notification_handler = NotificationHandler(
            notified_user=instance.store_reference,
            notification_type=notification_type,
            notification_about=instance.store_reference,
            image=instance.icon,
            points_count=points_count
        )
        notification_handler.create_notification(notification_handler)

        # Send notification to the store creator as well
        notification_handler = NotificationHandler(
            notified_user=instance.created_by.user_reference,
            notification_type=notification_type,
            notification_about=instance.store_reference,
            image=instance.icon,
            points_count=points_count
        )
        notification_handler.create_notification(notification_handler)

    def update_verification(self, instance, verification_identity, value):
        if verification_identity in ['GST', 'PAN', 'BOTH']:

            if verification_identity in ['GST', 'BOTH']:
                self.process_verification(instance, 'GST', value)

            if verification_identity in ['PAN', 'BOTH']:
                self.process_verification(instance, 'PAN', value)

            if instance.verification_type == "BUSINESS":
                instance.is_verification_completed = instance.is_gst_verified and instance.is_pan_verified
            elif instance.verification_type == "INDIVIDUAL":
                instance.is_verification_completed = instance.is_pan_verified
            else:
                instance.is_verification_completed = False

            if instance.is_verification_completed:
                self.handle_complete_verification(instance)

        else:
            raise ValueError("Invalid verification type")

    def process_verification(self, instance, verification_type, value):
        if value:
            setattr(instance, f'is_{verification_type.lower()}_verified', True)
            setattr(instance, f'{verification_type.lower()}_verified_time', timezone.now())
            if verification_type in instance.ID_verification_requested:
                instance.ID_verification_requested.remove(verification_type)
            self.send_notification(instance, f'{verification_type}_VERIFIED')
        else:
            setattr(instance, f'is_{verification_type.lower()}_verified', False)
            setattr(instance, f'{verification_type.lower()}_verified_time', None)
            if verification_type not in instance.ID_verification_requested:
                instance.ID_verification_requested.remove(verification_type)
                if not instance.verification_requested_time:
                    instance.verification_requested_time = timezone.now()
            self.send_notification(instance, f'{verification_type}_REJECTED')

    def handle_complete_verification(self, instance):
        store_config_instance = StoreConfig.objects.filter(store_reference=instance.store_reference).first()
        if store_config_instance:
            store_config_instance.enable_orders = True
            store_config_instance.save()
        if not instance.first_verified_date:
            instance.first_verified_date = timezone.now()
            instance.save()
        self.send_notification(instance, 'STORE_VERIFICATION_COMPLETE')


class StoresAwaitingVerification(APIView):
    def get(self, request):
        stores_awaiting_verification = Store.objects.filter(
            ID_verification_requested__len__gt=0,
            is_verification_completed=False
        ).order_by('-verification_requested_time')

        serializer = StoreVerificationSerializer(stores_awaiting_verification, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class GetPendingWithdrawalRequests(APIView):
    def get(self, request):
        pending_withdrawal_requests = PayoutTransactions.objects.filter(transaction_status=PayoutTransactions.Transaction_Status.PENDING)
        serializer = PayoutWithdrawalSerializer(pending_withdrawal_requests, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class CompleteWithdrawalRequest(APIView):
    @transaction.atomic
    def post(self, request):
        txn_reference = request.data.get('transaction_reference')
        status = request.data.get('status')
        bank_reference_number = request.data.get('bank_reference_number')
        txn_time = request.data.get('transaction_time')  # This is a Unix timestamp
        notes = request.data.get('notes', '')

        try:
            payout_txn = PayoutTransactions.objects.get(transaction_reference=txn_reference)
        except PayoutTransactions.DoesNotExist:
            return Response({'error': 'Transaction not found'}, status=404)

        if status not in [PayoutTransactions.Transaction_Status.SUCCESS, PayoutTransactions.Transaction_Status.FAILURE]:
            return Response({'error': 'Invalid status'}, status=400)

        payout_txn.transaction_status = status
        payout_txn.bank_reference_number = bank_reference_number
        
        # Convert Unix timestamp to datetime
        try:
            txn_time = int(txn_time)  # Ensure txn_time is an integer
            payout_txn.transaction_time = datetime.fromtimestamp(txn_time)
        except (ValueError, TypeError):
            return Response({'error': 'Invalid transaction time format. Please provide a valid Unix timestamp.'}, status=400)
        
        payout_txn.notes = notes
        
        if status == PayoutTransactions.Transaction_Status.SUCCESS:
            payout_balance = payout_txn.store_reference.account_payout_balance.first()
            if not payout_balance:
                return Response({'error': 'Payout balance not found for the store'}, status=400)
            if payout_balance.current_balance < payout_txn.payout_amount:
                return Response({'error': 'Insufficient balance'}, status=400)
            
            payout_balance.current_balance -= payout_txn.payout_amount
            payout_balance.save()

            # Send notification to store
            self.send_payment_notification(payout_txn, success=True)
        else:
            # Send notification to store
            self.send_payment_notification(payout_txn, success=False)

        payout_txn.save()

        return Response({'message': 'Withdrawal request updated successfully'}, status=200)

    def send_payment_notification(self, payout_txn, success):
        notification_handler = NotificationHandler(
            notified_user=payout_txn.store_reference.store_reference,
            notification_type=Notifications.Notifications_Type.PAYOUT_SUCCESS if success else Notifications.Notifications_Type.PAYOUT_FAILED,
            notification_about=payout_txn.store_reference.store_reference,
            image=payout_txn.store_reference.icon,  # You can add an image if needed
            payout_amount=payout_txn.payout_amount
        )
        notification_handler.create_notification(notification_handler)

        # # Send notification to the store creator as well
        # notification_handler = NotificationHandler(
        #     notified_user=payout_txn.store_reference.created_by.user_reference,
        #     notification_type=Notifications.Notifications_Type.PAYOUT_SUCCESS,
        #     notification_about=payout_txn.transaction_reference,
        #     image=payout_txn.store_reference.icon,  # You can add an image if needed
        #     payout_amount=payout_txn.payout_amount
        # )
        # notification_handler.create_notification(notification_handler)


class CreateSubscriptionPlanView(APIView):
    def post(self, request):
        serializer = SubscriptionPlanSerializer(data=request.data)

        if serializer.is_valid():
            try:
                # Create Basic Auth header
                auth = HTTPBasicAuth(config('RAZORPAY_ID'), config('RAZORPAY_KEY'))

                headers = {
                    'Content-Type': 'application/json'
                }

                # Convert amount to paise (smallest currency unit)
                amount_in_paise = int(float(serializer.validated_data['plan_amount']) * 100)

                # Prepare data for Razorpay API
                razorpay_plan_data = {
                    "period": serializer.validated_data['plan_period'],
                    "interval": 1,  # You might want to make this configurable
                    "item": {
                        "name": serializer.validated_data['plan_name'],
                        "amount": amount_in_paise,
                        "currency": "INR",
                        "description": serializer.validated_data['plan_description']
                    }
                }

                # Make API call to Razorpay
                response = requests.post(
                    'https://api.razorpay.com/v1/plans',
                    json=razorpay_plan_data,
                    auth=auth,
                    headers=headers
                )

                if response.status_code == 200:
                    razorpay_plan = response.json()

                    # Create plan in database
                    subscription_plan = SubscriptionPlan(
                        plan_name=serializer.validated_data['plan_name'],
                        plan_description=serializer.validated_data['plan_description'],
                        plan_period=serializer.validated_data['plan_period'],
                        plan_amount=serializer.validated_data['plan_amount'],
                        plan_type=serializer.validated_data['plan_type'],
                        razorpay_plan_id=razorpay_plan['id'],
                        plan_reference="PLAN" + str(int(time.time() * 1000))
                    )
                    subscription_plan.save()

                    return Response({
                        "message": "Subscription plan created successfully",
                        "plan_reference": subscription_plan.plan_reference,
                        "razorpay_plan_id": razorpay_plan['id']
                    }, status=status.HTTP_201_CREATED)
                else:
                    return Response({
                        "error": "Razorpay API error",
                        "details": response.json()
                    }, status=status.HTTP_400_BAD_REQUEST)

            except requests.exceptions.RequestException as e:
                return Response({
                    "error": "Failed to connect to Razorpay",
                    "details": str(e)
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            except Exception as e:
                return Response({
                    "error": "An unexpected error occurred",
                    "details": str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

