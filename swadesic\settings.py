"""
Django settings for swadesic project.

Generated by 'django-admin startproject' using Django 3.2.13.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

from pathlib import Path
import datetime
from datetime import timedelta
import neo4j
from decouple import config
import os
import django_heroku
import logging
from logging.handlers import TimedRotatingFileHandler
import sys
import dj_database_url
from datetime import datetime
import neomodel


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG", cast=bool)

IS_PRODUCTION = config("IS_PRODUCTION", cast=bool)
RESPONSE_STRUCTURE_IS_PRODUCTION = config("RESPONSE_STRUCTURE_IS_PRODUCTION", cast=bool)

if IS_PRODUCTION:
    ALLOWED_HOSTS = ['*************', '**************']
else:
    ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'daphne',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.postgres',
    'django.contrib.sites',
    'django.contrib.sitemaps',

    "debug_toolbar",
    'drf_yasg',
    'rest_framework',
    'rest_framework_simplejwt',
    'products.apps.ProductsConfig',
    'orders.apps.OrdersConfig',
    'stores.apps.StoresConfig',
    'users.apps.UsersConfig',
    'content.apps.ContentConfig',
    'GraphDB.apps.GraphdbConfig',
    'swadesic_admin.apps.SwadesicAdminConfig',
    'app_common.apps.AppCommonConfig',

    'django_celery_results',
    'django_celery_beat',
    'general.apps.GeneralConfig',
    'subscriptions.apps.SubscriptionsConfig',
    'messaging.apps.MessagingConfig',
    'lean_apis',
    'corsheaders',
    'graphene_django',
    'django_neomodel',
    'channels',
    'seo.apps.SeoConfig',
    'faq.apps.FaqConfig',
    ]

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=int(config("JWT_ACCESS_TOKEN_VALIDITY"))),  # short lifetime for access tokens
    'REFRESH_TOKEN_LIFETIME': timedelta(days=int(config("JWT_REFRESH_TOKEN_VALIDITY"))),  # longer lifetime for refresh tokens
    'ROTATE_REFRESH_TOKENS': True,  # issue a new refresh token on refresh
    'BLACKLIST_AFTER_ROTATION': True,
    'USER_ID_FIELD': 'user_reference'
}

REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ]
}

AUTH_USER_MODEL = 'users.User'

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = config("EMAIL_HOST_USER")  # Your Gmail email address
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD")

# if not os.path.exists('logs'):
#     os.makedirs('logs')
#
#
# log_file = 'logs/server.log'
# apps_logging_path = log_file
# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'formatters': {
#         'large': {
#             'format': '%(asctime)s,  %(levelname)s,  %(filename)s,  %(funcName)s, LineNo  %(lineno)d : %(message)s'
#         },
#         'tiny': {
#             'format': '%(asctime)s,  %(message)s'
#         }
#     },
#     'handlers': {
#         'console': {
#             'class': 'logging.StreamHandler',
#             'level': config('CONSOLE_DJANGO_LOG_LEVEL', 'WARNING'),
#             'formatter': 'large',
#         },
#         'app_logger': {
#             'level': config('SERVER_DJANGO_LOG_LEVEL', 'WARNING'),
#             'class': 'logging.handlers.TimedRotatingFileHandler',
#             'when': 's',
#             'interval': 5,
#             'filename': apps_logging_path,
#             'formatter': 'large',
#         },
#     },
#     'loggers': {
#         'django': {
#             'handlers': ['console', 'app_logger'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#     },
# }

MIDDLEWARE = [

    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    'swadesic.custom_middleware.SwadesicMiddleware',
    'swadesic.custom_middleware.InputValidationMiddleware'
]

CORS_ALLOW_ALL_ORIGINS = True
# CORS_ALLOWED_ORIGINS = [
#     "http://*",
#     "https://*",
# ]
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS']
CORS_ALLOW_HEADERS = [
    "accept",
    "authorization",
    "content-type",
    "Cache-Control-Speed"
]

neomodel.config.DATABASE_URL = config("NEO4J_DATABASE_URL")
# Additional driver configuration options
neomodel.config.MAX_CONNECTION_POOL_SIZE = 100
neomodel.config.CONNECTION_ACQUISITION_TIMEOUT = 60.0
neomodel.config.CONNECTION_TIMEOUT = 30.0
neomodel.config.ENCRYPTED = False
neomodel.config.KEEP_ALIVE = True
neomodel.config.MAX_CONNECTION_LIFETIME = 3600
neomodel.config.MAX_TRANSACTION_RETRY_TIME = 30.0
neomodel.config.RESOLVER = None
neomodel.config.USER_AGENT = config("NEO4J_USER_AGENT")

NEO4J_DATABASES = {
    'default': {
        'HOST': config("NEO4J_HOST"),
        'NAME': config("NEO4J_NAME"),
        'USER': config("NEO4J_USER"),
        'PASSWORD': config("NEO4J_PASSWORD"),
    }
}

# send the CSRF tokens in api calls
CSRF_COOKIE_SECURE = False  # Set to True if using HTTPS
CSRF_USE_SESSIONS = True   # Set to True to use session-based CSRF tokens
CSRF_COOKIE_HTTPONLY = False
#
SECURE_SSL_REDIRECT = False
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Sites framework configuration for SEO
SITE_ID = 1
SITE_DOMAIN = config("SITE_DOMAIN", default="swadesic.com")

ROOT_URLCONF = 'swadesic.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'swadesic.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

if IS_PRODUCTION:
    DATABASES = {

        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("PROD_DB_NAME"),
            'USER': config("PROD_DB_USER"),
            'PASSWORD': config("PROD_DB_PASSWORD"),
            'HOST': config("PROD_DB_HOST"),
            'PORT': '',
        },
        'content': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("PROD_DB_NAME"),
            'USER': config("PROD_DB_USER"),
            'PASSWORD': config("PROD_DB_PASSWORD"),
            'HOST': config("PROD_DB_HOST"),
            'PORT': '',
        },
        'product': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("PROD_DB_NAME"),
            'USER': config("PROD_DB_USER"),
            'PASSWORD': config("PROD_DB_PASSWORD"),
            'HOST': config("PROD_DB_HOST"),
            'PORT': '',
        },
        'order': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("PROD_DB_NAME"),
            'USER': config("PROD_DB_USER"),
            'PASSWORD': config("PROD_DB_PASSWORD"),
            'HOST': config("PROD_DB_HOST"),
            'PORT': '',
        },
        'store': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("PROD_DB_NAME"),
            'USER': config("PROD_DB_USER"),
            'PASSWORD': config("PROD_DB_PASSWORD"),
            'HOST': config("PROD_DB_HOST"),
            'PORT': '',
        },
        'user': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("PROD_DB_NAME"),
            'USER': config("PROD_DB_USER"),
            'PASSWORD': config("PROD_DB_PASSWORD"),
            'HOST': config("PROD_DB_HOST"),
            'PORT': '',
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("DB_NAME"),
            'USER': config("DB_USER"),
            'PASSWORD': config("DB_PASSWORD"),
            'HOST': config("DB_HOST"),
            'PORT': '',
        },
        'content': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("DB_NAME"),
            'USER': config("DB_USER"),
            'PASSWORD': config("DB_PASSWORD"),
            'HOST': config("DB_HOST"),
            'PORT': '',
        },
        'product': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("DB_NAME"),
            'USER': config("DB_USER"),
            'PASSWORD': config("DB_PASSWORD"),
            'HOST': config("DB_HOST"),
            'PORT': '',
        },
        'order': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("DB_NAME"),
            'USER': config("DB_USER"),
            'PASSWORD': config("DB_PASSWORD"),
            'HOST': config("DB_HOST"),
            'PORT': '',
        },
        'store': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("DB_NAME"),
            'USER': config("DB_USER"),
            'PASSWORD': config("DB_PASSWORD"),
            'HOST': config("DB_HOST"),
            'PORT': '',
        },
        'user': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': config("DB_NAME"),
            'USER': config("DB_USER"),
            'PASSWORD': config("DB_PASSWORD"),
            'HOST': config("DB_HOST"),
            'PORT': '',
        }
    }

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]





# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

# TIME_ZONE = 'UTC'

TIME_ZONE = 'Asia/Kolkata'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/


STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
STATIC_URL = "/static/"

# MEDIA_ROOT = os.path.join(BASE_DIR, "media")
# MEDIA_URL = "/media/"

MEDIA_URL = config("MEDIA_URL")
MEDIA_ROOT = config("MEDIA_ROOT")
SERVE_MEDIA_IN_PRODUCTION = True


# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

#  aws s3bucket configuration

# AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY')
# AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_KEY')
# AWS_STORAGE_BUCKET_NAME = config('AWS_S3_BUCKET_NAME')
# AWS_S3_SIGNATURE_VERSION = 's3v4'
# AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME')
# AWS_S3_FILE_OVERWRITE = False
# AWS_DEFAULT_ACL = None
# AWS_S3_VERIFY = True
# DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

#  paytm

TEST_MERCHANT_ID = config ('TEST_MERCHANT_ID')
WEBSITE = config ('WEBSITE')
TEST_MERCHANT_KEY = config ('TEST_MERCHANT_KEY')

PAYTM_CALL_BACK_URL = config ('PAYTM_CALL_BACK_URL')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django.request': {
            'handlers': ['console'],
            'level': 'DEBUG',  # change debug level as appropiate
            'propagate': False,
        },
    },
}
# Create logs directory if it doesn't exist

log_dir = 'logs'
if not os.path.exists('logs'):
    os.makedirs('logs')

today = datetime.today()
log_folder = os.path.join(log_dir, today.strftime('%Y-%m-%d'))
if not os.path.exists(log_folder):
    os.mkdir(log_folder)

# Create a TimedRotatingFileHandler
log_file = os.path.join(log_folder, 'server.log')
file_handler = TimedRotatingFileHandler(filename=log_file, when='H', interval=1)
file_handler.suffix = "%Y-%m-%d_%H-%M-%S"
file_handler.setLevel(logging.DEBUG)

console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.DEBUG)

# Set the log format
formatter = logging.Formatter('%(asctime)s %(levelname)s %(pathname)s LineNo %(lineno)d : %(message)s')
console_handler_formatter = logging.Formatter('%(asctime)s %(levelname)s LineNo %(lineno)d : %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(console_handler_formatter)

# Add the handler to the root logger
logging.root.addHandler(file_handler)
logging.root.addHandler(console_handler)

DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880


def show_toolbar(request):
    return True


if DEBUG:
    DEBUG_TOOLBAR_CONFIG = {
        "SHOW_TOOLBAR_CALLBACK": show_toolbar,
    }

    INTERNAL_IPS = [
        "127.0.0.1",
        "**************",
    ]

if "DATABASE_URL" in os.environ:
    DATABASES = {'default': dj_database_url.config(default='postgres://localhost'),
                 'order': dj_database_url.config(default='postgres://localhost'),
                 'product': dj_database_url.config(default='postgres://localhost'),
                 'user': dj_database_url.config(default='postgres://localhost')}

django_heroku.settings (locals())

ASGI_APPLICATION = 'swadesic.asgi.application'
CHANNEL_LAYERS = {
    # 'default': {
    #     'BACKEND': 'channels_redis.core.RedisChannelLayer',
    #     'CONFIG': {
    #         "hosts": [('127.0.0.1', 6379)],
    #     },
    # },

    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer"
}
}