from rest_framework import serializers
from products.models import Product, ProductImages
from stores.store_api.models import Store
from stores.store_settings_api.models import TrustCenter, RefundAndWarranty, DeliverySettings, DeliveryLocations
from GraphDB.models import Neo4jEntity, Neo4jContent
from users.user_api.models import User
from content.models import Posts, Comments, CommentImages, PostImages
import pytz
from datetime import datetime
import logging
from django.core.exceptions import ObjectDoesNotExist
from general.views import BuyStatusCheckerBatch, BuyStatusChecker

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class LeanProductImagesSerializer(serializers.ModelSerializer):
    """
    A lean serializer for ProductImages with only essential fields.
    Optimized for performance with minimal DB queries.
    """
    class Meta:
        model = ProductImages
        fields = [
            'productimageid',
            'reorder',
            'is_deleted',
            'product_image',
        ]


class LeanStoreProductsSerializer(serializers.ModelSerializer):
    """
    A lean serializer for Products with only essential fields.
    Optimized for performance with minimal DB queries.
    """
    product_images = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'product_reference',
            'product_name',
            'brand_name',
            'product_category',
            'mrp_price',
            'selling_price',
            'product_images',
        ]

    def get_product_images(self, obj):
        """
        Get only active product images and return them in the required format.
        """
        images = obj.prod_images.filter(is_deleted=False).order_by('reorder')
        return LeanProductImagesSerializer(images, many=True).data


class LeanProductDetailsSerializer(serializers.ModelSerializer):
    """
    A highly optimized lean serializer for Product details.
    Minimizes serializer method fields and database queries for better performance.
    """
    # Direct fields from related models using source notation
    storehandle = serializers.CharField(source="store_reference.storehandle")
    store_icon = serializers.ImageField(source="store_reference.icon")
    store_name = serializers.CharField(source="store_reference.store_name")
    is_test_store = serializers.BooleanField(source='store_reference.is_test_store')

    # Essential serializer method fields that can't be avoided
    prod_images = serializers.SerializerMethodField()
    updated_date = serializers.SerializerMethodField()
    created_date = serializers.SerializerMethodField()
    saved_or_not = serializers.BooleanField(default=False)

    class Meta:
        model = Product
        fields = [
            "productid",
            "product_reference",
            "product_name",
            "product_description",
            "product_category",
            "product_version",
            "updated_date",
            "created_date",
            "promotion_link",
            "brand_name",
            "mrp_price",
            "selling_price",
            "count_of_ratings",
            "rating",
            "in_stock",
            "hashtags",
            "targeted_gender",
            "store_reference",
            "storeid",
            "store_icon",
            "store_name",
            "storehandle",
            "swadeshi_brand",
            "swadeshi_made",
            "deleted",
            "is_test_store",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "orders_count",
            "returns_count",
            "cancels_count",
            "prod_images",
            "saved_or_not",
        ]

    def get_prod_images(self, obj):
        """Get product images"""
        # Use prefetched images if available
        if hasattr(obj, 'prefetched_images'):
            return LeanProductImagesSerializer(obj.prefetched_images, many=True).data
        images = obj.prod_images.filter(is_deleted=False).order_by('reorder')
        return LeanProductImagesSerializer(images, many=True).data

    def get_updated_date(self, obj):
        """Format updated date"""
        date = obj.modified_date
        if isinstance(date, datetime):
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d:%m:%Y %H:%M:%S")
        return None

    def get_created_date(self, obj):
        """Format created date"""
        date = obj.created_date
        if isinstance(date, datetime):
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d:%m:%Y %H:%M:%S")
        return None

    def to_representation(self, instance):
        """
        Override to_representation to add all required fields efficiently.
        This approach is more efficient than using many serializer method fields.
        """
        # Get the basic representation
        representation = super().to_representation(instance)

        # Get context data
        visitor_reference = self.context.get("visitor_reference")
        user_pincode = self.context.get('user_pincode')

        # Get cached data from context if available
        trust_centers = self.context.get('trust_centers', {})
        refund_warranties = self.context.get('refund_warranties', {})
        delivery_settings = self.context.get('delivery_settings', {})
        store_configs = self.context.get('store_configs', {})

        # Get store reference
        store_reference = instance.store_reference.store_reference if instance.store_reference else None

        # Add trust center related fields
        trust_center = trust_centers.get(store_reference)
        if trust_center:
            representation['location'] = trust_center.city
            representation['swadeshi_owned'] = trust_center.swadeshi_owned
        else:
            representation['location'] = None
            representation['swadeshi_owned'] = None

        # Add refund and warranty related fields
        refund_warranty = refund_warranties.get(store_reference)
        if refund_warranty:
            representation['return_period'] = refund_warranty.return_period

            # Convert return_conditions to a list of strings
            if refund_warranty.return_conditions:
                representation['return_conditions'] = refund_warranty.return_conditions.split('|')
            else:
                representation['return_conditions'] = []

            representation['return_pick_up'] = refund_warranty.return_pickup  # Fixed field name

            # Determine return cost responsibility
            if refund_warranty.return_cost_on_customer:
                representation['return_cost_on'] = "CUSTOMER"
            elif refund_warranty.return_cost_on_seller:
                representation['return_cost_on'] = "SELLER"
            else:
                representation['return_cost_on'] = None

            # Set refund_responsibility as a structured list
            representation['refund_responsibility'] = [
                {
                    "item_heading": "Refund policy",
                    "item_text": "Full refund based on conditions",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_text": "Partial Refund",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_heading": "On product returns",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                }
            ]
        else:
            representation['return_period'] = None
            representation['return_conditions'] = []
            representation['return_pick_up'] = None
            representation['return_cost_on'] = None
            representation['refund_responsibility'] = [
                {
                    "item_heading": "Refund policy",
                    "item_text": "Full refund based on conditions",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_text": "Partial Refund",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_heading": "On product returns",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                }
            ]

        # Add delivery settings related fields
        product_delivery = delivery_settings.get(instance.product_reference)
        store_delivery = delivery_settings.get(store_reference)
        delivery = product_delivery or store_delivery

        if delivery:
            # Calculate delivery_settings_type based on whether it's product or store level
            if product_delivery:
                representation['delivery_settings_type'] = "product_settings"
            else:
                representation['delivery_settings_type'] = "store_settings"

            # Calculate delivery_by from time_to_deliver
            representation['delivery_by'] = delivery.time_to_deliver

            # Calculate delivery_partner based on delivery method
            if delivery.deliverymethod_logistics:
                representation['delivery_partner'] = "logistics"
            elif delivery.deliverymethod_swadesic:
                representation['delivery_partner'] = "swadesic"
            elif delivery.deliverymethod_self:
                representation['delivery_partner'] = "self"
            else:
                representation['delivery_partner'] = None

            # Calculate delivery_fee with proper formatting
            if delivery.deliveryfeetype_all_free:
                representation['delivery_fee'] = "free"
            elif delivery.deliveryfeetype_standard:
                df_value = delivery.deliveryfee_value
                if delivery.product_reference:
                    representation['delivery_fee'] = f"₹{df_value} per product order"
                else:
                    representation['delivery_fee'] = f"₹{df_value} per store order"
            elif delivery.deliveryfeetype_distance:
                representation['delivery_fee'] = "distance_based"
            else:
                representation['delivery_fee'] = None

            # Get logistic_partner_name
            representation['logistic_partner_name'] = delivery.default_logistic_partner

            # Get fulfillment_options
            representation['fulfillment_options'] = delivery.fulfillment_options

            # Check deliverability
            if user_pincode and delivery.delivery_locations:
                # Parse delivery_locations - it's stored as a string
                try:
                    delivery_locations_list = delivery.delivery_locations.split(',')
                    if user_pincode in delivery_locations_list:
                        representation['deliverability'] = True
                    else:
                        representation['deliverability'] = False
                except Exception:
                    representation['deliverability'] = False
            else:
                representation['deliverability'] = False
        else:
            representation['delivery_settings_type'] = None
            representation['delivery_by'] = None
            representation['delivery_partner'] = None
            representation['delivery_fee'] = None
            representation['logistic_partner_name'] = None
            representation['fulfillment_options'] = None
            representation['deliverability'] = False

        # Add store config related fields
        store_config = store_configs.get(store_reference)
        representation['config_receive_orders'] = store_config.enable_orders if store_config else False

        # Add buy status related fields
        try:
            representation['is_buy_enabled'] = BuyStatusChecker.is_product_buyable(instance)
            representation['product_status_message'] = BuyStatusChecker.get_product_status_message(instance)
        except Exception:
            representation['is_buy_enabled'] = False
            representation['product_status_message'] = None

        # Add Neo4j related fields (only if visitor_reference is provided)
        if visitor_reference:
            try:
                content_node = Neo4jContent.nodes.get(reference=instance.product_reference)
                representation['like_status'] = content_node.get_like_status(visitor_reference=visitor_reference)
                representation['save_status'] = content_node.get_save_status(visitor_reference=visitor_reference)
                representation['repost_status'] = content_node.get_repost_status(visitor_reference=visitor_reference)
                representation['content_category'] = content_node.get_content_category(visitor_reference=visitor_reference)
                representation['content_headers'] = content_node.get_content_headers(visitor_reference=visitor_reference)
                representation['content_header_text'] = content_node.get_content_header_text(visitor_reference=visitor_reference)
            except Exception:
                representation['like_status'] = False
                representation['save_status'] = False
                representation['repost_status'] = False
                representation['content_category'] = None
                representation['content_headers'] = None
                representation['content_header_text'] = None
        else:
            representation['like_status'] = False
            representation['save_status'] = False
            representation['repost_status'] = False
            representation['content_category'] = None
            representation['content_headers'] = None
            representation['content_header_text'] = None

        # Add disclaimer message
        if instance.store_reference and (
            getattr(instance.store_reference, 'store_supporters_count', 0) < 30 or
            getattr(instance.store_reference, 'store_order_count', 0) < 5
        ):
            representation['disclaimer_message'] = "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        else:
            representation['disclaimer_message'] = ""

        return representation


class LeanProductPartialDetailsSerializer(serializers.ModelSerializer):
    product_images = LeanProductImagesSerializer(many=True, source='prefetched_images')
    storehandle = serializers.CharField(source='store_reference.storehandle', read_only=True)
    store_icon = serializers.URLField(source='store_reference.icon', read_only=True)

    class Meta:
        model = Product

        fields = [
            "productid",
            "product_reference",
            "store_reference",
            "store_icon",
            "storehandle",
            "product_name",
            "brand_name",
            "product_description",
            "mrp_price",
            "selling_price",
            "swadeshi_brand",
            "swadeshi_made",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "product_images"
        ]



    def to_representation(self, instance):
        rep = super().to_representation(instance)
        interactions = self.context.get("neo4j_interactions", {}).get(instance.product_reference, {})
        rep.update({
            "like_status": interactions.get("liked", False),
            "save_status": interactions.get("saved", False),
            "repost_status": interactions.get("reposted", False),
            "category": interactions.get("category", "POST"),
            "content_headers": interactions.get("headers", []),
            "content_header_text": interactions.get("header_text", "")
        })
        
        buy_info = self.context.get("buy_status_map", {}).get(instance.product_reference, {})
        rep["is_buy_enabled"] = buy_info.get("is_buy_enabled", False)
        rep["product_status_message"] = buy_info.get("product_status_message", "")

        content_creator = self.context.get("creator_details", {}).get(instance.product_reference, {})
        rep["created_by"] = content_creator
        
        details = instance.store_reference.store_details.all()
        if details:
            rep["swadeshi_owned"] = details[0].swadeshi_owned

        return rep


class LeanPostImagesSerializer(serializers.ModelSerializer):
    class Meta:
        model = PostImages
        fields = ['post_image_id', 'post_image', 'reorder', 'is_deleted']


class LeanPostPartialDetailsSerializer(serializers.ModelSerializer):
    post_images = LeanPostImagesSerializer(many=True, source='prefetched_images')
    storehandle = serializers.CharField(source='store_reference.storehandle', read_only=True)
    store_icon = serializers.URLField(source='store_reference.icon', read_only=True)

    class Meta:
        model = Posts
        fields = [
            "post_id",
            "post_reference",
            "store_reference",
            "store_icon",
            "storehandle",
            "post_text",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "post_images",
        ]

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        interactions = self.context.get("neo4j_interactions", {}).get(instance.post_reference, {})
        rep.update({
            "like_status": interactions.get("liked", False),
            "save_status": interactions.get("saved", False),
            "repost_status": interactions.get("reposted", False),
            "category": interactions.get("category", "POST"),
            "content_headers": interactions.get("headers", []),
            "content_header_text": interactions.get("header_text", "")
        })
        content_creator = self.context.get("creator_details", {}).get(instance.post_reference, {})
        rep["created_by"] = content_creator
        return rep


class LeanSearchUserSerializer(serializers.ModelSerializer):
    """
    A lean serializer for User search results.
    Optimized for performance with minimal DB queries.
    """
    display_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "user_reference",
            "user_name",
            "first_name",
            "last_name",
            "display_name",
            "icon",
            "user_location",
            "pincode",
            "subscription_type",
        ]

    def get_display_name(self, obj):
        """Get display name or fallback to username"""
        return obj.display_name if obj.display_name else obj.user_name

    def to_representation(self, instance):
        """
        Override to_representation to add Neo4j metrics and follow status.
        Uses batch-loaded data from context when available.
        """
        representation = super().to_representation(instance)

        # Get batch-loaded data from context
        entity_metrics = self.context.get('entity_metrics', {})
        follow_status = self.context.get('follow_status', {})

        # Get user reference
        user_ref = instance.user_reference

        # Add follower and following counts from Neo4j
        metrics = entity_metrics.get(user_ref, {})
        representation['follower_count'] = metrics.get('follower_count', 0)
        representation['following_count'] = metrics.get('following_count', 0)

        # Add follow status
        status_info = follow_status.get(user_ref, {})
        representation['follow_status'] = status_info.get('follow_status', 'Follow')

        return representation


class LeanSearchStoreSerializer(serializers.ModelSerializer):
    """
    A lean serializer for Store search results.
    Optimized for performance with minimal DB queries.
    """
    class Meta:
        model = Store
        fields = [
            "store_reference",
            "store_name",
            "storehandle",
            "business_description",
            "icon",
            "category_name",
            "is_test_store",
            "subscription_type",
        ]

    def to_representation(self, instance):
        """
        Override to_representation to add Neo4j metrics, follow status, and deliverability.
        Uses batch-loaded data from context when available.
        """
        representation = super().to_representation(instance)

        # Get context data
        user_pincode = self.context.get('user_pincode')
        entity_metrics = self.context.get('entity_metrics', {})
        follow_status = self.context.get('follow_status', {})

        # Get store reference
        store_ref = instance.store_reference

        # Add follower and following counts from Neo4j
        metrics = entity_metrics.get(store_ref, {})
        representation['storeSupportersCount'] = metrics.get('follower_count', 0)
        representation['storeSupportingCount'] = metrics.get('following_count', 0)

        # Add follow status
        status_info = follow_status.get(store_ref, {})
        representation['followStatus'] = status_info.get('follow_status', 'Support')

        # Add swadeshi_owned status
        try:
            from stores.store_settings_api.models import TrustCenter
            trust_center = TrustCenter.objects.filter(store_reference=store_ref).first()
            representation['swadeshi_owned'] = trust_center.swadeshi_owned if trust_center else None
        except Exception:
            representation['swadeshi_owned'] = None

        # Add deliverability status
        representation['deliverability'] = False
        if user_pincode:
            try:
                from stores.store_api.serializers import get_deliverability
                representation['deliverability'] = get_deliverability(store_ref, user_pincode)
            except Exception:
                pass

        return representation


class LeanSearchPostImageSerializer(serializers.Serializer):
    """
    A lean serializer for post images in search results.
    """
    image_url = serializers.URLField()
    reorder = serializers.IntegerField()

    class Meta:
        fields = [
            "image_url",
            "reorder"
        ]


class LeanStoreProductFullDetailsSerializer(serializers.ModelSerializer):
    """
    A highly optimized serializer for store product details with pagination.
    Uses batch loading and minimizes database and Neo4j queries for maximum performance.
    """
    # Direct fields from related models using source notation
    storehandle = serializers.CharField(source="store_reference.storehandle")
    store_icon = serializers.ImageField(source="store_reference.icon")
    store_name = serializers.CharField(source="store_reference.store_name")
    is_test_store = serializers.BooleanField(source='store_reference.is_test_store')

    # Essential serializer method fields
    prod_images = serializers.SerializerMethodField()
    updated_date = serializers.SerializerMethodField()
    created_date = serializers.SerializerMethodField()
    saved_or_not = serializers.BooleanField(default=False)

    class Meta:
        model = Product
        fields = [
            "productid",
            "product_reference",
            "product_name",
            "product_description",
            "product_category",
            "product_version",
            "updated_date",
            "created_date",
            "promotion_link",
            "brand_name",
            "mrp_price",
            "selling_price",
            "count_of_ratings",
            "rating",
            "in_stock",
            "hashtags",
            "store_reference",
            "storeid",
            "store_icon",
            "store_name",
            "storehandle",
            "swadeshi_brand",
            "swadeshi_made",
            "deleted",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "orders_count",
            "returns_count",
            "cancels_count",
            "prod_images",
            "saved_or_not",
            "is_test_store",
        ]

    def get_prod_images(self, obj):
        """Get product images"""
        # Use prefetched images if available
        if hasattr(obj, 'prefetched_images'):
            return LeanProductImagesSerializer(obj.prefetched_images, many=True).data
        # Fallback to database query if prefetched images not available
        images = obj.prod_images.filter(is_deleted=False).order_by('reorder')
        return LeanProductImagesSerializer(images, many=True).data

    def get_updated_date(self, obj):
        """Format updated date"""
        date = obj.modified_date
        if isinstance(date, datetime):
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d:%m:%Y %H:%M:%S")
        return None

    def get_created_date(self, obj):
        """Format created date"""
        date = obj.created_date
        if isinstance(date, datetime):
            local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
            return local_time.strftime("%d:%m:%Y %H:%M:%S")
        return None

    def to_representation(self, instance):
        """
        Override to_representation to add all required fields efficiently.
        Uses batch-loaded data from context when available.
        """
        # Get the basic representation
        representation = super().to_representation(instance)

        # Get context data
        visitor_reference = self.context.get("visitor_reference")
        user_pincode = self.context.get("user_pincode")

        # Get batch-loaded data from context
        context_data = self.context
        trust_centers = context_data.get('trust_centers', {})
        refund_warranties = context_data.get('refund_warranties', {})
        delivery_settings = context_data.get('delivery_settings', {})
        store_configs = context_data.get('store_configs', {})
        neo4j_data = context_data.get('neo4j_data', {})
        buy_status_data = context_data.get('buy_status_data', {})

        # Get store reference
        store_reference = instance.store_reference.store_reference if instance.store_reference else None

        # Add trust center related fields
        trust_center = trust_centers.get(store_reference)
        if trust_center:
            representation['location'] = trust_center.city
            representation['swadeshi_owned'] = trust_center.swadeshi_owned
        else:
            representation['location'] = None
            representation['swadeshi_owned'] = None

        # Add refund and warranty related fields
        product_refund_warranty = refund_warranties.get(instance.product_reference)
        store_refund_warranty = refund_warranties.get(store_reference)
        refund_warranty = product_refund_warranty or store_refund_warranty
        default_refund_responsibility =  [
                {
                    "item_heading": "Refund policy",
                    "item_text": "Full refund based on conditions",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_text": "Partial Refund",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_heading": "On product returns",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                }
            ]

        if refund_warranty:
            representation['return_period'] = refund_warranty.return_period

            # Convert return_conditions to a list of strings
            if refund_warranty.return_conditions:
                representation['return_conditions'] = refund_warranty.return_conditions.split('|')
            else:
                representation['return_conditions'] = []

            representation['return_pick_up'] = refund_warranty.return_pickup

            # Determine return cost responsibility
            if refund_warranty.return_cost_on_customer:
                representation['return_cost_on'] = "CUSTOMER"
            elif refund_warranty.return_cost_on_seller:
                representation['return_cost_on'] = "SELLER"
            else:
                representation['return_cost_on'] = None

            # Set refund_responsibility as a structured list
            representation['refund_responsibility'] = default_refund_responsibility
        else:
            representation['return_period'] = None
            representation['return_conditions'] = []
            representation['return_pick_up'] = None
            representation['return_cost_on'] = None
            representation['refund_responsibility'] = default_refund_responsibility

        # Add delivery settings related fields
        product_delivery = delivery_settings.get(instance.product_reference)
        store_delivery = delivery_settings.get(store_reference)
        delivery = product_delivery or store_delivery

        if delivery:
            # Calculate delivery_settings_type based on whether it's product or store level
            if product_delivery:
                representation['delivery_settings_type'] = "product_settings"
            else:
                representation['delivery_settings_type'] = "store_settings"

            # Calculate delivery_by from time_to_deliver
            representation['delivery_by'] = delivery.time_to_deliver

            # Calculate delivery_partner based on delivery method
            if delivery.deliverymethod_logistics:
                representation['delivery_partner'] = "logistics"
            elif delivery.deliverymethod_swadesic:
                representation['delivery_partner'] = "swadesic"
            elif delivery.deliverymethod_self:
                representation['delivery_partner'] = "self"
            else:
                representation['delivery_partner'] = None

            # Calculate delivery_fee with proper formatting
            if delivery.deliveryfeetype_all_free:
                representation['delivery_fee'] = "free"
            elif delivery.deliveryfeetype_standard:
                df_value = delivery.deliveryfee_value
                if delivery.product_reference:
                    representation['delivery_fee'] = f"₹{df_value} per product order"
                else:
                    representation['delivery_fee'] = f"₹{df_value} per store order"
            elif delivery.deliveryfeetype_distance:
                representation['delivery_fee'] = "distance_based"
            else:
                representation['delivery_fee'] = None

            # Get logistic_partner_name
            representation['logistic_partner_name'] = delivery.default_logistic_partner

            # Get fulfillment_options
            representation['fulfillment_options'] = delivery.fulfillment_options

            # Check deliverability
            if user_pincode and delivery.delivery_locations:
                # Parse delivery_locations - it's stored as a string
                try:
                    delivery_locations_list = delivery.delivery_locations.split(',')
                    if user_pincode in delivery_locations_list:
                        representation['deliverability'] = True
                    else:
                        representation['deliverability'] = False
                except Exception:
                    representation['deliverability'] = False
            else:
                representation['deliverability'] = False
        else:
            representation['delivery_settings_type'] = None
            representation['delivery_by'] = None
            representation['delivery_partner'] = None
            representation['delivery_fee'] = None
            representation['logistic_partner_name'] = None
            representation['fulfillment_options'] = None
            representation['deliverability'] = False

        # Add store config related fields
        store_config = store_configs.get(store_reference)
        representation['config_receive_orders'] = store_config.enable_orders if store_config else False

        # Add Neo4j related fields from batch-loaded data if available
        product_neo4j = neo4j_data.get(instance.product_reference, {})
        if product_neo4j:
            representation['like_status'] = product_neo4j.get('like_status', False)
            representation['save_status'] = product_neo4j.get('save_status', False)
            representation['repost_status'] = product_neo4j.get('repost_status', False)
            representation['content_category'] = product_neo4j.get('content_category')
            representation['content_headers'] = product_neo4j.get('content_headers')
            representation['content_header_text'] = product_neo4j.get('content_header_text')
        else:
            # Fallback to individual Neo4j query if batch data not available
            if visitor_reference:
                try:
                    content_node = Neo4jContent.nodes.get(reference=instance.product_reference)
                    representation['like_status'] = content_node.get_like_status(visitor_reference=visitor_reference)
                    representation['save_status'] = content_node.get_save_status(visitor_reference=visitor_reference)
                    representation['repost_status'] = content_node.get_repost_status(visitor_reference=visitor_reference)
                    representation['content_category'] = content_node.get_content_category(visitor_reference=visitor_reference)
                    representation['content_headers'] = content_node.get_content_headers(visitor_reference=visitor_reference)
                    representation['content_header_text'] = content_node.get_content_header_text(visitor_reference=visitor_reference)
                except Exception:
                    representation['like_status'] = False
                    representation['save_status'] = False
                    representation['repost_status'] = False
                    representation['content_category'] = None
                    representation['content_headers'] = None
                    representation['content_header_text'] = None
            else:
                representation['like_status'] = False
                representation['save_status'] = False
                representation['repost_status'] = False
                representation['content_category'] = None
                representation['content_headers'] = None
                representation['content_header_text'] = None

        # Add buy status related fields from batch-loaded data if available
        product_buy_status = buy_status_data.get(instance.product_reference, {})
        if product_buy_status:
            representation['is_buy_enabled'] = product_buy_status.get('is_buy_enabled', False)
            representation['product_status_message'] = product_buy_status.get('product_status_message')
        else:
            # Fallback to individual BuyStatusChecker if batch data not available
            try:
                if user_pincode:
                    checker = BuyStatusChecker(store=instance.store_reference, product=instance, user_pincode=user_pincode)
                    representation['is_buy_enabled'] = checker.is_buy_enabled()
                    representation['product_status_message'] = checker.get_status_message()
                else:
                    representation['is_buy_enabled'] = False
                    representation['product_status_message'] = None
            except Exception:
                representation['is_buy_enabled'] = False
                representation['product_status_message'] = None

        # Add disclaimer message
        if instance.store_reference and (
            getattr(instance.store_reference, 'store_supporters_count', 0) < 30 or
            getattr(instance.store_reference, 'store_order_count', 0) < 5
        ):
            representation['disclaimer_message'] = "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        else:
            representation['disclaimer_message'] = ""

        # Make sure all fields from the example are included
        # Convert prod_images to product_images (already handled above)

        # Make sure saved_or_not is set based on save_status
        if 'save_status' in representation:
            representation['saved_or_not'] = representation['save_status']

        return representation
    

class LeanCommentImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommentImages
        fields = ['comment_image_id', 'comment_image', 'reorder', 'is_deleted']


class LeanCommentsSerializer(serializers.ModelSerializer):
    comment_images = LeanCommentImageSerializer(many=True, source='prefetched_images')

    class Meta:
        model = Comments
        fields = [
            "comment_id",
            "comment_reference",
            "commenter_reference",
            "comment_text",
            "created_date",
            "is_deleted",
            "comment_type",
            "rating_count",
            "event_reference",
            "main_parent_id",
            "parent_comment_id",
            "level",
            "like_count",
            "comment_count",
            "repost_count",
            "repost_plus_count",
            "save_count",
            "share_count",
            "comment_images",
        ]

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        interactions = self.context.get("neo4j_interactions", {}).get(instance.comment_reference, {})
        rep.update({
            "like_status": interactions.get("liked", False),
            "save_status": interactions.get("saved", False),
            "repost_status": interactions.get("reposted", False),
            "category": interactions.get("category", "POST"),
            "content_headers": interactions.get("headers", []),
            "content_header_text": interactions.get("header_text", "")
        })
        content_creator = self.context.get("creator_details", {}).get(instance.comment_reference, {})
        rep["created_by"] = content_creator
        return rep
    
