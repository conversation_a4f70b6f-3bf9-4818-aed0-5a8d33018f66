# Generated by Django 3.2.13 on 2023-06-21 09:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0036_remove_store_categoryid'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessTypes',
            fields=[
                ('business_type_id', models.AutoField(primary_key=True, serialize=False)),
                ('business_type', models.CharField(max_length=40)),
            ],
            options={
                'verbose_name_plural': 'business types',
                'db_table': '"store"."business_types"',
            },
        ),
        migrations.CreateModel(
            name='Cities',
            fields=[
                ('city_id', models.AutoField(primary_key=True, serialize=False)),
                ('city', models.CharField(max_length=30)),
            ],
            options={
                'verbose_name_plural': 'cities',
                'db_table': '"store"."cities"',
            },
        ),
    ]
