# Generated by Django 3.2.13 on 2022-11-05 09:51

from django.db import migrations


def enter_product_reference_data(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in product_reference column, where
    # productid of CartItem model is same as productid in Product table.

    CartItem = apps.get_model("orders", "CartItem")
    Product = apps.get_model("products", "Product")
    for item in CartItem.objects.all():
        if Product.objects.filter(productid=item.productid).exists():
            item.product_reference = Product.objects.get(productid=item.productid)
        else:
            item.product_reference = None
        item.save(update_fields=["product_reference"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    CartItem = apps.get_model("orders", "CartItem")
    for item in CartItem.objects.all():
        item.product_reference = None
        item.save(update_fields=["product_reference"])


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0029_cartitem_product_reference"),
    ]

    operations = [migrations.RunPython(enter_product_reference_data, reverse_func)]
