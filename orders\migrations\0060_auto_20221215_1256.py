# Generated by Django 3.2.13 on 2022-12-15 07:26

from django.db import migrations


def enter_user_reference_order(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in user_reference column, where
    # userid of Order model is same as userid in User table.

    Order = apps.get_model("orders", "Order")
    User = apps.get_model("users", "User")
    for item in Order.objects.all():
        if User.objects.filter(userid=item.userid).exists():
            user = User.objects.get(userid=item.userid)
            item.user_reference = user.user_reference
        else:
            item.user_reference = None
        item.save(update_fields=["user_reference"])


def enter_user_reference_order_reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    Order = apps.get_model("orders", "Order")
    for item in Order.objects.all():
        item.user_reference = None
        item.save(update_fields=["user_reference"])


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0059_auto_20221215_1256"),
    ]

    operations = [
        migrations.RunPython(
            enter_user_reference_order, enter_user_reference_order_reverse_func
        ),
    ]
