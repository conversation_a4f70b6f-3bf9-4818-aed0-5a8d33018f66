# Generated by Django 3.2.13 on 2023-06-05 10:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0090_refundedamount_refunded_date'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='orderpayout',
            name='order_value_after_total_reduction',
        ),
        migrations.RemoveField(
            model_name='orderpayout',
            name='order_value_after_transaction_fee_reduction',
        ),
        migrations.AddField(
            model_name='orderpayout',
            name='commission_calculated',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='orderpayout',
            name='payout_amount',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='orderpayout',
            name='transaction_fee_calculated',
            field=models.PositiveIntegerField(default=0),
        ),
    ]
