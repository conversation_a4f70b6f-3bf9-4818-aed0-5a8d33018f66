from rest_framework.response import Response
from django.db.models import Sum
from decouple import config
from orders.order_api.models import *
from orders.payment_api.models import *
from common.util.notification_handler import NotificationHandler
from users.user_api.models import *
from users.notification_api.models import *
from .payout_utils import OrderPayoutCalculations
from .refund_utils import RefundCalculations
import datetime
from django.shortcuts import get_object_or_404
from general.views import get_app_config
from orders.swadesic_shipping.shiprocket_api.shiprocket_api import ShiprocketAPI
from orders.swadesic_shipping.models import StoreShippingBalanceHistory, SwadesicShippingPackageDetails


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


SKIP_RAZORPAY_API = config("SKIP_RAZORPAY_API", cast=bool)
RAZORPAY_ID = config("RAZORPAY_ID")
RAZORPAY_KEY = config("RAZORPAY_KEY")


class UpdateOrderStatus_single():
    # This is a constructor method that initializes an object by setting its attributes based
    # on the provided keyword arguments. It iterates over the key-value pairs in the `kwargs` dictionary
    # and sets the corresponding attributes on the object using the `setattr()` function.
    def __init__(self, response=True, **kwargs):
        self.response = response
        self.new_return_cost_on = None
        self.refund_requested_date = None
        self.estimated_delivery_date = None
        self.refund_scheduled_datetime = None
        self.return_estimated_pickup_date = None
        self.return_estimated_delivery_date = None
        self.commission_confirmed_statuses = [
            SubOrder.Suborder_Status.ORDER_CONFIRMED,
            SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS,
            SubOrder.Suborder_Status.ORDER_DELIVERED
        ]
        self.cancelled_by = None
        for key, value in kwargs.items():
            setattr(self, key, value)

    def update_order_status(self, optional_status: str = None):
        self.suborder = SubOrder.objects.get(suborder_number=self.suborder_number)
        self.current_status = self.suborder.suborder_status
        self.new_status = optional_status or self.status

        if not self.is_valid_transition():
            if self.response:
                logger.error(f"Invalid status transition from {self.current_status} to {self.new_status}")
                return Response(
                    {"message": f"Invalid status transition from {self.current_status} to {self.new_status}"},
                    status=400
                )
            else:
                logger.info(f"Invalid status transition from {self.current_status} to {self.new_status}")

        status_handlers = {
            SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION: self.handle_waiting_for_confirmation,
            SubOrder.Suborder_Status.ORDER_CONFIRMED: self.handle_order_confirmed,
            SubOrder.Suborder_Status.ORDER_CANCELLED: self.handle_order_cancelled,
            SubOrder.Suborder_Status.DELIVERY_IN_PROGRESS: self.handle_delivery_in_progress,
            SubOrder.Suborder_Status.ORDER_DELIVERED: self.handle_order_delivered,
            SubOrder.Suborder_Status.DELIVERY_FAILED: self.handle_delivery_failed,
            SubOrder.Suborder_Status.RETURN_REQUESTED: self.handle_return_requested,
            SubOrder.Suborder_Status.RETURN_CONFIRMED: self.handle_return_confirmed,
            SubOrder.Suborder_Status.REFUND_HOLD: self.handle_refund_hold,
            SubOrder.Suborder_Status.RETURN_IN_PROGRESS: self.handle_return_in_progress,
            SubOrder.Suborder_Status.RETURNED_TO_SELLER: self.handle_returned_to_seller,
            SubOrder.Suborder_Status.RETURN_FAILED: self.handle_return_failed,
            SubOrder.Suborder_Status.RETURN_REQUEST_CANCELLED: self.handle_return_request_cancelled,
        }
        handler = status_handlers.get(self.new_status)
        if handler:
            handler()
        if self.response:
            return Response({"message": "Status updated successfully"}, status=200)
        else:
            logger.info(f"Status updated successfully")

    def update_secondary_order_status(self, optional_status: str = None):
        self.suborder = SubOrder.objects.get(suborder_number=self.suborder_number)
        self.suborder.secondary_suborder_status = optional_status
        self.suborder.save()
    #######################################################################
    def is_valid_transition(self):
        status_transitions = {
            "WAITING_FOR_CONFIRMATION": ["PAYMENT_INITIATED", "PAYMENT_SUCCESS", "WAITING_FOR_CONFIRMATION"],
            "ORDER_CONFIRMED": ["PAYMENT_SUCCESS", "WAITING_FOR_CONFIRMATION", "ORDER_CONFIRMED"],
            "ORDER_CANCELLED": ["PAYMENT_SUCCESS", "WAITING_FOR_CONFIRMATION", "ORDER_CONFIRMED",
                                "DELIVERY_IN_PROGRESS","SCHEDULED_FOR_PICKUP"],
            "CANCELLED_IN_TRANSIT": ["DELIVERY_IN_PROGRESS"],
            "DELIVERY_IN_PROGRESS": ["ORDER_CONFIRMED", "DELIVERY_IN_PROGRESS","SCHEDULED_FOR_PICKUP"],
            "ORDER_DELIVERED": ["DELIVERY_IN_PROGRESS", "RETURN_REQUEST_CANCELLED","SCHEDULED_FOR_PICKUP"],
            "DELIVERY_FAILED": ["DELIVERY_IN_PROGRESS"],
            "RETURN_REQUESTED": ["ORDER_DELIVERED"],
            "RETURN_CONFIRMED": ["RETURN_REQUESTED","ORDER_DELIVERED"],
            "RETURN_IN_PROGRESS": ["RETURN_CONFIRMED"],
            "RETURNED_TO_SELLER": ["RETURN_CONFIRMED", "REFUND_HOLD", "RETURN_IN_PROGRESS"],
            "REFUND_HOLD": ["RETURN_CONFIRMED", "ORDER_CANCELLED", "RETURNED_TO_SELLER"],
            "RELEASE_REFUND": ["RETURNED_TO_SELLER", "REFUND_HOLD"],
            "RETURN_FAILED": ["RETURN_CONFIRMED"],
            "RETURN_REQUEST_CANCELLED": ["RETURN_REQUESTED", "RETURN_CONFIRMED"],
        }
        return self.current_status in status_transitions.get(self.new_status, [])

    def _update_suborder(self, *args, **kwargs):
        for key, value in kwargs.items():
            setattr(self.suborder, key, value)
        self.suborder.suborder_status = self.new_status

        # Get the field names of the Suborder model
        valid_fields = set(field.name for field in SubOrder._meta.get_fields())

        # Filter kwargs to only include valid fields
        valid_updates = {k: v for k, v in kwargs.items() if k in valid_fields}

        for key, value in valid_updates.items():
            setattr(self.suborder, key, value)

        self.suborder.save()

    def _update_order_lifecycle(self, *args, **kwargs):
        order_lifecycle = OrderLifeCycle.objects.filter(
            suborder_number=self.suborder.suborder_number,
            suborder_status=self.suborder.suborder_status
        ).last()
        order_lifecycle.suborder_status = self.new_status
        # Get the field names of the OrderLifeCycle model
        valid_fields = set(field.name for field in OrderLifeCycle._meta.get_fields())

        # Filter kwargs to only include valid fields
        valid_updates = {k: v for k, v in kwargs.items() if k in valid_fields}

        for key, value in valid_updates.items():
            setattr(order_lifecycle, key, value)
        order_lifecycle.save()

    def _update_order_payout_status(self, order_payout_status, OrderType_list=['PRODUCT'], payout_time=None):
        """
        OrderType_list can be ['STORE_DF', 'PRODUCT', 'COMMISSION'])
        """
        logger.info(f"Updating order payout status for order_payout_status called with inputs: {order_payout_status}, {OrderType_list}, {payout_time}")
        for order_type in OrderType_list:
            if order_type == 'PRODUCT':
                order_payout_instance = self.suborder.order_payout_items.filter(
                    order_type=OrderPayout.OrderType.PRODUCT).first()
            elif order_type == 'STORE_DF':
                order_payout_instance = self.suborder.orderid.order_payout_items.filter(
                    order_type=OrderPayout.OrderType.STORE_DF).first()
            elif order_type == 'COMMISSION':
                order_payout_instance = self.suborder.orderid.order_payout_items.filter(
                    order_type=OrderPayout.OrderType.COMMISSION).first()
            else:
                logger.error("Invalid order type")
            if order_payout_instance.payout_status != order_payout_status:
                if order_payout_status == "WAITING_FOR_RELEASE":
                    if payout_time == 'NOW':
                        payout_release_date = datetime.date.today()

                    else:
                        return_period = RefundAndWarranty.objects.filter(
                            refundandwarrantyid=self.suborder.refund_warranty_id).first().return_period
                        if return_period is None:
                            return_period=0
                        order_configuration_instance = OrderConfiguration.objects.first()
                        fulfillment_window_days = order_configuration_instance.fulfillment_window / (
                                24 * 60)  # Convert minutes to days
                        payout_period_timedelta = datetime.timedelta(days=max(return_period, fulfillment_window_days))

                        total_timedelta = payout_period_timedelta + datetime.timedelta(
                            days=(order_configuration_instance.grace_period / (24 * 60)))
                        payout_release_date = datetime.date.today() + total_timedelta

                    order_payout_instance.payout_release_date = payout_release_date
                    order_payout_instance.save()

                order_payout_instance.payout_status = order_payout_status
                order_payout_instance.save()

    def _update_suborder_payment_detail(self, store_df_record=False, *args, **kwargs ):

        # Get the field names of the OrderLifeCycle model
        valid_fields = set(field.name for field in SubOrderPaymentDetails._meta.get_fields())

        # Filter kwargs to only include valid fields
        valid_updates = {k: v for k, v in kwargs.items() if k in valid_fields}

        instance = self.suborder.suborder_payment_detail_items.first() if not store_df_record else self.suborder.orderid.suborder_payment_detail_items.filter(
            suborder_number__isnull=True).first()
        for key, value in valid_updates.items():
            setattr(instance, key, value)
        instance.save()
        # if store_df_record:
        #     store_df_refund_object = RefundedAmount.objects.filter(
        #         order_reference=self.suborder.order_number,
        #         suborder_reference__isnull=True).first()
        #     if store_df_refund_object is not None and store_df_refund_object.refund_id is None:
        #         store_df_payment_detail_instance = self.suborder.orderid.suborder_payment_detail_items.filter(
        #             suborder_number__isnull=True).first()
        #         store_df_payment_detail_instance.refund_id = store_df_refund_object.refunded_amount_id
        #         store_df_payment_detail_instance.refund_amount = store_df_refund_object.refunded_amount
        #         store_df_payment_detail_instance.save()

    def _notify(self, notified_entity, notification_type, **kwargs):
        # TODO: need to verify if this notification is happening everytime

        if notified_entity == 'USER':
            notified_entity_reference = self.suborder.user_reference
        elif notified_entity == 'STORE':
            notified_entity_reference = self.suborder.store_reference
        else:
            pass
        notification_handler = NotificationHandler(
            notified_user=notified_entity_reference,
            notification_type=notification_type,
            notification_about=self.suborder.order_number,
            image=self.suborder.product_image,
            **kwargs
        )
        notification_handler.create_notification(notification_handler)

    #####################################################
    def handle_waiting_for_confirmation(self):
        logger.info(f"Handling waiting for confirmation for suborder: {self.suborder.suborder_number}")
        self._update_suborder(estimated_delivery_date=self.suborder.estimated_delivery_date if not self.estimated_delivery_date else self.estimated_delivery_date)
        self._update_order_lifecycle(estimated_delivery_date=self.suborder.estimated_delivery_date if not self.estimated_delivery_date else self.estimated_delivery_date)

    #####################################################

    def handle_order_confirmed(self):
        logger.info(f"Handling order confirmation for suborder: {self.suborder.suborder_number}")

        now = datetime.datetime.now()
        self.confirmation_date = now.strftime("%d/%m/%Y %H:%M:%S")
        delivery_pincode = self.suborder.orderid.billing_address_id.pincode
        self.suborder.orderid.order_delivery_pincode = delivery_pincode
        self.suborder.orderid.save(update_fields=["order_delivery_pincode"])

        output_kwargs = {'confirmation_date': self.confirmation_date,
                         'estimated_delivery_date': self.estimated_delivery_date}

        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)

        self._update_swadesic_fee()

        self._update_order_payout_status(order_payout_status='IN_PROCESS',
                                         OrderType_list=['PRODUCT'])  # 'COMMISSION' is already updated

        # Notify if there's a change in estimated delivery date or status
        if ((self.suborder.estimated_delivery_date and self.estimated_delivery_date != self.suborder.estimated_delivery_date) or
                (self.current_status != self.new_status)):
            notification_type = Notifications.Notifications_Type.ORDER_UPDATE if self.estimated_delivery_date != self.suborder.estimated_delivery_date else Notifications.Notifications_Type.ORDER_CONFIRMED
            self._notify(notified_entity='USER', notification_type=notification_type)

    #####################################################

    def handle_order_cancelled(self):
        logger.info(f"Handling order cancellation for suborder: {self.suborder.suborder_number}")
        # kwargs contain suborder_number, status, cancellation_reason, cancelled_by
        notification_type, notified_entity = self._set_variables_for_cancelled()
        if self.suborder.delivery_by_swadesic:
            input_kwargs = {}
            is_swadesic_shipping_started = self.suborder.suborder_status in ["DELIVERY_IN_PROGRESS"]
            if is_swadesic_shipping_started:
                self.new_status = SubOrder.Suborder_Status.CANCELLED_IN_TRANSIT
            else:
                if self.suborder.suborder_status in ["ORDER_CONFIRMED", "WAITING_FOR_CONFIRMATION", "SCHEDULED_FOR_PICKUP"]:
                    self.swadesic_shipping_cancel_and_recharge_processing(is_swadesic_shipping_started)
        else:
            input_kwargs = {
                "refund_scheduled_datetime": self.refund_scheduled_datetime
            }

        self.suborder_payment_details = self.suborder.suborder_payment_detail_items.first()
        self.create_refund_entry(input_kwargs=input_kwargs)
        self._update_support_records()
        output_kwargs1 = {
            'cancelled_date': self.cancelled_date,
            'cancellation_reason': self.cancellation_reason,
            'cancelled_by': self.cancelled_by,
            'is_deleted': True
        }
        output_kwargs2 = {
            'is_suborder_deleted': True,
            'suborder_deleted_condition': SubOrderPaymentDetails.Deleted_Conditions.CANCELLED,
            'refund_id': self.refunded_amount_instance.refunded_amount_id,
            'refund_amount': self.refunded_amount_instance.refunded_amount,
        }

        self._update_suborder(**output_kwargs1)
        self._update_order_lifecycle(**output_kwargs1)
        # self._update_order_payout_status(order_payout_status='WAITING_FOR_RELEASE')  # TODO: Isn't this already in In-process
        self._update_suborder_payment_detail(**output_kwargs2)
        self._notify(notified_entity=notified_entity, notification_type=notification_type)

        order_payout_calculations = OrderPayoutCalculations(
            order_status_self=self,
            order_payout_instance=self.order_payout_instance,
            refunded_amount_instance=self.refunded_amount_instance)
        order_payout_calculations.update_orderpayout_entry(payout_status='WAITING_FOR_RELEASE')

        ########################

        # store df record
        # if delivery fee scope of current suborder should be store-df:
        #     if (all other store-df suborders of the order is cancelled (before shipping)) and {any store-df suborder should not be a positive delivery scenario}:
        #         current suborder status should be processed (refunded object should be created, payout should be updated ) as above

        if self.suborder.suborder_payment_detail_items.first().delivery_fee_scope == 'store-df':
            # Get all store-df suborders for this order
            store_df_suborder_payments = self.suborder.orderid.suborder_payment_detail_items.filter(
                delivery_fee_scope='store-df')

            # Check if all other store-df suborders are cancelled (before shipping)
            all_other_cancelled = all(
                suborder_payment.suborder_number.suborder_status in [
                    SubOrder.Suborder_Status.ORDER_CANCELLED,
                    SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
                    SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
                    SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED
                ] for suborder_payment in store_df_suborder_payments if
                suborder_payment.suborder_number != self.suborder.suborder_number
            )

            # Check if any store-df suborder is not a positive delivery scenario
            no_positive_delivery = not any(
                suborder_payment.suborder_number.package_number is not None
                for suborder_payment in store_df_suborder_payments
            )
            self.order_payout_instance = self.suborder.orderid.order_payout_items.filter(suborder_number__isnull=True,
                                              order_type=OrderPayout.OrderType.STORE_DF).first()

            # this is the last store-df ordered and all other are cancelled before shipping
            if all_other_cancelled and no_positive_delivery:  # TODO: make sure package number is not made None after cancelling a shipping
                # Process the current suborder status
                self.create_refund_entry(order_payout_instance=self.order_payout_instance, store_df_record=True,
                                         input_kwargs=input_kwargs)

                output_kwargs2 = {'is_suborder_deleted': True,
                                  'suborder_deleted_condition': SubOrderPaymentDetails.Deleted_Conditions.CANCELLED,
                                  'refund_id': self.refunded_amount_instance.refunded_amount_id,
                                  'refund_amount': self.refunded_amount_instance.refunded_amount,
                                  }
                # Update order payout status for the STORE_DF order payout entry
                # self._update_order_payout_status(
                #     order_payout_status='WAITING_FOR_RELEASE', OrderType_list=['STORE_DF'])  # TODO: Isn't this already in In-process
                self._update_suborder_payment_detail(**output_kwargs2, store_df_record=True)

                # update order payout status for the __
                order_payout_calculations = OrderPayoutCalculations(
                    order_status_self=self,
                    order_payout_instance=self.order_payout_instance,
                    refunded_amount_instance=self.refunded_amount_instance)
                order_payout_calculations.update_orderpayout_entry(payout_status='WAITING_FOR_RELEASE')
            # this is the last store df order processed with cancel but there are one/some other store df order went through positive delivery scenario but is either cancelled during progress or delivered or returned, so it was waiting for some positive order, and as the current order is cancelled, we are sending the store-df to seller
            if not no_positive_delivery and self._all_suborders_closed(store_df_suborder_payments):

                order_payout_calculations = OrderPayoutCalculations(
                    order_status_self=self,
                    order_payout_instance=self.order_payout_instance,
                    refunded_amount_instance=None)
                order_payout_calculations.update_orderpayout_entry(payout_status='WAITING_FOR_RELEASE')
                self._update_order_payout_status(order_payout_status='WAITING_FOR_RELEASE', OrderType_list=['STORE_DF'],
                                                 payout_time='NOW')
                # self._release_store_df_order_payout(self)

        self._update_swadesic_fee()

    def _all_suborders_closed(self, store_df_suborder_payments):
        all_other_closed = all(
            suborder_payment.suborder_number.suborder_status in [
                SubOrder.Suborder_Status.ORDER_CANCELLED,
                SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER,
                SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER,
                SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED,
                SubOrder.Suborder_Status.ORDER_DELIVERED,
                SubOrder.Suborder_Status.RETURN_REQUESTED,
                SubOrder.Suborder_Status.RETURN_CONFIRMED,
                SubOrder.Suborder_Status.RETURN_IN_PROGRESS,
                SubOrder.Suborder_Status.RETURNED_TO_SELLER,
                # SubOrder.Suborder_Status.DELIVERY_FAILED,
                # SubOrder.Suborder_Status.RETURN_FAILED,
                SubOrder.Suborder_Status.REFUNDED] for suborder_payment in store_df_suborder_payments
        )
        return all_other_closed

    def _update_support_records(self):

        order_payout_commission_instance = self.suborder.orderid.order_payout_items.filter(
            order_type=OrderPayout.OrderType.COMMISSION).first()
        # order_payout_commission_instance.

        pass

    def _update_swadesic_fee(self, new_payout_status='IN_PROCESS'):
        logger.info(f'Updating Swadesic fee with {new_payout_status}')

        order_payout_commission_instance = self.suborder.orderid.order_payout_items.filter(
            order_type=OrderPayout.OrderType.COMMISSION
        ).first()

        if not order_payout_commission_instance:
            logger.warning(
                f"No commission instance found for order {self.suborder.orderid}. Skipping Swadesic fee update.")
            return

        product_order_amounts = self.suborder.orderid.suborderitems.filter(
            suborder_status__in=self.commission_confirmed_statuses
        ).aggregate(total=Sum('order_payout_items__order_amount'))['total'] or 0

        store_df_amounts = self.suborder.orderid.order_payout_items.filter(
            order_type=OrderPayout.OrderType.STORE_DF
        ).aggregate(total=Sum('order_amount'))['total'] or 0

        current_order_amount = (product_order_amounts + store_df_amounts)
        calculated_swadesic_fee = calculate_swadesic_fee(current_order_amount, delivery_pincode=self.suborder.orderid.order_delivery_pincode, store_pincode=self.suborder.orderid.store_reference.store_details.get().pincode)
        order_payout_commission_instance = self.suborder.orderid.order_payout_items.filter(
            order_type=OrderPayout.OrderType.COMMISSION).first()
        current_swadesic_fee, current_payout_status = order_payout_commission_instance.payout_amount, order_payout_commission_instance.payout_status
        flag = False
        if calculated_swadesic_fee != current_swadesic_fee:
            order_payout_commission_instance.payout_amount = -calculated_swadesic_fee
            flag = True
        if current_payout_status != new_payout_status:
            # if current payout status is amount AMOUNT_RELEASED and calculated swadesic fee is
            # less than current swadesic fee, then we have to correct this by adding
            # a new order payout record with order type = ADDITION
            order_payout_commission_instance.payout_status = new_payout_status
            flag = True
        if flag:
            order_payout_commission_instance.save()

    def _set_variables_for_cancelled(self):
        now = datetime.datetime.now()
        self.cancelled_date = now.strftime("%d/%m/%Y %H:%M:%S")

        order_configuration_instance = OrderConfiguration.objects.all().first()

        # Variables that change based on who cancelled the order
        if self.cancelled_by == SubOrder.Cancelled_By.BUYER:
            self.new_status = SubOrder.Suborder_Status.ORDER_CANCELLED_BY_BUYER
            self.new_return_cost_on = 'STORE_TO_USER' if self.current_status == SubOrder.Suborder_Status.WAITING_FOR_CONFIRMATION else None
            self.refund_processing_waiting_time = order_configuration_instance.cancel_refund_wait_time
            notification_type = Notifications.Notifications_Type.BUYER_CANCELLED_ORDER
            notified_entity = 'STORE'
        elif self.cancelled_by == SubOrder.Cancelled_By.SELLER:
            self.new_status = SubOrder.Suborder_Status.ORDER_CANCELLED_BY_SELLER
            self.new_return_cost_on = None
            self.refund_processing_waiting_time = 0
            notification_type = Notifications.Notifications_Type.SELLER_CANCELLED_ORDER
            notified_entity = 'USER'
        elif self.cancelled_by == SubOrder.Cancelled_By.AUTO_CANCELLED:
            self.new_status = SubOrder.Suborder_Status.ORDER_AUTO_CANCELLED
            self.new_return_cost_on = 'STORE'
            self.refund_processing_waiting_time = 0
            notification_type = Notifications.Notifications_Type.AUTO_CANCELLED
            notified_entity = 'BOTH'
        else:
            # raise an exception saying cancelled by is an invalid value passed
            raise ValueError("Invalid cancelled by value passed")

        # self.refund_request_initiated_date = now
        self.refund_scheduled_datetime = now + datetime.timedelta(minutes=self.refund_processing_waiting_time)

        return notification_type, notified_entity

    def create_refund_entry(self, order_payout_instance=None, store_df_record=False, input_kwargs=None):
        self.order_payout_instance = order_payout_instance if order_payout_instance else self.suborder.order_payout_items.filter(
            order_type=OrderPayout.OrderType.PRODUCT).first()
        refund_calculations = RefundCalculations(order_status_self=self,
                                                 order_payout_instance=self.order_payout_instance,
                                                 store_df_record=store_df_record)
        create_args = {
            'user_reference': self.suborder.user_reference,
            'order_reference': self.suborder.orderid.order_number,
            'suborder_reference': None if store_df_record else self.suborder,
            'refund_status': RefundedAmount.Status.UNPROCESSED,
            'refunded_amount': refund_calculations.calculate_refund_amount(),
            'razorpay_order_payment_id': self.suborder.orderid.razorpay_payment_id,
        }

        if input_kwargs:
            create_args.update(input_kwargs)

        conditional_arguments = {
            "user_reference": self.suborder.user_reference,
            "order_reference": self.suborder.order_number,
            "refund_status": RefundedAmount.Status.UNPROCESSED
        }
        if not store_df_record:
            conditional_arguments.update({"suborder_reference": self.suborder})
        else:
            conditional_arguments.update({"suborder_reference": None})

        if not RefundedAmount.objects.filter(**conditional_arguments).exists():
            self.refunded_amount_instance = RefundedAmount.objects.create(**create_args)
        else:
            self.refunded_amount_instance = RefundedAmount.objects.filter(**conditional_arguments).first()

    #Swadesic shipping cancel related methods

    def swadesic_shipping_cancel_and_recharge_processing(self, is_swadesic_shipping_started):
        package_number = self.suborder.package_number
        if package_number is not None:
            self.swadesic_shipping_package = SwadesicShippingPackageDetails.objects.filter(package_number=package_number).first()
            shiprocket_order_id = self.swadesic_shipping_package.shiprocket_order_id
            store_instance = get_object_or_404(Store, store_reference=self.suborder.store_reference)

            #initialize shiprocket Mixin
            shiprocket = ShiprocketAPI(token=config('SHIPROCKET_TOKEN'))

            if not is_swadesic_shipping_started:
                #cancel order
                cancel_order_response, cancel_order_status = shiprocket.cancel_order(order_ids=[shiprocket_order_id])

                logger.info(f"Cancel order response: {cancel_order_response}")
                logger.info(f"Cancel order status: {cancel_order_status}")

                #cancel shipment
                # shiprocket.cancel_shipment(shipment_ids=[shiprocket_shipment_id])

                if cancel_order_status == 204:
                    logger.info(f"Successfully cancelled order {shiprocket_order_id}")
                    wallet_refund_check, wallet_refund_amount = self.check_wallet_refund()
                    if wallet_refund_check:
                        self.process_refund_shipping_balance(wallet_refund_amount, store_instance)
                else:
                    logger.error(f"Failed to cancel order {shiprocket_order_id}")
            else:
                # if swadesic shipping is already started then deduct the amount from shipping charges for RTO purposes as well
                self.deduct_shipping_balance_for_RTO_charges(self.swadesic_shipping_package.shipping_cost, store_instance)

    def check_wallet_refund(self):
        #TODO : get the related api from shiprocket api and validate the refund for the cancelled order
        #TODO : if the refund is not immediate Queue it to rabbitmq for processing
        wallet_refund_amount = self.swadesic_shipping_package.shipping_cost

        return True, wallet_refund_amount

    def process_refund_shipping_balance(self, wallet_refund_amount,store_instance):
        # check if the store shipping balance history for that order exists
        store_shipping_balance_history_instance = StoreShippingBalanceHistory.objects.filter(
            store_reference=store_instance,
            payment_status=StoreShippingBalanceHistory.PaymentStatus.SUCCESS,
            transaction_type=StoreShippingBalanceHistory.TransactionTypeChoices.CREDIT,
            order_reference=self.suborder.order_number,
            package_number=self.suborder.package_number,
            payment_amount=wallet_refund_amount,
            is_refund_transaction=True
        ).first()

        if not store_shipping_balance_history_instance:
            #Deduct the amount from the store shipping balance
            store_shipping_balance_instance = get_object_or_404(StoreShippingAccountBalance, store_reference=store_instance)
            store_shipping_balance_instance.shipping_balance -= wallet_refund_amount
            store_shipping_balance_instance.save()

            # Create store shipping balance history for the refund
            shipping_balance_payment_reference = self.generate_shipping_payment_reference()
            store_shipping_balance_history = StoreShippingBalanceHistory.objects.create(
                store_reference=store_instance,
                shipping_balance_payment_reference=shipping_balance_payment_reference,
                package_number=self.suborder.package_number,
                order_reference=self.suborder.order_number,
                transaction_type=StoreShippingBalanceHistory.TransactionTypeChoices.CREDIT,
                payment_status=StoreShippingBalanceHistory.PaymentStatus.SUCCESS,
                payment_amount=wallet_refund_amount,
                is_refund_transaction=True
            )
            return store_shipping_balance_history
        else:
            return store_shipping_balance_history_instance

    def deduct_shipping_balance_for_RTO_charges(self, shipping_cost, store_instance):
        # Deduct the amount from the store shipping balance
        store_shipping_balance_instance = get_object_or_404(StoreShippingAccountBalance, store_reference=store_instance)
        store_shipping_balance_instance.shipping_balance -= shipping_cost
        store_shipping_balance_instance.save()

        # Create store shipping balance history for the RTO charge
        shipping_balance_payment_reference = self.generate_shipping_payment_reference()
        store_shipping_balance_history = StoreShippingBalanceHistory.objects.create(
            store_reference=store_instance,
            shipping_balance_payment_reference=shipping_balance_payment_reference,
            package_number=self.suborder.package_number,
            order_reference=self.suborder.order_number,
            transaction_type=StoreShippingBalanceHistory.TransactionTypeChoices.DEBIT,
            payment_status=StoreShippingBalanceHistory.PaymentStatus.SUCCESS,
            payment_amount=shipping_cost,
            is_refund_transaction=False,
            notes="RTO Charges"
        )
        return store_shipping_balance_history

    def generate_shipping_payment_reference(self):
        return f"SP-{int(time.time() * 1000)}"


    ########################################################

    def handle_delivery_in_progress(self):
        logger.info(f"Handling order delivery in progress for suborder: {self.suborder.suborder_number}")
        output_kwargs = {'package_number': self.package_number}
        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)
        self._notify(notification_type=Notifications.Notifications_Type.ORDER_SHIPPED, notified_entity='USER')
        # TODO:display_package_number where is it coming from

    #########################################################

    def handle_order_delivered(self):
        logger.info(f"Handling order delivered for suborder: {self.suborder.suborder_number}")
        now = datetime.datetime.now()
        self.delivered_date = now.strftime("%d/%m/%Y %H:%M:%S")
        output_kwargs = {'delivered_date': self.delivered_date}

        self.order_payout_instance = self.suborder.order_payout_items.first()

        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)
        order_type_list = ['PRODUCT', 'COMMISSION']

        # if the current order is a store_df order, then we need to update the store_df_record to release
        if self.suborder.suborder_payment_detail_items.first().delivery_fee_scope == 'store-df':
            order_type_list.append('STORE_DF')
        logger.info(f"Going to call Update payout status with inputs {OrderPayout.Payout_Status.WAITING_FOR_RELEASE}, {order_type_list}" )
        self._update_order_payout_status(order_payout_status=OrderPayout.Payout_Status.WAITING_FOR_RELEASE,
                                         OrderType_list=order_type_list)


        # TODO: move this notification to place where package is saved so user can redirect to that place

        logger.info(f"Exited order delivered section for suborder: {self.suborder.suborder_number}")

    ########################################################

    def handle_delivery_failed(self):
        logger.info(f"Handling delivery failure for suborder: {self.suborder.suborder_number}")
        self._update_suborder()
        self._update_order_lifecycle()
        self._notify(notified_entity='USER', notification_type=Notifications.Notifications_Type.DELIVERY_FAILED)
        # TODO: Create an internal escalation

    ########################################################

    def handle_return_requested(self):
        logger.info(f"Handling return request for suborder: {self.suborder.suborder_number}")
        self._set_variables_for_return_requested()

        if not RefundedAmount.objects.filter(
                user_reference=self.suborder.user_reference,
                order_reference=self.suborder.order_number,
                suborder_reference=self.suborder,
                refund_status=RefundedAmount.Status.UNPROCESSED).exists():

            self.create_refund_entry()
        else:
            logger.info(f"Refunded amount entry for suborder {self.suborder.suborder_number} already exists")

        output_kwargs = {
            'return_initiate_date': self.return_requested_date,
            'return_reason': self.return_reason,
            'return_conditions_json': self.return_conditions_json,
        }
        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)
        order_type_list = ["PRODUCT"]
        if not self.suborder.orderid.suborderitems.filter(
                suborder_status__in=self.commission_confirmed_statuses).exclude(suborder_number=self.suborder).exists():
            order_type_list.append("COMMISSION")

        self._update_order_payout_status(order_payout_status="PAYOUT_HOLD", OrderType_list=order_type_list)
        self._notify(notified_entity='STORE', notification_type=Notifications.Notifications_Type.RETURN_REQUESTED)

    def _set_variables_for_return_requested(self):
        now = datetime.datetime.now()

        self.return_requested_date = now
        # order_configuration_instance = OrderConfiguration.objects.all().first()
        # self.refund_processing_waiting_time = order_configuration_instance.return_refund_wait_time

    ########################################################

    def handle_return_confirmed(self):
        logger.info(f"Handling return confirmation for suborder: {self.suborder.suborder_number}")
        self._set_variables_for_return_confirmation()
        output_kwargs = {
            'return_confirmation_date': self.return_confirmation_date,
            'return_estimated_pickup_date': self.return_estimated_pickup_date,
        }
        self.create_refund_entry()
        order_payout_calculations = OrderPayoutCalculations(
            order_status_self=self,
            order_payout_instance=self.order_payout_instance,
            refunded_amount_instance=self.refunded_amount_instance)
        order_payout_calculations.update_orderpayout_entry(payout_status=OrderPayout.Payout_Status.PAYOUT_HOLD)

        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)
        self._notify(notified_entity='USER', notification_type=Notifications.Notifications_Type.RETURN_ACCEPTED)
        self._update_swadesic_fee()

    def _set_variables_for_return_confirmation(self):
        now = datetime.datetime.now()
        self.return_confirmation_date = now.strftime("%d/%m/%Y %H:%M:%S")

    ########################################################

    def handle_refund_hold(self):
        logger.info(f"Handling refund hold for suborder: {self.suborder.suborder_number}")

        output_kwargs = {
            'refund_hold_reason': self.refund_hold_reason}
        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)
        self._update_refund_status()
        self._notify(notified_entity='USER', notification_type=Notifications.Notifications_Type.REFUND_HOLD)

    def _update_refund_status(self):
        refund_instance = self.suborder.refund_details.last()
        refund_instance.refund_status = RefundedAmount.Status.HOLD
        refund_instance.refund_hold_reason = self.refund_hold_reason
        refund_instance.save(update_fields=["refund_status", "refund_hold_reason"])

    ########################################################

    def handle_return_in_progress(self):
        logger.info(f"Handling return in progress for suborder: {self.suborder.suborder_number}")

        output_kwargs = {
            'return_package_number': self.return_package_number,
            'return_estimated_delivery_date': self.return_estimated_delivery_date,
        }

        if self.suborder.delivery_by_swadesic:
            self.refund_scheduled_datetime = datetime.datetime.now() + datetime.timedelta(hours=24)
            self._update_refund_instance()

        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)

    ########################################################

    def handle_returned_to_seller(self):
        logger.info(f"Handling return to seller for suborder: {self.suborder.suborder_number}")

        self._set_variables_for_return_to_seller()

        output_kwargs1 = {
            'returned_date': self.returned_date
        }

        output_kwargs2 = {
            'is_suborder_deleted': True,
            'suborder_deleted_condition': SubOrderPaymentDetails.Deleted_Conditions.RETURNED,
            'refund_id': self.suborder.refund_details.last().refund_id,
            'refund_amount': self.suborder.refund_details.last().refunded_amount,
        }

        self._update_suborder(**output_kwargs1)
        self._update_order_lifecycle(**output_kwargs1)
        self._update_suborder_payment_detail(**output_kwargs2)
        self._update_order_payout_status(
            order_payout_status=OrderPayout.Payout_Status.WAITING_FOR_RELEASE, payout_time='NOW'
        )
        self._update_refund_instance()
        self._notify(notified_entity='USER', notification_type=Notifications.Notifications_Type.RETURN_RECEIVED)

    def _set_variables_for_return_to_seller(self):
        now = datetime.datetime.now()
        self.returned_date = now.strftime("%d/%m/%Y %H:%M:%S")
        order_configuration = OrderConfiguration.objects.all().first()
        if not self.current_status == "REFUND_HOLD":
            self.refund_processing_waiting_time = 0 if self.current_status == "REFUND_HOLD" else order_configuration.return_refund_wait_time
            self.refund_scheduled_datetime = now + datetime.timedelta(minutes=self.refund_processing_waiting_time)

    def _update_refund_instance(self):
        # refund_instance = RefundedAmount.objects.get(suborder_reference=self.suborder.suborder_number)
        if not self.current_status == "REFUND_HOLD":
            refund_instance = self.suborder.refund_details.last()
            refund_instance.refund_scheduled_datetime = self.refund_scheduled_datetime
            refund_instance.save(update_fields=["refund_scheduled_datetime"])

    ########################################################

    def handle_return_failed(self):
        logger.info(f"Handling return failure for suborder: {self.suborder.suborder_number}")
        self._update_suborder()
        self._update_order_lifecycle()
        self._notify(notified_entity='USER', notification_type=Notifications.Notifications_Type.RETURN_FAILED)
        # TODO: Create an escalation ticket

    ########################################################

    def handle_return_request_cancelled(self):
        logger.info(f"Handling return request cancelled for suborder: {self.suborder.suborder_number}")
        self.refunded_amount_instance = RefundedAmount.objects.filter(
                user_reference=self.suborder.user_reference,
                order_reference=self.suborder.order_number,
                suborder_reference=self.suborder,
                refund_status=RefundedAmount.Status.UNPROCESSED).first()

        now = datetime.datetime.now()
        self.return_request_cancelled_date = now.strftime("%d/%m/%Y %H:%M:%S")
        output_kwargs = {
            'return_request_cancelled_date': self.return_request_cancelled_date,
            'suborder_status': SubOrder.Suborder_Status.ORDER_DELIVERED
        }

        self._update_order_payout_amount()
        self._update_suborder(**output_kwargs)
        self._update_order_lifecycle(**output_kwargs)
        self._change_refund_status()
        self._update_swadesic_fee()

        # TODO: move this notification to place where package is saved so user can redirect to that place

        self._notify(notified_entity='STORE', notification_type=Notifications.Notifications_Type.RETURN_REQUEST_CANCELLED)

        logger.info(f"Exited order delivered section for suborder: {self.suborder.suborder_number}")

    def _update_order_payout_amount(self):
        self.order_payout_instance = self.suborder.order_payout_items.first()
        self.order_payout_instance.payout_amount = self.order_payout_instance.expected_payout_amount
        self.order_payout_instance.payout_status = OrderPayout.Payout_Status.WAITING_FOR_RELEASE
        self.order_payout_instance.save(update_fields=["payout_amount", "payout_status"])

    def _change_refund_status(self):
        if self.refunded_amount_instance:
            self.refunded_amount_instance.refund_status = RefundedAmount.Status.CANCELLED
            self.refunded_amount_instance.save(update_fields=["refund_status"])
        else:
            pass


def calculate_swadesic_fee(total_order_amount, delivery_pincode=None, store_pincode=None):
    """
    Calculate the Swadesic fee based on order amount and delivery/store locations.

    Args:
        total_order_amount (float): The total amount of the order
        delivery_pincode (str, optional): The pincode of the delivery location
        store_pincode (str, optional): The pincode of the store location

    Returns:
        float: The calculated Swadesic fee

    Fee is zero when:
    1. Total order amount is less than the minimum cart value for commission fee
    2. Delivery pincode matches store pincode (and both are not None)
    """
    order_config = OrderConfiguration.objects.all().first()

    # Check if delivery is in the same pincode as the store (local delivery)
    is_local_delivery = False
    if delivery_pincode and store_pincode and delivery_pincode == store_pincode:
        is_local_delivery = True

    # Calculate fee based on conditions
    if is_local_delivery:
        # No fee for local deliveries (same pincode)
        swadesic_fee = 0
    elif total_order_amount >= order_config.min_cart_value_for_commission_fee:
        # Regular commission fee for orders above minimum cart value
        swadesic_fee = order_config.commission_fee
    else:
        # No fee for orders below minimum cart value
        swadesic_fee = 0

    return swadesic_fee
