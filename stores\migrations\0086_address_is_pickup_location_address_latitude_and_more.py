# Generated by Django 4.2.7 on 2025-02-02 09:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0085_store_js_messaging_token"),
    ]

    operations = [
        migrations.AddField(
            model_name="address",
            name="is_pickup_location",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="address",
            name="latitude",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="address",
            name="location_link",
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddField(
            model_name="address",
            name="longitude",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="address",
            name="pickup_location_name",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="address",
            name="pickup_timings",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="deliverysettings",
            name="fulfillment_options",
            field=models.CharField(
                blank=True,
                choices=[
                    ("DELIVERY", "Delivery"),
                    ("IN_STORE_PICKUP", "In Store Pickup"),
                    ("DELIVERY_AND_IN_STORE_PICKUP", "Delivery and In Store Pickup"),
                ],
                default="DELIVERY",
                max_length=30,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="deliverysettings",
            name="pickup_location_id",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="pickup_location",
                to="stores.address",
            ),
        ),
    ]
