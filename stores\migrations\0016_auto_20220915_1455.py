# Generated by Django 3.2.13 on 2022-09-15 09:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0015_auto_20220915_1449"),
    ]

    operations = [
        migrations.AddField(
            model_name="documents",
            name="storeid",
            field=models.IntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="refundandwarranty",
            name="store_reference",
            field=models.ForeignKey(
                db_column="store_reference",
                default="S6889545",
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.store",
                to_field="store_reference",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="refundandwarranty",
            name="storeid",
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name="storelink",
            name="storeid",
            field=models.IntegerField(),
        ),
    ]
