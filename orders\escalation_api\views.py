from rest_framework import status, generics
from rest_framework.response import Response
from .models import Escalation
from .serializers import EscalationSerializer
from drf_yasg.utils import swagger_auto_schema
from orders.order_api.models import ShippingHistory, Order, OrderLifeCycle
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class EscalationAv(generics.ListCreateAPIView):
    @swagger_auto_schema(operation_summary="Get all reviews")
    def get(self, request, *args, **kwargs):
        instance = Escalation.objects.all()
        serializer = EscalationSerializer(instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def post(self, request, *args, **kwargs):
        serializer = EscalationSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            OrderLifeCycle.objects.create(
                order_number=request.data['order_reference'],
                suborder_number=request.data['suborder_reference'],
                suborder_status='ORDER_ESCALATED',
                package_number=request.data['package_reference'],
            )
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class EscalationDetails(generics.RetrieveUpdateDestroyAPIView):
    @swagger_auto_schema(auto_schema=None, operation_summary="Get single review")
    def get(self, request, *args, **kwargs):
        instance = Escalation.objects.get(pk=kwargs["escalation_id"])
        serializer = EscalationSerializer(instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema (auto_schema=None, operation_summary="Edit single review")
    def put(self, request, *args, **kwargs):
        instance = Escalation.objects.get(pk=kwargs["escalation_id"])
        serializer = EscalationSerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @swagger_auto_schema(auto_schema=None, operation_summary="Delete single review")
    def delete(self, request, *args, **kwargs):
        instance = Escalation.objects.get(pk=kwargs["escalation_id"])
        instance.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)
