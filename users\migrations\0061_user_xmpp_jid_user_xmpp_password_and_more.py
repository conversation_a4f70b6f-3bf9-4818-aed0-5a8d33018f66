# Generated by Django 4.2.7 on 2024-05-16 14:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0060_alter_user_user_name_alter_user_website_link"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="xmpp_jid",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="xmpp_password",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="notifications",
            name="notification_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING", "onboarding"),
                    ("PRODUCT_VERSION_CHANGED", "Product version changed"),
                    ("STORE_CREATED", "New store created near you"),
                    ("NEW_ORDER", "New order"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("ORDER_SHIPPED", "Order shipped"),
                    ("SHIPPING_PACAKGE_UPDATED", "Shipping package updated"),
                    ("DELIVERY_OTP", "Delivery otp"),
                    ("PACKAGE_DELIVERED", "Package delivered"),
                    ("DELIVERY_FAILED", "Delivery failed"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("SELLER_CANCELLED_ORDER", "Seller cancelled order"),
                    ("BUYER_CANCELLED_ORDER", "Buyer cancelled order"),
                    ("AUTO_CANCELLED", "Auto cancelled"),
                    ("RETURN_REQUESTED", "Return requested"),
                    ("RETURN_ACCEPTED", "Return accepted"),
                    ("RETURN_RECEIVED", "Return received"),
                    ("REFUND_INITIATED", "Refund initiated"),
                    ("REFUND_HOLD", "Refund hold"),
                    ("REFUND_RELEASED", "Refund released"),
                    ("RETURN_OTP", "Return otp"),
                    ("COMMENTED", "Commented"),
                    ("CONTENT_LIKED", "content_liked"),
                    ("SOMEONE_FOLLOWED_ENTIY", "Someone_followed_entity"),
                    ("SOMEONE_COMMENTED_ON_CONTENT", "Someone_commented_on_content"),
                    ("SOMEONE_REPOSTED_CONTENT", "Someone reposted content"),
                ],
                max_length=100,
                null=True,
            ),
        ),
    ]
