# Generated by Django 4.2.7 on 2024-09-27 10:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0073_store_first_verified_date"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="deliverysettings",
            name="distance_based_max_deliveryfee",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="deliverysettings",
            name="no_deliveryfee_maxvalue",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="deliverysettings",
            name="no_deliveryfee_products",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="refundandwarranty",
            name="return_period",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="refundandwarranty",
            name="warranty_period_no",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="trustcenter",
            name="trustscore",
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
    ]
