from rest_framework import status, generics
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormParser

from django.db.models import Q

from drf_yasg.utils import swagger_auto_schema
from .models import FeedBack, FeedBackFiles
from .serializers import (
    GetFeedBackSerializer,
    FeedBackSerializer,
    FeedBackFilesSerializer,
)


class FeedBackAv(APIView):
    @swagger_auto_schema(operation_summary="get list of feed back")
    def get(self, request):
        """get all feed back"""
        # instance = FeedBack.objects.filter(Q(feedback_type=FeedBack.Feedback_Type.REPORT) |
        #                                    Q(feedback_type=FeedBack.Feedback_Type.SUGGESTION)
        #                                    ).prefetch_related('feedback_files').order_by("-created_date")
        #
        # instance = FeedBack.objects.exclude(
        #     Q(feedback_type=FeedBack.Feedback_Type.REPORT) &
        #     Q(screen_category='Account deletion')
        # ).prefetch_related('feedback_files').order_by("-created_date")

        instance = FeedBack.objects.filter(
            Q(feedback_type=FeedBack.Feedback_Type.REPORT) |
            Q(feedback_type=FeedBack.Feedback_Type.SUGGESTION) | Q(feedback_type=FeedBack.Feedback_Type.USER_REPORT)
        ).exclude(
            Q(feedback_type=FeedBack.Feedback_Type.REPORT) &
            Q(screen_category='Account deletion')
        ).prefetch_related('feedback_files').order_by("-created_date")

        serializer = GetFeedBackSerializer(instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Add feedback", request_body=FeedBackSerializer
    )
    def post(self, request):

        """create feed back"""
        serializer = FeedBackSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            data = serializer.data
            if request.data['feedback_type'] == FeedBack.Feedback_Type.USER_RESPONSE or request.data['feedback_type'] == FeedBack.Feedback_Type.SWADESIC_RESPONSE:
                feedback_id = request.data['feedback_id']
                instance = FeedBack.objects.get(feedback_id=feedback_id)
                responses = instance.responses
                if responses:
                    responses_list = responses.split('|')
                    responses_list.append(data['feedback_id'])
                    responses_list_to_string = '|'.join([str(elem) for elem in responses_list])
                    instance.responses = responses_list_to_string
                else:
                    instance.responses = str(data['feedback_id'])
                instance.save(update_fields=['responses'])
            return Response(
                {"message": "success", "data": data}, status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class FeedBackDetailsAV(APIView):
    @swagger_auto_schema(operation_summary="Get single feedback")
    def get(self, request, feedback_id):
        """get a feedback by id."""
        try:
            instance = FeedBack.objects.get(pk=feedback_id)
        except FeedBack.DoesNotExist:
            return Response(
                {"Error": "store not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = GetFeedBackSerializer(instance, context={"response": True})
        lst_data = [serializer.data]
        return Response(
            {"message": "success", "data": lst_data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Edit feedback", request_body=FeedBackSerializer
    )
    def put(self, request, feedback_id):

        """Edit feedback"""
        instance = FeedBack.objects.get(pk=feedback_id)
        serializer = FeedBackSerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(operation_summary="Delete feedback")
    def delete(self, request, feedback_id):

        """Delete feedback"""
        instance = FeedBack.objects.get(pk=feedback_id)
        instance.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class FeedBackFilesAv(APIView):
    parser_classes = (MultiPartParser, FormParser)
    @swagger_auto_schema(
        auto_schema=None, operation_summary="get list of feedback files"
    )
    def get(self, request, feedback_id):
        """get all feedback files"""
        instance = FeedBackFiles.objects.filter(feedback=feedback_id).order_by(
            "-created_date"
        )
        serializer = FeedBackFilesSerializer(instance, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_summary="Add feedback files", request_body=FeedBackFilesSerializer
    )
    def post(self, request, feedback_id):

        """create feed back file"""

        if "images" in request.FILES:
            s_data = {"feedback": feedback_id}
            images = request.FILES.getlist("images")
            for image in images:
                s_data["images"] = image
                serializer = FeedBackFilesSerializer(data=s_data)
                if serializer.is_valid():
                    serializer.save()
        if "attachments" in request.FILES:
            a_data = {"feedback": feedback_id}
            attachments = request.FILES.getlist("attachments")
            for attachment in attachments:
                a_data["attachments"] = attachment
                serializer = FeedBackFilesSerializer(data=a_data)
                if serializer.is_valid():
                    serializer.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class FeedBackFilesDetailsAV(APIView):
    @swagger_auto_schema(operation_summary="Get single feedback")
    def get(self, request, feedback_file_id):
        """get a feedback file by id."""
        try:
            instance = FeedBackFiles.objects.get(pk=feedback_file_id)
        except FeedBackFiles.DoesNotExist:
            return Response(
                {"Error": "store not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = FeedBackFilesSerializer(instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        auto_schema=None,
        operation_summary="Edit feedback", request_body=FeedBackSerializer
    )
    def put(self, request, feedback_file_id):

        """Edit feedback file"""
        instance = FeedBackFiles.objects.get(pk=feedback_file_id, deleted=False)
        serializer = FeedBackFilesSerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_404_NOT_FOUND,
            )

    @swagger_auto_schema(operation_summary="Delete feedback file")
    def delete(self, request, feedback_file_id):

        """Delete feedback file"""
        instance = FeedBackFiles.objects.get(pk=feedback_file_id)
        instance.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)

class UpvoteFeedback(generics.UpdateAPIView):
    def patch(self, request, *args, **kwargs):
        feedback_id = kwargs['feedback_id']
        voter_reference = request.data['voter_reference']

        instance = FeedBack.objects.get(feedback_id=feedback_id)
        upvote_users = instance.upvote_users

        if upvote_users:
            upvote_users_list = upvote_users.split('|')
            if voter_reference in upvote_users_list:
                instance.upvote_count -= 1
                upvote_users_list.remove(voter_reference)
                upvote_users_list_to_string = '|'.join([elem for elem in upvote_users_list])
                instance.upvote_users = upvote_users_list_to_string
            else:
                instance.upvote_count += 1
                upvote_users_list.append(voter_reference)
                upvote_users_list_to_string = '|'.join([elem for elem in upvote_users_list])
                instance.upvote_users = upvote_users_list_to_string
        else:
            instance.upvote_count += 1
            instance.upvote_users = voter_reference
        instance.save(update_fields=['upvote_count', 'upvote_users'])
        return Response({"message": "success"}, status=status.HTTP_200_OK)
