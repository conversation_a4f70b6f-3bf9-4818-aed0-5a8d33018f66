# Generated by Django 3.2.13 on 2023-01-25 06:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0028_auto_20230124_1104'),
    ]

    operations = [
        migrations.CreateModel(
            name='SearchedItems',
            fields=[
                ('searched_item_id', models.AutoField(primary_key=True, serialize=False)),
                ('user_reference', models.CharField(max_length=20)),
                ('search_input_text', models.CharField(max_length=20)),
                ('search_item', models.CharField(max_length=20)),
                ('search_item_type', models.CharField(blank=True, choices=[('USER', 'User'), ('PRODUCT', 'Product'), ('STORE', 'Store')], max_length=20, null=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('is_deleted', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
            ],
            options={
                'verbose_name_plural': 'searched items',
                'db_table': '"store"."searched_items"',
            },
        ),
        migrations.CreateModel(
            name='SearchHistory',
            fields=[
                ('search_history_id', models.Auto<PERSON>ield(primary_key=True, serialize=False)),
                ('user_reference', models.CharField(max_length=20)),
                ('search_input_text', models.CharField(max_length=20)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('is_deleted', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'search history',
                'db_table': '"store"."search_history"',
            },
        ),
    ]
