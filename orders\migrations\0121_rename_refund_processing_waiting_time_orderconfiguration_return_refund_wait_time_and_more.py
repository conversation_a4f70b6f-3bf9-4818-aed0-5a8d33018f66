# Generated by Django 4.2.7 on 2024-08-11 07:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0120_order_pg_gst_tax_perc_order_pg_transctionfee_perc_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="orderconfiguration",
            old_name="refund_processing_waiting_time",
            new_name="return_refund_wait_time",
        ),
        migrations.AddField(
            model_name="orderconfiguration",
            name="cancel_refund_wait_time",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="orderconfiguration",
            name="fulfillment_window",
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
