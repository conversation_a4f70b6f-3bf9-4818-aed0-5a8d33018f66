from rest_framework import serializers
from ..models import Banner, ReservedHandles


class BannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Banner
        fields = '__all__'


class BannerSerializerV2(serializers.ModelSerializer):
    class Meta:
        model = Banner
        fields = [
            'page_reference',
            'page_url',
            'banner_location',
            'current_user_type',
            'minimum_targeted_version',
            'opening_page',
            'image_url'
        ]

    def to_internal_value(self, data):
        # Mapping old keys to new keys
        mapping = {
            'reference': 'page_reference',
            'response_link': 'page_url',
            'banner_location': 'banner_location',
            'targeted_entity': 'current_user_type',
            'targeted_version': 'minimum_targeted_version',
            'banner_image': 'image_url'
        }

        # Replace old keys with new keys in the data dictionary
        for old_key, new_key in mapping.items():
            if old_key in data:
                data[new_key] = data[old_key]

        return super(BannerSerializer, self).to_internal_value(data)


class ReservedHandlesSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReservedHandles
        fields = '__all__'
