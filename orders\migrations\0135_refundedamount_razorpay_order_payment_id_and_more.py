# Generated by Django 4.2.7 on 2024-08-30 14:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "orders",
            "0134_orderpayout_flash_points_orderpayout_infinity_points_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="refundedamount",
            name="razorpay_order_payment_id",
            field=models.CharField(blank=True, max_length=25, null=True),
        ),
        migrations.AlterField(
            model_name="suborder",
            name="cancelled_by",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("buyer", "buyer"),
                    ("seller", "seller"),
                    ("auto_cancelled", "auto_cancelled"),
                ],
                max_length=50,
                null=True,
            ),
        ),
    ]
