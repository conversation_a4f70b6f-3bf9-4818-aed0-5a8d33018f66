# Generated by Django 3.2.13 on 2022-11-28 07:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0018_auto_20221111_1736"),
        ("orders", "0040_orderpayout"),
    ]

    operations = [
        migrations.CreateModel(
            name="PayoutBalance",
            fields=[
                (
                    "order_payout_balance_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "life_time_balance",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("current_balance", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "store_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="store_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "order payout balance",
                "db_table": '"order"."order_payout_balance"',
            },
        ),
    ]
