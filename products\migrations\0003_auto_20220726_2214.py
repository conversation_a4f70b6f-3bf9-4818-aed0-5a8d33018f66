# Generated by Django 3.2.13 on 2022-07-26 16:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0002_address_storelink_userstore"),
        ("products", "0002_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="category",
            options={"verbose_name_plural": "categories"},
        ),
        migrations.AlterModelOptions(
            name="product",
            options={"verbose_name_plural": "products"},
        ),
        migrations.CreateModel(
            name="UserProduct",
            fields=[
                ("userproductid", models.AutoField(primary_key=True, serialize=False)),
                ("is_saved", models.BooleanField(null=True)),
                ("is_ordered", models.BooleanField(default=False)),
                ("is_visited", models.BooleanField(default=False)),
                ("modified_date", models.DateTimeField(auto_now=True)),
                (
                    "productid",
                    models.ForeignKey(
                        blank=True,
                        db_column="productid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                    ),
                ),
                (
                    "userid",
                    models.ForeignKey(
                        blank=True,
                        db_column="userid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="get_users",
                        to="stores.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "user products",
                "db_table": '"product"."user_product"',
            },
        ),
        migrations.CreateModel(
            name="ProductImages",
            fields=[
                ("productimageid", models.AutoField(primary_key=True, serialize=False)),
                ("product_image", models.ImageField(upload_to="post_images")),
                ("active", models.BooleanField(default=True)),
                ("created_by", models.CharField(max_length=100)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_by", models.CharField(max_length=100)),
                ("modified_date", models.DateField(auto_now=True)),
                ("reorder", models.IntegerField()),
                (
                    "productid",
                    models.ForeignKey(
                        db_column="productid",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prod_images",
                        to="products.product",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "product images",
                "db_table": '"product"."product_images"',
            },
        ),
    ]
