# Generated by Django 3.2.13 on 2022-10-13 02:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0011_auto_20221004_1008"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductVersion",
            fields=[
                (
                    "product_version_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("productid", models.IntegerField()),
                ("product_reference", models.CharField(max_length=15)),
                (
                    "product_name",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                ("product_description", models.TextField(blank=True, null=True)),
                ("brand_name", models.CharField(default=True, max_length=50)),
                ("product_price", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "product_images",
                    models.ImageField(
                        blank=True, null=True, upload_to="version_product_images"
                    ),
                ),
                (
                    "product_hashtags",
                    models.Char<PERSON>ield(blank=True, max_length=300, null=True),
                ),
                (
                    "product_version",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "product version",
                "db_table": '"product"."product_version"',
            },
        ),
        migrations.CreateModel(
            name="ProductVersionManagement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product_column",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "severity",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("MAJOR", "major"),
                            ("MINOR", "minor"),
                            ("IDLE", "idle"),
                        ],
                        max_length=200,
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "product version management",
                "db_table": '"product"."product_version_management"',
            },
        ),
        migrations.AddField(
            model_name="product",
            name="product_version",
            field=models.CharField(default="1.0.0", max_length=255),
        ),
    ]
