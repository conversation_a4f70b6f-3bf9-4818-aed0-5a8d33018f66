# Generated by Django 3.2.13 on 2022-12-15 08:08

from django.db import migrations


def enter_user_reference_suborder(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in user_reference column of SubOrder table, where
    # order_number of SubOrder model is same as order_number in Order table.

    SubOrder = apps.get_model("orders", "SubOrder")
    Order = apps.get_model("orders", "Order")
    for item in SubOrder.objects.all():
        if Order.objects.filter(order_number=item.order_number).exists():
            order = Order.objects.get(order_number=item.order_number)
            item.user_reference = order.user_reference
        else:
            item.user_reference = None
        item.save(update_fields=["user_reference"])


def enter_user_reference_suborder_reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    SubOrder = apps.get_model("orders", "SubOrder")
    for item in SubOrder.objects.all():
        item.user_reference = None
        item.save(update_fields=["user_reference"])


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0060_auto_20221215_1256"),
    ]

    operations = [
        migrations.RunPython(
            enter_user_reference_suborder, enter_user_reference_suborder_reverse_func
        )
    ]
