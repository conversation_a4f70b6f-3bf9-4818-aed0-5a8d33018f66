from django.db.models import Q
from django.views.generic import TemplateView, DetailView
from django.shortcuts import get_object_or_404
from content.models import Story, Posts
from products.models import Product
from stores.store_api.models import Store
from stores.store_settings_api.models import TrustCenter, DeliverySettings
from GraphDB.queries import dbqueries
from content.models import Comments

class StoreHomeView(TemplateView):
    template_name = 'seo/store_home.html'

    def get_store_stories(self, store_reference, limit=10):
        """Get stories for a store using Neo4j relationships"""
        # Get story references from Neo4j
        story_refs = dbqueries.get_entity_stories(store_reference)
        
        # Fetch stories from Django ORM
        if story_refs:
            return Story.objects.filter(story_reference__in=story_refs).order_by('-created_at')[:limit]
        return Story.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        store_reference = kwargs.get('store_reference')
        store = get_object_or_404(Store, store_reference=store_reference)
        
        # Get store location and delivery settings
        store_location = TrustCenter.objects.filter(store_reference=store).first()
        delivery_settings = DeliverySettings.objects.filter(store_reference=store).first()
        
        # Get recent products
        recent_products = Product.objects.filter(
            store_reference=store,
            deleted=False
        ).order_by('-created_date')[:10]
        
        # Get recent posts
        recent_posts = Posts.objects.filter(
            store_reference=store,
            is_deleted=False
        ).order_by('-created_date')[:10]
        
        # Get recent stories using Neo4j relationship
        recent_stories = self.get_store_stories(store_reference)
        
        context.update({
            'store': store,
            'store_location': store_location,
            'delivery_settings': delivery_settings,
            'recent_products': recent_products,
            'recent_posts': recent_posts,
            'recent_stories': recent_stories,
            'meta': {
                'title': f"{store.store_name} - Swadesic Store",
                'description': store.store_desc,
                'image': store.icon.url if store.icon else None,
            }
        })
        return context

class StoreProductsView(TemplateView):
    template_name = 'seo/store_products.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        store_reference = kwargs.get('store_reference')
        store = get_object_or_404(Store, store_reference=store_reference)
        
        products = Product.objects.filter(
            store_reference=store,
            deleted=False,
            hide=False
        ).order_by('-created_date')
        
        context.update({
            'store': store,
            'products': products,
            'meta': {
                'title': f"Products - {store.store_name}",
                'description': f"Browse all products from {store.store_name}",
                'image': store.icon.url if store.icon else None,
            }
        })
        return context

class StorePostsView(TemplateView):
    template_name = 'seo/store_posts.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        store_reference = kwargs.get('store_reference')
        store = get_object_or_404(Store, store_reference=store_reference)
        
        posts = Posts.objects.filter(
            store_reference=store,
            is_deleted=False
        ).order_by('-created_date')
        
        context.update({
            'store': store,
            'posts': posts,
            'meta': {
                'title': f"Posts - {store.store_name}",
                'description': f"Read all posts from {store.store_name}",
                'image': store.icon.url if store.icon else None,
            }
        })
        return context

class ProductDetailView(TemplateView):
    template_name = 'seo/product_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        product_reference = kwargs.get('product_reference')
        product = get_object_or_404(Product, 
            product_reference=product_reference,
            deleted=False,
            hide=False
        )
        store = product.store_reference
        
        # Get store location and delivery settings
        store_location = TrustCenter.objects.filter(store_reference=store).first()
        delivery_settings = DeliverySettings.objects.filter(store_reference=store).first()
        
        # Split hashtags
        hashtags = []
        if product.hashtags:
            hashtags = [tag.strip() for tag in product.hashtags.split(',') if tag.strip()]
        
        # Get comments with more than 10 characters
        comments = Comments.objects.filter(
            Q(event_reference=product_reference) & 
            Q(comment_text__isnull=False) & 
            Q(comment_text__regex=r'.{10,}') & 
            Q(is_deleted=False)
        ).order_by('-created_date')
        
        context.update({
            'store': store,
            'store_location': store_location,
            'product': product,
            'delivery_settings': delivery_settings,
            'product_comments': comments,
            'hashtags': hashtags,
            'meta': {
                'title': f"{product.product_name} - {store.store_name}",
                'description': product.product_description[:160] if product.product_description else None,
                'image': product.prod_images.first().product_image.url if product.prod_images.exists() else None,
            }
        })
        return context

class PostDetailView(TemplateView):
    template_name = 'seo/post_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        post_reference = kwargs.get('post_reference')
        post = get_object_or_404(Posts, 
            post_reference=post_reference,
            is_deleted=False
        )
        entity = post.store_reference if post.store_reference else post.user_reference
        
        # Get non-deleted comments
        comments = Comments.objects.filter(
            event_reference=post_reference,
            is_deleted=False
        ).order_by('-created_date')
        
        context.update({
            'entity': entity,
            'post': post,
            'post_comments': comments,
            'meta': {
                'title': f"Post by {entity.storehandle if isinstance(entity, Store) else entity.user_name} - Swadesic",
                'description': post.post_text[:160] if post.post_text else None,
                'image': post.post_images.first().post_image.url if post.post_images.exists() else None,
            }
        })
        return context



class StoryDetailView(DetailView):
    model = Story
    template_name = 'seo/story_detail.html'
    context_object_name = 'story'
    slug_field = 'story_reference'
    slug_url_kwarg = 'story_reference'

    def get_store_from_tagged_content(self, story):
        """Get store reference from the first tagged product or post"""
        if not story.tagged_content:
            return None

        for tag in story.tagged_content:
            if tag.startswith('P'):  # Product reference
                try:
                    product = Product.objects.get(product_reference=tag)
                    return product.store_reference
                except Product.DoesNotExist:
                    continue
            elif tag.startswith('PO'):  # Post reference
                try:
                    post = Posts.objects.get(post_reference=tag)
                    return post.store_reference
                except Posts.DoesNotExist:
                    continue
        return None

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        story = self.object
        
        # Get store from tagged content
        store = self.get_store_from_tagged_content(story)
        if not store:
            # Handle case where no store is found
            context.update({
                'error': 'Store not found',
                'story': story,
            })
            return context
        
        # Get store location from trust center
        store_location = TrustCenter.objects.filter(store_reference=store).first()
        
        # Get first section's image if it exists
        first_image = None
        if story.sections:
            for section in story.sections:
                if section.get('type') == 'image' and section.get('content'):
                    first_image = section['content']
                    break
        
        context.update({
            'store': store,
            'store_location': store_location,
            'story': story,
            'meta': {
                'title': f"Story by {store.store_name}",
                'description': story.sections[0].get('content', '')[:160] if story.sections else None,
                'image': first_image,
            }
        })
        return context
