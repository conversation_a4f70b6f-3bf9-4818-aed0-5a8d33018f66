# Generated by Django 4.2.7 on 2024-08-11 13:46

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0120_order_pg_gst_tax_perc_order_pg_transctionfee_perc_and_more"),
    ]

    operations = [
        migrations.RunSQL(
            sql='''
                ALTER TABLE "order".order_payout_balance DROP CONSTRAINT IF EXISTS order_payout_balance_current_balance_check;
                ALTER TABLE "order".order_payout_balance DROP CONSTRAINT IF EXISTS order_payout_balance_life_time_balance_check;
                ALTER TABLE "order".order_payout_balance DROP CONSTRAINT IF EXISTS order_payout_balance_missed_revenue_check;
            ''',
            reverse_sql=''  # Leave this empty to ensure the constraints are not reintroduced
        ),
    ]
