# Generated by Django 4.2.7 on 2024-10-29 11:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("content", "0023_alter_comments_level"),
    ]

    operations = [
        migrations.CreateModel(
            name="Story",
            fields=[
                ("story_id", models.AutoField(primary_key=True, serialize=False)),
                ("story_reference", models.<PERSON>r<PERSON>ield(max_length=20, unique=True)),
                ("title", models.Char<PERSON>ield(max_length=255)),
                ("sections", models.JSO<PERSON>ield()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "stories",
                "db_table": '"content"."stories"',
            },
        ),
    ]
