from django.db import models
import datetime
from django.utils.timezone import utc
from ..models import Product
from users.user_api.models import User
from django.utils.translation import gettext_lazy as _
from common.util.support_helper import compress

class Comment(models.Model):
    class Comment_Type(models.TextChoices):
        REVIEW = "review", _("Review")
        COMMENT = "comment", _("Comment")
        QUESTION = "question", _("Question")

    commentid = models.AutoField(primary_key=True)
    productid = models.IntegerField(null=True, blank=True)
    product_reference = models.ForeignKey(
        "products.Product",
        to_field="product_reference",
        on_delete=models.CASCADE,
        related_name='product_comments',
        db_column="product_reference",
    )
    product_version = models.CharField(max_length=10, null=True, blank=True)
    reference = models.CharField(max_length=10, null=True, blank=True)
    comments = models.TextField(null=True, blank=True)
    comment_type = models.CharField(max_length=100, choices=Comment_Type.choices)
    claps = models.IntegerField(default=0)
    clapped_users = models.CharField(max_length=100000, null=True, blank=True)
    review = models.FloatField(default=0)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now_add=True)
    report_comment = models.BooleanField(default=False)
    order_reference = models.CharField(max_length=30, null=True, blank=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__comments = self.comments

    def save(self, *args, **kwargs):
        if self.comments != self.__comments:
            self.modified_date = datetime.datetime.utcnow().replace(tzinfo=utc)
        if self.commentid is None:
            self.productid = self.product_reference.productid
        product_version = Product.objects.values_list("product_version", flat=True).get(
            product_reference=self.product_reference.product_reference
        )
        self.product_version = product_version
        super().save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "comments"
        db_table = '"product"."comment"'


class ReviewImages(models.Model):
    review_image_id = models.AutoField(primary_key=True)
    commentid = models.ForeignKey(
        "products.Comment",
        on_delete=models.CASCADE,
        related_name="comment_review_image",
        db_column="commentid",
    )
    created_by = models.CharField(max_length=10, blank=True, null=True
    )
    review_image = models.ImageField(upload_to="review_images")
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if self.review_image:
            new_image = compress(self.review_image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
            self.review_image = new_image
        super(ReviewImages, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "review images"
        db_table = '"product"."review_images"'


class Reply(models.Model):
    replyid = models.AutoField(primary_key=True)
    commentid = models.ForeignKey(
        "products.Comment",
        on_delete=models.CASCADE,
        related_name="replies",
        db_column="commentid",
    )
    product_version = models.CharField(max_length=100, null=True, blank=True)
    reference = models.CharField (max_length=10, null=True, blank=True)
    reply = models.TextField()
    claps = models.IntegerField(default=0)
    clapped_users = models.CharField(max_length=300, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now_add=True)
    report_reply = models.BooleanField(default=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__reply = self.reply

    def save(self, *args, **kwargs):
        if self.reply != self.__reply:
            self.modified_date = datetime.datetime.utcnow().replace(tzinfo=utc)
        product_version = Product.objects.values_list("product_version", flat=True).get(
            product_reference=self.commentid.product_reference.product_reference
        )
        self.product_version = product_version
        super().save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "replies"
        db_table = '"product"."reply"'
