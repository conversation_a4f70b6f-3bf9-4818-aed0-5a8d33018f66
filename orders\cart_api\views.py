import datetime
from rest_framework import generics, mixins
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from stores.store_api.models import Store
from users.user_api.models import User
from .models import CartItem
from .serializers import (
    CartSerializer,
    GetCartProductDetailsSerializer,
    UserProfileOrderPageSerializer,
)
import logging
from ..helpers import input_formatter_cart_to_fee_calculation_wrapper, fee_calculation_wrapper
from swadesic.settings import IS_PRODUCTION, RESPONSE_STRUCTURE_IS_PRODUCTION

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class AddToOrRemoveFromCart(
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    mixins.DestroyModelMixin,
    generics.GenericAPIView,
):
    queryset = CartItem.new_objects.all()
    serializer_class = CartSerializer

    @swagger_auto_schema(auto_schema=None)
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Add to cart",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "product_reference",
                "storeid",
                "userid",
                "product_quantity",
                "cart_product_status",
            ],
            properties={
                "product_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "storeid": openapi.Schema(type=openapi.TYPE_INTEGER),
                "userid": openapi.Schema(type=openapi.TYPE_INTEGER),
                "product_quantity": openapi.Schema(type=openapi.TYPE_INTEGER),
                "cart_product_status": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):

        """
        if cart for a particular user, store, product already exists, then update the
        product_quantity alone. Otherwise, create a new cart.

        To add cart items and delete cart items uses same api, with passing cart_id in the url.

        so while adding a cart item always keep the cart_id in url as 0.
        """
        store_id = request.data["storeid"]
        product_reference = request.data["product_reference"]
        user_id = request.data["userid"]
        product_quantity = request.data["product_quantity"]
        logger.info("Entered add to cart api")
        if CartItem.new_objects.filter(
            storeid=store_id,
            product_reference=product_reference,
            userid=user_id,
            cart_product_status="IN_CART",
        ).exists():
            cart_instance = CartItem.new_objects.get(
                storeid=store_id,
                product_reference=product_reference,
                userid=user_id,
                cart_product_status="IN_CART",
            )
            cart_instance.product_quantity = product_quantity
            cart_instance.save()
            serializer = self.get_serializer(cart_instance)
            logger.info("Existing cart updated and exited add to cart api")
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            #  if not above condition, create a new cart instance
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                self.perform_create(serializer)
                logger.info("created a new cart and exited add to cart api.")
                return Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "error", "data": serializer.errors},
                    status=status.HTTP_200_OK,
                )

    @swagger_auto_schema(operation_summary="Delete a cart")
    def delete(self, *args, **kwargs):
        """Delete a cart item by passing cart_id"""
        instance = self.get_object()
        instance.is_deleted = True
        instance.save()
        return Response({"message": "success"}, status=status.HTTP_200_OK)

class DeleteCarItems(generics.DestroyAPIView):

    @swagger_auto_schema (operation_summary="Delete a cart_items")
    def delete(self,request, *args, **kwargs):
        """Delete cart item"""
        cart_item_list = request.data['cart_item_list']
        CartItem.new_objects.filter(cartitemid__in=cart_item_list).update(is_deleted=True)
        return Response ({"message": "success"}, status=status.HTTP_200_OK)


class UpdateProductQuantity(generics.UpdateAPIView):

    queryset = CartItem.new_objects.all()
    serializer_class = CartSerializer

    @swagger_auto_schema(
        operation_summary="Update product quantity of a cart",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["product_quantity"],
            properties={"product_quantity": openapi.Schema(type=openapi.TYPE_INTEGER)},
        ),
    )
    def patch(self, request, *args, **kwargs):
        """update cart_product_quantity by cart id"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class GetCartItems(mixins.ListModelMixin, generics.GenericAPIView):
    @swagger_auto_schema(operation_summary="get all current cart item ids of a user.")
    def get(self, request, *args, **kwargs):

        """
        get all the cart items of a user. This will filter all the items with userid as given and cart_status as 'IN_CART'
        to get all the items that are currently inside the cart. Result is list of cart item ids.
        """
        try:
            user_id = self.kwargs["userid"]
            cart_items = CartItem.new_objects.filter(
                userid=user_id, cart_product_status="IN_CART"
            ).values_list("cartitemid", flat=True)
            return Response(
                {"message": "success", "cart_item_id": cart_items},
                status=status.HTTP_200_OK,
            )
        except CartItem.DoesNotExists:
            return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)


class GetCartDetails(APIView):
    @swagger_auto_schema(
        operation_summary="Get cart details",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["user_id", "cart_items_list"],
            properties={
                "user_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                "cart_items_list": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(type=openapi.TYPE_INTEGER),
                ),
            },
        ),
    )
    def post(self, request):
        """Get all cart details, that include all the store and product details of cart items of a user."""
        logger.info("Entered get cart details api")
        user_id = request.data["user_id"]
        cart_items_list = request.data["cart_items_list"]

        cart_items = CartItem.new_objects.filter(
            userid=user_id, cartitemid__in=cart_items_list
        ).values("storeid", "product_reference")
        input_data = list(cart_items)

        final_lst = {}
        for d in input_data:
            final_lst.setdefault(d["storeid"], []).append(d["product_reference"])
        final_lst = [
            {"storeid": k, "product_reference": v} for k, v in final_lst.items()
        ]

        store = [
            Store.objects.get(storeid=ids["storeid"])
            for ids in final_lst
        ]

        serializer = GetCartProductDetailsSerializer(
            store, many=True, context={"value_to_pass": final_lst, "user_id": user_id}
        )
        logger.info("Exited get cart details api")
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class GetUserDetails(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = UserProfileOrderPageSerializer

    @swagger_auto_schema(operation_summary="Get customer details on cart page")
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


# del fee cal with pandas
class FeeCalculation(APIView):
    @swagger_auto_schema(
        operation_summary="calculate the cart total",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["user_id", "cart_items_list"],
            properties={
                "user_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                "cart_items_list": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(type=openapi.TYPE_INTEGER),
                ),
            },
        ),
    )
    def post(self, request):
        logger.info("Entered fee calculation api")

        user_id = request.data["user_id"]
        cart_items_list = request.data["cart_items_list"]

        final_input = input_formatter_cart_to_fee_calculation_wrapper(user_id, cart_items_list)
        (
            grand_total,
            order_amount,
            order_delivery_fee,
            store_list,
            order_delivery_fee_after_promotion,
            grant_total_after_promotion,
            store_order_df,
            order_amount_list,
            order_df_list
        ) = fee_calculation_wrapper(final_input)
        logger.info("exited fee calculation api")

        order_amount_str = self.add_rupee_sign(order_amount)
        order_delivery_fee_str = self.add_rupee_sign(order_delivery_fee)
        grand_total_str = self.add_rupee_sign(grand_total)

        order_amount_breakup = self.breakup_formatting(order_amount_list)
        order_df_breakup = self.breakup_formatting(order_df_list)

        cart_fee_breakup = []

        product_total_obj = self.output_formatter("Product total", order_amount_breakup, order_amount_str)
        df_obj = self.output_formatter("Delivery fees", order_df_breakup, order_delivery_fee_str)
        promotions_obj = self.output_formatter("Promotions", None, u'\u20B9'"0")
        grand_total_obj = self.output_formatter("Grand total", None, grand_total_str)

        cart_fee_breakup.append(product_total_obj)
        cart_fee_breakup.append(df_obj)
        cart_fee_breakup.append(promotions_obj)
        cart_fee_breakup.append(grand_total_obj)
        return Response(
            {
                "message": "success",
                "cart_fees": cart_fee_breakup,
                "amount_split": store_list,
            },
            status=status.HTTP_200_OK,
        )

    @staticmethod
    def output_formatter(text, subtext, value):
        return {
            "order_breakup_item_text": text,
            "order_breakup_item_subtext": subtext,
            "order_breakup_item_value": value,
        }

    @staticmethod
    def add_rupee_sign(num):
        num_to_string = str(num)
        result = u'\u20B9'"{}".format(num_to_string)
        return result

    def breakup_formatting(self, input_list):

        # step 1: convert the input lists of list to a string. each list element is separated by '+' and each list
        # separated by ','

        # input -> [[1600, 200, 100, 2000, 100], [500, 600, 960]]
        # output -> "1600+200+100+2000+100,500+600+960"

        list_to_sting = ','.join([' + '.join(map(self.add_rupee_sign, sublist)) for sublist in input_list])

        # Step 2: again split the string by commas
        split_parts = list_to_sting.split(',')

        # Step 3: Add opening and closing brackets to each split part
        formatted_parts = ['[' + part + ']' for part in split_parts]

        # Step 4: Join the formatted parts with commas
        # final output -> "[1600+200+100+2000+100]+[500+600+960]"
        output_string = '+'.join(formatted_parts)

        return output_string
