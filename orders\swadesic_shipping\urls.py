from django.urls import path
from .views import (
    CheckServiceabilityView,
    CreateShiprocketOrderView,
    CreateShiprocketReturnOrderView,
    CreateShippingOrderView,
    TrackShipmentView,
    GetShippingBalanceView,
    ShippingBalanceHistoryView,
    GetPackageDetailsView,
    GenerateShippingLabelView,
    ShiprocketWebhookView,
    AddShippingBalanceAndInitiatePayment,
    ValidateShippingBalancePayment,
    AddShippingBalanceFromAccountBalance
)

# Available URLs:
# POST /order/check-serviceability/ - Check courier serviceability
# POST /order/create-shipping-order/ - Create new shipping order
# GET /order/track-shipment/{package_number}/ - Track shipment status
# GET /order/shipping-balance/ - Get store's shipping balance
# GET /order/shipping-history/ - Get store's shipping history
# GET /order/package-details/<package_number>/ - Get package details by package number
# POST /order/generate-shipping-label/ - Generate shipping label for a package
# POST /order/shiprocket-webhook/ - Handle Shiprocket tracking updates

urlpatterns = [
    path('check-serviceability/', CheckServiceabilityView.as_view(), name='check-serviceability'),
    path('create-shipping-order/', CreateShippingOrderView.as_view(), name='create-shipping-order'),
    path('create-shiprocket-order/', CreateShiprocketOrderView.as_view(), name='create-shiprocket-order'),
    path('create-shiprocket-return-order/', CreateShiprocketReturnOrderView.as_view(), name='create-shiprocket-return-order'),
    path('track-shipment/<str:package_number>/', TrackShipmentView.as_view(), name='track-shipment'),
    path('shipping-balance/', GetShippingBalanceView.as_view(), name='shipping-balance'),
    path('shipping-balance-history/', ShippingBalanceHistoryView.as_view(), name='shipping-history'),
    path('package-details/<str:package_number>/', GetPackageDetailsView.as_view(), name='package-details'),
    path('generate-shipping-label/', GenerateShippingLabelView.as_view(), name='generate-shipping-label'),
    path('update-swadesic-shipping-details-wh/', ShiprocketWebhookView.as_view(), name='shiprocket-webhook'),
    path('add-shipping-balance/', AddShippingBalanceAndInitiatePayment.as_view(), name='add-shipping-balance'),
    path('validate-shipping-balance-payment/', ValidateShippingBalancePayment.as_view(), name='validate-shipping-balance-payment'),
    path('add-shipping-balance-from-account-balance/', AddShippingBalanceFromAccountBalance.as_view(), name='add-shipping-balance-from-account-balance')
]
