import token
import requests
import pyotp
import string
import random
import logging
from decouple import config
from typing import Dict, Any
from django.core.exceptions import ValidationError
import base64
from common.util.support_helper import client, generateKey, GenerateNotifications
from django.template.loader import get_template
from django.core.mail import EmailMultiAlternatives
from .models import User
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
import datetime
from datetime import datetime
from GraphDB.models import Neo4jUser
from django.db.models import Count
from .serializers import UserSerializer, NewUserCheckSerializer
from django.core.exceptions import ObjectDoesNotExist
from ..invite_api.models import UserRewards, RewardsHistory
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from general.models import RewardTransferConfig
from itertools import cycle
from stores.store_api.models import Store
from stores.store_reward_api.models import StoreRewards
from general.views import get_app_config
import os
from django.core.files import File
from django.conf import settings

# Get an instance of a logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def get_config():
    return get_app_config()

def google_get_access_token(*, code: str, redirect_uri: str) -> str:
    data = {
        'code': code,
        'client_id': config("GOOGLE_OAUTH2_CLIENT_ID"),
        'client_secret': config("GOOGLE_OAUTH2_CLIENT_SECRET"),
        'redirect_uri': redirect_uri,
        'grant_type': 'authorization_code'
    }

    response = requests.post(config("GOOGLE_ACCESS_TOKEN_OBTAIN_URL"), data=data)

    if not response.ok:
        raise ValidationError('Failed to obtain access token from Google.')

    access_token = response.json()['access_token']

    return access_token


def google_get_user_info(*, access_token:  str) -> Dict[str, Any]:
    response = requests.get(
        config('GOOGLE_USER_INFO_URL'),
        params={'access_token': access_token}
    )

    if not response.ok:
        raise ValidationError('Failed to obtain user info from Google.')

    return response.json()


def generate_otp(identifier):
    keygen = generateKey()
    key = base64.b32encode(keygen.returnValue(identifier).encode())  # Generating Key
    TOTP = pyotp.TOTP(key, interval=600)  # TOTP Model with 10-minute interval
    return TOTP.now()


def validate_otp(identifier, otp):
    logger.info(f"Validating OTP: {otp} for identifier: {identifier}")
    
    if not identifier or not otp:
        logger.warning("Missing identifier or OTP")
        return False
    
    try:
        keygen = generateKey()
        key = base64.b32encode(keygen.returnValue(identifier).encode())  # Generating Key
        TOTP = pyotp.TOTP(key, interval=600)  # TOTP Model with 10-minute interval
        
        # Check current, previous, and next time steps
        for time_step in [-1, 0, 1]:
            result = TOTP.verify(otp, valid_window=time_step)
            if result:
                logger.info(f" {identifier} OTP validation successful for time step: {time_step}")
                return True
        
        logger.info("OTP validation failed for all time steps")
        return False
    except Exception as e:
        logger.error(f"Error validating OTP: {str(e)}")
        return False


def send_otp_via_sms(phone_number, otp):
    autofetch_id = config('AUTOFETCH_ID')
    url = "https://www.fast2sms.com/dev/bulkV2"
    querystring = {"authorization": config('FAST2SMS_API_KEY'),
                   "sender_id": config("DLT_SENDER_ID"),
                   "message": config("MESSAGE_ID"),
                   "variables_values": f"{otp}|{autofetch_id}",
                   "route": "dlt",
                   "numbers": phone_number}

    headers = {
        'cache-control': "no-cache"
    }
    response = requests.request("GET", url, headers=headers, params=querystring)
    return response.text


def send_otp_via_email(email_id, otp):
    # Load the HTML content from the template file
    html_content = get_template('otp_card.html').render({'otp': otp})

    # Create the EmailMultiAlternatives object
    email = EmailMultiAlternatives(
        f'Swadesic verification OTP: {otp}',
        '',
        'Swadesic Team <<EMAIL>>',  # Change this to your email address
        [email_id]
    )
        # Add headers to reduce spam likelihood
    email.extra_headers = {
        'Reply-To': '<EMAIL>',
        'X-Priority': '1',  # High priority
    }

    # Attach the HTML content
    email.attach_alternative(html_content, "text/html")

    # Send the email
    try:
        email.send()
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")


def mask_email(email):
    try:
        local_part, domain_part = email.split('@')

        # Ensure that at least one character remains unmasked
        if len(local_part) <= 1:
            return email

        # Determine the range to mask (70% of the local part)
        length = len(local_part)
        if length < 5:
            mask_length = 3
        else:
            mask_length = int(length * 0.7)

        start_index = (length - mask_length) // 2
        end_index = start_index + mask_length

        # Convert the local part to a list of characters for mutability
        local_part_list = list(local_part)

        # Mask the selected range
        for i in range(start_index, end_index):
            local_part_list[i] = '*'

        # Convert the local part list back to a string
        masked_local_part = ''.join(local_part_list)

        return masked_local_part + '@' + domain_part

    except ValueError:
        raise ValueError("Invalid email address")


def generate_jwt_token(user):
    refresh = RefreshToken.for_user(user)
    token_response_data = {
        "message": "success",
        "user_exist": user.is_email_verified,
        'refresh': str(refresh),
        'access': str(refresh.access_token),
        'refresh_token_validity': datetime.now() + refresh.lifetime,
        'access_token_validity': datetime.now() + refresh.access_token.lifetime
    }
    return token_response_data


def generate_referral_code(name, invite_type='USER'):
    """
    Generate a unique referral code.
    invite_type : USER or STORE
    """
    prefix = "SWC" if invite_type.upper() == 'USER' else "SWS"
    name_part = name[:4].upper()
    alphabet = string.ascii_uppercase
    alphabet_cycle = cycle(alphabet)

    def generate_code(name_part):
        if len(name_part) == 4:
            for _ in range(256):  # All possible 2-digit hexadecimal combinations
                random_part = ''.join(random.choices(string.hexdigits.upper(), k=2))
                referral_code = f"{prefix}{name_part}{random_part}"
                
                if not User.objects.filter(invite_code=referral_code).exists() and \
                not Store.objects.filter(invite_code=referral_code).exists():
                    return referral_code
        else:
            for _ in range(26):  # Limit to 26 alphabet attempts
                alphabet_next = next(alphabet_cycle)
                for _ in range(256):  # All possible 2-digit hexadecimal combinations
                    random_part = ''.join(random.choices(string.hexdigits.upper(), k=2))
                    padded_name = name_part.ljust(4, alphabet_next)
                    referral_code = f"{prefix}{padded_name}{random_part}"
                    
                    if not User.objects.filter(invite_code=referral_code).exists() and \
                    not Store.objects.filter(invite_code=referral_code).exists():
                        return referral_code
        return None


    for i in range(4, 0, -1):
        code = generate_code(name_part[:i])
        if code:
            return code

    raise Exception("Unable to generate unique referral code after exhausting all possibilities.")


def create_messaging_server_user(user, email=None):
    """
    Create user in Swadesic messaging server and return username, password, and token if successful
    """
    try:
        # Generate username based on user reference
        username = user.user_name
        user_email = f"{user.user_reference}@swadesic.com"
        token1, token2 = None, None
        # Generate a secure random password
        password = ''.join(random.choices(string.ascii_letters + string.digits + string.punctuation, k=12))
        
        # Prepare registration data
        register_data = {
            "username": username,
            "user_reference": user.user_reference,
            "email": user_email,
            "password": password
        }

        # Messaging server 1 
        # Make API request to messaging server
        # register_response = requests.post(
        #     f"{config('JS_MESSAGING_SERVER_URL', default='http://localhost:3000')}/api/auth/register",
        #     json=register_data
        # )

        # if register_response.status_code == 201:
        #     # Get token from registration response
        #     token1 = register_response.json().get('token')
        #     if not token:
        #         logger.error(f"No token received for user {username}")
        #         return None, None, None, None, None

        #     logger.info(f"Successfully registered user {username} in messaging server")
           
        # else:
        #     logger.error(f"Failed to register user in messaging server1: {register_response.status_code} - {register_response.text}")
        #     return None, None, None, None, None
            


        # Messaging server 2
        register_response = requests.post(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/auth/register",
            json=register_data
        )

        if register_response.status_code == 201:
            login_response = requests.post(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/auth/login",
            json={
                "email": user_email,
                "password": password
            }
            )
            token2 = login_response.json().get('token')
            new_messaging_user_id = login_response.json().get('user', {}).get('messaging_user_id')
        else: 
            logger.error(f"Failed to login user in messaging server2")
            return None, None, None, None, None

        return user.user_name, password, token1, token2, new_messaging_user_id
    except Exception as e:
        logger.error(f"Error creating messaging server user: {str(e)}")
        return None, None, None, None, None


def update_messaging_server_user_data(instance, handle=None, icon=None, followers_count=None, name=None):
    try:
        update_data = {
            'user_reference': instance.user_reference if isinstance(instance, User) else instance.store_reference,
        }
        # Prepare update data
        if handle:
            update_data['handle'] = handle
        if icon:
            update_data['icon'] = icon
        if followers_count:
            update_data['followers_count'] = followers_count

        entity_reference = instance.user_reference if isinstance(instance, User) else instance.store_reference

        # Get token from database
        token1 = instance.js_messaging_token
        token2 = instance.new_messaging_token

        # Make API request to messaging server1
        # update_response = requests.post(
        #         f"{config('JS_MESSAGING_SERVER_URL', default='http://localhost:3000')}/api/update_user?entity_reference={entity_reference}",
        #         headers={
        #             'Content-Type': 'application/json',
        #             'Authorization': f'Bearer {token1}'
        #         },
        #         json=update_data
        #     )

        # if update_response.status_code == 200:
        #     logger.info(f"Successfully updated user {entity_reference} in messaging server")
        # else:
        #     logger.error(f"Failed to update user in messaging server: {update_response.status_code} - {update_response.text}")

        # Update user details in messaging server2
        update_data2 = {}
        # Prepare update data
        if handle:
            update_data2['username'] = handle
        if icon:
            update_data2['user_icon'] = icon
        if followers_count:
            update_data2['followers_count'] = followers_count
        if name:
            update_data2['name'] = name

        logger.info(f"update_data2: {update_data2}")


        # Make API request to messaging server2
        update_response = requests.put(
                f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/auth/update_profile",
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {token2}'
                },
                json=update_data2
            )

        if update_response.status_code == 200:
            logger.info(f"Successfully updated user {entity_reference} in messaging server2")
        else:
            logger.error(f"Failed to update user in messaging server2: {update_response.status_code} - {update_response.text}")

    except Exception as e:
        logger.error(f"Error updating messaging server user: {str(e)}")


def handle_successful_login(Mobile, is_phonenumber_verified, is_email_verified, email, google_auth=None):
    # Build access control dictionary using dictionary comprehension
    access_control = {
        key: getattr(Mobile, key) for key in [
            "search_and_view", "view_stores", "follow_stores", "view_store_contact",
            "report_stores", "view_store_documents", "view_product", "save_product",
            "buy_product", "report_product", "view_comment_question_review", "add_comment",
            "add_question", "add_review", "report_comment_question_review", "clap_comment_question_review",
            "follow_people", "report_people", "message_people", "create_store", "create_test_store", "store_live",
            "add_products", "receive_orders", "receive_payments", "send_member_invites",
            "send_seller_invites", "vote_for_membership", "giving_suggestions", "create_promotion",
            "view_community_forum", "edit_community_forum", "include_in_rating_calculation",
            "include_in_analytics", "included_in_others_trust_score", "include_in_self_trust_score"
        ]
    }

    welcome_message = Mobile.is_phonenumber_verified

    # Create Neo4jUser node if not already done
    if ((not Mobile.is_phonenumber_verified and not Mobile.is_email_verified)
            or (not Mobile.is_phonenumber_verified and Mobile.is_email_verified and google_auth)):
        try:
            neo4j_user = Neo4jUser.nodes.get(phonenumber=Mobile.phonenumber)
        except Neo4jUser.DoesNotExist:
            neo4j_user = Neo4jUser(
                reference=Mobile.user_reference,
                created_date=Mobile.created_date,
                phonenumber=Mobile.phonenumber,
                pincode=Mobile.pincode
                # Add other properties as needed
            )
            neo4j_user.save()
            logger.info(f"Neo4jUser node created successfully: {neo4j_user}") if neo4j_user.reference else logger.warning("Failed to create Neo4jUser node")

    # # Create messaging server user if not already done
    # if ((not Mobile.is_phonenumber_verified and not Mobile.is_email_verified) 
    #         or (not Mobile.is_phonenumber_verified and Mobile.is_email_verified and google_auth)):
    #     if not Mobile.xmpp_jid:  # Only create if not already exists
    #         username, password = create_messaging_server_user(Mobile, email)
    #         if username and password:
    #             Mobile.xmpp_jid = username
    #             Mobile.xmpp_password = password
    #             Mobile.save()
    #             logger.info(f"Saved messaging server credentials for user {Mobile.user_reference}")

    # Update verification statuses
    if not Mobile.is_phonenumber_verified:
        Mobile.is_phonenumber_verified = is_phonenumber_verified
    if not Mobile.is_email_verified:
        Mobile.is_email_verified = is_email_verified
        Mobile.email = email
        Mobile.save()
        # Save Mobile if any verification status is updated
    if Mobile.is_phonenumber_verified or Mobile.is_email_verified:
        Mobile.save()
    # Check if user profile exists
    # profile_exists_or_not = User.objects.filter(phonenumber=Mobile.phonenumber).values_list("user_name", flat=True).exists()
    profile_exists_or_not = User.objects.filter(phonenumber=Mobile.phonenumber).values_list("user_name",
                                                                                            flat=True).first() is not None

    response_data = {
        "message": "welcome" if not welcome_message else "welcome back",
        "userid": Mobile.userid,
        "user_reference": Mobile.user_reference,
        "pin_code": Mobile.pincode,
        "role": Mobile.invite_type,
        "profile_complete": profile_exists_or_not,
        "member_invite_balance": Mobile.member_invite_balance,
        "seller_invite_balance": Mobile.seller_invite_balance,
        "access_control": access_control,
    }

    return response_data


# code to create ejabberd user instance
        # ejabberd_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        # ejabberd_host = config('EJABBERD_HOST')
        #
        # url = f'http://{ejabberd_host}:5443/api/register'
        # headers = {
        #     'Content-Type': 'application/json',
        #     'Authorization': config('EJABBERD_AUTHORIZATION')
        # }
        # payload = {
        #     'user': Mobile.user_reference,
        #     'host': ejabberd_host,
        #     'password': ejabberd_password
        # }
        #
        # ejabberd_response = requests.post(url, headers=headers, json=payload)
        #
        # if ejabberd_response.status_code == 200:
        #     Mobile.xmpp_jid = f"{Mobile}@{ejabberd_host}"
        #     Mobile.xmpp_password = ejabberd_password
        #     Mobile.save()
        #     logger.info({'message': 'User registered on ejabberd successfully'})
        # else:
        #     logger.info({'error': 'User registration on ejabberd failed'})

def otp_verification_response(phone_otp_verified, email_otp_verified):
    if not phone_otp_verified or not email_otp_verified:
        channel = ''
        if not phone_otp_verified:
            channel = channel + 'phone'
        if not email_otp_verified:
            if len(channel) > 1:
                channel = channel + ' and '
            channel = channel + 'email'
        return Response({
            "message": f"Please enter valid {channel} OTP",
            "is_custom": True,
        },status=400)
    return None


def delete_unverified_duplicates_of_user(verified_user):
    """
    Delete unverified duplicate entries in the User table based on phone number or email.

    Args:
        user: The verified user instance.
    """
    phone_duplicates = User.objects.filter(
        phonenumber=verified_user.phonenumber
    ).exclude(userid=verified_user.userid).annotate(
        duplicate_count=Count('phonenumber')
    ).filter(duplicate_count__gt=0)

    email_duplicates = User.objects.filter(
        email=verified_user.email
    ).exclude(userid=verified_user.userid).annotate(
        duplicate_count=Count('email')
    ).filter(duplicate_count__gt=0)

    # Delete unverified duplicates with the same phone number
    for dup in phone_duplicates:
        if not dup.is_phonenumber_verified or not dup.is_email_verified:
            dup.delete()

    # Delete unverified duplicates with the same email
    for dup in email_duplicates:
        if not dup.is_phonenumber_verified or not dup.is_email_verified:
            dup.delete()

    logger.info(f"Deleted {len(phone_duplicates) + len(email_duplicates)} unverified duplicate users for user id {verified_user.userid}")


def save_google_profile_picture(email, picture_url):
    full_resolution_url = picture_url.split('=')[0]
    response = requests.get(full_resolution_url)
    if response.status_code == 200:
        # Create the media directory if it doesn't exist
        media_root = settings.MEDIA_ROOT
        user_pictures_dir = os.path.join(media_root, 'profile_image')
        os.makedirs(user_pictures_dir, exist_ok=True)

        # Generate a unique filename
        filename = f"{email.split('@')[0]}_google_profile.jpg"
        file_path = os.path.join(user_pictures_dir, filename)

        # Save the image
        with open(file_path, 'wb') as f:
            f.write(response.content)

        # Return the relative path to be stored in the database
        return os.path.join('profile_image', filename)
    return None


def handle_google_profile_picture(user_info_from_google, user=None):
    if 'picture' in user_info_from_google:
        email = user_info_from_google['email']
        profile_picture_path = save_google_profile_picture(email, user_info_from_google['picture'])
        if profile_picture_path:
            if user and not user.icon:
                if len(Neo4jUser.nodes.filter(reference=user.user_reference)) > 0:
                    neo4j_user_node = Neo4jUser.nodes.filter(reference=user.user_reference).first()
                    if neo4j_user_node:
                        neo4j_user_node.icon = config('MEDIA_URL') + profile_picture_path
                        neo4j_user_node.save()

                user.icon = profile_picture_path
                user.save()
                return profile_picture_path


def Authentication_Flow(action, phonenumber=None, input_email=None, google_access_token=None, phonenumber_otp=None, email_otp=None):
    logger.info(
        f"Authentication_Flow called with action: {action}, phonenumber: {phonenumber}, input_email: {input_email}, google_access_token: {'present' if google_access_token else 'not present'}, phonenumber_otp: {phonenumber_otp}, email_otp: {email_otp}")
    config_send_otp = config("SEND_OTP").lower() == 'true'  # Get the value of SEND_OTP from .env
    logger.info(f"config_send_otp: {config_send_otp}")
    phone = phonenumber[3:]

    if action == 'initiate':
        # Get user object that has the given phone number
        user = User.objects.filter(phonenumber=phonenumber, deleted=False).first()
        # get email from input or access token
        email = None
        google_auth = False
        if google_access_token:
            user_info_from_google = google_get_user_info(access_token=google_access_token)
            email = user_info_from_google['email']
            google_auth = True
        if input_email:
            email = input_email
            google_auth = False

        if user:
            # check if the user object exists
            # If user object exists generate token for that user
            token_info = generate_jwt_token(user=user)

            if not email:
                # This means only phone number is given in the input.
                # so, we have to check if the user object has a verified email and
                # then send OTP to that email if Manual email is given
                # and directly login the user if Gmail token is given
                sent_email_otp = False
                sent_phonenumber_otp = False
                if user.email and user.is_email_verified and user.is_phonenumber_verified:
                    if config_send_otp:
                        email_otp = generate_otp(identifier=user.email)
                        send_otp_via_email(email_id=user.email, otp=email_otp)
                        sent_email_otp = True
                    else:
                        sent_email_otp = True
                elif user.email and user.is_email_verified and not user.is_phonenumber_verified:
                    if config_send_otp:
                        phone_otp = generate_otp(identifier=phone)
                        send_otp_via_sms(phone_number=phonenumber, otp=phone_otp)
                        sent_phonenumber_otp = True
                    else:
                        sent_email_otp = True
                else:
                    return Response(
                        {
                            "message": "Verified email does not exist for this account",
                            "is_custom" : True
                            }, status=400
                    )

                return Response(
                    {
                        "sent_phonenumber_otp": sent_phonenumber_otp,
                        "sent_email_otp": sent_email_otp
                    }, status=200
                )

            if email:
                # This means an email is provided either manually or by Google token.
                # So, we need to cheque if that email has already been verified by some other users
                verified_email_user = User.objects.filter(email=email, is_email_verified=True, is_phonenumber_verified=True, deleted=False).exclude(phonenumber=phonenumber).first()
                if verified_email_user:
                    return Response(
                        {"message": "Provided Email is connected to some other account",
                        "is_custom" : True
                        }, status=400
                    )
                if user.email:
                    # This means the user object has an email associated to it
                    if user.is_email_verified:
                        # This means that associated email is verified
                        if user.email != email:
                            # This means that verified email is not equal to provided email
                            return Response(
                                {
                                    "message": f"Your account is connect with this email:{mask_email(user.email)}",
                                    "is_custom": True
                                    }, status=400
                            )
                        else:
                            # This means the verified email Matches with  the provided email (GAuth, input)
                            if google_auth:
                                if user.is_phonenumber_verified:
                                    #Sign in
                                    # This means the provided email is from Google authentication
                                    # so, we directly send the response along with login info

                                    user_info = handle_successful_login(user, user.is_email_verified,
                                                                        user.is_phonenumber_verified,
                                                                        email)
                                    return Response({"token_info": token_info, "user_info": user_info}, status=200)

                                else:
                                    #sign up
                                    if config_send_otp:
                                        if not user.is_phonenumber_verified:
                                            phone_otp = generate_otp(identifier=phone)
                                            send_otp_via_sms(phone_number=phone, otp=phone_otp)
                                        user.counter += 1

                                    return Response(
                                        {
                                            "sent_phonenumber_otp": not user.is_phonenumber_verified,
                                            "sent_email_otp": not user.is_email_verified
                                        }, status=200
                                )
                            else:
                                # User is authenticating with phonenumber and manual email
                                if user.is_phonenumber_verified:
                                    # This means the provided email is from Google authentication
                                    # so, we directly send the response along with login info
                                    if config_send_otp:
                                        email_otp = generate_otp(identifier=email)
                                        send_otp_via_email(email_id=email, otp=email_otp)
                                        return Response(
                                            {
                                                "sent_phonenumber_otp": not user.is_phonenumber_verified,
                                                "sent_email_otp": True
                                            }, status=200
                                        )
                                    else:
                                        return Response(
                                            {
                                                "sent_phonenumber_otp": not user.is_phonenumber_verified,
                                                "sent_email_otp": True
                                            }, status=200
                                        )
                                    # user_info = handle_successful_login(user, user.is_email_verified,
                                    #                                     user.is_phonenumber_verified,
                                    #                                     email)
                                    # return Response({"token_info": token_info, "user_info": user_info}, status=200)
                                # This means the provided email is manually entered
                                # so, we send otp to that email
                                else:

                                    if config_send_otp:
                                        if not user.is_phonenumber_verified:
                                            phone_otp = generate_otp(identifier=phone)
                                            send_otp_via_sms(phone_number=phone, otp=phone_otp)
                                        email_otp = generate_otp(identifier=email)
                                        send_otp_via_email(email_id=email, otp=email_otp)

                                    return Response(
                                        {
                                            "sent_phonenumber_otp": not user.is_phonenumber_verified,
                                            "sent_email_otp": True
                                        }, status=200
                                    )
                    else:
                        # This means the existing user objects email is not verified
                        # so, we update the email of that user with provided email
                        # And send OTPS accordingly
                        user.email = email
                        # user.is_email_verified = False
                        # user.is_phonenumber_verified = False
                        user.save()
                        sent_email_otp = False
                        sent_phonenumber_otp = False

                        if not user.is_phonenumber_verified:
                            if config_send_otp:
                                phone_otp = generate_otp(identifier=phone)
                                send_otp_via_sms(phone_number=phone, otp=phone_otp)
                                user.counter += 1
                            sent_phonenumber_otp = True
                        if user.is_phonenumber_verified and not user.is_email_verified and not google_auth:
                            if config_send_otp:
                                phone_otp = generate_otp(identifier=phone)
                                send_otp_via_sms(phone_number=phone, otp=phone_otp)
                                email_otp = generate_otp(identifier=user.email)
                                send_otp_via_email(email_id=user.email, otp=email_otp)
                                user.counter += 1
                            sent_phonenumber_otp = True
                            sent_email_otp = True

                        if not user.is_email_verified and not google_auth:
                            if config_send_otp:
                                email_otp = generate_otp(identifier=user.email)
                                send_otp_via_email(email_id=user.email, otp=email_otp)
                                user.counter += 1
                            sent_email_otp = True
                        if user.is_phonenumber_verified and google_auth:
                            if config_send_otp:
                                phone_otp = generate_otp(identifier=phone)
                                send_otp_via_sms(phone_number=phone, otp=phone_otp)
                                user.counter += 1
                            sent_phonenumber_otp = True

                        return Response(
                            {
                                "sent_phonenumber_otp": sent_phonenumber_otp,
                                "sent_email_otp": sent_email_otp
                            }, status=200)
                else:
                    # This means the user object does not have an email associated with it
                    # so we update the email fields And send OTPS accordingly
                    user.email = email
                    # user.is_email_verified = False
                    # user.is_phonenumber_verified = False
                    user.save()
                    sent_email_otp = False
                    sent_phonenumber_otp = False

                    if not user.is_phonenumber_verified:
                        if config_send_otp:
                            phone_otp = generate_otp(identifier=phone)
                            send_otp_via_sms(phone_number=phone, otp=phone_otp)
                            user.counter += 1
                        sent_phonenumber_otp = True
                    if user.is_phonenumber_verified and not user.is_email_verified:
                        if config_send_otp:
                            phone_otp = generate_otp(identifier=phone)
                            send_otp_via_sms(phone_number=phone, otp=phone_otp)
                            email_otp = generate_otp(identifier=user.email)
                            send_otp_via_email(email_id=user.email, otp=email_otp)
                            user.counter += 1
                        sent_phonenumber_otp = True
                        sent_email_otp = True

                    if not user.is_email_verified and not google_auth:
                        if config_send_otp:
                            email_otp = generate_otp(identifier=user.email)
                            send_otp_via_email(email_id=user.email, otp=email_otp)
                            user.counter += 1
                        sent_email_otp = True
                    return Response(
                        {
                            "sent_phonenumber_otp": sent_phonenumber_otp,
                            "sent_email_otp": sent_email_otp
                        }, status=200)

        else:
            # This means there is no associated user object with that phone number
            # so, we create a new user object
            if User.objects.filter(email=email, is_email_verified=True).exclude(phonenumber=phonenumber).exists():
                return Response({
                    "message": "Provided Email is connected to some other account",
                    "is_custom": True
                }, status=400)
            serializer = UserSerializer(data={
                "phonenumber": phonenumber,
                "email": email
            })
            try:
                serializer.is_valid(raise_exception=True)
                serializer.save()
                new_user = User.objects.get(phonenumber=phonenumber, email=email)
            except ValidationError:
                return Response(
                    next(iter(serializer.errors.values())),
                    status=status.HTTP_400_BAD_REQUEST,
                )
            # if google_auth:
            #     new_user.is_email_verified = True
            #     new_user.save()
            if config_send_otp:
                if not new_user.is_phonenumber_verified:
                    phone_otp = generate_otp(identifier=phone)
                    send_otp_via_sms(phone_number=phone, otp=phone_otp)
                if not new_user.is_email_verified and not google_auth:
                    email_otp = generate_otp(identifier=new_user.email)
                    send_otp_via_email(email_id=new_user.email, otp=email_otp)
                new_user.counter += 1

            return Response(
                {
                    "sent_phonenumber_otp": not new_user.is_phonenumber_verified,
                    "sent_email_otp": not new_user.is_email_verified and not google_auth
                }, status=200
            )

    if action == 'validate':
        # Get user object that has the given phone number
        user = User.objects.filter(phonenumber=phonenumber, deleted=False).first()
        # get email from input or access token
        email = None
        google_auth = False
        if google_access_token:
            user_info_from_google = google_get_user_info(access_token=google_access_token)
            email = user_info_from_google['email']
            google_auth = True
        if input_email:
            email = input_email
            google_auth = False

        # check if the user object exists
        if user:
            # If user object exists generate token for that user
            token_info = generate_jwt_token(user=user)

            if not email: #only phonenumber
                # This means only phone number is given in the input.
                # so, we have to check if the user object has a verified email and
                # then validate OTP to that email if Manual email is given
                # and directly login the user if Gmail token is given
                email_otp_verified = False
                phone_otp_verified = False
                if user.email and user.is_email_verified and user.is_phonenumber_verified:
                    if config_send_otp:
                        if email_otp:
                            email_otp_verified = validate_otp(otp=email_otp, identifier=user.email)
                    else:
                        if email_otp:
                            email_otp_verified = True if email_otp == '222222' else False

                    otp_response = otp_verification_response(phone_otp_verified=True,
                                                             email_otp_verified=email_otp_verified)
                    if otp_response is not None:
                        return otp_response

                    response_data = handle_successful_login(user, is_phonenumber_verified=True,
                                                            is_email_verified=email_otp_verified, email=input_email)
                    return Response({"token_info": token_info, "user_info":response_data }, status=200)

                elif user.email and user.is_email_verified and not user.is_phonenumber_verified:
                    if config_send_otp:
                        if phonenumber_otp:
                            phone_otp_verified = validate_otp(otp=phonenumber_otp, identifier=phone)
                        if email_otp:
                            email_otp_verified = validate_otp(otp=email_otp, identifier=user.email)
                    else:
                        if phonenumber_otp:
                            phone_otp_verified = True if phonenumber_otp == '222222' else False
                        if email_otp:
                            email_otp_verified = True if email_otp == '222222' else False

                    otp_response = otp_verification_response(phone_otp_verified=phone_otp_verified,
                                                             email_otp_verified=email_otp_verified)
                    if otp_response is not None:
                        return otp_response

                    response_data = handle_successful_login(user, is_phonenumber_verified=phone_otp_verified,
                                                            is_email_verified=email_otp_verified, email=input_email)
                    return Response({"token_info": token_info, "user_info":response_data}, status=200)

                else:
                    return Response(
                        {
                            "message": "Verified email does not exist for this account",
                            "is_custom" :True
                            }, status=400
                    )

            if email:
                # This means an email is provided either manually or by Google token.
                # So, we need to check if that email has already been verified by some other users
                verified_email_user = User.objects.filter(email=email, is_email_verified=True,
                                                          is_phonenumber_verified=True, deleted=False).exclude(
                    phonenumber=phonenumber).first()
                if verified_email_user:
                    return Response(
                        {
                            "message": "Provided Email is connected to some other account",
                            "is_custom" : True
                            }, status=400
                    )
                if user.email:
                    # This means the user object has an email associated to it
                    if user.is_email_verified:
                        # This means that associated email is verified
                        if user.email != email:
                            # This means that verified email is not equal to provided email
                            return Response(
                                {
                                    "message": f"Your account is connected with this email:{mask_email(user.email)}",
                                    "is_custom" : True
                                    }, status=400
                            )
                        else:
                            # This means the verified email Matches with  the provided email
                            if google_auth:
                                phone_otp_verified = False
                                if config_send_otp:
                                    if not user.is_phonenumber_verified:
                                        phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                                else:
                                    if phonenumber_otp:
                                        phone_otp_verified = True if phonenumber_otp == '222222' else False

                                otp_response = otp_verification_response(phone_otp_verified=phone_otp_verified,
                                                                         email_otp_verified=True)
                                if otp_response is not None:
                                    return otp_response

                                response_data = handle_successful_login(user,
                                                                        is_phonenumber_verified=phone_otp_verified,
                                                                        is_email_verified=True,
                                                                        email=input_email,
                                                                        google_auth=google_auth)
                                if not user.icon:
                                    icon = handle_google_profile_picture(user_info_from_google, user)
                                    if icon:
                                        response_data['icon'] = icon
                                return Response({"token_info": token_info, "user_info": response_data}, status=200)
                            else:
                                phone_otp_verified = False
                                email_otp_verified = False
                                if config_send_otp:
                                    if not user.is_phonenumber_verified:
                                        phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                                    email_otp_verified = validate_otp(identifier=email, otp=email_otp)
                                else:
                                    phone_otp_verified = True if phonenumber_otp == '222222' or user.is_phonenumber_verified else False
                                    if email_otp:
                                        email_otp_verified = True if email_otp == '222222' else False

                                otp_response = otp_verification_response(phone_otp_verified=phone_otp_verified,
                                                                         email_otp_verified=email_otp_verified)
                                if otp_response is not None:
                                    return otp_response

                                response_data = handle_successful_login(user,
                                                                        is_phonenumber_verified=phone_otp_verified,
                                                                        is_email_verified=True,
                                                                        email=input_email)
                                return Response({"token_info": token_info, "user_info":response_data}, status=200)
                    else:
                        # This means the existing user objects email is not verified
                        # so, we update the email of that user with provided email
                        # And validate OTPS accordingly
                        if google_auth:
                            phone_otp_verified = False
                            if config_send_otp:
                                if not user.is_phonenumber_verified:
                                    phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                                else:
                                    phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                            else:
                                if phonenumber_otp:
                                    phone_otp_verified = True if phonenumber_otp == '222222' else False

                            otp_response = otp_verification_response(phone_otp_verified=phone_otp_verified,
                                                                     email_otp_verified=True)
                            if otp_response is not None:
                                return otp_response

                            response_data = handle_successful_login(user,
                                                                    is_phonenumber_verified=phone_otp_verified,
                                                                    is_email_verified=True,
                                                                    email=email)
                            if not user.icon:
                                icon = handle_google_profile_picture(user_info_from_google, user)
                                if icon:
                                    response_data['icon'] = icon
                            return Response({"token_info": token_info, "user_info": response_data}, status=200)
                        else:
                            phone_otp_verified = False
                            email_otp_verified = False
                            if config_send_otp:
                                if user.is_phonenumber_verified and not user.is_email_verified:
                                    phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                                    email_otp_verified = validate_otp(identifier=email, otp=email_otp)
                                if not user.is_phonenumber_verified:
                                    phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                                if not user.is_email_verified:
                                    email_otp_verified = validate_otp(identifier=email, otp=email_otp)
                            else:
                                if phonenumber_otp:
                                    phone_otp_verified = True if phonenumber_otp == '222222' else False
                                if email_otp:
                                    email_otp_verified = True if email_otp == '222222' else False

                            otp_response = otp_verification_response(phone_otp_verified=phone_otp_verified,
                                                                     email_otp_verified=email_otp_verified)
                            if otp_response is not None:
                                return otp_response

                            response_data = handle_successful_login(user,
                                                                    is_phonenumber_verified=phone_otp_verified,
                                                                    is_email_verified=email_otp_verified,
                                                                    email=email)
                            return Response({"token_info": token_info, "user_info":response_data}, status=200)
                else:
                    # This means the user object does not have an email associated with it
                    # so, we update the email fields And send OTPS accordingly
                    if google_auth:
                        phone_otp_verified = False
                        if config_send_otp:
                            if not user.is_phonenumber_verified:
                                phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                        else:
                            if phonenumber_otp:
                                phone_otp_verified = True if phonenumber_otp == '222222' else False

                        otp_response = otp_verification_response(phone_otp_verified=phone_otp_verified,
                                                                 email_otp_verified=True)
                        if otp_response is not None:
                            return otp_response

                        response_data = handle_successful_login(user,
                                                                is_phonenumber_verified=phone_otp_verified,
                                                                is_email_verified=True,
                                                                email=email)
                        if not user.icon:
                            icon = handle_google_profile_picture(user_info_from_google, user)
                            if icon:
                                response_data['icon'] = icon
                        return Response({"token_info": token_info, "user_info":response_data}, status=200)
                    else:
                        phone_otp_verified = False
                        email_otp_verified = False
                        if config_send_otp:
                            if user.is_phonenumber_verified and not user.is_email_verified:
                                phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                                email_otp_verified = validate_otp(identifier=email, otp=email_otp)
                            if not user.is_phonenumber_verified:
                                phone_otp_verified = validate_otp(identifier=phone, otp=phonenumber_otp)
                            if not user.is_email_verified:
                                email_otp_verified = validate_otp(identifier=email, otp=email_otp)
                        else:
                            if phonenumber_otp:
                                phone_otp_verified = True if phonenumber_otp == '222222' else False
                            if email_otp:
                                email_otp_verified = True if email_otp == '222222' else False

                        otp_response = otp_verification_response(phone_otp_verified=phone_otp_verified,
                                                                 email_otp_verified=email_otp_verified)
                        if otp_response is not None:
                            return otp_response

                        response_data = handle_successful_login(user,
                                                                is_phonenumber_verified=phone_otp_verified,
                                                                is_email_verified=email_otp_verified,
                                                                email=email)
                        return Response({"token_info": token_info, "user_info": response_data}, status=200)

        else:
            return Response(
                {
                    "message": "User with given phonenumber and email combination does not exist",
                    "is_custom" : True
                    }, status=400)  # False Call


##############################################################################

class InviteCodeHandler():
    def __init__(self, invitee_instance, serializer):
        self.invitee_instance = invitee_instance
        self.serializer = serializer
        self.exceptions = []

    def handle_invite_codes(self, invitee_instance, serializer):
        # initialization
        ## Get the invite code from the serializer
        input_invited_by_code = serializer.validated_data.get('invited_by_code')

        ## Get the invitee user points
        invitee_point_instance, _ = UserRewards.objects.get_or_create(user_reference=invitee_instance)

        ## Determine the invitor type and get the corresponding points instance
        if input_invited_by_code and input_invited_by_code.startswith('SWS'):
            invitor_type = 'STORE'
            invitor_instance = Store.objects.filter(invite_code=input_invited_by_code, deleted=False).first()
            invitor_point_instance, _ = StoreRewards.objects.get_or_create(store_reference=invitor_instance)
        else:
            invitor_type = 'USER'
            invitor_instance = User.objects.filter(invite_code=input_invited_by_code, deleted=False).first()
            invitor_point_instance, _ = UserRewards.objects.get_or_create(user_reference=invitor_instance)

        # Create self invite code for the user
        if not invitee_instance.invite_code:
            invitee_user_name = invitee_instance.user_name or serializer.validated_data.get("user_name")
            serializer.validated_data["invite_code"] = generate_referral_code(name=invitee_user_name)

        # Link invitor & invitee and send rewards, notifications
        if not invitee_instance.invited_by_code and input_invited_by_code:
           x = self.process_invite_code(invitor_instance, invitee_instance, invitor_point_instance,invitee_point_instance, input_invited_by_code, serializer)

        return serializer, self.exceptions

    def process_invite_code(self, invitor_instance, invitee_instance, invitor_point_instance,invitee_point_instance, input_invited_by_code, serializer):
        # when invite code is swadesic code
        if input_invited_by_code == get_config().swadesic_default_invite_code:
            return self.handle_swadesic_code(invitee_instance, invitee_point_instance, serializer)

        if not invitor_instance:
            self.exceptions.append(
             {
                 "message": "Given Invite Code is not valid",
                 "data": f"{input_invited_by_code}"
             }
            )
        else:
            try:
                invitor_point_instance.infinity_points += get_config().invite_reward_onboarding
                invitee_point_instance.infinity_points += get_config().invite_reward_onboarding
                invitor_point_instance.save()
                invitee_point_instance.save()
            except Exception as e:
                self.exceptions.append(
                    {
                        "message": "Error while updating points",
                        "data": f"{e}"
                    }
                )
            self.create_reward_histories(invitor=invitor_instance, invitee=invitee_instance, input_invited_by_code=input_invited_by_code)
            self.create_notifications(invitor_instance, invitee_instance, serializer)

        return None

    def handle_swadesic_code(self, user_instance, current_userpoint_instance, serializer):

        referral_reward_ratio = RewardTransferConfig.objects.get(
            transfer_type=RewardsHistory.RewardTypeChoices.ONBOARDING_REFERRAL).ratio

        current_userpoint_instance.infinity_points += get_config().invite_reward_onboarding
        current_userpoint_instance.save()

        self.create_reward_history(None, user_instance, get_config().swadesic_default_invite_code, referral_reward_ratio)
        # create_notification(user_instance, None, Notifications.Notifications_Type.YOU_SIGNED_UP_WITH_SOMEONES_CODE)

        return None

    def create_reward_histories(self, invitor, invitee, input_invited_by_code):
        event_reference = invitor.user_reference if isinstance(invitor, User) else invitor.store_reference
        self.create_reward_history(sender='SWADESIC', receiver=invitee, invite_code=input_invited_by_code, event_reference=event_reference, reward_type=RewardsHistory.RewardTypeChoices.ONBOARDING_REFERRAL)
        self.create_reward_history(sender='SWADESIC', receiver=invitor, invite_code=input_invited_by_code, event_reference=invitee.user_reference, reward_type=RewardsHistory.RewardTypeChoices.INVITE_CODE_REWARD)


    def create_reward_history(self, sender, receiver, invite_code, event_reference=None, reward_type=None):

        try:
            referral_reward_ratio = RewardTransferConfig.objects.get(
                transfer_type=RewardsHistory.RewardTypeChoices.ONBOARDING_REFERRAL).ratio

            RewardsHistory.objects.create(
                reward_ratio=referral_reward_ratio,
                reward_type=reward_type,
                transaction_type=RewardsHistory.TransactionTypeChoices.CREDIT,
                invite_code=invite_code,
                sender_reference= sender,
                receiver_reference=receiver.user_reference if isinstance(receiver, User) else receiver.store_reference,
                received_value=get_config().invite_reward_onboarding,
                sent_value=get_config().invite_reward_onboarding,
                event_reference=event_reference
            )
        except Exception as e:
            self.exceptions.append(
                {
                    "message": "Could not create infinity reward history",
                    "data": f"{e}"
                }
            )

    def create_notifications(self, invitor_instance, invitee_instance, serializer):
        self.create_notification(invitor_instance, invitee_instance, Notifications.Notifications_Type.SOMEONE_SIGNED_UP_WITH_YOUR_CODE, serializer)
        self.create_notification(invitee_instance, invitor_instance, Notifications.Notifications_Type.YOU_SIGNED_UP_WITH_SOMEONES_CODE, serializer)

    def create_notification(self, notified_user, other_user, notification_type, serializer):

        try:
            notification_handler = NotificationHandler(
                notified_user=notified_user.user_reference,
                notification_about=other_user.user_name if other_user.user_name else serializer.validated_data['user_name'],
                notification_type=notification_type,
                points_count=get_config().invite_reward_onboarding,
            )
            notification_handler.create_notification(notification_handler)
        except:
            logger.error(f"Could not create notification for {notification_type}")
            self.exceptions.append(
                {
                    "message": "Could not create notification",
                    "data": f"{notification_type}"
                }
            )

def update_user_device_details_in_messaging_server(user_reference, device_id, fcm_token, user_app_version=None):
    try:
        user_instance = User.objects.get(user_reference=user_reference)
        token = user_instance.js_messaging_token
        update_data = {
            'user_reference': user_reference,
            'device_id': device_id,
            'fcm_token': fcm_token,
            'user_app_version': user_app_version
        }
        update_response = requests.post(
            f"{config('JS_MESSAGING_SERVER_URL', default='http://localhost:3000')}/api/users/update_user_details",
            headers={
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {token}'
            },
            json=update_data
        )
        if update_response.status_code == 200:
            logger.info(f"Successfully updated user {user_reference} in messaging server")
        else:
            logger.error(f"Failed to update user in messaging server: {update_response.text}")
    except Exception as e:
        logger.error(f"Could not update messaging server user: {e}")

################################################################################
def get_user_total_savings(user_instance):
    total_savings = 0
    return total_savings


def get_user_total_earnings(user_instance):
    total_earnings = 0
    return total_earnings
