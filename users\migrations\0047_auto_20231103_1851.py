# Generated by Django 3.2.22 on 2023-11-03 13:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0045_merge_20231005_1006'),
        ('users', '0046_auto_20231103_0017'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userstore',
            name='store_reference',
        ),
        migrations.RemoveField(
            model_name='userstore',
            name='store_supporter',
        ),
        migrations.RemoveField(
            model_name='userstore',
            name='user_supporter',
        ),
        migrations.AddField(
            model_name='userstore',
            name='storeid',
            field=models.ForeignKey(blank=True, db_column='storeid', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='supports', to='stores.store'),
        ),
        migrations.AddField(
            model_name='userstore',
            name='userid',
            field=models.ForeignKey(blank=True, db_column='userid', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='supporter', to='users.user'),
        ),
    ]
