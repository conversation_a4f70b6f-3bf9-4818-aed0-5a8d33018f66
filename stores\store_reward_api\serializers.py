# serializers.py

from rest_framework import serializers
from .models import StoreRewards, StoreRewardsHistory, StoreRewardsMonthlyHistory
from ..store_api.models import Store
from users.user_api.models import User


class StoreRewardsSerializer(serializers.ModelSerializer):
    class Meta:
        model = StoreRewards
        fields = ['store_reference', 'infinity_points', 'flash_points']


class StoreRewardsHistorySerializer(serializers.ModelSerializer):
    transaction_title = serializers.SerializerMethodField("get_transaction_title")

    class Meta:
        model = StoreRewardsHistory
        fields = '__all__'

    def get_transaction_title(self, obj):
        title = None

        if obj.reward_type == 'MONTHLY_CREDIT':
            title = "Monthly Flash Credit"
        elif obj.reward_type == 'REDEEM':
            title = "Redeemed for Order Fee"
        elif obj.reward_type == 'TRANSFER':
            entity_instance = User.objects.get(user_reference=obj.event_reference, deleted=False) \
                if obj.event_reference.startswith('U') \
                else Store.objects.get(store_reference=obj.event_reference, deleted=False)
            entity_handle = entity_instance.user_name if isinstance(entity_instance,User) else entity_instance.storehandle
            if obj.transaction_type == 'CREDIT':
                title = f"Transfer from @{entity_handle}"
            if obj.transaction_type == 'DEBIT':
                title = f"Transfer to @{entity_handle}"
        return title


class StoreRewardsMonthlyHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = StoreRewardsMonthlyHistory
        fields = [
            'month_reward_id', 'month_with_year', 'reward_granted', 'reward_used',
            'store_reference', 'created_date'
        ]
