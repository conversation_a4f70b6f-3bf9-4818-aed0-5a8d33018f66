# Generated by Django 4.2.7 on 2024-10-10 11:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0140_alter_orderpayout_product_amount_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="BankDetails",
            fields=[
                (
                    "bank_details_id",
                    models.<PERSON>Field(primary_key=True, serialize=False),
                ),
                (
                    "bank_detail_reference",
                    models.Char<PERSON>ield(
                        blank=True, max_length=100, null=True, unique=True
                    ),
                ),
                (
                    "account_name",
                    models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True),
                ),
                (
                    "account_holder_name",
                    models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=100, null=True),
                ),
                ("ifsc_code", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("account_number", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("bank_branch", models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=50, null=True)),
                ("bank_name", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=50, null=True)),
                ("is_primary", models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=False)),
                (
                    "entity_reference",
                    models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=50, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "bank details",
                "db_table": '"order"."bank_details"',
            },
        ),
    ]
