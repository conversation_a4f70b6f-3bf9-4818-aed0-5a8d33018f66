# Generated by Django 4.2.7 on 2024-01-04 11:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0003_postlikes_is_liked'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeedPost',
            fields=[
                ('feed_post_id', models.AutoField(primary_key=True, serialize=False)),
                ('reference', models.CharField(blank=True, max_length=20, null=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('is_seen', models.BooleanField(default=False)),
                ('category', models.CharField(blank=True, choices=[('CONTACT', 'Contact'), ('CONNECTION', 'Connection'), ('MUTUAL', 'Mutual'), ('PINCODE', 'Pincode'), ('CITY', 'City'), ('STATE', 'State')], max_length=15, null=True)),
                ('post_reference', models.ForeignKey(blank=True, db_column='post_reference', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='feed_post', to='content.posts', to_field='post_reference')),
            ],
            options={
                'verbose_name_plural': 'feed_posts',
                'db_table': '"content"."feed_post"',
            },
        ),
    ]
