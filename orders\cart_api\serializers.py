from rest_framework import serializers
from .models import CartItem
from stores.store_api.models import Store
from stores.store_settings_api.models import RefundAndWarranty, TrustCenter, DeliverySettings, DeliveryLocations
from stores.store_settings_api.serializers import TrustCenterSerializer
from products.models import Product
from users.user_api.models import User, UserProduct
from products.api.serializers import ProductImagesSerializer
import pandas as pd
from django_pandas.io import read_frame


class CartSerializer(serializers.ModelSerializer):
    class Meta:
        model = CartItem
        fields = "__all__"


class OrderedProductSerializer(serializers.ModelSerializer):
    prod_images = ProductImagesSerializer(many=True)
    refund_warranty = serializers.SerializerMethodField("get_refund_warranty_message")
    cart = serializers.SerializerMethodField("get_cart_details")
    is_wishlisted = serializers.SerializerMethodField("is_wishlisted_or_not")
    deliverability = serializers.SerializerMethodField("get_deliverability")

    class Meta:
        model = Product
        fields = [
            "productid",
            "product_reference",
            "product_name",
            "brand_name",
            "product_description",
            "product_version",
            "deleted",
            "deliverability",
            "created_by",
            "modified_by",
            "in_stock",
            "mrp_price",
            "selling_price",
            "prod_images",
            "refund_warranty",
            "is_wishlisted",
            "cart"
        ]

    def get_deliverability(self, obj):
        deliverability = False
        if self.context.get("user_pincode"):
            user_pincode = self.context.get("user_pincode")
            if DeliverySettings.objects.filter(
                    product_reference=obj.product_reference, storeid=obj.storeid, is_deleted=False
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    product_reference=obj.product_reference, storeid=obj.storeid, is_deleted=False
                ).last()
            elif DeliverySettings.objects.filter(
                    storeid=obj.storeid, product_reference__isnull=True, is_deleted=False
                ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    storeid=obj.storeid, product_reference__isnull=True, is_deleted=False
                ).last()
            else:
                delivery_settings_instance = None

            if delivery_settings_instance:
                delivery_locations = delivery_settings_instance.delivery_locations
                if delivery_locations:
                    list_of_pincode = self.convert_delivery_locations_to_pincode(delivery_locations)
                    if user_pincode in list_of_pincode:
                        deliverability = True
        return deliverability

    @staticmethod
    def convert_delivery_locations_to_pincode(delivery_locations):
        queryset = DeliveryLocations.objects.all()
        '''Use python django-pandas package to filter the pincodes easily
        Read the querysets into a dataframe using read_frame method
        '''

        df_all_delivery_locations = read_frame(queryset)

        '''
        Convert the values in 'city', 'state' columns in dataframe and input 'delivery_locations' string 
        as lower case to avoid comparison issues
        '''

        df_all_delivery_locations['city'] = df_all_delivery_locations['city'].str.lower()
        df_all_delivery_locations['state'] = df_all_delivery_locations['state'].str.lower()

        list_of_locations = delivery_locations.lower().split("|")

        '''
        Filter the DataFrame (df_all_delivery_locations) to get pincodes based on specified 
        locations in either the 'state' or 'city' columns. The resulting pincodes are then converted to lists (tolist()).
        '''

        state_pincodes= df_all_delivery_locations[df_all_delivery_locations.state.isin(list_of_locations)]['pincode'].tolist()
        city_pincodes = df_all_delivery_locations[df_all_delivery_locations.city.isin(list_of_locations)]['pincode'].tolist()
        input_pincodes = [x for x in list_of_locations if str(x).isnumeric()]

        ''' uncomment below line to test the lengths of each list'''

        # print([len(x) for  x  in [state_pincodes, city_pincodes, input_pincodes]])

        lst = list(set(input_pincodes + state_pincodes + city_pincodes))
        return lst

        # all_states = list(queryset.values_list("state", flat=True).order_by('state').distinct())
        # all_cities = list(queryset.values_list("city", flat=True).order_by('city').distinct())
        # for elem in list_of_locations:
        #     if elem in all_states:
        #         pincode = list(queryset.filter(state=elem).values_list("pincode", flat=True))
        #         # lst.extend(pincode)
        #     elif elem in all_cities:
        #         pincode = queryset.filter(city=elem).values_list("pincode", flat=True)
        #         lst.extend(pincode)
        #     else:
        #         lst.append(elem)

    def is_wishlisted_or_not(self, obj):
        user_id = self.context.get("user_id")
        product_reference = obj.product_reference

        user_instance = User.objects.get(userid=user_id, deleted=False)
        product_instance = Product.objects.get(product_reference=product_reference)

        wish_listed = False
        if UserProduct.objects.filter(userid=user_instance, productid=product_instance).exists():
            instance = UserProduct.objects.get(userid=user_instance, productid=product_instance)
            wish_listed = instance.is_saved

        return wish_listed

    def get_refund_warranty_message(self, obj):
        store_id = self.context.get("storeid")
        product_reference = obj.product_reference

        if RefundAndWarranty.objects.filter(
            product_reference=product_reference, storeid=store_id, is_deleted=False
        ).exists():
            value = RefundAndWarranty.objects.filter(
                product_reference=product_reference, storeid=store_id, is_deleted=False
            ).last()
        else:
            value = RefundAndWarranty.objects.filter(
                product_reference__isnull=True, storeid=store_id, is_deleted=False
            ).last()

        if value.return_type == False and value.replacement_type == True:
            message = (
                "replacement accepted in "
                + str(value.return_period)
                + " days from delivery."
            )
        elif value.replacement_type == False and value.return_type == True:
            message = (
                "return accepted in "
                + str(value.return_period)
                + " days from delivery."
            )
        elif value.replacement_type == False and value.return_type == False:
            message = "no returns accepted"
        return message

    def get_cart_details(self, obj):
        user_id = self.context.get("user_id")
        cart_item = CartItem.new_objects.get(
            userid=user_id,
            product_reference=obj.product_reference,
            cart_product_status="IN_CART",
        )
        quantity = cart_item.product_quantity
        cart_item_id = cart_item.cartitemid
        cart_status = cart_item.cart_product_status
        cart = {
            "cart_item_id": cart_item_id,
            "quantity": quantity,
            "cart_status": cart_status,
        }
        return cart

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if representation.get("prod_images"):
            list_of_prod_images = representation.get("prod_images")
            images = [
                    lst["product_image"]
                    for lst in list_of_prod_images
                    if lst["reorder"] == 1
            ]
            first_image = images[0] if images else None

            representation.update(prod_images=first_image)
            return representation
        else:
            representation.update(prod_images=None)
            return representation



class CartProductSerializer(serializers.ModelSerializer):
    prod_images = ProductImagesSerializer(many=True)
    refund_warranty = serializers.SerializerMethodField("get_refund_warranty_message")
    cart = serializers.SerializerMethodField("get_cart_details")
    is_wishlisted = serializers.SerializerMethodField("is_wishlisted_or_not")
    deliverability = serializers.SerializerMethodField("get_deliverability")

    class Meta:
        model = Product
        fields = [
            "productid",
            "product_reference",
            "product_name",
            "brand_name",
            "product_description",
            "product_version",
            "deleted",
            "deliverability",
            "created_by",
            "modified_by",
            "in_stock",
            "mrp_price",
            "selling_price",
            "prod_images",
            "refund_warranty",
            "is_wishlisted",
            "cart"
        ]

    def get_deliverability(self, obj):
        deliverability = False
        if self.context.get("user_pincode"):
            user_pincode = self.context.get("user_pincode")
            if DeliverySettings.objects.filter(
                    product_reference=obj.product_reference, storeid=obj.storeid, is_deleted=False
            ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    product_reference=obj.product_reference, storeid=obj.storeid, is_deleted=False
                ).last()
            elif DeliverySettings.objects.filter(
                    storeid=obj.storeid, product_reference__isnull=True, is_deleted=False
                ).exists():
                delivery_settings_instance = DeliverySettings.objects.filter(
                    storeid=obj.storeid, product_reference__isnull=True, is_deleted=False
                ).last()
            else:
                delivery_settings_instance = None

            if delivery_settings_instance:
                delivery_locations = delivery_settings_instance.delivery_locations
                if delivery_locations:
                    list_of_pincode = self.convert_delivery_locations_to_pincode(delivery_locations)
                    if user_pincode in list_of_pincode:
                        deliverability = True
        return deliverability

    @staticmethod
    def convert_delivery_locations_to_pincode(delivery_locations):
        queryset = DeliveryLocations.objects.all()
        '''Use python django-pandas package to filter the pincodes easily
        Read the querysets into a dataframe using read_frame method
        '''

        df_all_delivery_locations = read_frame(queryset)

        '''
        Convert the values in 'city', 'state' columns in dataframe and input 'delivery_locations' string 
        as lower case to avoid comparison issues
        '''

        df_all_delivery_locations['city'] = df_all_delivery_locations['city'].str.lower()
        df_all_delivery_locations['state'] = df_all_delivery_locations['state'].str.lower()

        list_of_locations = delivery_locations.lower().split("|")

        '''
        Filter the DataFrame (df_all_delivery_locations) to get pincodes based on specified 
        locations in either the 'state' or 'city' columns. The resulting pincodes are then converted to lists (tolist()).
        '''

        state_pincodes= df_all_delivery_locations[df_all_delivery_locations.state.isin(list_of_locations)]['pincode'].tolist()
        city_pincodes = df_all_delivery_locations[df_all_delivery_locations.city.isin(list_of_locations)]['pincode'].tolist()
        input_pincodes = [x for x in list_of_locations if str(x).isnumeric()]

        ''' uncomment below line to test the lengths of each list'''

        # print([len(x) for  x  in [state_pincodes, city_pincodes, input_pincodes]])

        lst = list(set(input_pincodes + state_pincodes + city_pincodes))
        return lst

        # all_states = list(queryset.values_list("state", flat=True).order_by('state').distinct())
        # all_cities = list(queryset.values_list("city", flat=True).order_by('city').distinct())
        # for elem in list_of_locations:
        #     if elem in all_states:
        #         pincode = list(queryset.filter(state=elem).values_list("pincode", flat=True))
        #         # lst.extend(pincode)
        #     elif elem in all_cities:
        #         pincode = queryset.filter(city=elem).values_list("pincode", flat=True)
        #         lst.extend(pincode)
        #     else:
        #         lst.append(elem)

    def is_wishlisted_or_not(self, obj):
        user_id = self.context.get("user_id")
        product_reference = obj.product_reference

        user_instance = User.objects.get(userid=user_id, deleted=False)
        product_instance = Product.objects.get(product_reference=product_reference)

        wish_listed = False
        if UserProduct.objects.filter(userid=user_instance, productid=product_instance).exists():
            instance = UserProduct.objects.get(userid=user_instance, productid=product_instance)
            wish_listed = instance.is_saved

        return wish_listed

    def get_refund_warranty_message(self, obj):
        store_id = self.context.get("storeid")
        product_reference = obj.product_reference

        if RefundAndWarranty.objects.filter(
            product_reference=product_reference, storeid=store_id, is_deleted=False
        ).exists():
            value = RefundAndWarranty.objects.filter(
                product_reference=product_reference, storeid=store_id, is_deleted=False
            ).last()
        else:
            value = RefundAndWarranty.objects.filter(
                product_reference__isnull=True, storeid=store_id, is_deleted=False
            ).last()

        if value.return_type == False and value.replacement_type == True:
            message = (
                "replacement accepted in "
                + str(value.return_period)
                + " days from delivery."
            )
        elif value.replacement_type == False and value.return_type == True:
            message = (
                "return accepted in "
                + str(value.return_period)
                + " days from delivery."
            )
        elif value.replacement_type == False and value.return_type == False:
            message = "no returns accepted"
        return message

    def get_cart_details(self, obj):
        user_id = self.context.get("user_id")
        cart_item = CartItem.new_objects.get(
            userid=user_id,
            product_reference=obj.product_reference,
            cart_product_status="IN_CART",
        )
        quantity = cart_item.product_quantity
        cart_item_id = cart_item.cartitemid
        cart_status = cart_item.cart_product_status
        cart = {
            "cart_item_id": cart_item_id,
            "quantity": quantity,
            "cart_status": cart_status,
        }
        return cart

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if representation.get("prod_images"):
            list_of_prod_images = representation.get("prod_images")
            images = [
                    lst["product_image"]
                    for lst in list_of_prod_images
                    if not lst.get("is_deleted", False)
            ]
            first_image = images[0] if images else None

            representation.update(prod_images=first_image)
            return representation
        else:
            representation.update(prod_images=None)
            return representation





class GetCartProductDetailsSerializer(serializers.ModelSerializer):

    products = serializers.SerializerMethodField("get_products")
    trust_center = serializers.SerializerMethodField("get_trust_center")

    class Meta:
        model = Store
        fields = [
            "storeid",
            "store_reference",
            "storehandle",
            "icon",
            "store_name",
            "store_desc",
            "open_for_order",
            "deleted",
            "trust_center",
            "products"
        ]

    def get_trust_center(self, obj):

        store_reference = obj.store_reference
        trust_center = TrustCenter.objects.get(store_reference=store_reference)
        serializer = TrustCenterSerializer(trust_center)
        context = {
            "trustscore": serializer.data["trustscore"],
            "sellerlevel": serializer.data["sellerlevel"],
        }
        return context
    #
    # def get_products(self, obj):
    #     context = self.context.get("value_to_pass")
    #     user_id = self.context.get("user_id")
    #     product_serializer_data = []
    #
    #     # Extract product references for the current store
    #     product_references = [
    #         id["product_reference"] for id in context if obj.storeid == int(id["storeid"])
    #     ]
    #
    #     if product_references:
    #         # Filter products based on product references
    #         product_instances = Product.objects.filter(product_reference__in=product_references)
    #
    #         # Iterate through each product instance and serialize
    #         for product in product_instances:
    #             product_serializer = OrderedProductSerializer(
    #                 product,
    #                 context={"storeid": obj.storeid,
    #                          "user_id": user_id,
    #                          "product_reference": product.product_reference
    #                          },
    #             )
    #             product_serializer_data.append(product_serializer.data)
    #
    #     return product_serializer_data

    def get_products(self, obj):
        context = self.context.get("value_to_pass")
        user_id = self.context.get("user_id")
        user_instance = User.objects.get(userid=user_id)
        user_pincode = user_instance.pincode
        product_serializer_data = []
        for ids in context:
            if obj.storeid == int(ids["storeid"]):
                product_references = ids["product_reference"]
                for product_reference in product_references:
                    try:
                        product_instance = Product.objects.get(
                            product_reference=product_reference
                        )
                        product_serializer = CartProductSerializer(
                            product_instance,
                            context={
                                "storeid": obj.storeid,
                                "user_id": user_id,
                                "user_pincode": user_pincode,
                                "product_reference": product_instance.product_reference
                            },
                        )
                        product_serializer_data.append(product_serializer.data)

                    except Product.DoesNotExist:
                        # Handle the case where the product does not exist
                        pass
        return product_serializer_data


class UserProfileOrderPageSerializer(serializers.ModelSerializer):
    class Meta:

        model = User
        fields = [
            "userid",
            "icon",
            "user_name",
            "user_location",
            "pincode",
            "phonenumber",
            "email",
            "gender",
            "age",
        ]
        read_only_fields = fields