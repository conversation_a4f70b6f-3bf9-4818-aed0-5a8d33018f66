from decimal import Decimal
from rest_framework import serializers
from datetime import datetime
from .models import SubscriptionPlan
from stores.store_api.models import StoreSubscription
from users.user_api.models import UserSubscription

class SubscriptionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        fields = ['plan_name', 'plan_description', 'plan_period', 'plan_amount', 'plan_type', 'plan_details']

    def validate_plan_amount(self, value):
        # Razorpay expects amount in paise (smallest currency unit)
        if value < Decimal('1.00'):
            raise serializers.ValidationError("Plan amount must be at least ₹1.00")
        return value
