import re
from functools import wraps
from django.http import JsonResponse
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def validate_post_reference(func):
    """
    Validates a post reference in the format 'PO<14 digits>'.
    Example: PO202402281533078988
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        post_reference = request.POST.get('post_reference')
        # Validate user reference (example)
        if not re.match(r'^PO\d{18}$', post_reference):
            return JsonResponse({'error': 'Invalid post reference'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_user_reference(func):
    """
    Validates a user reference in the format 'U<13 digits>'.
    Example: U1234567890123
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        user_reference = request.POST.get('user_reference')
        if not (re.match(r'^U\d{13}$', user_reference)) or bool(re.match(r'^U\d{9}$', user_reference)):
            return JsonResponse({'error': 'Invalid user reference'}, status=400)
        return func(request, *args, **kwargs)    
    return wrapper


def validate_store_reference(func):
    """
    Validates a store reference in the format "S<13 digits>".
    Example: S1234567890123
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        store_reference = request.POST.get('store_reference')
        if not (re.match(r'^S\d{13}$', store_reference)) or bool(re.match(r'^S\d{7}$', store_reference)):
            return JsonResponse({'error': 'Invalid store reference'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_product_reference(func):
    """
    Validates a product reference in the format 'P<16 Numeric characters><4 Uppercase Alphabets>'.
    Example: P1234567890123456ACMJ
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        product_reference = request.POST.get('product_reference')
        if not (re.match(r'^P\d{16}[A-Z]{4}$', product_reference)) or bool(re.match(r'^P\d{7}[A-Z]{4}$', product_reference)):
            return JsonResponse({'error': 'Invalid product reference'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_phone_number(func):
    """
    Validates A phone number in the format '+91<10 Numeric Characters>'
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        phone_number = request.POST.get('phone_number')
        if not re.match(r'^+91\d{10}$', phone_number):
            return JsonResponse({'error': 'Invalid phone number'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_order_number(func):
    """
    Validates an order number in the format 'O<15 digits>'.
    Example: O2311172100430001
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        order_number = request.POST.get('order_number')
        if not re.match(r'^O\d{16}$', order_number):
            return JsonResponse({'error': 'Invalid order number'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_suborder_number(func):
    """
    Validates a suborder number in the format 'O<15 digits>-<2 digits>'.
    Example: O2311172100430001-01
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        suborder_number = request.POST.get('suborder_number')
        if not re.match(r'^O\d{16}-\d{2}$', suborder_number):
            return JsonResponse({'error': 'Invalid suborder number'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_pincode(func):
    """
    Validates a pincode in the format '<6 digits>'.
    Example: 110001
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        pincode = request.POST.get('pincode')
        if not re.match(r'^\d{6}$', pincode):
            return JsonResponse({'error': 'Invalid pincode'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_email(func):
    """
    Validates an email in the format '<any text>@<any text>.<any text>'.
    Example: <EMAIL>
    """
    @wraps(func)    
    def wrapper(request, *args, **kwargs):
        email = request.POST.get('email')
        if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', email):
            return JsonResponse({'error': 'Invalid email'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_login_otp(func):
    """
    Validates an otp in the format '<6 digits>'.
    Example: 110001
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        otp = request.POST.get('otp')
        if not re.match(r'^\d{6}$', otp):
            return JsonResponse({'error': 'Invalid otp'}, status=400)
        return func(request, *args, **kwargs)
    return wrapper


def validate_phonenumbers_list(func):
    """
    Validates A list of phone numbers in the format '+91<10 Numeric Characters>'
    Example: ['+919999999999', '+918888888888']
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        phonenumbers_list = request.POST.getlist('phonenumbers_list[]')
        # Validate each phone number in the list
        if not all(re.match(r'^\+91\d{10}$', phone_number) for phone_number in phonenumbers_list):
            return JsonResponse({'error': 'Invalid phone numbers list'}, status=400)
        return func(request, *args, **kwargs)

    return wrapper

#
def validate_user_reference_list(func):
    """
    Validates a list of user references in the format 'U<13 digits>'.
    Example: ['U1234567890123', 'U1234567890123']
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        user_reference_list = request.POST.getlist('user_reference_list[]')
        # Validate each user reference in the list
        if not all(re.match(r'^U\d{13}$', user_reference) or re.match(r'^U\d{9}$', user_reference) for user_reference in user_reference_list):
            return JsonResponse({'error': 'Invalid user reference list'}, status=400)
        return func(request, *args, **kwargs)

    return wrapper