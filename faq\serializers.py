from rest_framework import serializers
from .models import FaqCategory, FaqItem, FaqItemImages


class FaqItemImageSerializer(serializers.ModelSerializer):
    """Serializer for FAQ item images"""
    
    class Meta:
        model = FaqItemImages
        fields = ['id', 'image', 'order']
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Return full URL for the image
        if instance.image:
            request = self.context.get('request')
            if request:
                representation['image'] = request.build_absolute_uri(instance.image.url)
            else:
                representation['image'] = instance.image.url
        return representation


class FaqItemSerializer(serializers.ModelSerializer):
    """Serializer for FAQ items"""
    item_images = serializers.SerializerMethodField()
    
    class Meta:
        model = FaqItem
        fields = [
            'item_key',
            'question', 
            'answer',
            'order',
            'item_images'
        ]
    
    def get_item_images(self, obj):
        """Get ordered images for the FAQ item"""
        images = obj.images.all().order_by('order')
        return [
            self.context['request'].build_absolute_uri(img.image.url) 
            if self.context.get('request') else img.image.url
            for img in images
        ]


class FaqCategorySerializer(serializers.ModelSerializer):
    """Serializer for FAQ categories with nested items"""
    items = serializers.SerializerMethodField()
    
    class Meta:
        model = FaqCategory
        fields = [
            'category_key',
            'name',
            'order',
            'items'
        ]
    
    def get_items(self, obj):
        """Get active FAQ items for the category"""
        active_items = obj.get_active_items()
        return FaqItemSerializer(
            active_items, 
            many=True, 
            context=self.context
        ).data


class FaqDataSerializer(serializers.Serializer):
    """Main serializer for the complete FAQ data structure"""
    faq_categories = FaqCategorySerializer(many=True, read_only=True)


class FaqImageUploadSerializer(serializers.ModelSerializer):
    """Serializer for uploading FAQ item images"""
    
    class Meta:
        model = FaqItemImages
        fields = ['item', 'image', 'order']
        
    def validate_item(self, value):
        """Validate that the FAQ item exists and is active"""
        if not value.is_active:
            raise serializers.ValidationError("Cannot upload images to inactive FAQ items.")
        return value
        
    def validate_order(self, value):
        """Validate order field"""
        if value < 0:
            raise serializers.ValidationError("Order must be a positive integer.")
        return value
