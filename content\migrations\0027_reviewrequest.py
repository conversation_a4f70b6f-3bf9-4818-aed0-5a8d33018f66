# Generated by Django 4.2.7 on 2025-02-26 08:08

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0057_product_affiliate_commission_amount_and_more"),
        ("content", "0026_story_tagged_content"),
    ]

    operations = [
        migrations.CreateModel(
            name="ReviewRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("store_reference", models.Char<PERSON>ield(max_length=20)),
                ("token", models.UUIDField(default=uuid.uuid4, unique=True)),
                ("is_used", models.<PERSON>oleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user_identifier",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "product_reference",
                    models.ForeignKey(
                        db_column="product_reference",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_requests",
                        to="products.product",
                        to_field="product_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "review requests",
                "db_table": '"content"."review_request"',
            },
        ),
    ]
