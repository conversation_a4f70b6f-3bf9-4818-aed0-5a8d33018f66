# Generated by Django 4.2.7 on 2024-07-12 08:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0060_remove_store_flash_points_and_more"),
        ("users", "0068_remove_user_infinity_points"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserPoints",
            fields=[
                ("user_point_id", models.AutoField(primary_key=True, serialize=False)),
                ("infinity_points", models.PositiveIntegerField(default=0)),
                (
                    "user_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="user_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_points",
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "user points",
                "db_table": '"user"."user_points"',
            },
        ),
        migrations.CreateModel(
            name="StorePoints",
            fields=[
                ("store_point_id", models.AutoField(primary_key=True, serialize=False)),
                ("infinity_points", models.PositiveIntegerField(default=0)),
                ("flash_points", models.PositiveIntegerField(default=0)),
                (
                    "store_reference",
                    models.ForeignKey(
                        blank=True,
                        db_column="store_reference",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="store_points",
                        to="stores.store",
                        to_field="store_reference",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "store points",
                "db_table": '"store"."store_points"',
            },
        ),
    ]
