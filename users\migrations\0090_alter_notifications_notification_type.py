# Generated by Django 4.2.7 on 2024-09-27 19:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0089_alter_inviteuser_number_of_invites_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="notifications",
            name="notification_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING", "onboarding"),
                    ("PRODUCT_VERSION_CHANGED", "Product version changed"),
                    ("STORE_CREATED", "New store created near you"),
                    ("NEW_ORDER", "New order"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("ORDER_UPDATE", "Order update"),
                    ("ORDER_SHIPPED", "Order shipped"),
                    ("SHIPPING_PACAKGE_UPDATED", "Shipping package updated"),
                    ("DELIVERY_OTP", "Delivery otp"),
                    ("PACKAGE_DELIVERED", "Package delivered"),
                    ("DELIVERY_FAILED", "Delivery failed"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("SELLER_CANCELLED_ORDER", "Seller cancelled order"),
                    ("BUYER_CANCELLED_ORDER", "Buyer cancelled order"),
                    ("AUTO_CANCELLED", "Auto cancelled"),
                    ("RETURN_REQUESTED", "Return requested"),
                    ("RETURN_REQUEST_CANCELLED", "Return request cancelled"),
                    ("RETURN_ACCEPTED", "Return accepted"),
                    ("RETURN_RECEIVED", "Return received"),
                    ("REFUND_INITIATED", "Refund initiated"),
                    ("REFUND_HOLD", "Refund hold"),
                    ("REFUND_RELEASED", "Refund released"),
                    ("RETURN_OTP", "Return otp"),
                    ("COMMENTED", "Commented"),
                    ("PAYOUT_CREDITED", "Payout credited"),
                    ("PAYOUT_DEBITED", "Payout debited"),
                    ("CONTENT_LIKED", "content_liked"),
                    ("SOMEONE_FOLLOWED_ENTIY", "Someone_followed_entity"),
                    ("SOMEONE_COMMENTED_ON_CONTENT", "Someone_commented_on_content"),
                    ("SOMEONE_REPOSTED_CONTENT", "Someone reposted content"),
                    (
                        "SOMEONE_SIGNED_UP_WITH_YOUR_CODE",
                        "Someone signed up with your code",
                    ),
                    (
                        "YOU_SIGNED_UP_WITH_SOMEONES_CODE",
                        "You signed up with someones code",
                    ),
                    ("PAYOUT_OTP", "Payout otp"),
                    ("STATE_CHANGE", "State change"),
                    (
                        "STORE_VERIFICATION_ONBOARDING_REWARD",
                        "Store verification onboarding reward",
                    ),
                    (
                        "STORE_VERIFICATION_PROMOTION_REWARD",
                        "Store verification promotion reward",
                    ),
                    ("STORE_VERIFICATION_APPROVED", "Store verification approved"),
                    ("GST_VERIFICATION_APPROVED", "GST verification approved"),
                    ("PAN_VERIFICATION_APPROVED", "PAN verification approved"),
                    ("GST_VERIFICATION_REJECTED", "GST verification rejected"),
                    ("PAN_VERIFICATION_REJECTED", "PAN verification rejected"),
                    (
                        "PRODUCT_REMOVED_FROM_SHIPPING_GROUP",
                        "Product removed from shipping group",
                    ),
                    (
                        "PRODUCT_UPDATED_FROM_SHIPPING_GROUP",
                        "Product updated from shipping group",
                    ),
                ],
                max_length=100,
                null=True,
            ),
        ),
    ]
