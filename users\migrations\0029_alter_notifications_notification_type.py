# Generated by Django 3.2.13 on 2023-01-03 07:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0028_auto_20221229_2107'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notifications',
            name='notification_type',
            field=models.CharField(blank=True, choices=[('ONBOARDING', 'onboarding'), ('PRODUCT_VERSION_CHANGED', 'Product version changed'), ('STORE_CREATED', 'New store created near you'), ('ORDER_CONFIRMED', 'Order confirmed'), ('ORDER_SHIPPED', 'Order shipped'), ('NEW_ORDER', 'New order'), ('ORDER_CANCELLED', 'Order cancelled'), ('RETURN_REQUESTED', 'Return requested'), ('DELIVERY_OTP', 'Delivery otp'), ('RETURN_OTP', 'Return otp')], max_length=100, null=True),
        ),
    ]
