# Generated by Django 4.2.7 on 2024-03-12 09:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("content", "0008_alter_commentimages_reorder_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="commentimages",
            name="reorder",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="comments",
            name="comment_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="comments",
            name="like_count",
            field=models.IntegerField(default=0),
        ),
        migrations.Alter<PERSON>ield(
            model_name="comments",
            name="repost_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="comments",
            name="repost_plus_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="comments",
            name="save_count",
            field=models.Integer<PERSON>ield(default=0),
        ),
        migrations.Alter<PERSON>ield(
            model_name="comments",
            name="share_count",
            field=models.IntegerField(default=0),
        ),
        migrations.Alter<PERSON>ield(
            model_name="postimages",
            name="reorder",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="posts",
            name="comment_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="posts",
            name="like_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="posts",
            name="repost_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="posts",
            name="repost_plus_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="posts",
            name="save_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="posts",
            name="share_count",
            field=models.IntegerField(default=0),
        ),
    ]
