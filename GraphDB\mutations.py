import graphene
import os
import logging
from django.core.files import File
from decouple import config
import neo4j
from numpy import isin
from django.db.models import F
from users.user_api.utils import update_messaging_server_user_data
from .models import *
from .queries import dbqueries
from neomodel import db
from .helpers import *
import pandas as pd
from content.models import Posts, PostImages, Comments, CommentImages
from products.models import Product
from common.util.notification_handler import NotificationHandler
from users.notification_api.models import Notifications
from users.user_api.models import User
from stores.store_api.models import Store
from GraphDB.helpers import get_content_creator
import requests

logger = logging.getLogger(__name__)


def get_store_admin_group_chat_ids(admin_token):
    """
    Get all group chat IDs where the store is an admin using the messaging server API
    """
    try:

        # Make API request to get admin group chat IDs
        response = requests.get(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/chats/admin-groups",
            headers={
                'Authorization': f'Bearer {admin_token}',
                'Content-Type': 'application/json'
            },
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            chat_ids = data.get('data', [])
            logger.info(f"Retrieved {len(chat_ids)} admin group chat IDs for store ")
            return chat_ids
        else:
            logger.error(f"Failed to get admin group chat IDs for store: {response.status_code} - {response.text}")
            return []

    except Store.DoesNotExist:
        logger.error(f"Store not found")
        return []
    except Exception as e:
        logger.error(f"Error getting admin group chat IDs for store: {str(e)}")
        return []


def add_member_to_chat(chat_id, visitor_id, admin_token):
    """
    Add a member to a chat using the messaging server API
    """
    try:
        response = requests.post(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/chats/chatId={chat_id}/members",
            headers={
                'Authorization': f'Bearer {admin_token}',
                'Content-Type': 'application/json'
            },
            json={
                'memberIds': [visitor_id],
                'role_type': 'MEMBER'
            },
            timeout=10
        )

        if response.status_code == 200:
            logger.info(f"Successfully added member {visitor_id} to chat {chat_id}")
            return True
        else:
            logger.error(f"Failed to add member {visitor_id} to chat {chat_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"Error adding member {visitor_id} to chat {chat_id}: {str(e)}")
        return False


def remove_member_from_chat(chat_id, visitor_id, admin_token):
    """
    Remove a member from a chat using the messaging server API
    """
    try:
        response = requests.delete(
            f"{config('JS_MESSAGING_SERVER_URL2', default='http://localhost:4000')}/api/chats/chatId-{chat_id}/members/userId={visitor_id}",
            headers={
                'Authorization': f'Bearer {admin_token}',
                'Content-Type': 'application/json'
            },
            timeout=10
        )

        if response.status_code == 200:
            logger.info(f"Successfully removed member {visitor_id} from chat {chat_id}")
            return True
        else:
            logger.error(f"Failed to remove member {visitor_id} from chat {chat_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"Error removing member {visitor_id} from chat {chat_id}: {str(e)}")
        return False


def handle_store_chat_membership(vistor_id, store_id, store_token, is_following):
    """
    Handle adding/removing visitor from store admin group chats when following/unfollowing
    """
    try:
        if not all([vistor_id, store_id, store_token]):
            return
        # Get all admin group chat IDs for the store
        chat_ids = get_store_admin_group_chat_ids(store_token)

        if not chat_ids:
            logger.info(f"No admin group chats found for store")
            return

        # Add or remove visitor from each admin group chat
        for chat_id in chat_ids:
            if is_following:
                add_member_to_chat(chat_id, vistor_id, store_token)
            else:
                remove_member_from_chat(chat_id, vistor_id, store_token)

    except Store.DoesNotExist:
        logger.error(f"Store not found")
    except Exception as e:
        logger.error(f"Error handling store chat membership: {str(e)}")


class DeletePostMutation(graphene.Mutation):
    class Arguments:
        post_reference = graphene.String(required=True)

    success = graphene.Boolean()

    def mutate(self, info, post_reference):
        try:
            neo4j_post = Neo4jPost.nodes.get(reference=post_reference, is_deleted=False)
        except Neo4jPost.DoesNotExist:
            raise Exception(f"Node with post reference {post_reference} does not exist.")

        try:
            post = Posts.objects.get(post_reference=post_reference, is_deleted=False)
        except Posts.DoesNotExist:
            raise Exception(f"Entry with post reference {post_reference} does not exist.")

        # post_instance.is_deleted = True
        # post_instance.save()

        # post_media = post_instance.media.all()[0]
        # for post_image in post_images_queryset:
        #     post_image.is_deleted = True
        #     post_image.save()
        with db.transaction:
            post_images = PostImages.objects.filter(post_reference=post_reference, is_deleted=False)
            if post_images:
                for post_image in post_images:
                    post_image.delete()
            for media_node in neo4j_post.media.all():
                if media_node.media:
                    df = pd.DataFrame(media_node.media)
                    if df is not None:
                        media_paths_to_delete = df['media_path'].tolist()
                        # Delete media files
                        delete_media_file(media_paths_to_delete)
                        # Delete associated media nodes
                        media_node.delete()
            neo4j_post.delete()
            post.delete()



            # Delete associated media nodes

        return DeletePostMutation(success=True)


class LikeContentMutation(graphene.Mutation):
    class Arguments:
        content_reference = graphene.String(required=True)
        current_reference = graphene.String(required=True)
        action = graphene.Boolean(required=True)

    is_liked = graphene.Boolean()
    success = graphene.Boolean()
    message = graphene.String()
    is_custom = graphene.Boolean()

    def mutate(self, info, content_reference, current_reference, action):
        try:
            neo4j_content = Neo4jContent.nodes.get(reference=content_reference, is_deleted=False)
            neo4j_entity = Neo4jEntity.nodes.get(reference=current_reference, is_deleted=False)

            # get the contents owner and content type and visitor instance
            content_owner = get_content_creator(content_reference=content_reference)
            visitor_instance = User.objects.get(
                user_reference=current_reference, deleted=False) if current_reference.startswith(
                'U') else Store.objects.get(store_reference=current_reference, deleted=False)

            parent = get_content_instance(content_reference)

            if neo4j_entity.handle.startswith("test_") and neo4j_entity.handle != content_owner:
                return LikeContentMutation(
                    success=False,
                    is_liked=False,
                    message="Test Stores are not allowed to like content",
                    is_custom=True
                )

            if action:
                if not neo4j_content.liked_by.is_connected(neo4j_entity):
                    neo4j_content.liked_by.connect(neo4j_entity)
                    # increment the like count of content in RDB

                    parent.like_count += 1
                    parent.save()

                    # Create a notification
                    try:
                        if content_owner.reference != current_reference:
                            notification_handler = NotificationHandler(
                                notified_user=content_owner.reference,
                                notification_type=Notifications.Notifications_Type.CONTENT_LIKED,
                                notification_about=content_reference,
                                notified_from=visitor_instance.storehandle if isinstance(visitor_instance, Store) else visitor_instance.user_name,
                                image=visitor_instance.icon if visitor_instance.icon else None,
                                content_type=get_content_type(content_reference=content_reference)
                            )
                            notification_handler.create_notification(notification_handler)
                    except Exception as e:
                        logger.error(f"Content liked notification Failed:{e}")

                is_liked = True

            else:
                if neo4j_content.liked_by.is_connected(neo4j_entity):
                    neo4j_content.liked_by.disconnect(neo4j_entity)
                    duplicate_notifications = Notifications.objects.filter(
                        notified_user=content_owner.reference,
                        notification_about=content_reference,
                        notification_type=Notifications.Notifications_Type.CONTENT_LIKED)
                    duplicate_notifications.delete()

                    parent.like_count -= 1
                    parent.save()
                is_liked = False
        except Neo4jContent.DoesNotExist or Neo4jEntity.DoesNotExist:
            raise Exception("Content or Entity not found.")

        return LikeContentMutation(
            success=True,
            is_liked=is_liked,
            message="Operation Successful",
            is_custom=False
        )


class DeletePostImageMutation(graphene.Mutation):
    class Arguments:
        post_reference = graphene.String(required=True)
        media_id = graphene.String(required=True)

    success = graphene.Boolean()
    message = graphene.String()

    def mutate(self, info, post_reference, media_id):
        try:
            neo4j_post = Neo4jPost.nodes.get(reference=post_reference, is_deleted=False)
            media_node = neo4j_post.media.all()[0]
            # Soft delete post image by setting is_deleted to True
            # post_image_instance.is_deleted = True
            # post_image_instance.save()
            # media_set_to_be_deleted = [media_set for media_set in media_node.media if media_set['media_id'] == media_id]
            # media_url = media_set_to_be_deleted[0]['media_path']
            # delete_media_file(media_url)

            # new_media_set = [media_set for media_set in media_node.media if media_set['media_id'] != media_id]
            # media_node.media = new_media_set
            with db.transaction:
                df = pd.DataFrame(media_node.media)
                df_media_to_remove = df[df['media_id'] == media_id]
                image_path_to_remove = df_media_to_remove['media_path'].tolist()
                # Delete media file
                delete_media_file(image_path_to_remove)

                new_df = df[df['media_id'] != media_id]
                new_media_set = new_df.to_dict('records')
                media_node.media = new_media_set
                media_node.save()
                try:
                    post_image = PostImages.objects.get(post_reference=post_reference, post_image_id=media_id)
                    post_image.delete()
                except PostImages.DoesNotExist:
                    logger.error("No relevant Postimage entry found")

            return DeletePostImageMutation(success=True, message="Post image deleted successfully")

        except Neo4jPost.DoesNotExist:
            return DeletePostImageMutation(success=False, message=f"No post found for post reference{post_reference}.")

        except Exception as e:
            return DeletePostImageMutation(success=False, message=f"An error occurred: {str(e)}")


class FollowOrSupportMutation(graphene.Mutation):
    class Arguments:
        visitor_reference = graphene.String(required=True)
        entity_reference = graphene.String(required=True)

    is_following = graphene.Boolean()
    follow_status = graphene.String()

    def mutate(self, info, visitor_reference, entity_reference):
        visitor = Neo4jEntity.nodes.get(reference =visitor_reference,is_deleted=False)
        if entity_reference.startswith('UR'):
            entity = Neo4jUnrUser.nodes.get(reference=entity_reference)
        else:
            entity = Neo4jEntity.nodes.get(reference=entity_reference)
            entity_instance = User.objects.get(user_reference=entity_reference) if entity_reference.startswith('U') else Store.objects.get(store_reference=entity_reference)

        # get the visitor instance from RDB
        visitor_instance = User.objects.get(user_reference=visitor_reference) if visitor_reference.startswith('U') else Store.objects.get(store_reference=visitor_reference)


        if not entity.followed_by.is_connected(visitor):
            # relationship = entity.followed_by.connect(visitor,  {'datetimestamp': datetime.now()})
            relationship = dbqueries.follow_or_unfollow_entity(
                visitor_reference=visitor_reference,
                entity_reference=entity_reference
                )
            if relationship:
                is_following = True
            follow_status = entity.follow_status(visitor.reference)

            # Create a notification
            if not entity_reference.startswith('UR'):
                notification_handler = NotificationHandler(
                    notified_user=entity_reference,
                    notification_type=Notifications.Notifications_Type.SOMEONE_FOLLOWED_ENTITY,
                    notification_about=visitor_reference,
                    image=visitor_instance.icon if visitor_instance.icon else None
                )
                notification_handler.create_notification(notification_handler)

            # Handle messaging server chat membership for store follows
            try:
                handle_store_chat_membership(visitor_instance.new_messaging_user_id, entity_instance.new_messaging_user_id, entity_instance.new_messaging_token, is_following=True)
            except Exception as e:
                logger.error(f"Error adding visitor to store chat: {str(e)}")

        else:
            entity.followed_by.disconnect(visitor)
            is_following = False
            follow_status = entity.follow_status(visitor.reference)

            # Handle messaging server chat membership for store unfollows
            try:
                handle_store_chat_membership(visitor_instance.new_messaging_user_id, entity_instance.new_messaging_user_id, entity_instance.new_messaging_token, is_following=False)
            except Exception as e:
                logger.error(f"Error removing visitor from store chat: {str(e)}")

            # content_removed_from_feed = dbqueries.remove_content_from_feed(
            #     follower_reference=visitor_reference,
            #     unfollowed_reference=entity_reference
            # )
            # logger.info(f"Content removed from feed: {content_removed_from_feed}")
        if not isinstance(entity, Neo4jUnrUser):
            follower_count = entity.get_follower_count()
            update_messaging_server_user_data(instance=entity_instance, followers_count=follower_count)


        return FollowOrSupportMutation(is_following=is_following,
                                       follow_status=follow_status )

# todo add bulk following of entities
# class BulkFollowOrSupportMutation(graphene.Mutation):
#     class Arguments:
#         action = graphene.Boolean(required=True)
#         visitor_reference = graphene.String(required=True)
#         entity_references = graphene.String(required=True)
#
#     is_following = graphene.Boolean()
#     follow_status = graphene.String()
#
#     def mutate(self, info, visitor_reference, entity_reference, action):
#         try:
#             visitor = Neo4jEntity.nodes.get(reference =visitor_reference,is_deleted=False)
#             if entity_reference.startswith('UR'):
#                 entity = Neo4jUnrUser.nodes.get(reference=entity_reference)
#             else:
#                 entity = Neo4jEntity.nodes.get(reference=entity_reference)
#
#             if action:
#                 if not entity.followed_by.is_connected(visitor):
#                     entity.followed_by.connect(visitor)
#                 is_following = True
#                 follow_status = entity.follow_status(visitor.reference)
#
#                 # get the visitor instance from RDB
#                 visitor_instance = User.objects.get(user_reference=visitor_reference) if visitor_reference.startswith(
#                     'U') else Store.objects.get(store_reference=visitor_reference)
#
#                 # Create a notification
#                 notification_handler = NotificationHandler(
#                     notified_user=entity_reference,
#                     notification_type=Notifications.Notifications_Type.SOMEONE_FOLLOWED_ENTITY,
#                     notification_about=visitor_reference,
#                     image=visitor_instance.icon,
#                 )
#                 notification_handler.create_notification(notification_handler)
#
#             else:
#                 if entity.followed_by.is_connected(visitor):
#                     entity.followed_by.disconnect(visitor)
#                 is_following = False
#                 follow_status = entity.follow_status(visitor.reference)
#         except Neo4jEntity.DoesNotExist or Neo4jUnrUser.DoesNotExist:
#             raise Exception("Content or Entity not found.")
#
#         return FollowOrSupportMutation(is_following=is_following,
#                                        follow_status=follow_status )


class GetFollowStatusMutation(graphene.Mutation):
    class FollowStatusData(graphene.ObjectType):
        reference = graphene.String()
        follow_status = graphene.String()

    class Arguments:
        visitor_reference = graphene.String(required=True)
        references = graphene.List(graphene.String, required=True)

    data = graphene.List(FollowStatusData)

    def mutate(self, info, visitor_reference, references):
        data = dbqueries.get_follow_status(visitor_reference, references)
        return GetFollowStatusMutation(data=data)


class AddVisitMutation(graphene.Mutation):
    class Arguments:
        visitor_reference = graphene.String(required=True)
        visited_reference = graphene.String(required=True)

    visited = graphene.Boolean()

    def mutate(self, info, visitor_reference, visited_reference):
        try:
            # Use the simplified Cypher query to create/update the visit relationship
            visit_success = dbqueries.add_visit_relationship(
                visitor_reference=visitor_reference,
                visited_reference=visited_reference
            )

            if not visit_success:
                return AddVisitMutation(visited=False)

            # Update the analytics_view_count in the appropriate Django model using F() for atomic updates
            try:
                # Determine which model to update based on the reference prefix
                if visited_reference.startswith('PO'):
                    # It's a post
                    Posts.objects.filter(
                        post_reference=visited_reference,
                        is_deleted=False
                    ).update(analytics_view_count=F('analytics_view_count') + 1)

                elif visited_reference.startswith('P') and not visited_reference.startswith('PO'):
                    # It's a product
                    Product.objects.filter(
                        product_reference=visited_reference,
                        deleted=False
                    ).update(analytics_view_count=F('analytics_view_count') + 1)

                elif visited_reference.startswith('C'):
                    # It's a comment
                    Comments.objects.filter(
                        comment_reference=visited_reference,
                        is_deleted=False
                    ).update(analytics_view_count=F('analytics_view_count') + 1)

                elif visited_reference.startswith('S') and not visited_reference.startswith('ST'):
                    # It's a store
                    Store.objects.filter(
                        store_reference=visited_reference,
                        deleted=False
                    ).update(analytics_view_count=F('analytics_view_count') + 1)

            except Exception as e:
                # Log the error but don't fail the mutation if the model update fails
                # The relationship was still created in Neo4j
                logger.error(f"Error updating analytics_view_count: {str(e)}")

            return AddVisitMutation(visited=True)

        except Exception as e:
            logger.error(f"Error in AddVisitMutation: {str(e)}")
            raise Exception(f"Error processing visit: {str(e)}")


class SaveContentMutation(graphene.Mutation):
    class Arguments:
        content_reference = graphene.String(required=True)
        current_reference = graphene.String(required=True)
        action = graphene.Boolean(required=True)

    is_saved = graphene.Boolean()

    def mutate(self, info, content_reference, current_reference, action):
        try:
            neo4j_content = Neo4jContent.nodes.get(reference=content_reference, is_deleted=False)
            neo4j_entity = Neo4jEntity.nodes.get(reference=current_reference, is_deleted=False)

            parent = get_content_instance(content_reference)

            if action:
                if not neo4j_content.saved_by.is_connected(neo4j_entity):
                    neo4j_content.saved_by.connect(neo4j_entity)
                is_saved = True
                parent.save_count += 1
                parent.save()
            else:
                if neo4j_content.saved_by.is_connected(neo4j_entity):
                    neo4j_content.saved_by.disconnect(neo4j_entity)
                is_saved = False
                parent.save_count -= 1
                parent.save()
        except Neo4jContent.DoesNotExist or Neo4jEntity.DoesNotExist:
            raise Exception("Content or Entity not found.")

        return SaveContentMutation(is_saved=is_saved)


class UnsyncContactsMutation(graphene.Mutation):

    class Arguments:
        asking_reference = graphene.String(required=True)

    deleted_contacts = graphene.Int()
    deleted_unruser_nodes = graphene.Int()

    def mutate(self, info, asking_reference):
        result = dbqueries.unsync_contacts(asking_reference)
        deleted_contacts = 0
        deleted_unruser_nodes = 0
        if result:
            deleted_contacts = result[0][0] if result[0][0] else 0
            deleted_unruser_nodes = result[0][1] if result[0][1] else 0

        return UnsyncContactsMutation(deleted_contacts=deleted_contacts,
                                      deleted_unruser_nodes=deleted_unruser_nodes)


class DeleteCommentMutation(graphene.Mutation):
    class Arguments:
        comment_reference= graphene.String(required=True)

    success = graphene.Boolean()
    message = graphene.String()
    def mutate(self, info, comment_reference):

        try:
            comment = Comments.objects.get(comment_reference=comment_reference, is_deleted=False)
            neo4j_comment = Neo4jComment.nodes.get(reference=comment_reference, is_deleted=False)
        except Comments.DoesNotExist or Neo4jComment.DoesNotExist:
            return DeleteCommentMutation(success=True, message="Comment entry/node does not exist")

        with db.transaction:
            # Delete comment text and images but keep other references intact
            comment.comment_text = None
            comment.comment_images.all().delete()
            comment.is_deleted = True
            comment.save()

            neo4j_comment.comment_text = None
            neo4j_comment.is_deleted = True
            neo4j_comment.save()

            if comment.parent_comment_id:
                parent_comment = Comments.objects.get(comment_reference=comment.parent_comment_id, is_deleted=False)
                parent_comment.comment_count -= 1
                parent_comment.save()

            if not comment.parent_comment_id:
                #if there is no parent comment it means its a direct comment of a post or a product
                if comment.main_parent_id.startswith('PO'):
                    parent = Posts.objects.get(post_reference=comment.main_parent_id, is_deleted=False)
                else: #comment.main_parent_id.startswith('P'):
                    parent = Product.objects.get(product_reference=comment.main_parent_id, deleted=False)
                parent.comment_count -= 1
                parent.save()
        return DeleteCommentMutation(success=True, message="Comment deleted successfully")


class DeleteCommentImageMutation(graphene.Mutation):
    class Arguments:
        comment_image_id = graphene.String(required=True)

    success = graphene.Boolean()
    message = graphene.String()

    def mutate(self,info, comment_image_id):
        try:
            comment_image_instance = CommentImages.objects.get(comment_image_id=comment_image_id, is_deleted=False)
        except CommentImages.DoesNotExist:
            raise Exception("Comment image does not exist")
        with db.transaction:
            comment_image_url = comment_image_instance.comment_image
            delete_media_file([comment_image_url])
            comment_image_instance.delete()

        return DeleteCommentImageMutation(success=True, message="Post image deleted successfully")


class AddRepostMutation(graphene.Mutation):
    class Arguments:
        content_reference = graphene.String(required=True)
        entity_reference = graphene.String(required=True)
        action = graphene.Boolean(required=True)

    is_reposted = graphene.Boolean()

    def mutate(self,info,content_reference, entity_reference, action):
        try:
            neo4j_content = Neo4jContent.nodes.get(reference=content_reference, is_deleted=False)
            neo4j_entity = Neo4jEntity.nodes.get(reference=entity_reference, is_deleted=False)
            content_owner = get_content_creator(content_reference=content_reference)
            visitor_instance = User.objects.get(
                user_reference=entity_reference, deleted=False) if entity_reference.startswith(
                'U') else Store.objects.get(store_reference=entity_reference, deleted=False)
        except Neo4jContent.DoesNotExist or Neo4jEntity.DoesNotExist:
            raise Exception("Content or Entity not found.")

        content_instance = get_content_instance(content_reference)

        with db.transaction:
            if action:
                if not neo4j_entity.reposts.is_connected(neo4j_content):
                    relation = neo4j_entity.reposts.connect(neo4j_content)
                    relation.datetimestamp = datetime.now()
                    relation.save()
                    content_instance.repost_count += 1
                    content_instance.save()
                    try:
                        if content_owner.reference != entity_reference:
                            notification_handler = NotificationHandler(
                                notified_user=content_owner.reference,
                                notification_type=Notifications.Notifications_Type.SOMEONE_REPOSTED_CONTENT,
                                notification_about=content_reference,
                                notified_from=neo4j_entity.handle,
                                image=visitor_instance.icon if visitor_instance.icon else None,
                                content_type=get_content_type(content_reference=content_reference)
                            )
                            notification_handler.create_notification(notification_handler)
                    except Exception as e:
                        logger.error(f"Content liked notification Failed:{e}")
                is_reposted = True
            else:
                if neo4j_entity.reposts.is_connected(neo4j_content):
                    neo4j_entity.reposts.disconnect(neo4j_content)
                    duplicate_notifications = Notifications.objects.filter(
                        notified_user=content_owner.reference,
                        notification_about=content_reference,
                        notification_type=Notifications.Notifications_Type.SOMEONE_REPOSTED_CONTENT)
                    duplicate_notifications.delete()
                    content_instance.repost_count -= 1
                    content_instance.save()
                is_reposted = False

        return AddRepostMutation(is_reposted=is_reposted)
