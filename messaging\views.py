from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import requests
import logging
from django.core.cache import cache
from decouple import config
from django.db.models import Q
from users.user_api.models import User
from stores.store_api.models import Store
from GraphDB.models import Neo4jUser, Neo4jStore, Neo4jEntity
from datetime import datetime
import asyncio
import aiohttp
from typing import Tuple, Optional, Dict, Any

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def get_instance_by_reference(entity_reference):
        """
        Helper method to get entity (User or Store) based on reference prefix.
        U prefix -> User
        S prefix -> Store
        """
        try:
            if not entity_reference:
                return None
                
            if entity_reference.startswith('U'):
                return User.objects.get(user_reference=entity_reference)
            elif entity_reference.startswith('S'):
                return Store.objects.get(store_reference=entity_reference)
            else:
                return None
        except (User.DoesNotExist, Store.DoesNotExist):
            return None

def get_entity_profile_from_neo4j(entity_reference):
    """
    Helper method to get entity profile details from Neo4j based on reference
    """
    try:
        record = Neo4jEntity.nodes.get(reference=entity_reference)
        if record:
            return {
                    'icon': record.icon,
                    'name': record.name,
                    'handle': record.handle,
                    'reference': entity_reference,
                    'follower_or_supporter_count': record.get_follower_count()
                }
            return None
    except Exception as e:
        logger.error(f"Error fetching entity profile from Neo4j: {str(e)}")
        return None

def get_or_create_room_id(from_instance, to_instance):
    """
    Helper method to get/create room id from messaging server by both entity references
    Returns tuple of (success: bool, room_id: str, error: str)
    """
    try:
        # First get auth token for the requesting user
        success, auth_data, error = get_or_create_auth_token(
            from_instance.xmpp_jid,
            from_instance.xmpp_password
        )
        if not success:
            return False, None, f"Authentication failed: {error}"

        # Call im.create to get or create DM room
        response = requests.post(
            f"{config('MESSAGING_SERVER_URL')}/api/v1/im.create",
            json={
                "username": to_instance.xmpp_jid  # Username of the user to create DM with
            },
            headers={
                'X-Auth-Token': auth_data['auth_token'],
                'X-User-Id': auth_data['user_id'],
                'Content-Type': 'application/json'
            }
        )

        if response.status_code == 200:
            room_data = response.json()
            room_id = room_data.get('room', {}).get('rid')
            if room_id:
                return True, room_id, None
            else:
                return False, None, "Room ID not found in response"
        else:
            return False, None, f"Failed to create room: {response.text}"

    except Exception as e:
        logger.error(f"Error in get_or_create_room_id: {str(e)}")
        return False, None, str(e)

def get_or_create_auth_token(user_xmpp_jid, user_xmpp_password):
    """
    Helper method to get cached auth token or create new one
    Returns tuple of (success: bool, data: dict, error: str)
    """
    # Check cache first
    auth_data = cache.get(f"messaging_auth_{user_xmpp_jid}")
    if auth_data:
        return True, auth_data, None

    # Make login request to messaging server
    try:
        response = requests.post(
            f"{config('MESSAGING_SERVER_URL')}/api/v1/login",
            json={
                "username": user_xmpp_jid,
                "password": user_xmpp_password
            },
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            data = response.json()
            cache_data = {
                'auth_token': data['data']['authToken'],
                'user_id': data['data']['userId']
            }
            cache.set(f"messaging_auth_{user_xmpp_jid}", cache_data, timeout=86400)  # Cache for 24 hours
            return True, cache_data, None
        else:
            return False, None, response.text
    except Exception as e:
        logger.error(f"Error in getting auth token: {str(e)}")
        return False, None, str(e)


def get_node_by_jid(xmpp_jid):
        """
                Get user or store Node by XMPP JID
        """
        try:
            return Neo4jEntity.nodes.get(reference=xmpp_jid)
        except Exception:
            return None


class MessagingLogin(APIView):
    """
    API to login to messaging server and cache the auth token
    """
    def post(self, request):
        try:
            user_reference = request.data.get('user_reference')

            if not user_reference:
                return Response({
                    "message": "user_reference is required",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            user_instance = User.objects.get(user_reference=user_reference)
            user_xmpp_jid = user_instance.xmpp_jid
            user_xmpp_password = user_instance.xmpp_password

            if not user_xmpp_jid or not user_xmpp_password:
                return Response({
                    "message": "user is not registered with messaging server",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            if cache.get(f"messaging_auth_{user_xmpp_jid}"):
                return Response(status=status.HTTP_200_OK)

            else:
                # Make login request to messaging server
                response = requests.post(
                    f"{config('MESSAGING_SERVER_URL')}/api/v1/login",
                    json={
                        "username": user_xmpp_jid,
                        "password": user_xmpp_password
                    },
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code == 200:
                    data = response.json()
                    
                    # Cache the auth token with username as key
                    cache_data = {
                        'auth_token': data['data']['authToken'],
                        'user_id': data['data']['userId']
                    }
                    cache.set(f"messaging_auth_{user_xmpp_jid}", cache_data, timeout=86400)  # Cache for 24 hours

                    return Response(status=status.HTTP_200_OK)
                else:
                    return Response({
                        "message": "Login failed",
                        "error": response.text,
                        "is_custom": True
                    }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in messaging login: {str(e)}")
            return Response({
                "message": "Internal server error",
                "error": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class SendMessage(APIView):
    """
    API to send a message to another user using realtime WebSocket connection
    """
    async def post(self, request):
        try:
            from_user_reference = request.data.get('from_user_reference')
            to_user_reference = request.data.get('to_user_reference')
            message = request.data.get('message')

            if not all([from_user_reference, to_user_reference, message]):
                return Response({
                    "message": "from_user_reference, to_user_reference and message are required",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user instances
            from_user_instance = User.objects.get(user_reference=from_user_reference) if from_user_reference.startswith('U') else Store.objects.get(store_reference=from_user_reference)
            to_user_instance = User.objects.get(user_reference=to_user_reference) if to_user_reference.startswith('U') else Store.objects.get(store_reference=to_user_reference)

            from_user_xmpp_jid = from_user_instance.xmpp_jid
            to_user_xmpp_jid = to_user_instance.xmpp_jid

            # Replace the auth check with new helper method
            auth_data = cache.get(f"messaging_auth_{from_user_xmpp_jid}")
            if not auth_data:
                success, auth_data, error = get_or_create_auth_token(
                    from_user_xmpp_jid,
                    from_user_instance.xmpp_password
                )
                if not success:
                    return Response({
                        "message": "Authentication failed",
                        "error": error,
                        "is_custom": True
                    }, status=status.HTTP_401_UNAUTHORIZED)

            # Send message request to messaging server
            response = requests.post(
                f"{config('MESSAGING_SERVER_URL')}/api/v1/chat.postMessage",
                json={
                    "channel": f"@{to_user_xmpp_jid}",
                    "text": message
                },
                headers={
                    'X-Auth-Token': auth_data['auth_token'],
                    'X-User-Id': auth_data['user_id'],
                    'Content-Type': 'application/json'
                }
            )

            if response.status_code == 200:
                return Response({
                    "message": "Message sent successfully",
                    "room_id": response.json()['message']['rid']
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "message": "Failed to send message",
                    "error": response.text,
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in sending message: {str(e)}")
            return Response({
                "message": "Internal server error",
                "error": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GetChatList(APIView):
    """API to get messaging home screen data including DM list and recommendations."""
    def get(self, request):
        try:
            entity_reference = request.query_params.get('entity_reference')
            if not entity_reference:
                return Response({
                    "error": "entity_reference is required",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user instance
            instance = get_instance_by_reference(entity_reference)
            if not instance:
                return Response({
                    "error": "Invalid entity reference",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get auth token
            success, auth_data, error = get_or_create_auth_token(
                instance.xmpp_jid,
                instance.xmpp_password
            )
            if not success:
                return Response({
                    "error": "Authentication failed",
                    "details": error,
                    "is_custom": True
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Get DM list from Rocket.Chat
            response = requests.get(
                f"{config('MESSAGING_SERVER_URL')}/api/v1/subscriptions.get",
                headers={
                    'X-Auth-Token': auth_data['auth_token'],
                    'X-User-Id': auth_data['user_id']
                }
            )

            if response.status_code != 200:
                return Response({
                    "error": "Failed to fetch DM list",
                    "details": response.text,
                    "is_custom": True
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            subscriptions_data = response.json()
            dm_list = []

            # Process DM list
            for room in subscriptions_data.get('update', []):
                # Only process direct messages
                if room.get('t') != 'd':
                    continue

                other_username = room.get('name')
                if other_username:
                    other_user = get_entity_profile_from_neo4j(other_username)
                    if other_user:
                        room_id = room.get('rid')
                        last_message = None

                        if room_id:
                            history_response = requests.get(
                                f"{config('MESSAGING_SERVER_URL')}/api/v1/im.history",
                                params={
                                    'roomId': room_id,
                                    'count': 1  # Get only the last message
                                },
                                headers={
                                    'X-Auth-Token': auth_data['auth_token'],
                                    'X-User-Id': auth_data['user_id']
                                }
                            )

                            if history_response.status_code == 200:
                                history_data = history_response.json()
                                messages = history_data.get('messages', [])
                                if messages:
                                    last_message = {
                                        'text': messages[0].get('msg', ''),
                                        'timestamp': messages[0].get('ts', ''),
                                        'sender': messages[0].get('u', {}).get('username', '')
                                    }
                        dm_info = {
                            'handle': other_user['handle'],
                            'entity_reference': other_user['reference'],
                            'icon': other_user['icon'],
                            'is_store': other_user['reference'].startswith('S'),
                            'last_message': last_message,
                            'room_id': room.get('rid'),
                            'unread_count': room.get('unread', 0),
                            'last_read': room.get('ls'),
                            '_updatedAt': room.get('_updatedAt')
                        }
                        dm_list.append(dm_info)

            # Sort DM list by last update timestamp
            dm_list.sort(key=lambda x: x.get('_updatedAt', ''), reverse=True)

            # Get recommendations if DM list is less than 20
            recommendations = []
            if len(dm_list) < 20:
                needed_recommendations = 20 - len(dm_list)
                
                # Get users that the current user is following
                following_users = Neo4jEntity.nodes.get(reference=entity_reference).get_following()

                # Convert Neo4j nodes to recommendation objects
                for following in following_users:
                    if len(recommendations) >= needed_recommendations:
                        break

                    # Skip if user is already in DM list
                    if any(dm['entity_reference'] == following.reference for dm in dm_list):
                        continue

                    other_user = get_entity_profile_from_neo4j(following.reference)
                    if other_user:
                        recommendations.append({
                            'handle': other_user['handle'],
                            'entity_reference': other_user['reference'],
                            'icon': other_user['icon'],
                            'is_store': other_user['reference'].startswith('S')
                        })

                # If still need more recommendations, add other users
                if len(recommendations) < needed_recommendations:
                    remaining_needed = needed_recommendations - len(recommendations)
                    
                    # Exclude users already in DM list and recommendations
                    excluded_references = [dm['entity_reference'] for dm in dm_list] + [rec['entity_reference'] for rec in recommendations]
                    other_users = Neo4jEntity.nodes.exclude(reference__in=excluded_references)[:remaining_needed]
                    
                    for other_user in other_users:
                        recommendations.append({
                            'handle': other_user.handle,
                            'entity_reference': other_user.reference,
                            'icon': other_user.icon,
                            'is_store': other_user.reference.startswith('S')
                        })

            return Response({
                'message': 'Success',
                'data': {
                    'has_existing_dms': bool(dm_list),
                    'dm_list': dm_list,
                    'recommendations': recommendations
                }
            })

        except Exception as e:
            logger.error(f"Error in get_dm_list: {str(e)}")
            return Response({
                "error": "Internal server error",
                "details": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class VisitChat(APIView):
    def post(self, request):
        try:
            user_reference = request.data.get('user_reference')
            room_id = request.data.get('room_id')
            
            if not all([user_reference, room_id]):
                return Response({
                    "message": "user_reference and room_id are required",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user instance and auth token
            instance = User.objects.get(user_reference=user_reference) if user_reference.startswith('U') else Store.objects.get(store_reference=user_reference)
            
            # Get auth token
            auth_data = cache.get(f"messaging_auth_{instance.xmpp_jid}")
            if not auth_data:
                success, auth_data, error = get_or_create_auth_token(
                    instance.xmpp_jid,
                    instance.xmpp_password
                )
                if not success:
                    return Response({
                        "message": "Authentication failed",
                        "error": error,
                        "is_custom": True
                    }, status=status.HTTP_401_UNAUTHORIZED)

            # Use subscriptions.read endpoint to mark messages as read
            response = requests.post(
                f"{config('MESSAGING_SERVER_URL')}/api/v1/subscriptions.read",
                json={"rid": room_id},  # rid is the room ID
                headers={
                    'X-Auth-Token': auth_data['auth_token'],
                    'X-User-Id': auth_data['user_id'],
                    'Content-Type': 'application/json'
                }
            )

            if response.status_code != 200:
                return Response({
                    "message": "Failed to update read status",
                    "error": response.text,
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({"message": "Success"}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error updating last visited timestamp: {str(e)}")
            return Response({
                "message": "Internal server error",
                "error": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class RateLimiter:
    """Rate limiter for API requests"""
    def __init__(self):
        self._cache = cache
        
    def check_rate_limit(self, user_id, room_id, max_requests=30, window_seconds=60):
        cache_key = f"msg_rate_limit_{user_id}_{room_id}"
        current_count = self._cache.get(cache_key, 0)
        if current_count >= max_requests:
            return False
        self._cache.set(cache_key, current_count + 1, window_seconds)
        return True

class GetMessages(APIView):
    """API to fetch messages for a DM conversation based on direction and anchor time."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.rate_limiter = RateLimiter()
        self.logger = logging.getLogger(__name__)

    def get_subscription_last_read(self, room_id, auth_data):
        """Get the last read timestamp for a specific room"""
        try:
            # First try to get from subscriptions
            response = requests.get(
                f"{config('MESSAGING_SERVER_URL')}/api/v1/subscriptions.get",
                headers={
                    'X-Auth-Token': auth_data['auth_token'],
                    'X-User-Id': auth_data['user_id']
                },
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                # Find the subscription for our room
                for sub in data.get('update', []):
                    if sub.get('rid') == room_id:
                        self.unread_count = sub.get('unread', 0)
                        last_seen = sub.get('ls')
                        
                        # Handle different timestamp formats
                        if last_seen:
                            try:
                                if isinstance(last_seen, dict) and '$date' in last_seen:
                                    # Handle MongoDB date format
                                    timestamp = last_seen.get('$date')
                                    if isinstance(timestamp, int):
                                        return datetime.fromtimestamp(timestamp/1000).isoformat() + 'Z', self.unread_count
                                elif isinstance(last_seen, str):
                                    # Handle ISO format string
                                    return last_seen, self.unread_count
                            except (ValueError, TypeError) as e:
                                self.logger.error(f"Error parsing last_seen timestamp {last_seen}: {str(e)}")

            # If no valid last_seen or subscription not found, try to get it from message history
            history_response = requests.get(
                f"{config('MESSAGING_SERVER_URL')}/api/v1/im.history",
                params={
                    'roomId': room_id,
                    'count': '1'  # Just get the latest message
                },
                headers={
                    'X-Auth-Token': auth_data['auth_token'],
                    'X-User-Id': auth_data['user_id']
                },
                timeout=10
            )

            if history_response.status_code == 200:
                history_data = history_response.json()
                messages = history_data.get('messages', [])
                if messages:
                    return messages[0].get('ts') , self.unread_count # Return the timestamp of the latest message

            return None, 0

        except Exception as e:
            self.logger.error(f"Error getting subscription last read: {str(e)}", exc_info=True)
            return None

    def _get_cached_profile(self, entity_reference):
        """Get cached profile or fetch from Neo4j"""
        cache_key = f"profile_{entity_reference}"
        profile = cache.get(cache_key)
        if not profile:
            profile = get_entity_profile_from_neo4j(entity_reference)
            if profile:
                cache.set(cache_key, profile, 300)  # Cache for 5 minutes
        return profile

    def _get_cached_room_id(self, from_instance, to_instance):
        """Get cached room ID or create new one"""
        cache_key = f"room_id_{from_instance.xmpp_jid}_{to_instance.xmpp_jid}"
        room_id = cache.get(cache_key)
        if not room_id:
            success, room_id, error = get_or_create_room_id(from_instance, to_instance)
            if success:
                cache.set(cache_key, room_id, 3600)  # Cache for 1 hour
                return room_id, None
            return None, error
        return room_id, None


    def post(self, request):
        try:
            # Validate input parameters
            from_entity_reference = request.data.get('from_entity_reference')
            to_entity_reference = request.data.get('to_entity_reference')
            direction = request.data.get('direction', 'PAST')
            anchor_time = request.data.get('anchor_time')
            limit = min(max(int(request.data.get('limit', 20)), 1), 100)  # Bounded limit

            if not all([from_entity_reference, to_entity_reference]):
                return Response({
                    "error": "Missing required parameters",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user instances and validate
            from_instance = get_instance_by_reference(from_entity_reference)
            to_instance = get_instance_by_reference(to_entity_reference)

            if not all([from_instance, to_instance]):
                return Response({
                    "error": "Invalid entity references",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check rate limit
            if not self.rate_limiter.check_rate_limit(
                from_instance.user_reference if isinstance(from_instance, User) else from_instance.store_reference,
                f"{from_entity_reference}_{to_entity_reference}"
            ):
                return Response({
                    "error": "Rate limit exceeded. Please wait before making more requests.",
                    "is_custom": True
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)

            # Get auth token with caching
            success, auth_data, error = get_or_create_auth_token(
                from_instance.xmpp_jid,
                from_instance.xmpp_password
            )
            if not success:
                return Response({
                    "error": "Authentication failed",
                    "details": error,
                    "is_custom": True
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Get cached room ID
            room_id, error = self._get_cached_room_id(from_instance, to_instance)
            if error:
                return Response({
                    "error": "Failed to get chat room",
                    "details": error,
                    "is_custom": True
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Prepare parameters for Rocket.Chat API
            params = {
                'roomId': room_id,
                'count': str(limit),  # Must be string
                'unreads': 'true'     # Must be string 'true' not boolean
            }

            # Set direction-specific parameters
            if direction == 'PAST':
                params['latest'] = anchor_time
            else:  # FUTURE
                params['oldest'] = anchor_time
            params['inclusive'] = 'false'  # Don't include the anchor message

            # Add offset if needed (for pagination)
            params['offset'] = '0'  # Must be string

            # Log the parameters for debugging
            self.logger.info(f"Direction: {direction}")
            self.logger.info(f"Anchor timestamp: {anchor_time}")
            self.logger.info(f"Rocket.Chat API parameters: {params}")

            # Get messages from Rocket.Chat
            try:
                response = requests.get(
                    f"{config('MESSAGING_SERVER_URL')}/api/v1/im.history",
                    params=params,
                    headers={
                        'X-Auth-Token': auth_data['auth_token'],
                        'X-User-Id': auth_data['user_id']
                    },
                    timeout=10
                )

                if response.status_code != 200:
                    self.logger.error(f"Rocket.Chat API error: {response.text}")
                    return Response({
                        "error": "Failed to fetch messages",
                        "details": response.text,
                        "is_custom": True
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                messages_data = response.json()
                self.logger.info(f"Got {len(messages_data.get('messages', []))} messages from Rocket.Chat")

                # Mark messages as read using subscriptions.read endpoint
                try:
                    read_response = requests.post(
                        f"{config('MESSAGING_SERVER_URL')}/api/v1/subscriptions.read",
                        json={"rid": room_id},
                        headers={
                            'X-Auth-Token': auth_data['auth_token'],
                            'X-User-Id': auth_data['user_id'],
                            'Content-Type': 'application/json'
                        },
                        timeout=10
                    )
                    
                    if read_response.status_code != 200:
                        self.logger.error(f"Error marking messages as read: {read_response.text}")
                except Exception as e:
                    self.logger.error(f"Error calling subscriptions.read: {str(e)}")

                # Get unread count from the response
                unread_count = messages_data.get('unread', 0)

                # Get last read timestamp
                last_read = messages_data.get('ls')  # Try to get from response first
                if not last_read:
                    last_read, unread_count = self.get_subscription_last_read(room_id, auth_data)
                    self.logger.info(f"Got last_read from subscription: {last_read}")
                
                # Process messages with simplified format
                processed_messages = []
                messages = messages_data.get('messages', [])
                
                # Sort messages based on direction
                if direction == 'PAST':
                    messages = sorted(messages, key=lambda x: x['ts'], reverse=True)
                else:
                    messages = sorted(messages, key=lambda x: x['ts'])
                
                for msg in messages:
                    processed_messages.append({
                        'message_id': msg.get('_id'),
                        'text': msg.get('msg'),
                        'timestamp': msg.get('ts'),
                        'sender_reference': msg.get('u', {}).get('username')
                    })

                # Get cached profiles
                to_profile = self._get_cached_profile(to_entity_reference)
                from_profile = self._get_cached_profile(from_entity_reference)

                return Response({
                    'message': 'Success',
                    'unread_count': unread_count,
                    'last_read': last_read,
                    'room_id': room_id,
                    'data': {
                        'to_profile': to_profile,
                        'from_profile': from_profile,
                        'messages': processed_messages
                    }
                })

            except Exception as e:
                self.logger.error(f"Error fetching messages: {str(e)}", exc_info=True)
                return Response({
                    "error": "Failed to fetch messages",
                    "details": str(e),
                    "is_custom": True
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            self.logger.error(f"Error in get_messages: {str(e)}", exc_info=True)
            return Response({
                "error": "Internal server error",
                "details": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GetDMMetadata(APIView):
    """API to get DM metadata including last read time, unread count, and room ID."""

    def get(self, request):
        try:
            # Validate input parameters
            from_entity_reference = request.query_params.get('from_entity_reference')
            to_entity_reference = request.query_params.get('to_entity_reference')

            if not all([from_entity_reference, to_entity_reference]):
                return Response({
                    "error": "Missing required parameters",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user instances
            from_instance = get_instance_by_reference(from_entity_reference)
            to_instance = get_instance_by_reference(to_entity_reference)

            if not all([from_instance, to_instance]):
                return Response({
                    "error": "Invalid entity references",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get auth token
            success, auth_data, error = get_or_create_auth_token(
                from_instance.xmpp_jid,
                from_instance.xmpp_password
            )
            if not success:
                return Response({
                    "error": "Authentication failed",
                    "details": error,
                    "is_custom": True
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Get or create room ID
            success, room_id, error = get_or_create_room_id(from_instance, to_instance)
            if not success:
                return Response({
                    "error": "Failed to get chat room",
                    "details": error,
                    "is_custom": True
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Get subscription info
            response = requests.get(
                f"{config('MESSAGING_SERVER_URL')}/api/v1/subscriptions.get",
                headers={
                    'X-Auth-Token': auth_data['auth_token'],
                    'X-User-Id': auth_data['user_id']
                }
            )

            if response.status_code != 200:
                return Response({
                    "error": "Failed to fetch subscription data",
                    "details": response.text,
                    "is_custom": True
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            subscription_data = response.json()
            subscription = None
            
            # Find the subscription for our room
            for sub in subscription_data.get('update', []):
                if sub.get('rid') == room_id:
                    subscription = sub
                    break

            if not subscription:
                # If no subscription found, try to get last message timestamp
                history_response = requests.get(
                    f"{config('MESSAGING_SERVER_URL')}/api/v1/im.history",
                    params={
                        'roomId': room_id,
                        'count': 1
                    },
                    headers={
                        'X-Auth-Token': auth_data['auth_token'],
                        'X-User-Id': auth_data['user_id']
                    }
                )
                
                if history_response.status_code == 200:
                    messages = history_response.json().get('messages', [])
                    if messages:
                        last_message = messages[0]
                        timestamp = last_message.get('ts')
                        if isinstance(timestamp, dict) and '$date' in timestamp:
                            last_read = datetime.fromtimestamp(timestamp['$date']/1000).isoformat() + 'Z'
                        else:
                            last_read = None
                    else:
                        last_read = None
                    
                return Response({
                    "message": "Success",
                    "data": {
                        "last_read_time": last_read,
                        "unread_count": 0,
                        "room_id": room_id
                    }
                })

            # Get last read time
            last_read = subscription.get('ls')
            if isinstance(last_read, dict) and '$date' in last_read:
                timestamp = last_read.get('$date')
                if isinstance(timestamp, int):
                    last_read = datetime.fromtimestamp(timestamp/1000).isoformat() + 'Z'
            elif isinstance(last_read, str):
                try:
                    parsed_date = datetime.fromisoformat(last_read.replace('Z', '+00:00'))
                    last_read = parsed_date.isoformat() + 'Z'
                except ValueError:
                    last_read = None

            return Response({
                "message": "Success",
                "data": {
                    "last_read_time": last_read,
                    "unread_count": subscription.get('unread', 0),
                    "room_id": room_id
                }
            })

        except Exception as e:
            logger.error(f"Error in get_dm_metadata: {str(e)}", exc_info=True)
            return Response({
                "error": "Internal server error",
                "details": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class MarkAsRead(APIView):
    """API to mark messages as read up to a specific timestamp."""
    def post(self, request):
        try:
            from_entity_reference = request.data.get('from_entity_reference')
            room_id = request.data.get('room_id')
            anchor_time = request.data.get('anchor_time')

            if not all([from_entity_reference, room_id]):
                return Response({
                    "message": "Missing required parameters",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user instance
            user = get_instance_by_reference(from_entity_reference)
            if not user:
                return Response({
                    "error": "Invalid user reference",
                    "is_custom": True
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get auth token
            success, auth_data, error = get_or_create_auth_token(
                user.xmpp_jid,
                user.xmpp_password
            )
            if not success:
                return Response({
                    "error": "Authentication failed",
                    "details": error,
                    "is_custom": True
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Call Rocket.Chat API to mark messages as read
            response = requests.post(
                f"{config('MESSAGING_SERVER_URL')}/api/v1/subscriptions.read",
                headers={
                    'X-Auth-Token': auth_data['auth_token'],
                    'X-User-Id': auth_data['user_id'],
                    'Content-Type': 'application/json'
                },
                json={
                    'rid': room_id
                }
            )

            if response.status_code != 200:
                logger.error(f"Failed to mark messages as read. Status: {response.status_code}, Response: {response.text}")
                return Response({
                    "message": "Failed to mark messages as read",
                    "details": response.text,
                    "is_custom": True
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({
                'message': 'Success',
            }, status=200)

        except Exception as e:
            logger.error(f"Error in mark_as_read: {str(e)}")
            return Response({
                "message": "Internal server error",
                "error": str(e),
                "is_custom": True
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
