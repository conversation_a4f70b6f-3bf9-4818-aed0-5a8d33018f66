# Generated by Django 3.2.13 on 2022-12-23 08:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0027_alter_notifications_notification_hide_date"),
        ("stores", "0020_auto_20221222_1525"),
    ]

    operations = [
        migrations.AlterField(
            model_name="deliverysettings",
            name="storeid",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="refundandwarranty",
            name="storeid",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="store",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                db_column="created_by",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created",
                to="users.user",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="store",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                db_column="modified_by",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="modified",
                to="users.user",
            ),
        ),
        migrations.AlterField(
            model_name="trustcenter",
            name="storeid",
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
