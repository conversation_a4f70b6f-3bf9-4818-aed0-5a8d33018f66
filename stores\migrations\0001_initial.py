# Generated by Django 3.2.13 on 2022-07-26 14:05

from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("products", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                ("userid", models.AutoField(primary_key=True, serialize=False)),
                (
                    "phonenumber",
                    phonenumber_field.modelfields.PhoneNumberField(
                        max_length=128, region=None
                    ),
                ),
                ("is_verified", models.BooleanField(default=False)),
                ("counter", models.IntegerField(default=0)),
                ("email", models.EmailField(blank=True, max_length=100, null=True)),
                ("gender", models.CharField(blank=True, max_length=50, null=True)),
                ("age", models.IntegerField(blank=True, null=True)),
                (
                    "icon",
                    models.ImageField(blank=True, null=True, upload_to="profile_image"),
                ),
                ("user_name", models.CharField(blank=True, max_length=50, null=True)),
                ("user_handle", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "user_location",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("pincode", models.CharField(blank=True, max_length=6, null=True)),
                ("deleted", models.BooleanField(default=False)),
            ],
            options={
                "db_table": '"store"."user"',
            },
        ),
        migrations.CreateModel(
            name="Store",
            fields=[
                ("storeid", models.AutoField(primary_key=True, serialize=False)),
                ("store_name", models.CharField(max_length=100)),
                ("store_desc", models.CharField(blank=True, max_length=500, null=True)),
                ("active", models.BooleanField(default=True)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("modified_date", models.DateTimeField(auto_now=True)),
                (
                    "icon",
                    models.ImageField(blank=True, null=True, upload_to="store_icons"),
                ),
                ("location", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "storehandle",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("value_added_to_whitelabel", models.BooleanField(default=False)),
                ("deleted", models.BooleanField(default=False)),
                ("trustcenter_detail", models.BooleanField(default=False)),
                ("warranty_and_return", models.BooleanField(default=False)),
                ("delivery_settings", models.BooleanField(default=False)),
                ("add_products", models.BooleanField(default=False)),
                ("open_for_order", models.BooleanField(default=False)),
                (
                    "categoryid",
                    models.ForeignKey(
                        db_column="categoryid",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.category",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        db_column="created_by",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created",
                        to="stores.user",
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        db_column="modified_by",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="modified",
                        to="stores.user",
                    ),
                ),
            ],
            options={
                "db_table": '"store"."store"',
            },
        ),
    ]
