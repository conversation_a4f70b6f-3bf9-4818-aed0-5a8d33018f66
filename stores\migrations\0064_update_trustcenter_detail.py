# Generated by Django 4.2.7 on 2024-07-27 06:09

from django.db import migrations
from ..store_settings_api.models import Trust<PERSON><PERSON><PERSON>he<PERSON>


def update_trustcenter_detail(apps, schema_editor):
    Store = apps.get_model('stores', 'Store')
    TrustCenter = apps.get_model('stores', 'TrustCenter')

    for store in Store.objects.all():
        try:
            trust_center = TrustCenter.objects.get(store_reference=store.store_reference)
            checker = TrustCenterChecker(trust_center)
            if checker.check_trust_completed(trust_center):
                store.trustcenter_detail = True
            else:
                store.trustcenter_detail = False
            store.save(update_fields=['trustcenter_detail'])
        except TrustCenter.DoesNotExist:
            # If there's no corresponding TrustCenter, set trustcenter_detail to False
            store.trustcenter_detail = False
            store.save(update_fields=['trustcenter_detail'])


class Migration(migrations.Migration):

    dependencies = [
        ("stores", "0063_store_gst_business_name"),
    ]

    operations = [
        migrations.RunPython(update_trustcenter_detail),
    ]
