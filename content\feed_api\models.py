from django.db import models
import logging
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Create your models here.


class FeedPost(models.Model):
    class CategoryType(models.TextChoices):
        CONTACT = "CONTACT", _("Contact")
        CONNECTION = "CONNECTION", _("Connection")
        MUTUAL = "MUTUAL", _("Mutual")
        PINCODE = "PINCODE", _("Pincode")
        CITY = "CITY", _("City")
        STATE = "STATE", _("State")

    feed_post_id = models.AutoField(primary_key=True)
    reference = models.CharField(max_length=20, blank=True, null=True)
    post_reference = models.ForeignKey(
        "content.Posts",
        on_delete=models.CASCADE,
        to_field="post_reference",
        null=True,
        blank=True,
        related_name="feed_post",
        db_column="post_reference",
    )
    created_date = models.DateTimeField(auto_now_add=True)
    is_seen = models.BooleanField(default=False)
    category = models.CharField(
        max_length=15, choices=CategoryType.choices, null=True, blank=True
    )

    class Meta:
        verbose_name_plural = "feed_posts"
        db_table = '"content"."feed_post"'
