# Generated by Django 3.2.13 on 2022-12-15 05:07

from django.db import migrations


def add_user_reference_data_comment_table(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.

    # This func will fill data in user_reference column, where
    # userid of User model is same as Userid in comment table.

    Comment = apps.get_model("products", "Comment")
    User = apps.get_model("users", "User")
    for item in Comment.objects.all():
        user_id = item.userid.userid
        if User.objects.filter(userid=user_id).exists():
            user_instance = User.objects.get(userid=user_id)
            item.user_reference = user_instance.user_reference
        else:
            item.user_reference = None
        item.save(update_fields=["user_reference"])


def reverse_func(apps, schema_editor):
    # Reverse the change that has done in above func.
    Comment = apps.get_model("products", "Comment")
    for item in Comment.objects.all():
        item.user_reference = None
        item.save(update_fields=["user_reference"])


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0032_auto_20221215_1035"),
    ]

    operations = [
        migrations.RunPython(add_user_reference_data_comment_table, reverse_func)
    ]
