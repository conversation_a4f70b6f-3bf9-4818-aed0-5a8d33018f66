from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from ..models import Banner, ReservedHandles
from .serializers import BannerSerializer, ReservedHandlesSerializer, BannerSerializerV2


class BannerListAV(APIView):

    def get(self, request):
        banners = Banner.objects.all()
        old_banner_data = []
        for banner in banners:
            old_banner = {
                'banner_id': banner.banner_id,
                'banner_image': "/media/" + str(banner.image_url),
                'created_date': banner.created_date,
                'modified_date': banner.modified_date,
                'note': banner.note,
                'reference': banner.page_reference,
                'response_link': banner.page_url,
                'banner_location': banner.banner_location,
                'targeted_entity': banner.current_user_type,
                'targeted_version': list(banner.minimum_targeted_version) if banner.minimum_targeted_version else [],
            }
            old_banner_data.append(old_banner)
        return Response({"message": "success", "data": old_banner_data})

    def post(self, request):
        serializer = BannerSerializerV2(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_404_NOT_FOUND,
            )


class UpdatedBannerListAV(APIView):
    def get(self, request):
        banners = Banner.objects.all()
        serializer = BannerSerializer(banners, many=True)
        return Response({"message": "success", "data": serializer.data})

    def post(self, request):
        serializer = BannerSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UpdatedBannerDetailsAV(APIView):
    def get(self, request, banner_id):
        try:
            banner = Banner.objects.get(pk=banner_id)
        except Banner.DoesNotExist:
            return Response(
                {"Error": "category not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = BannerSerializer(banner)
        old_banner_data = []
        old_banner = {
            'banner_id': banner.banner_id,
            'banner_image': str(banner.image_url),
            'created_date': banner.created_date,
            'modified_date': banner.modified_date,
            'note': banner.note,
            'reference': banner.page_reference,
            'response_link': banner.page_url,
            'banner_location': banner.banner_location,
            'targeted_entity': banner.current_user_type,
            'targeted_version': banner.minimum_targeted_version,
        }
        old_banner_data.append(old_banner)
        return Response(
            {"message": "success", "data": old_banner_data, "new_banners": serializer.data}, status=status.HTTP_200_OK
        )

    def put(self, request, banner_id):
        try:
            category = Banner.objects.get(pk=banner_id)
        except Banner.DoesNotExist:
            return Response(
                {"Error": "category not found"}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = BannerSerializer(category, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete(self, request, banner_id):
        try:
            category = Banner.objects.get(pk=banner_id)
        except Banner.DoesNotExist:
            return Response(
                {"Error": "category not found"}, status=status.HTTP_404_NOT_FOUND
            )
        category.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


#These class is not used yet.
class BannerDetailsAV(APIView):

    def get(self, request, banner_id):
        try:
            instance = Banner.objects.get(pk=banner_id)
        except Banner.DoesNotExist:
            return Response(
                {"Error": "category not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = BannerSerializer(instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def put(self, request, banner_id):
        category = Banner.objects.get(pk=banner_id)
        serializer = BannerSerializer(category, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request, banner_id):
        category = Banner.objects.get(pk=banner_id)
        category.delete()
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class BulkCreateReservedHandles(generics.CreateAPIView):
    serializer_class = ReservedHandlesSerializer

    # here create will be a bulk create. instead of an object,
    # input will be a list of objects.
    def get_serializer(self, *args, **kwargs):
        if isinstance(kwargs.get("data", {}), list):
            kwargs["many"] = True
        return super(BulkCreateReservedHandles, self).get_serializer(*args, **kwargs)

    def get(self, request, *args, **kwargs):
        queryset = ReservedHandles.objects.all()  # Replace YourModel with the actual model you are using
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

