# urls.py

from django.urls import path
from . import views
from .views import Monthly<PERSON><PERSON><PERSON>ointJob, SingleStoreFlashRewardAPI

urlpatterns = [
    path('store_rewards/', views.get_store_rewards, name='get_store_rewards'),
    path('store_rewards_history/', views.get_store_rewards_history, name='get_store_rewards_history'),
    path('store_monthly_flash_points/', views.get_store_monthly_flash_points, name='get_store_monthly_flash_points'),
    path('single_store_flash_reward/', SingleStoreFlashRewardAPI.as_view(), name='single-store-flash-reward'),

    path('flash_points_scheduler_job/', MonthlyFlashPointJob.as_view(), name='monthly_flash_point_job'),
]
