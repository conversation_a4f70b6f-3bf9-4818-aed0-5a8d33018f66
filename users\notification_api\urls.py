from django.urls import path
from .views import (AllNotifications, UpdateNotificationStatus, DisappearNotifications, ClearAllNotifications,
                    AllStoreNotifications)


urlpatterns = [
    path(
            "all_notifications/<str:notified_user>/",
            AllNotifications.as_view(),
            name="all-notifications",
        ),
    path(
            "clear_all_notifications/<str:notified_user>/",
            ClearAllNotifications.as_view(),
            name="clear-all-notifications",
        ),
    path(
            "update_notification_status/",
            UpdateNotificationStatus.as_view(),
            name="update-notification-status",
        ),
    path(
            "disappear_notifications/",
            DisappearNotifications.as_view(),
            name="disappear-notifications",
        ),
    path(
        "all_stores_notifications/<str:user_reference>/",
        AllStoreNotifications.as_view(),
        name="all_stores_notifications",
    )
]