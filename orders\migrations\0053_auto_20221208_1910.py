# Generated by Django 3.2.13 on 2022-12-08 13:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0052_alter_payouttransactions_transaction_status"),
    ]

    operations = [
        migrations.AlterField(
            model_name="orderpayout",
            name="payout_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("IN_PROCESS", "In process"),
                    ("WAITING_FOR_RELEASE", "Waiting for release"),
                    ("AMOUNT_RELEASED", "Amount released"),
                    ("NOTHING_TO_BE_ADDED", "Nothing to be added"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="payouttransactions",
            name="transaction_status",
            field=models.CharField(
                blank=True,
                choices=[("SUCCESS", "Success"), ("FAILURE", "Failure")],
                max_length=10,
                null=True,
            ),
        ),
    ]
