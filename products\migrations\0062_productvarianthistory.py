# Generated by Django 4.2.23 on 2025-06-23 11:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0061_product_options_productvariants'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductVariantHistory',
            fields=[
                ('product_variant_historyid', models.AutoField(primary_key=True, serialize=False)),
                ('variant_reference', models.CharField(max_length=25, unique=True)),
                ('product_reference', models.CharField(max_length=25, unique=True)),
                ('combinations', models.JSONField(blank=True, default=dict, null=True)),
                ('mrp_price', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('selling_price', models.PositiveIntegerField(blank=True, default=0, null=True)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('created_date', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('modified_date', models.DateField(auto_now=True)),
                ('product_variant_version', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('change_type', models.CharField(blank=True, max_length=100, null=True)),
                ('changed_fields', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'product variant history',
                'db_table': '"product"."product_variant_history"',
            },
        ),
    ]
