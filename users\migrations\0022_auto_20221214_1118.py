# Generated by Django 3.2.13 on 2022-12-14 05:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0021_notifications"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="notifications",
            name="user_reference",
        ),
        migrations.AddField(
            model_name="notifications",
            name="reference_to",
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="notifications",
            name="notification_type",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("ON_BOARDING", "on boarding"),
                    ("PRODUCT_VERSION_CHANGED", "Product version changed"),
                    ("STORE_CREATED", "New store created near you"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("ORDER_SHIPPED", "Order shipped"),
                ],
                max_length=100,
                null=True,
            ),
        ),
    ]
