from datetime import datetime, <PERSON><PERSON><PERSON>

from django_neomodel import DjangoNode
from neomodel import (StructuredNode,
                      StringProperty,
                      IntegerProperty,
                      BooleanProperty,
                      RelationshipTo,
                      RelationshipFrom,
                      DateTimeProperty,
                      ArrayProperty,
                      DateTimeFormatProperty,
                      UniqueIdProperty,
                      JSONProperty,
                      StructuredRel)
from content.models import Posts
from users.user_api.models import User
from stores.store_api.models import Store
from .queries import dbqueries


def get_content_creator(content_reference):
    try:
        # Check if the content is a post
        post = Neo4jPost.nodes.get(reference=content_reference)
        # If it's a post, return the entity that posted it
        return post.get_post_creator()
    except Neo4jPost.DoesNotExist:
        pass

    try:
        # Check if the content is a product
        product = Neo4jProduct.nodes.get(reference=content_reference)
        # If it's a product, return the entity that listed it
        return product.listed_by.single()
    except Neo4jProduct.DoesNotExist:
        pass

    try:
        # Check if the content is a comment
        comment = Neo4jComment.nodes.get(reference=content_reference)
        # If it's a comment, return the entity that commented it
        return comment.commented_by.single()
    except Neo4jComment.DoesNotExist:
        pass

    try:
        # Check if the content is a comment
        story = Neo4jStory.nodes.get(reference=content_reference)
        # If it's a comment, return the entity that commented it
        return story.posted_by.single()
    except Neo4jStory.DoesNotExist:
        pass

        # If the content reference does not belong to any known type, return None
    return None

# Create your models here.

class Neo4jMedia(DjangoNode):
    media = JSONProperty()
    has_media = RelationshipFrom('Neo4jPost', 'HAS_MEDIA')

    class Meta:
        app_label = "GraphDB"


class Neo4jVisitStatus(StructuredRel):
    datetimestamp = DateTimeProperty()


class Neo4jRepostStatus(StructuredRel):
    datetimestamp = DateTimeProperty()


class Neo4jFeedPostStatus(StructuredRel):
    seen_status = BooleanProperty()
    category = ArrayProperty(StringProperty())


class Neo4jStoryDetails(StructuredRel):
    order = IntegerProperty()


class CommentRelationshipDetails(StructuredRel):
    is_external_comment = BooleanProperty()
    external_review_token = StringProperty()
    # Properties


class Neo4jContent(DjangoNode, StructuredNode):#post, product, comment, story
    reference = StringProperty(unique_index=True)
    created_date = DateTimeProperty()
    is_deleted = BooleanProperty(default=False)

    # created_by = RelationshipFrom('Neo4jEntity', 'CREATED')
    liked_by = RelationshipFrom('Neo4jEntity', 'LIKED')
    visited_by = RelationshipFrom('Neo4jEntity', 'VISITED', model=Neo4jVisitStatus)
    reposted_by = RelationshipFrom('Neo4jEntity', 'REPOSTED', model=Neo4jRepostStatus)
    #TODO add views count to content in RDB

    # new relationship
    saved_by = RelationshipFrom('Neo4jEntity', 'SAVED')
    has_comment = RelationshipTo('Neo4jComment', 'HAS_COMMENT', model=CommentRelationshipDetails)  # Used for both comments and replies
    has_tag = RelationshipTo('Neo4jContent', 'TAGGED')

    def get_like_status(self, visitor_reference=None):
        try:
            visitor_entity = Neo4jEntity.nodes.get(reference=visitor_reference)
        except Neo4jEntity.DoesNotExist:
            raise Exception("User or Store with the given reference does not exist.")

        return self.liked_by.is_connected(visitor_entity)

    def get_repost_status(self, visitor_reference=None):
        try:
            visitor_entity = Neo4jEntity.nodes.get(reference=visitor_reference)
        except Neo4jEntity.DoesNotExist:
            raise Exception("User or Store with the given reference does not exist.")
        return self.reposted_by.is_connected(visitor_entity)

    def get_save_status(self, visitor_reference=None):
        try:
            visitor_entity = Neo4jEntity.nodes.get(reference=visitor_reference)
        except Neo4jEntity.DoesNotExist:
            raise Exception("User or Store with the given reference does not exist.")
        return self.saved_by.is_connected(visitor_entity)

    def get_content_headers(self, visitor_reference=None):
        try:
            result = dbqueries.get_content_headers(content_reference=self.reference, visitor_reference=visitor_reference)
            headers = [{'reference': res[1], 'handle': res[0]} for res in result]
            return headers
        except Exception as e:
            raise Exception(f"{e}")

    def get_content_category(self, visitor_reference=None):
        try:
            entity = Neo4jEntity.nodes.get(reference=visitor_reference)
            content = Neo4jContent.nodes.get(reference=self.reference)
            content_creator = get_content_creator(content_reference=self.reference)
            entity_followers = entity.get_following()
            if entity.reposts.is_connected(content):
                results = 'REPOST'
            else:
                if entity.follows.is_connected(content_creator):
                    results = 'POST'
                elif any(follower.reposts.is_connected(content) for follower in entity_followers) and content.created_date.replace(tzinfo=None) > (datetime.now() - timedelta(days=30)):
                    results = 'REPOST'
                else:
                    results = 'POST'
            return results
        except Neo4jEntity.DoesNotExist or Neo4jContent.DoesNotExist:
            raise Exception("Entity or content with the given reference does not exist.")

    def get_content_header_text(self, visitor_reference=None):
        result = dbqueries.get_content_headers(content_reference=self.reference,
                                               visitor_reference=visitor_reference)
        headers = [{'reference': res[1], 'handle': res[0]} for res in result]
        if not headers:
            return None
        
        if len(headers) == 1:
            return f"reposted by @{headers[0]['handle']}"
        elif len(headers) == 2:
            return f"reposted by @{headers[0]['handle']} and @{headers[1]['handle']}"
        else:
            others_count = len(headers) - 1
            return f"reposted by @{headers[0]['handle']} and {others_count} others"
        

    class Meta:
        app_label = "GraphDB"


class Neo4jPost(Neo4jContent):
    post_text = StringProperty()
    is_deleted = BooleanProperty(default=False)

    # Relationships
    posted_by = RelationshipFrom('Neo4jEntity', 'POSTED')
    media = RelationshipTo('Neo4jMedia', 'HAS_MEDIA')
    is_feed_post = RelationshipFrom('Neo4jFeed', 'CONTAINS', model=Neo4jFeedPostStatus)

    # new relationships
    tagged_entity = RelationshipTo('Neo4jEntity', 'TAGGED')
    tagged_product = RelationshipTo('Neo4jProduct', 'TAGGED')

    def get_post_creator(self):
        # Fetch the entity that posted the post (either user or store)
        posted_by_entities = self.posted_by.all()
        if posted_by_entities:
            return posted_by_entities[0]
        else:
            return None

    def get_post_images(self):
        # Retrieve post images associated with this post
        post_images = list(self.media.all())
        return post_images

    def get_like_count(self):
        return Posts.objects.get(post_reference=self.reference).like_count

    def get_comment_count(self):
        return Posts.objects.get(post_reference=self.reference).comment_count

    def serialized_post_images(self):
        post_images_data = []
        media_data = self.media.all()
        if len(media_data) != 0:
            for media in media_data:
                for media in media.media:
                    post_images_data.append({
                        "mediaId": media["media_id"],
                        "mediaType": media["media_type"],
                        "mediaPath": media["media_path"],
                        "order": media["order"]
                    })
            post_images_data.sort(key=lambda x: x['order'])
        else:
            post_images_data = []
        return post_images_data

    def serialize(self):
        post_images_data = self.serialized_post_images()
        return {
            'post_reference': self.reference,
            'post_text': self.post_text,
            'created_date': self.created_date,
            'created_by': self.get_post_creator().reference,
            'post_images': post_images_data,
            'like_count': self.get_like_count(),
        }

    class Meta:
        app_label = "GraphDB"


class Neo4jPostImage(Neo4jMedia):
    post_image_id = UniqueIdProperty(auto=True)
    post_image = StringProperty()
    order = IntegerProperty()
    is_deleted = BooleanProperty(default=False)

    def serialize(self):
        return {
            'postImageId': self.post_image_id,
            'postImage': self.post_image,
            'isDeleted': self.is_deleted,
            'order': self.order
            # Add other properties as needed
        }

    class Meta:
        app_label = "GraphDB"


class Neo4jFollowStatus(StructuredRel):
    datetimestamp = DateTimeProperty()


class Neo4jEntity(DjangoNode, StructuredNode):
    # Common properties
    reference = StringProperty()
    name = StringProperty()
    handle = StringProperty()
    icon = StringProperty()
    pincode = StringProperty()
    city = StringProperty()
    phonenumber = StringProperty()
    email = StringProperty()
    is_deleted = BooleanProperty(default=False)
    created_date = DateTimeProperty()
    entity_type = StringProperty()

    follower_count = IntegerProperty()
    following_count = IntegerProperty()

    # Follow relationships
    visited = RelationshipTo('Neo4jEntity', 'VISITED', model=Neo4jVisitStatus)
    visited_content = RelationshipTo('Neo4jContent', 'VISITED', model=Neo4jVisitStatus)
    follows = RelationshipTo('Neo4jEntity', 'FOLLOWS', model=Neo4jFollowStatus)
    followed_by = RelationshipFrom('Neo4jEntity', 'FOLLOWS')
    feed = RelationshipTo('Neo4jFeed', 'FEED')
    has_contact = RelationshipTo('Neo4jEntity', 'HAS_CONTACT')
    has_contact_unr = RelationshipTo('Neo4jUnrUser', 'HAS_CONTACT')

    # new relationships
    likes = RelationshipTo('Neo4jContent', 'LIKED')
    saves = RelationshipTo('Neo4jContent', 'SAVED')
    posts = RelationshipTo('Neo4jPost', 'POSTED')
    comments = RelationshipTo('Neo4jComment', 'COMMENTED')
    reposts = RelationshipTo('Neo4jContent', 'REPOSTED', model=Neo4jRepostStatus)
    reposted_plus = RelationshipTo('Neo4jPost', 'REPOSTED_PLUS')  # New relationship for reposts with additional content
    tagged_by = RelationshipFrom('Neo4jPost', 'TAGGED')
    has_story = RelationshipTo('Neo4jStory', 'HAS_STORY',  model=Neo4jStoryDetails)


    def serialize(self):
        return {
            "reference": self.reference,
            "handle": self.handle,
            "icon": self.icon,
            "entityType": self.get_entity_type()
        }

    def get_entity_type(self):
        entity_type = "UNREGISTERED"
        if isinstance(self, Neo4jUser):
            entity_type = "USER"
        elif isinstance(self, Neo4jStore):
            entity_type = "STORE"
        elif isinstance(self, Neo4jUnrUser):
            entity_type = "UNREGISTERED"
        else:
            pass

        return entity_type

    def follow_status(self, visitor_reference):
        try:
            visitor = Neo4jEntity.nodes.get(reference=visitor_reference)
        except Neo4jEntity.DoesNotExist:
            raise Exception("User or Store with the given reference does not exist.")
        return self._get_entity_follow_status(visitor)

    def _get_entity_follow_status(self, visitor):

        if visitor.is_following(self):
            x = "ing"
        elif self.is_following(visitor):
            x = " back"
        else:
            x = ""

        if isinstance(self, Neo4jStore):
            return 'Support' + x
        elif isinstance(self, Neo4jUnrUser):
            return 'Pend' + x
        else:
            return 'Follow' + x

    def get_follower_count(self):
        return len([follower for follower in self.followed_by if not follower.is_deleted])

    def get_following_count(self):
        return len([following for following in self.follows if not following.is_deleted])

    def follow(self, other_entity):
        self.follows.connect(other_entity)

    def unfollow(self, other_entity):
        self.follows.disconnect(other_entity)

    def is_following(self, other_entity):
        return self.follows.is_connected(other_entity)

    def get_followers(self):
        return [follower for follower in self.followed_by.all() if not follower.is_deleted]

    def get_following(self):
        return [following for following in self.follows.all() if not following.is_deleted]

    def get_or_create_feed(self):
        # Try to get the existing feed node for the entity
        existing_feed = self.feed.single()

        if existing_feed:
            return existing_feed
        else:
            # If no existing feed, create a new feed node and connect it to the entity
            new_feed = Neo4jFeed()
            new_feed.save()
            self.feed.connect(new_feed)
            return new_feed

    def add_post_to_feed(self, post, seen_status=None, category=None):
        # Get or create the feed node for the entity
        feed_node = self.get_or_create_feed()
        if not feed_node.contains.is_connected(post):
            # Add the post to the feed node
            feed_node.add_post(post, seen_status=seen_status, category=category)
            feed_node.save()

    def get_subscription_type(self):
        try:
            entity_instance = User.objects.get(user_reference=self.reference) if isinstance(self,Neo4jUser) else Store.objects.get(store_reference=self.reference)
            subscription_type = entity_instance.subscription_type
            return subscription_type
        except (User.DoesNotExist, Store.DoesNotExist):
            return None

    class Meta:
        app_label = "GraphDB"


class Neo4jUser(Neo4jEntity):
    # user_reference = StringProperty(unique_index=True) #

    # Relationships
    posts = RelationshipTo(Neo4jPost, 'POSTED')
    liked_posts = RelationshipTo(Neo4jPost, 'LIKED')
    created = RelationshipTo(Neo4jEntity, 'CREATED') # create a store

    def __hash__(self):
        return hash(self.reference)

    class Meta:
        app_label = "GraphDB"


class Neo4jStore(Neo4jEntity):
    # store_reference = StringProperty(unique_index=True)
    # store_handle = StringProperty()
    category_name = StringProperty()
    is_active = BooleanProperty(default=False)
    is_open = BooleanProperty(default=False)
    state = StringProperty()
    cover_image = StringProperty()

    created_by = RelationshipFrom(Neo4jUser, 'CREATED')

    # Relationships
    posts = RelationshipTo(Neo4jPost, 'POSTED')
    liked_posts = RelationshipTo(Neo4jPost, 'LIKED')
    listed = RelationshipTo('Neo4jProduct', 'LISTED')
    has_comment = RelationshipTo('Neo4jComment', 'HAS_COMMENT', model=CommentRelationshipDetails)


    def __hash__(self):
        return hash(self.reference)

    class Meta:
        app_label = "GraphDB"


class Neo4jUnrUser(StructuredNode):
    # store_reference = StringProperty(unique_index=True)
    # store_handle = StringProperty()
    reference = StringProperty()
    phonenumber = StringProperty()

    followed_by = RelationshipFrom(Neo4jEntity, 'FOLLOWS', model=Neo4jFollowStatus)

    def follow_status(self, visitor_reference):
        try:
            visitor = Neo4jEntity.nodes.get(reference=visitor_reference)
        except Neo4jEntity.DoesNotExist:
            raise Exception("User or Store with the given reference does not exist.")

        if self.followed_by.is_connected(visitor):
            x = "Pending"
        else:
            x = "Follow"
        return x



    def __hash__(self):
        return hash(self.reference)

    class Meta:
        app_label = "GraphDB"


class Neo4jFeed(DjangoNode):
    feed_id = UniqueIdProperty(auto=True)

    # Relationships
    contains = RelationshipTo('Neo4jContent', 'CONTAINS', model=Neo4jFeedPostStatus)
    feed_posts = RelationshipFrom('Neo4jEntity', 'FEED')

    def add_post(self, post, seen_status=None, category=None):
        # Connect post to feed with optional properties
        self.contains.connect(post, {'seen_status': seen_status, 'category': category})

    def get_posts_in_feed(self, limit, offset):
        # Return posts contained in the feed with optional limit and offset
        posts = self.contains.all()
        posts.sort(key=lambda x: x.created_date, reverse=True)

        if offset is not None and offset >= 0:
            posts = posts[offset:]

        if limit is not None and limit >= 0:
            posts = posts[:limit]

        return [post.reference for post in posts]

    class Meta:
        app_label = "GraphDB"


class Neo4jProduct(Neo4jContent):
    # Relationships
    listed_by = RelationshipFrom('Neo4jStore', 'LISTED')
    tagged_by = RelationshipFrom('Neo4jPost', 'TAGGED')
    has_story = RelationshipTo('Neo4jStory', 'HAS_STORY', model=Neo4jStoryDetails)

    def get_product_creator(self):
        # Fetch the entity that posted the post (either user or store)
        listed_by_store = self.listed_by.all()
        if listed_by_store:
            return listed_by_store[0]
        else:
            return None

    def get_stories(self):
        """Get all stories associated with this product in order"""
        # Using the Neo4jStory's has_story relationship to find connected stories
        stories = dbqueries.get_product_stories(self.reference)
        return stories

    class Meta:
        app_label = "GraphDB"


class Neo4jComment(Neo4jContent):
    level = IntegerProperty() #TODO remove this field p3
    comment_type = StringProperty()
    # Properties

    commented_by = RelationshipFrom('Neo4jEntity', 'COMMENTED')

    def get_comment_creator(self):
        commented_by_entities = self.commented_by.all()
        if commented_by_entities:
            return commented_by_entities[0]
        else:
            return None

    class Meta:
        app_label = "GraphDB"


class Neo4jStory(Neo4jContent):

    # Relationships
    has_story = RelationshipFrom('Neo4jProduct', 'HAS_STORY', model=Neo4jStoryDetails)
    posted_by = RelationshipFrom('Neo4jEntity', 'POSTED')
    class Meta:
        app_label = "GraphDB"
