# Generated by Django 3.2.13 on 2023-06-29 08:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0102_logisticpartner'),
    ]

    operations = [
        migrations.AddField(
            model_name='refundedamount',
            name='refund_initiate_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='refundedamount',
            name='refund_request_date',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AlterField(
            model_name='orderpayout',
            name='expected_payout_amount',
            field=models.FloatField(default=0),
        ),
        migrations.AlterField(
            model_name='payouttransactions',
            name='payout_amount',
            field=models.FloatField(default=0),
        ),
    ]
