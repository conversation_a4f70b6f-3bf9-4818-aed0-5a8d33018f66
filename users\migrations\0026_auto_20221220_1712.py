# Generated by Django 3.2.13 on 2022-12-20 11:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0025_alter_notifications_notification_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="notifications",
            name="is_hidden",
            field=models.<PERSON>olean<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name="notifications",
            name="notification_hide_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="notifications",
            name="notification_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ONBOARDING", "onboarding"),
                    ("PRODUCT_VERSION_CHANGED", "Product version changed"),
                    ("STORE_CREATED", "New store created near you"),
                    ("ORDER_CONFIRMED", "Order confirmed"),
                    ("ORDER_SHIPPED", "Order shipped"),
                    ("NEW_ORDER", "New order"),
                    ("ORDER_CANCELLED", "Order cancelled"),
                    ("RETURN_REQUESTED", "Return requested "),
                ],
                max_length=100,
                null=True,
            ),
        ),
    ]
