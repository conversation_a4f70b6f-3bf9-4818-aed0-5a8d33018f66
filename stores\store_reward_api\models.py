from django.db import models
from datetime import datetime
import random
import string
import logging
import time
from django.utils import timezone
from common.util.support_helper import compress
from django.utils.translation import gettext_lazy as _
from django.db.models import Sum, Case, When, IntegerField, F, Value
from django.db.models.functions import Cast

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class StoreRewards(models.Model):
    store_reward_id = models.AutoField(primary_key=True)
    store_reference = models.ForeignKey(
        "stores.Store",
        to_field="store_reference",
        on_delete=models.CASCADE,
        related_name="store_rewards",
        db_column="store_reference",
    )
    infinity_points = models.PositiveIntegerField(default=0)
    flash_points = models.PositiveIntegerField(default=0)
    created_date = models.DateTimeField(auto_now_add=True)
    modified_date = models.DateTimeField(auto_now=True)

    def check_flash_points(store_reference, points_to_deduct, return_balance=False):
        """
            Check if a store has sufficient flash points for a deduction and optionally return the current balance.

            Parameters:
            - store_reference (str): The reference identifier for the store.
            - points_to_deduct (int): The number of flash points to check for possible deduction.
            - return_balance (bool, optional): If True, the function will return the current balance of flash points 
            along with the result. Defaults to False.

            Returns:
            - bool: True if the store has sufficient flash points for deduction, False otherwise.
            - int or None (optional): The current balance of flash points if `return_balance` is True. 
            If the store doesn't exist, returns None.
        """
        try:
            store_rewards_instance = StoreRewards.objects.get(store_reference=store_reference)
            if store_rewards_instance.flash_points >= points_to_deduct:
                logger.info(f"Flash points available for store {store_reference}")
                if return_balance:
                    return True, store_rewards_instance.flash_points
                return True
            else:
                logger.info(f"Insufficient flash points for store {store_reference}")
                if return_balance:
                    return False, store_rewards_instance.flash_points
                return False
        except StoreRewards.DoesNotExist:
            logger.info(f"Store Rewards entry is missing for store {store_reference}")
            if return_balance:
                return False, None
            return False

    def update_flash_points(self,store_reference, points, event_reference, operation):
        """
            Update the flash points for a store by either adding or deducting points.

            Parameters:
            - store_reference (str): The unique identifier for the store.
            - points (int): The number of flash points to add or deduct. 
            - For adding points, provide a positive integer.
            - For deducting points, provide a positive integer representing the amount to be deducted.
            - event_reference (str): A reference identifier for the event that triggered this update, 
            typically used for logging and tracking purposes.
            - operation (str): The type of operation to perform on the flash points. 
            - Use 'ADDITION' to add points.
            - Use 'DEDUCTION' to deduct points.

            Returns:
            - bool: True if the operation was successful (points were added or deducted), False otherwise.
            
        """
        try:
            store_rewards_instance = StoreRewards.objects.get(store_reference=store_reference)
            
            if operation == 'DEDUCTION':
                if store_rewards_instance.flash_points >= points:
                    store_rewards_instance.flash_points -= points
                    logger.info(f"Deducted {points} flash points from store {store_reference}")
                    reward_type, transaction_type = 'DEDUCTION', 'DEBIT'

                else:
                    logging.info(f"Deduction failed for {store_reference} due to insufficient points")
                    return False
            elif operation == 'ADDITION':
                store_rewards_instance.flash_points += points
                logger.info(f"Added {points} flash points to store {store_reference}")
                reward_type, transaction_type = 'ADDITION', 'CREDIT'

            else:
                logging.error(f"Invalid operation '{operation}' specified for store {store_reference}")
                return False

            store_rewards_instance.save()

            # Create a history entry using StoreRewardsHistory.add_store_rewards_history
            StoreRewardsHistory.add_store_rewards_history(
                store_reference=store_reference,
                reward_category='FLASH',
                reward_value=points,
                reward_type=reward_type,
                transaction_type=transaction_type,
                event_reference=event_reference,
                reward_status='SUCCESS'
            )
            return True
        except StoreRewards.DoesNotExist:
            logger.info(f"Store Rewards entry is missing for store {store_reference}")
            return False

    @classmethod
    def get_rewards_breakup(cls, store_reference, reward_category):
        """
        Fetch rewards breakup for a store, calculating lifetime earnings, in-process earnings, and pending earnings.

        Parameters:
        - store_reference (str): The reference identifier for the store.
        - reward_category (str): Either 'FLASH' or 'INFINITY'.

        Returns:
        - dict: A dictionary containing lifetime_earnings, in_process_earnings, and pending_earnings.
        """
        logger.info(f"Fetching rewards breakup for store {store_reference}, category {reward_category}")
        
        # Use database aggregation instead of Python-side calculations
        aggregations = StoreRewardsHistory.objects.filter(
            store_reference=store_reference,
            reward_category=reward_category,
            reward_status__in=['SUCCESS', 'INPROCESS', 'PENDING']
        ).aggregate(
            lifetime_earnings=Sum(Case(
                When(transaction_type='CREDIT', reward_status='SUCCESS', then=Cast('reward_value', IntegerField())),
                When(transaction_type='DEBIT', reward_status='SUCCESS', then=Cast('reward_value', IntegerField()) * -1),
                output_field=IntegerField(),
                default=0
            )),
            in_process_earnings=Sum(Case(
                When(transaction_type='CREDIT', reward_status='INPROCESS', then=Cast('reward_value', IntegerField())),
                When(transaction_type='DEBIT', reward_status='INPROCESS', then=Cast('reward_value', IntegerField()) * -1),
                output_field=IntegerField(),
                default=0
            )),
            waiting_to_release_earnings=Sum(Case(
                When(transaction_type='CREDIT', reward_status='PENDING', then=Cast('reward_value', IntegerField())),
                When(transaction_type='DEBIT', reward_status='PENDING', then=Cast('reward_value', IntegerField()) * -1),
                output_field=IntegerField(),
                default=0
            ))
        )

        # Replace None values with 0
        return {k: v or 0 for k, v in aggregations.items()}

    @classmethod
    def get_flash_rewards_breakup(cls, store_reference):
        """
        Fetch flash rewards breakup for a store.

        Parameters:
        - store_reference (str): The reference identifier for the store.

        Returns:
        - dict: A dictionary containing lifetime_earnings, in_process_earnings, and pending_earnings for flash rewards.
        """
        return cls.get_rewards_breakup(store_reference, 'FLASH')

    @classmethod
    def get_infinity_rewards_breakup(cls, store_reference):
        """
        Fetch infinity rewards breakup for a store.

        Parameters:
        - store_reference (str): The reference identifier for the store.

        Returns:
        - dict: A dictionary containing lifetime_earnings, in_process_earnings, and pending_earnings for infinity rewards.
        """
        return cls.get_rewards_breakup(store_reference, 'INFINITY')

    class Meta:
        verbose_name_plural = "store rewards"
        db_table = '"store"."store_rewards"'


class StoreRewardsHistory(models.Model):
    class RewardTypeChoices(models.TextChoices):
        MONTHLY_CREDIT = "MONTHLY_CREDIT", _("Monthly Credit")
        TRANSFER = "TRANSFER", _("Transfer")
        REDEEM = "REDEEM", _("Redeem")
        ONBOARDING_REFERRAL = "ONBOARDING_REFERRAL", _("Onboarding Referral")
        VERIFICATION_PROMOTIONAL_REWARD = "VERIFICATION_PROMOTIONAL_REWARD", _("Verification Promotional Reward")

    class RewardStatusChoices(models.TextChoices):
        SUCCESS = "SUCCESS", _("Success") # Successfully added/deducted points
        PENDING = "PENDING", _("Pending") # Points added/deducted but not processed yet - waiting for release
        FAILED = "FAILED", _("Failed") # Failed to add/deduct points - will not be released
        INPROCESS = "INPROCESS", _("In Process") # Points added/deducted but not processed yet - Inprocess (particularly in orders lifecycle)

    class TransactionTypeChoices(models.TextChoices):
        CREDIT = "CREDIT", _("Credit")
        DEBIT = "DEBIT", _("Debit")

    class RewardCategoryChoices(models.TextChoices):
        INFINITY = "INFINITY", _("Infinity")
        FLASH = "FLASH", _("Flash")

    reward_id = models.AutoField(primary_key=True)
    reward_reference = models.CharField(max_length=20, unique=True)
    reward_category = models.CharField(max_length=20, blank=True, null=True, choices=RewardCategoryChoices.choices)
    reward_value = models.CharField(max_length=15, blank=True, null=True)
    reward_type = models.CharField(max_length=40, blank=True, null=True, choices=RewardTypeChoices.choices)
    transaction_type = models.CharField(max_length=7, blank=True, null=True, choices=TransactionTypeChoices.choices)
    store_reference = models.CharField(max_length=20, blank=True, null=True)
    event_reference = models.CharField(max_length=20, blank=True, null=True)
    reward_status = models.CharField(max_length=10, blank=True, null=True, default=RewardStatusChoices.SUCCESS, choices=RewardStatusChoices.choices)
    created_date = models.DateTimeField(auto_now_add=True)

    @staticmethod
    def add_store_rewards_history(store_reference, reward_type, reward_value, reward_category, transaction_type,
                                  event_reference, reward_status):
        store_rewards_history = StoreRewardsHistory(
            store_reference=store_reference,
            reward_type=reward_type,
            reward_value=reward_value,
            reward_category=reward_category,
            transaction_type=transaction_type,
            event_reference=event_reference,
            reward_status=reward_status
        )
        store_rewards_history.save()
        return store_rewards_history



    class Meta:
        verbose_name_plural = "store rewards histories"
        db_table = '"store"."store_rewards_history"'

    def generate_reward_reference(self):
        while True:
            reference = 'RW' + ''.join(random.choices(string.digits, k=8))
            if not StoreRewardsHistory.objects.filter(reward_reference=reference).exists():
                return reference

    def save(self, *args, **kwargs):
        if not self.reward_reference:
            self.reward_reference = self.generate_reward_reference()
        super(StoreRewardsHistory, self).save(*args, **kwargs)


class StoreRewardsMonthlyHistory(models.Model):
    month_reward_id = models.AutoField(primary_key=True)
    month_with_year = models.CharField(max_length=14, blank=True, null=True)
    reward_granted = models.PositiveIntegerField(default=0)
    reward_used = models.PositiveIntegerField(default=0)
    store_reference = models.CharField(max_length=20, blank=True, null=True)
    created_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "store rewards monthly"
        db_table = '"store"."store_rewards_monthly_history"'


