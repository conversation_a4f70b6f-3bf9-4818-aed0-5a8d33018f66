from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from stores.store_api.models import Store
from products.models import Product
from content.models import Posts, Story
from django.conf import settings
from django.contrib.sites.models import Site

class BaseSitemap(Sitemap):
    protocol = 'http' if settings.DEBUG else 'https'

    def get_urls(self, page=1, site=None, protocol=None):
        if site is None:
            site = Site.objects.get(id=settings.SITE_ID)
        return super().get_urls(page=page, site=site, protocol=self.protocol)

class StoreSitemap(BaseSitemap):
    changefreq = "daily"
    priority = 0.9

    def items(self):
        return Store.objects.filter(is_active=True)

    def lastmod(self, obj):
        return obj.modified_date

    def location(self, obj):
        return f'/seo/store/{obj.store_reference}/'

class ProductSitemap(BaseSitemap):
    changefreq = "daily"
    priority = 0.8

    def items(self):
        return Product.objects.filter(deleted=False, hide=False)

    def lastmod(self, obj):
        return obj.modified_date

    def location(self, obj):
        return f'/seo/product/{obj.product_reference}/'

class PostSitemap(BaseSitemap):
    changefreq = "weekly"
    priority = 0.7

    def items(self):
        return Posts.objects.filter(is_deleted=False)

    def lastmod(self, obj):
        return obj.created_date

    def location(self, obj):
        return f'/seo/post/{obj.post_reference}/'

class StorySitemap(BaseSitemap):
    changefreq = "weekly"
    priority = 0.7

    def items(self):
        return Story.objects.all()

    def lastmod(self, obj):
        return obj.created_at

    def location(self, obj):
        return f'/seo/story/{obj.story_reference}/'
