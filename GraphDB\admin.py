from django.contrib import admin
from .models import Neo4j<PERSON>ost, Neo4jPost<PERSON>mage, Neo4jUser, Neo4jStore, Neo4jEntity
from django_neomodel import admin as neo_admin


class MyModelAdmin(admin.ModelAdmin):
    pass
# Register your models with the admin site


neo_admin.register(Neo4j<PERSON><PERSON>, MyModelAdmin)
neo_admin.register(Neo4jPostImage, MyModelAdmin)
neo_admin.register(Neo4j<PERSON>ser, MyModelAdmin)
neo_admin.register(Neo4jStore, MyModelAdmin)
neo_admin.register(Neo4jEntity, MyModelAdmin)

