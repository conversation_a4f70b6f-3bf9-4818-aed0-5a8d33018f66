from orders.payout_api.models import encrypt_string
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework import generics, mixins
from rest_framework.mixins import UpdateModelMixin
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from twilio.base.exceptions import TwilioException
import urllib.request
import urllib.parse

from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated


from ..helpers import save_invite_credentials, find_applicable_invite_type
from common.util.support_helper import compress, get_unread_conversations_count
from django.core.exceptions import ObjectDoesNotExist
from django.shortcuts import get_object_or_404
from django.http import Http404
from general.models import ReservedHandles
from django.db.models import Q, Count
from neomodel import Q as NeoQ
from django.db import transaction
from django.core.files.storage import default_storage

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.authtoken.models import Token

import logging
import datetime
import base64
import pyotp
import random
import string
from django.core.mail import send_mail
from django.utils.crypto import get_random_string
import requests
from decouple import config
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from common.util.support_helper import client, generateKey, GenerateNotifications
from orders.order_api.models import SubOrder
from stores.store_api.models import Store
from stores.store_settings_api.models import TrustCenter
from stores.store_api.serializers import StoreFollowerSerializer
from common.util.notification_handler import NotificationHandler
from ..notification_api.models import Notifications
from .models import User, UserAddress, UserFollow, UserStore, UserDeviceDetails, UnregisteredUser, UserReferralCodeTemp
from ..invite_api.models import InviteUser, UserRewards
from GraphDB.models import Neo4jUser, Neo4jStore, Neo4jEntity
from .serializers import (
    UserSerializer,
    UserCheckSerializer,
    NewUserCheckSerializer,
    UserProfileSerializer,
    UserProfilePictureSerializer,
    UpdatePincodeSerializer,
    UserAddressSerializer,
    GetUserDetailsSerializer,
    UserFollowSerializer,
    UserFollowerListSerializer,
    PrivateUserDetailsSerializer,
    UserDeviceDetailsSerializer,
    UnregisteredUserSerializer
)
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.authentication import JWTAuthentication
from common.util.validators import *
from django.template.loader import get_template
from django.core.mail import EmailMultiAlternatives
from rest_framework.exceptions import ValidationError
from django.shortcuts import redirect
from urllib.parse import urlencode
from .utils import (google_get_user_info,
                    google_get_access_token,
                    generate_jwt_token,
                    handle_successful_login,
                    generate_otp,
                    validate_otp,
                    send_otp_via_sms,
                    send_otp_via_email,
                    mask_email,
                    otp_verification_response,
                    delete_unverified_duplicates_of_user,
                    Authentication_Flow,
                    InviteCodeHandler,
                    create_messaging_server_user,
                    update_messaging_server_user_data,
                    update_user_device_details_in_messaging_server
                    )
from messaging.views import get_or_create_auth_token

# Get an instance of a logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

################ login and home screens  ####################


class CheckUserInfo(APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        phonenumber = kwargs.get('phonenumber')
        user = User.objects.filter(phonenumber=phonenumber, deleted=False).first()

        # Only PhoneNumber is given We need to check if user exists and provide associated email
        return Response(
            data={
                'user_exists': True if (user and user.is_phonenumber_verified) else None,
                'user_email': mask_email(user.email) if (user and user.email and user.is_email_verified) else None,
            }, status=200)


class UserSignIn(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        phonenumber = request.data.get("phonenumber")
        input_email = request.data.get("email")
        google_access_token = request.data.get('access_token')

        response = Authentication_Flow(action='initiate',
                                       phonenumber=phonenumber,
                                       input_email=input_email,
                                       google_access_token=google_access_token)
        return response


class VerifyUserSignIn(APIView):
    serializer_class = NewUserCheckSerializer

    permission_classes = [AllowAny]

    def post(self, request):
        """get phone number and otp then varify that otp"""
        logger.info("entered check_otp post method")
        phonenumber = request.data.get("phonenumber")
        phonenumber_otp = request.data.get("phonenumber_otp")
        email = request.data.get("email")
        email_otp = request.data.get("email_otp")
        access_token = request.data.get("access_token")
        
        logger.info(f"Received data: phonenumber={phonenumber}, email={email}, phonenumber_otp={phonenumber_otp}, email_otp={email_otp}")
        
        response = Authentication_Flow(action='validate',
                                       phonenumber=phonenumber,
                                       input_email=email,
                                       phonenumber_otp=phonenumber_otp,
                                       email_otp=email_otp,
                                       google_access_token=access_token)
        
        logger.info(f"Authentication_Flow response: {response.data}")
        return response


class ResendOtp(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        otp_target = request.data.get('send_otp_to')
        is_user_verified = request.data.get('is_user_verified')

        if is_user_verified:
            user_instance = User.objects.get(phonenumber=otp_target, is_phonenumber_verified=True, deleted=False)
            otp_target = user_instance.email

        config_send_otp = config("SEND_OTP").lower() == 'true'  # Get the value of SEND_OTP from .env

        if config_send_otp:
            if otp_target.startswith('+91'):
                phone = otp_target[3:]
                phone_otp = generate_otp(identifier=phone)
                send_otp_via_sms(phone_number=phone, otp=phone_otp)
            else:
                email_otp = generate_otp(identifier=otp_target)
                send_otp_via_email(email_id=otp_target, otp=email_otp)

        return Response({"message": "OTP sent Successfully"}, status=200)


class PreviousFollows(generics.RetrieveAPIView):
    def post(self,request,phonenumber):
        try:
            previous_follows = UnregisteredUser.objects.filter(phonenumber=phonenumber, is_following=True)
        except:
            return Response({"message": "no previous follows"}, status=status.HTTP_404_NOT_FOUND)

        user_instance = User.objects.get(phonenumber=phonenumber, deleted=False)
        user_reference=user_instance.user_reference

        user_follower_count = previous_follows.filter(user_follower__isnull=False).count()
        store_follower_count = previous_follows.filter(store_follower__isnull=False).count()

        for entry in previous_follows:
            if entry.user_follower:
                UserFollow.objects.create(user_reference=user_reference, user_follower=entry.user_follower, is_following=True)
            if entry.store_follower:
                UserFollow.objects.create(user_reference=user_reference, store_follower=entry.store_follower, is_following=True)
        return Response({"message": "success",
                         "user_followers_count": user_follower_count,
                         "store_followers_count": store_follower_count,
                         }, status=status.HTTP_200_OK)


class GetUserDetails(generics.RetrieveAPIView):
    def get(self, request, user_reference, private=None):
        try:
            user_instance = User.objects.get(user_reference=user_reference)
            entity_node = Neo4jEntity.nodes.get(reference=user_reference)
        except Exception as e:
            return Response({"message": "error", "data": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)
        if private:
            user_details_serializer = PrivateUserDetailsSerializer(user_instance)
            entity_node.handle = user_details_serializer.data['user_name']
            entity_node.save()
        else:
            user_details_serializer = GetUserDetailsSerializer(user_instance)

        purchase_count = SubOrder.objects.filter(suborder_status=SubOrder.Suborder_Status.ORDER_DELIVERED, user_reference=user_reference).distinct('order_number').count()

        followers_count = entity_node.get_follower_count()
        following_count = entity_node.get_following_count()

        # Get unread conversations count using the new method
        # TODO need to update it to work for new messaging server
        # unread_conversations_count = get_unread_conversations_count(user_instance)

        # Create user_data dictionary with all the data
        user_data = user_details_serializer.data
        user_data['unread_conversations_count'] = 0

        return Response({
            "message": "success",
            "followers_count": followers_count,
            "following_count": following_count,
            "purchases": purchase_count,
            "data": user_data
        }, status=status.HTTP_200_OK)


class AddAndGetUserProfilePicture(generics.UpdateAPIView):

    serializer_class = UserProfilePictureSerializer
    queryset = User.objects.all()
    lookup_field = 'user_reference'
    parser_classes = (MultiPartParser, FormParser)

    def update(self, request, *args, **kwargs):
        logger.info("entered update method of class AddAndGetUserProfilePicture")
        """
            Add userprofile picture by userid
        """
        try:
            user_instance = self.get_object()
            image = request.data.get("icon")
            if image:
                image = compress(image, min_image_length=1000, desired_min_size_in_KB=60, desired_max_size_in_KB=70)
                file_path = 'profile_image/' + image.name.replace("'", "")
                file_path = default_storage.save(file_path, image)
                file_url = default_storage.url(file_path)
                user_instance.icon = file_path
                user_instance.save()

                user_reference = user_instance.user_reference
                try:
                    neo4j_user = Neo4jUser.nodes.get(reference=user_reference)
                    neo4j_user.icon = default_storage.url(user_instance.icon)
                    neo4j_user.save()
                    logger.info("User Node icon details updated")
                except Neo4jUser.DoesNotExist:
                    logger.info("User Node does not exist")
                logger.info("exited update method of class AddAndGetUserProfilePicture")
                # update icon in messaging server 
                update_messaging_server_user_data(instance=user_instance, icon=file_url)

                return Response(
                    {
                        "message": "success",
                        "data": {
                            "icon": file_url
                        }
                    },
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"message": "error", "data": "No File is selected"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response({"message": "error", "data": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, *args, **kwargs):

        logger.info("entered get method of class AddAndGetUserProfilePicture")
        """get user details by userid"""
        partial = kwargs.pop("partial", False)
        user_instance = self.get_object()
        serializer = self.get_serializer(
            user_instance, data=request.data, partial=partial
        )
        if serializer.is_valid():
            serializer.save()
            logger.info("exited get method of class AddAndGetUserProfilePicture")
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        return Response(
            {"message": "success", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class AddAndGetUserProfile(generics.RetrieveUpdateAPIView):

    serializer_class = UserProfileSerializer

    def get_object(self, user_reference):
        instance = User.objects.get(user_reference=user_reference)
        return instance

    def update(self, request, *args, **kwargs):
        """
        Add userprofile by user_reference, since user_instance has already created when user login,
        here using update method to fill all other user details
        """
        logger.info("entered update method of class AddAndGetUserProfile")
        partial = kwargs.pop("partial", False)
        request.data['invited_by_code'] = request.data.get("invited_by_code").upper() if request.data.get("invited_by_code") else None
        user_reference = kwargs['user_reference']
        user_instance = self.get_object(user_reference)

        # Creating user in messaging server
        user_instance_messaging = self.get_object(user_reference)
        user_instance_messaging.user_name = request.data.get("user_name")
        if request.data.get("display_name"):
            user_instance_messaging.display_name = request.data.get("display_name")

        current_user_name = user_instance_messaging.user_name
        current_display_name = user_instance_messaging.display_name
        if not user_instance.xmpp_jid: # checking if user has messaging credentials
            username, password, token1, token2, new_messaging_user_id = create_messaging_server_user(user_instance_messaging, email=user_instance.email)
            if username:
                request.data['xmpp_jid'] = username
                request.data['xmpp_password'] = encrypt_string(password)
                request.data['js_messaging_token'] = token1
                request.data['new_messaging_token'] = token2
                request.data['new_messaging_user_id'] = new_messaging_user_id
        # end of creating user in messaging server

        # Update invite code
        if user_instance.invited_by_code:
            if request.data.get("invited_by_code") is not None:
                if user_instance.invited_by_code != request.data.get("invited_by_code").upper():
                    return Response(
                        {"message": "error", "data": "Cannot update already existing code to a new one"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

        serializer = self.get_serializer(
            user_instance, data=request.data, partial=partial
        )
        # Update in Neo4j User Node
        if serializer.is_valid():
            with transaction.atomic():
                invite_code_handler = InviteCodeHandler(invitee_instance=user_instance, serializer=serializer)
                updated_serializer, exceptions = invite_code_handler.handle_invite_codes(invitee_instance=user_instance, serializer=serializer)
                updated_serializer.save()
                try:
                    neo4j_user = Neo4jUser.nodes.get(reference=user_reference)
                    neo4j_user.handle = request.data.get('display_name')
                    neo4j_user.name = request.data.get('first_name')
                    neo4j_user.city = request.data.get('user_location')
                    neo4j_user.save()
                    logger.info("User Node details updated")
                except Neo4jUser.DoesNotExist:
                    logger.info("User Node does not exist")

                logger.info("exited update method of class AddAndGetUserProfile")
                # update username & displayname in messaging server
                if request.data.get('user_name') and (request.data.get('user_name') != current_user_name):
                    try:
                        update_messaging_server_user_data(instance=user_instance, handle=request.data.get('user_name'))
                    except Exception as e:
                        logger.error(e)

                if request.data.get('display_name') and (request.data.get('display_name') != current_display_name):
                    try:
                        update_messaging_server_user_data(instance=user_instance, name=request.data.get('display_name'))
                    except Exception as e:
                        logger.error(e)

                response = Response(
                    {"message": "success", "data": serializer.data},
                    status=status.HTTP_200_OK,
                )
                return response
        response = Response(
            {"message": "error", "data": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )
        response._exceptions = list(response.data)
        return response
        # except:
        #     return Response({"message": "error"}, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, *args, **kwargs):

        """get user details by userid"""

        logger.info("entered get method of AddAndGetUserProfile")
        user_reference = kwargs['user_reference']
        user_instance = self.get_object(user_reference)
        serializer = self.get_serializer(
            user_instance
        )

        logger.info("exited get method of AddAndGetUserProfile")
        return Response(
            {"message": "success", "data": serializer.data},
            status=status.HTTP_200_OK,
        )


class UpdatePincodeAV(generics.RetrieveUpdateAPIView):

    def get_object(self, userid):
        instance = User.objects.get(userid=userid)
        return instance



    def update(self, request, *args, **kwargs):
        """
        Add pincode- since user_instance is already created use update method to
        update pincode to user model
        """
        try:
            new_pincode = request.data.get('pincode')
            instance = self.get_object(kwargs['pk'])
            try:
                User.objects.filter(userid=kwargs['pk']).update(pincode=new_pincode)
                neo4j_user = Neo4jUser.nodes.get(reference=instance.user_reference)
                neo4j_user.pincode = request.data.get('pincode')
                neo4j_user.save()
                logger.info("User Node pincode details updated")
            except Neo4jUser.DoesNotExist:
                logger.info("User Node does not exist")
            return Response(
                {"message": "success", "data": {"pincode": new_pincode}},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response({"message": "error", "data": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)


class GetUserContributions(APIView):
    def get(self, request, user_reference):
        try:
            # user_instance = User.objects.get(user_reference=user_reference)
            # user_id = user_instance.userid
            # user_contributions = SubOrder.objects.filter(user_reference=user_reference, suborder_status='DELIVERED').aggregate(Sum('total_amount'))['total_amount__sum'] or 0
            # user_contributions = int(user_contributions)

            return Response(
                {
                    "message": "success", 
                    "data": []
                    }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"message": "error", "data": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)


# CART PAGE
class UserAddressList(
    mixins.ListModelMixin, mixins.CreateModelMixin, generics.GenericAPIView
):
    queryset = UserAddress.objects.all()
    serializer_class = UserAddressSerializer

    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('user_id')
        if not UserAddress.objects.filter(userid=user_id).exists():
            request.data['address_type'] = 'BILLING'
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class UserAddressDetails(
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    generics.GenericAPIView,
):
    queryset = UserAddress.objects.all()
    serializer_class = UserAddressSerializer

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetAllUserAddress(generics.ListAPIView):
    serializer_class = UserAddressSerializer

    def list(self, request, *args, **kwargs):
        userid = self.kwargs["user_id"]
        queryset = UserAddress.objects.filter(userid=userid)
        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {"message": "success", "data": serializer.data}, status=status.HTTP_200_OK
        )


class DeleteUser(generics.DestroyAPIView):
    def delete(self, request, *args, **kwargs):
        logger.info("entered delete method")
        user_id = self.kwargs["user_id"]
        user_instance = User.objects.get(userid=user_id)
        user_instance.deleted = True
        InviteUser.objects.filter(
            invited_user=user_id, invite_status=InviteUser.Invite_Status.CREATED, is_deleted=False
        ).update(invite_status=InviteUser.Invite_Status.DISABLED)
        user_instance.save(update_fields=["deleted"])
        logger.info("exited delete method")
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class CheckUserRole(generics.RetrieveAPIView):
    def get(self, request, *args, **kwargs):
        logger.info("entered get method of CheckUserRole")
        user_id = self.kwargs["user_id"]
        try:
            user_role = User.objects.values_list("invite_type", flat=True).get(
                userid=user_id, deleted=False
            )
            return Response(
                {"message": "success", "user_role": user_role},
                status=status.HTTP_200_OK,
            )
        except:
            return Response({"message": "error"}, status=status.HTTP_200_OK)


class FollowUnfollowUser(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Follow or unfollow a user",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["user_reference", "user_follower", "store_follower"],
            properties={
                "user_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "user_follower": openapi.Schema(type=openapi.TYPE_STRING),
                "store_follower": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        user_reference = request.data.get('user_reference')
        user_follower = request.data.get('user_follower')
        store_follower = request.data.get('store_follower')

        if not user_reference:
            return Response({"messages": "error", "detail": "Missing user_reference"}, status=status.HTTP_400_BAD_REQUEST)

        if not (user_follower or store_follower):
            return Response({"messages": "error", "detail": "Missing user_follower or store_follower"}, status=status.HTTP_400_BAD_REQUEST)

        image = None

        if user_follower:
            store_follower = None
            user_instance = User.objects.get(user_reference=user_follower)
            if not user_instance:
                return Response({"messages": "error", "detail": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
            if user_instance.icon:
                image = user_instance.icon
            else:
                image=None

        if store_follower:
            user_follower = None
            store_instance = Store.objects.get(store_reference=store_follower)
            if not store_instance:
                return Response({"messages": "error", "detail": "Store not found"}, status=status.HTTP_400_BAD_REQUEST)
            if store_instance.icon:
                image = store_instance.icon
            else:
                image = None

        # Neo4j node follow relationship
        try:
            neo4j_entity = Neo4jUser.nodes.get(reference=user_reference)

            if user_follower:
                try:
                    neo4j_user = Neo4jUser.nodes.get(reference=user_follower)
                    if neo4j_user.is_following(neo4j_entity):
                        neo4j_user.unfollow(neo4j_entity)
                    else:
                        neo4j_user.follow(neo4j_entity)
                except Neo4jUser.DoesNotExist:
                    logger.info("The user follower node does not exist")

            elif store_follower:
                try:
                    neo4j_store = Neo4jStore.nodes.get(reference=store_follower)
                    if neo4j_store.is_following(neo4j_entity):
                        neo4j_store.unfollow(neo4j_entity)
                    else:
                        neo4j_store.follow(neo4j_entity)
                except Neo4jStore.DoesNotExist:
                    logger.info("The store follower node does not exist")

        except Neo4jUser.DoesNotExist:
            logger.info("The entity node does not exist")

        # Try to find an existing Userfollow record

        if UserFollow.objects.filter(user_reference=user_reference, user_follower=user_follower, store_follower=store_follower).exists():

            instance = UserFollow.objects.get(user_reference=user_reference, user_follower=user_follower, store_follower=store_follower)
            if instance.is_following:
                instance.is_following = False
                instance.save(update_fields=['is_following'])
                return Response({"messages": "success", "following": instance.is_following}, status=status.HTTP_200_OK)
            else:
                instance.is_following = True
                instance.save(update_fields=['is_following'])
                # Create a notification
                notification_handler = NotificationHandler(
                    notified_user=user_reference,
                    notification_type=Notifications.Notifications_Type.SOMEONE_FOLLOWED_USER,
                    notification_about=user_follower if user_follower else store_follower,
                    image=image,
                )
                notification_handler.create_notification(notification_handler)

                return Response({"messages": "success", "following": instance.is_following}, status=status.HTTP_200_OK)
        else:
            request.data['is_following'] = True
            serializer = UserFollowSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                # Create a notification
                notification_handler = NotificationHandler(
                    notified_user=user_reference,
                    notification_type=Notifications.Notifications_Type.SOMEONE_FOLLOWED_USER,
                    notification_about=user_follower if user_follower else store_follower,
                    image=image,
                )
                notification_handler.create_notification(notification_handler)
                return Response({"messages": "success", "following": True}, status=status.HTTP_200_OK)

            return Response({"messages": "error", "detail": "Invalid data"}, status=status.HTTP_400_BAD_REQUEST)


class UserFollowerList(generics.RetrieveAPIView):
    def get(self, request, *args, **kwargs):
        user_reference = kwargs['user_reference']
        user = User.objects.get(user_reference=user_reference)

        user_follower_reference_list = user.following.filter(is_following=True).values_list('user_follower', flat=True)
        store_follower_reference_list = user.following.filter(is_following=True).values_list('store_follower',flat=True)
        user_followers = User.objects.filter(user_reference__in=user_follower_reference_list)
        store_followers = Store.objects.filter(store_reference__in=store_follower_reference_list)
        user_serializer = UserFollowerListSerializer(user_followers, many=True)
        store_serializer = StoreFollowerSerializer(store_followers,many=True)
        return Response(
            {'message': 'success',
             "data": {
                 "users": user_serializer.data,
                 "stores": store_serializer.data}}, status=status.HTTP_200_OK)


class CheckUserNameAvailability(generics.CreateAPIView):
    def post(self, request, *args, **kwargs):
        user_name = request.data['user_name']
        user_name_without_space = user_name.replace(" ", "")

        is_user_name_exists = User.objects.filter(
            Q(user_name__iexact=user_name) | Q(user_name__iexact=user_name_without_space),
            deleted=False,).exists()

        is_store_handle_exists = Store.objects.filter(
            Q(storehandle__iexact=user_name) | Q(storehandle__iexact=user_name_without_space),
            deleted=False,
        ).exists()

        is_reserved_handle_exists = ReservedHandles.objects.filter(
            Q(handle_name__iexact=user_name) | Q(handle_name__iexact=user_name_without_space)
        ).exists()

        if is_user_name_exists or is_store_handle_exists or is_reserved_handle_exists:

            return Response({"message": "success", "exists": True}, status=status.HTTP_200_OK)
        else:
            return Response({"message": "success", "exists": False}, status=status.HTTP_200_OK)


class UserDeviceDetailsAV(UpdateModelMixin,generics.GenericAPIView):

    queryset = UserDeviceDetails.objects.all()
    serializer_class = UserDeviceDetailsSerializer
    lookup_field = 'user_reference'

    def get(self, request, *args, **kwargs):
        user_reference = self.kwargs.get("user_reference")
        user_device_details = UserDeviceDetails.objects.filter(user_reference=user_reference)
        user_device_details_serializer = UserDeviceDetailsSerializer(user_device_details, many=True)

        return Response(
            {
                "message": "success",
                "data": user_device_details_serializer.data
            }, status=status.HTTP_200_OK
        )

    def post(self, request, *args, **kwargs):
        user_reference = self.kwargs.get("user_reference")
        device_id = request.data.get('device_id')
        fcm_token = request.data.get('fcm_token')
        user_app_version = request.data.get('user_app_version', "")

        if not device_id or not fcm_token:
            return Response(
                {"message": "error", "data": "Device ID and FCM Token both are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        existing_entry = UserDeviceDetails.objects.filter(user_reference=user_reference, device_id=device_id).first()

        if existing_entry and (user_app_version == existing_entry.user_app_version):
            return Response(
                {"message": f"FCM token for given user reference {user_reference} and device id {device_id} already exists."},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = UserDeviceDetailsSerializer(
            data={'user_reference': user_reference, 'device_id': device_id, 'fcm_token': fcm_token, 'user_app_version':user_app_version})

        if serializer.is_valid():
            serializer.save()
            try:
                update_user_device_details_in_messaging_server(user_reference, device_id, fcm_token, user_app_version)
            except Exception as e:
                logger.error(f"Error updating user device details in messaging server: {str(e)}")
            return Response(
                {"message": "success", "data": serializer.data},
                status=status.HTTP_201_CREATED
            )
        else:
            return Response(
                {"message": "error", "data": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

    def patch(self, request, *args, **kwargs):

        user_reference = self.kwargs['user_reference']
        device_id = request.data.get('device_id')
        user_app_version = request.data.get('user_app_version', "")

        try:
            instance = UserDeviceDetails.objects.get(user_reference=user_reference, device_id=device_id)
        except UserDeviceDetails.DoesNotExist:
            return Response(
                {"message": f"Entry with user reference {user_reference} and device id {device_id} does not exist."},
                status=status.HTTP_404_NOT_FOUND
            )

        partial_update_data = {}

        if 'fcm_token' in request.data:
            partial_update_data['fcm_token'] = request.data['fcm_token']
        if user_app_version:
            partial_update_data['user_app_version'] = request.data['user_app_version']

        serializer = self.get_serializer(instance, data=partial_update_data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(
            {"message": "success", "data": serializer.data},
            status=status.HTTP_200_OK
        )

    def delete(self, request, *args, **kwargs):
        device_id = request.data.get('device_id')
        if not device_id:
            return Response(
                {"message": "error", "data": "Device ID not provided"},
                status=status.HTTP_400_BAD_REQUEST
            )
        user_reference = self.kwargs.get("user_reference")

        user_device_detail_instances = UserDeviceDetails.objects.filter(user_reference=user_reference, device_id=device_id)
        user_device_detail_instances.delete()

        return Response(
            {"message": "success", "data": "deleted"},
            status=status.HTTP_200_OK
        )


class CheckUserDeviceEntry(APIView):
    def get(self, request, user_reference, device_id, *args, **kwargs):
        entry_exists = UserDeviceDetails.objects.filter(
            user_reference=user_reference,
            device_id=device_id
        ).exists()

        return Response(
            {"entry_exists": entry_exists},
            status=status.HTTP_200_OK
        )


class FollowUnregisteredUser(generics.CreateAPIView):
    @swagger_auto_schema(
        operation_summary="Unregistered user follow/unfollow entries",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["following_reference", "followed_contacts", "to_follow"],
            properties={
                "following_reference": openapi.Schema(type=openapi.TYPE_STRING),
                "followed_contacts": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        required=["name", "phonenumber"],
                        properties={
                            "name": openapi.Schema(type=openapi.TYPE_STRING),
                            "phonenumber": openapi.Schema(type=openapi.TYPE_STRING),
                        },
                    ),
                ),
                "to_follow": openapi.Schema(type=openapi.TYPE_BOOLEAN),
            },
        ),
    )
    def post(self, request, *args, **kwargs):
        following_reference = request.data.get('following_reference')
        followed_contacts = request.data.get('followed_contacts', [])
        to_follow = request.data.get('to_follow')

        if not following_reference:
            return Response({"messages": "error", "detail": "No following_reference provided"},
                            status=status.HTTP_400_BAD_REQUEST)

        if not followed_contacts:
            return Response({"messages": "error", "detail": "No followed_contacts provided"},
                            status=status.HTTP_400_BAD_REQUEST)

        success_responses = []
        error_responses = []

        with transaction.atomic():
            user_follower = User.objects.get(user_reference=following_reference) if following_reference.startswith('U') else None
            store_follower = Store.objects.get(store_reference=following_reference) if following_reference.startswith('S') else None

            unregistered_users_to_create = []

            for contact in followed_contacts:
                name = contact.get('name')
                phonenumber = contact.get('phonenumber')

                if not name:
                    error_responses.append({"messages": "error", "detail": "Missing name"})
                    continue

                if not phonenumber:
                    error_responses.append({"messages": "error", "detail": f"Missing phonenumber for contact {name}"})
                    continue

                # Check if UnregisteredUser record exists
                unregistered_users = UnregisteredUser.objects.filter(
                    name=name,
                    phonenumber=phonenumber,
                    user_follower=user_follower,
                    store_follower=store_follower
                )

                if unregistered_users.exists():
                    # Update the existing record
                    unregistered_users.update(is_following=to_follow)
                else:
                    # Create a new record
                    unregistered_users_to_create.append(UnregisteredUser(
                        name=name,
                        phonenumber=phonenumber,
                        user_follower=user_follower,
                        store_follower=store_follower,
                        is_following=to_follow
                    ))

                success_responses.append({"messages": "success", "following": to_follow, "contact": name})

            # Bulk create new UnregisteredUser objects
            UnregisteredUser.objects.bulk_create(unregistered_users_to_create)

        response_data = {
            "success_responses": success_responses,
            "error_responses": error_responses,
        }

        return Response(response_data, status=status.HTTP_200_OK)


def get_follower_followed(asking_reference, user_references, store_references):
    followers_users_selfUser = UserFollow.objects.filter(
        user_reference=asking_reference, user_follower__in=user_references, is_following=True
    ).select_related('user_follower')

    followers_stores_selfUser = UserFollow.objects.filter(
        user_reference=asking_reference, store_follower__in=store_references, is_following=True
    ).select_related('store_follower')

    followers_users_selfStore = UserStore.objects.filter(
        store_reference=asking_reference, user_supporter__in=user_references, is_following=True
    ).select_related('user_supporter')

    followers_stores_selfStore = UserStore.objects.filter(
        store_reference=asking_reference, store_supporter__in=store_references, is_following=True
    ).select_related('store_supporter')

    followers_references = (
        [x.user_follower.user_reference for x in followers_users_selfUser] +
        [x.store_follower.store_reference for x in followers_stores_selfUser] +
        [x.user_supporter.user_reference for x in followers_users_selfStore] +
        [x.store_supporter.store_reference for x in followers_stores_selfStore]
    )

    followed_users_selfUser = UserFollow.objects.filter(
        user_follower=asking_reference, user_reference__in=user_references, is_following=True
    ).select_related('user_reference')

    followed_users_selfStore = UserFollow.objects.filter(
        store_follower=asking_reference, user_reference__in=user_references, is_following=True
    ).select_related('user_reference')

    followed_stores_selfUser = UserStore.objects.filter(
        user_supporter=asking_reference, store_reference__in=store_references, is_following=True
    ).select_related('store_reference')

    followed_stores_selfStore = UserStore.objects.filter(
        store_supporter=asking_reference, store_reference__in=store_references, is_following=True
    ).select_related('store_reference')

    followed_references = (
        [x.user_reference.user_reference for x in followed_users_selfUser] +
        [x.user_reference.user_reference for x in followed_users_selfStore] +
        [x.store_reference.store_reference for x in followed_stores_selfUser] +
        [x.store_reference.store_reference for x in followed_stores_selfStore]
    )

    following_contacts_selfUser = UnregisteredUser.objects.filter(
        user_follower=asking_reference, is_following=True
    )

    following_contacts_selfStore = UnregisteredUser.objects.filter(
        store_follower=asking_reference, is_following=True
    )

    following_contacts = (
        [x.phonenumber for x in following_contacts_selfUser] +
        [x.phonenumber for x in following_contacts_selfStore]
    )

    return followers_references, followed_references, following_contacts


class GetUserAndStoreProfiles(generics.RetrieveAPIView):
    def post(self, request, *args, **kwargs):
        asking_reference = request.data.get('asking_reference')
        contact_list = request.data.get('contact_list', [])
        contact_number_list = [contact['phonenumber'] for contact in contact_list]

        # asking_reference_type = 'user'
        # if asking_reference[0] == 'S':
        #     asking_reference_type = 'store'

        if not len(contact_list):
            return Response({'detail': 'No phonenumbers provided'}, status=status.HTTP_400_BAD_REQUEST)
        if not asking_reference:
            return Response({'detail': 'No asking reference provided'}, status=status.HTTP_400_BAD_REQUEST)

        results = []

        user_list = User.objects.filter(phonenumber__in=contact_number_list, deleted=False)
        # user_list.get(phonenumber=contact_number)
        user_ids = [user.userid for user in user_list]
        user_contact_numbers = [user.phonenumber for user in user_list]
        user_references = [user.user_reference for user in user_list]

        store_list = Store.objects.filter(created_by__in=user_ids, deleted=False)
        store_references = [store.store_reference for store in store_list]

        followers_references, followed_references,following_contacts = get_follower_followed(asking_reference, user_references, store_references)

        def get_follow_status(reference_type, reference =None, contact_number=None):

            text = 'Support' if reference_type == 'store' else 'Follow'

            if reference in followed_references:
                status = text + 'ing'
            elif reference in followers_references:
                status = text + ' back'
            elif contact_number in following_contacts:
                status = 'Pending'
            else:
                status = text

            return status

        for contact in contact_list:
            try:
                contact_name = contact['name']
                contact_number = contact['phonenumber']
                # user_instance = User.objects.get(phonenumber=contact_number, deleted=False)
                # if user_list.get(phonenumber=contact_number).exists():

                user_instance_exists = contact_number in user_contact_numbers

                if user_instance_exists:
                    user_instance = user_list.filter(phonenumber=contact_number).first()
                else:
                    user_instance=None

                # userid = user_instance.userid
                reference = user_instance.user_reference if user_instance else None
                reference_type = 'user' if user_instance else 'contact'
                name = contact_name
                handle = user_instance.user_name if user_instance else None
                icon = user_instance.icon.url if user_instance and user_instance.icon else None
                follow_status = get_follow_status(reference_type, reference , contact_number)

                result = {
                    'reference': reference,
                    'reference_type': reference_type,
                    'name': name,
                    'phonenumber': contact_number,
                    'handle': handle,
                    'icon': icon,
                    'follow_status':follow_status
                }
                results.append(result)
            except User.DoesNotExist:
                pass

        for store_instance in store_list:
            try:
                reference = store_instance.store_reference
                reference_type = 'store'
                name = store_instance.store_name
                handle = store_instance.storehandle
                icon = store_instance.icon.url if store_instance and store_instance.icon else None
                follow_status = get_follow_status(reference_type, reference)

                result = {
                    'reference': reference,
                    'reference_type': reference_type,
                    'name': name,
                    'handle': handle,
                    'icon': icon,
                    'follow_status': follow_status
                }

                results.append(result)
            except User.DoesNotExist:
                pass
        return Response({"message": "success", "data": results}, status=status.HTTP_200_OK)


class FollowOrSupportAll(generics.CreateAPIView):
    def post(self, request, *args, **kwargs):
        following_reference = request.data.get('following_reference')
        references_list = request.data.get('followed_references', [])
        to_follow = request.data.get('to_follow')

        user_references = [reference for reference in references_list if reference.startswith('U')]
        store_references = [reference for reference in references_list if reference not in user_references]

        if following_reference.startswith('U'):
            follower_instance = User.objects.get(user_reference=following_reference, deleted=False)
        else:
            follower_instance = Store.objects.get(store_reference=following_reference, deleted=False)

        if not following_reference:
            return Response({"messages": "error", "detail": "Missing following_reference"}, status=status.HTTP_400_BAD_REQUEST)
        if not references_list:
            return Response({"messages": "error", "detail": "followed_references list is empty"}, status=status.HTTP_400_BAD_REQUEST)

        success_responses = []
        error_responses = []

        def get_follow_status( reference_type, reference =None, contact_number = None):

            text = 'Support' if reference_type == 'store' else 'Follow'

            if reference in followed_references:
                status = text + 'ing'
            elif reference in followers_references:
                status = text + ' back'
            elif contact_number in following_contacts:
                status = 'Pending'
            else:
                status = text

            return status

        for reference in references_list:
            try:
                if reference.startswith('U'):
                    target_instance = User.objects.get(user_reference=reference)
                    reference_type = 'user'
                    model_type = UserFollow
                    follower_field = 'user_follower'
                    store_field = 'store_follower'
                    target_field = 'user_reference'

                else:
                    target_instance = Store.objects.get(store_reference=reference, deleted=False)
                    reference_type = 'store'
                    model_type = UserStore
                    follower_field = 'user_supporter'
                    store_field = 'store_supporter'
                    target_field = 'store_reference'

                query = (Q(**{follower_field: follower_instance}) & Q(**{target_field: target_instance})) \
                    if isinstance(follower_instance, User) \
                    else (Q(**{store_field: follower_instance}) & Q(**{target_field: target_instance}))

                if to_follow:
                    existing_instances = model_type.objects.filter(query)
                    if existing_instances.exists():
                        instance = existing_instances.first()
                        if not instance.is_following:
                            instance.is_following = True
                            instance.save(update_fields=['is_following'])
                    else:
                        model_type.objects.create(
                            **{follower_field: follower_instance, target_field: target_instance, 'is_following': True})

                    followers_references, followed_references, following_contacts = get_follower_followed(
                        asking_reference=following_reference,
                        user_references=user_references,
                        store_references=store_references)

                    follow_status = get_follow_status(reference_type, reference)
                else:
                    # Unfollow logic
                    model_type.objects.filter(query).update(is_following=False)

                    followers_references, followed_references, following_contacts = get_follower_followed(
                        asking_reference=following_reference,
                        user_references=user_references,
                        store_references=store_references)

                    follow_status = get_follow_status(reference_type, reference)

                success_responses.append({"messages": "success",
                                          "following": to_follow,
                                          "reference": reference,
                                          "follow_status": follow_status
                                          })
            except (User.DoesNotExist, Store.DoesNotExist):
                error_responses.append({"messages": "error", "detail": f"Invalid reference: {reference}"})

        response_data = {
            "success_responses": success_responses,
            "error_responses": error_responses,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class AddReferralCodeTemp(APIView):
    def post(self, request):
        try:
            user_device_id = request.data.get('user_device_id')
            referral_code = request.data.get('referral_code')
            if not referral_code or not user_device_id:
                return Response({"messages": "error", "detail": "Missing referral_code or user_device_id"},
                                status=status.HTTP_400_BAD_REQUEST)

            user_referral_code_temp = UserReferralCodeTemp.objects.filter(user_device_id=user_device_id).first()
            if user_referral_code_temp:
                user_referral_code_temp.referral_code = referral_code
                user_referral_code_temp.save(update_fields=['referral_code'])
            else:
                UserReferralCodeTemp.objects.create(user_device_id=user_device_id, referral_code=referral_code)

            return Response({"messages": "success"}, status=200)
        except:
            return Response({"messages": "error", "detail": "Something went wrong"},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request):
        try:
            user_device_id = request.query_params.get('user_device_id')
            if not user_device_id:
                return Response({"messages": "error", "detail": "Missing user_device_id"},
                                status=status.HTTP_400_BAD_REQUEST)

            user_referral_code_temp = UserReferralCodeTemp.objects.filter(user_device_id=user_device_id).first()
            if user_referral_code_temp:
                return Response({"messages": "success", "referral_code": user_referral_code_temp.referral_code},
                                status=status.HTTP_200_OK)
            else:
                return Response({"messages": "error", "detail": "No referral code found for this user_device_id"},
                                status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"messages": "error", "detail": str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetChatEntityRecommendations(APIView):
    def get(self, request):
        try:
            entity_type = request.query_params.get('entity_type')
            limit = int(request.query_params.get('limit', 10))
            offset = int(request.query_params.get('offset', 0))
            
            if entity_type == "USER":
                user_entities = User.objects.filter(deleted=False, icon__isnull=False, user_name__isnull=False)[offset:offset + limit]
                entities = user_entities
            elif entity_type == "STORE":
                store_entities = Store.objects.filter(deleted=False, icon__isnull=False, storehandle__isnull=False, is_active=True)[
                                 offset:offset + limit]
                entities = store_entities
            else:
                user_entities = User.objects.filter(deleted=False, icon__isnull=False, user_name__isnull=False)[offset:offset + limit]
                store_entities = Store.objects.filter(deleted=False, icon__isnull=False, storehandle__isnull=False, is_active=True)[
                                 offset:offset + limit]
                entities = list(user_entities) + list(store_entities)
            if not entities:
                return Response({"messages": "error", "detail": "Entities not found"},
                                status=status.HTTP_404_NOT_FOUND)
            
            chat_entities = [{
                "chat_id": "44c1a343-2cb1-410d-898e-d1478756b57d",
                "chat_icon": "/media/profile_image/er_1736524900448.jpg",
                "chat_name": "krishna_kanth",
                "chat_type": "direct",
                "created_at": "",
                "updated_at": "",
                "last_read_sequence": 0,
                "unread_count": 0,
                "is_subscribed": True,
                "entity_type": "USER"
            }]
            chat_entities.extend([{
                "chat_id": None,
                "chat_icon": entity.icon.url if entity.icon else None,
                "chat_name": entity.user_name if isinstance(entity, User) else entity.storehandle,
                "chat_type": "direct",
                "created_at": "",
                "updated_at": "",
                "last_read_sequence": 0,
                "unread_count": 0,
                "is_subscribed": False,
                "entity_type": "USER" if isinstance(entity, User) else "STORE",
                "connecting_id": entity.new_messaging_user_id or ""
            } for entity in entities])
            
            return Response(chat_entities, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({"messages": "error", "detail": str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# do not use this api (Bulk device detail delete) in app, it for testing purposes only ,
# can be used in future if user deletes his account
class BulkDeviceDetailsDelete(APIView):
    def delete(self, request, *args, **kwargs):
        user_reference = self.kwargs.get("user_reference")
        try:
            deleted_count, _ = UserDeviceDetails.objects.filter(user_reference=user_reference).delete()

            if deleted_count == 0:
                raise UserDeviceDetails.DoesNotExist

        except UserDeviceDetails.DoesNotExist:
            raise Http404("No matching entries found for the given user_reference.")

        return Response(
            {"message": "success", "data": f"Deleted {deleted_count} entries"},
            status=status.HTTP_200_OK
        )
