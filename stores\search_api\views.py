from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from fuzzywuzzy import process
from django.db.models import Q  # Add this import
import logging
from .models import SearchHistory, SearchedItems
from ..store_api.models import Store
from ..store_api.serializers import GetStoreSerializer, NewGetstoreSerializer
from .serializers import (
    SearchHistorySerializer,
    SearchedItemsSerializer,
    RecentSearchSerializer,
    ProductTypingSuggestionsSerializer,
    EntityTypingSuggestionsSerializer
)
from users.user_api.models import User
from products.models import Product
from content.models import Posts
from users.user_api.serializers import GetUserDetailsSerializer
from products.api.serializers import GetProductSerializer, GetProductListSerializer
from GraphDB.models import Neo4jPost
from GraphDB.queries import dbqueries, sqlqueries
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank, TrigramSimilarity
from django.db.models import TextField
from django.db.models import Q, Value
from django.db.models import Case, When
from django.db.models.functions import Greatest
from neomodel import db
import json
from general.views import BuyStatusCheckerBatch



logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class SearchAV(APIView):
    def get(self, request, *args, **kwargs):
        visitor_reference = request.query_params.get("visitor_reference")
        user_pincode = request.query_params.get("user_pincode")
        limit = int(request.query_params.get("limit"))
        offset = int(request.query_params.get("offset"))
        entity_type = request.query_params.get("entity_type")
        query = request.query_params.get("query")

        all_test_store_references = Store.objects.filter(storehandle__startswith='test_').values_list("store_reference",flat=True)
        all_inactive_store_references = Store.objects.filter(is_active=False).values_list("store_reference",flat=True)
        try:
            if visitor_reference.startswith('U'):
                visitor_id = User.objects.get(user_reference=visitor_reference, deleted=False).userid
            elif visitor_reference.startswith('S'):
                visitor_id = Store.objects.get(store_reference=visitor_reference, deleted=False).created_by
            user_test_stores = Store.objects.filter(created_by=visitor_id, is_active=True, storehandle__startswith='test_')
            user_test_stores_references = user_test_stores.values_list('store_reference', flat=True)
        except Exception as e:
            user_test_stores = []
            user_test_stores_references = []
            logger.info(f"An exception occured :{e}")

        if entity_type == "PRODUCT":
            # Create a combined search vector for full-text search
            search_vector = (
                SearchVector('product_name', weight='A') +
                SearchVector('brand_name', weight='A') +
                SearchVector('product_description', weight='C') +
                SearchVector('store_reference__store_name', weight='B') +
                SearchVector('store_reference__storehandle', weight='B')
            )
            search_query = SearchQuery(query)

            # Prepare the base queryset
            master_query_product = Product.objects.filter(
                deleted=False,
                store_reference__is_active=True
            ).exclude(store_reference__in=all_test_store_references)

            if user_test_stores_references:
                test_store_products = Product.objects.filter(store_reference__in=user_test_stores_references, deleted=False)
                master_query_product = master_query_product | test_store_products

            # Perform combined full-text and partial match search
            combined_results = master_query_product.annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query),
                name_similarity=TrigramSimilarity('product_name', query),
                brand_similarity=TrigramSimilarity('brand_name', query)
            ).filter(
                Q(rank__gte=0.1) |
                Q(product_name__icontains=query) |
                Q(brand_name__icontains=query) |
                Q(name_similarity__gt=0.3) |
                Q(brand_similarity__gt=0.3)
            ).annotate(
                combined_score=Case(
                    When(product_name__iexact=query, then=Value(1.0)),
                    When(brand_name__iexact=query, then=Value(0.1)),
                    default=Greatest('rank', 'name_similarity', 'brand_similarity'),
                    output_field=TextField()
                )
            ).order_by('-combined_score')[offset:offset+limit]

            return Response(
                {
                    "message": "success",
                    "product": GetProductSerializer(
                        combined_results,
                        many=True,
                        context={"user_pincode": user_pincode, "visitor_reference": visitor_reference}
                    ).data,
                },
                status=status.HTTP_200_OK,
            )

        if entity_type == "STORE":
            # Create a combined search vector for full-text search
            search_vector = (
                SearchVector('store_name', weight='A') +
                SearchVector('storehandle', weight='A') +
                SearchVector('business_description', weight='B') +
                SearchVector('store_details__city', weight='C') +
                SearchVector('store_desc', weight='C')
            )
            search_query = SearchQuery(query)

            # Prepare the base queryset
            master_query_store = Store.objects.filter(deleted=False, is_active=True).exclude(storehandle__startswith='test_')
            if user_test_stores:
                master_query_store = master_query_store | user_test_stores

            # Perform combined full-text and partial match search
            combined_results = master_query_store.annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query),
                name_similarity=TrigramSimilarity('store_name', query),
                handle_similarity=TrigramSimilarity('storehandle', query)
            ).filter(
                Q(rank__gte=0.1) |
                Q(store_name__icontains=query) |
                Q(storehandle__icontains=query) |
                Q(business_description__icontains=query) |
                Q(store_details__city__icontains=query) |
                Q(name_similarity__gt=0.3) |
                Q(handle_similarity__gt=0.3)
            ).annotate(
                combined_score=Case(
                    When(storehandle__iexact=query, then=Value(1.0)),
                    When(store_name__iexact=query, then=Value(0.9)),
                    default=Greatest('rank', 'handle_similarity', 'name_similarity'),
                    output_field=TextField()
                )
            ).order_by('-combined_score')[offset:offset+limit]

            # Apply eager loading and additional annotations
            combined_results = NewGetstoreSerializer.setup_eager_loading(combined_results)

            return Response(
                {
                    "message": "success",
                    "store": NewGetstoreSerializer(combined_results, many=True, context={"user_pincode": user_pincode}).data,
                },
                status=status.HTTP_200_OK,
            )

        if entity_type == "USER":
            # Create a combined search vector for full-text search
            search_vector = (
                SearchVector('user_name', weight='A') +
                SearchVector('first_name', weight='B') +
                SearchVector('display_name', weight='B') +
                SearchVector('about_user', weight='C')
            )
            search_query = SearchQuery(query)

            # Perform combined full-text and partial match search
            combined_results = User.objects.annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query),
                similarity=TrigramSimilarity('user_name', query)
            ).filter(
                Q(rank__gte=0.1) | Q(user_name__icontains=query) | Q(similarity__gt=0.3)
            ).annotate(
                combined_score=Case(
                    When(user_name__iexact=query, then=Value(1.0)),
                    default=Greatest('rank', 'similarity'),
                    output_field=TextField()
                )
            ).order_by('-combined_score')[offset:offset+limit]


            return Response(
                {
                    "message": "success",
                    "user": GetUserDetailsSerializer(combined_results, many=True).data,
                },
                status=status.HTTP_200_OK,
            )

        if entity_type == "POST":
            from django.db.models import Subquery
            search_vector = (
                SearchVector('post_text', weight='A') +
                SearchVector('store_reference__store_name', weight='B') +
                SearchVector('store_reference__storehandle', weight='B')
            )
            search_query = SearchQuery(query)

            # Optimize the stores_to_omit query
            stores_to_omit = list(Store.objects.filter(
                Q(store_reference__in=all_test_store_references) | ~Q(store_reference__in=user_test_stores_references) |
                Q(is_active=False)
            ).values_list('store_reference', flat=True))

            # Prepare the base queryset with annotations
            combined_results = Posts.objects.filter(
                is_deleted=False
            ).exclude(
                store_reference__in=stores_to_omit
            ).annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query),
                text_similarity=TrigramSimilarity('post_text', query)
            ).filter(
                Q(rank__gte=0.01) |
                Q(post_text__icontains=query) |
                Q(store_reference__store_name__icontains=query) |
                Q(store_reference__storehandle__icontains=query) |
                Q(text_similarity__gt=0.1)
            ).annotate(
                combined_score=Case(
                    When(post_text__icontains=query, then=Value(0.1)),
                    default=Greatest('rank', 'text_similarity'),
                    output_field=TextField()
                )
            ).order_by('-combined_score')[offset:offset+limit]

            # Fetch Neo4j data for the paginated results
            post_references = list(combined_results.values_list('post_reference', flat=True))
            required_posts = Neo4jPost.nodes.filter(reference__in=post_references)

            # Create a mapping of post references to Posts objects with counts
            posts_mapping = {
                post.post_reference: {
                    'like_count': post.like_count,
                    'comment_count': post.comment_count,
                    'repost_count': post.repost_count,
                    'repost_plus_count': post.repost_plus_count,
                    'save_count': post.save_count,
                    'share_count': post.share_count
                }
                for post in combined_results
            }

            post_data = list(map(lambda neo4j_post: {
                'postReference': neo4j_post.reference,
                'postText': neo4j_post.post_text,
                'createdDate': neo4j_post.created_date.isoformat() if neo4j_post.created_date else None,
                'isDeleted': neo4j_post.is_deleted,
                'likeCount': posts_mapping[neo4j_post.reference]['like_count'] if neo4j_post.reference in posts_mapping else 0,
                'commentCount': posts_mapping[neo4j_post.reference]['comment_count'] if neo4j_post.reference in posts_mapping else 0,
                'repostCount': posts_mapping[neo4j_post.reference]['repost_count'] if neo4j_post.reference in posts_mapping else 0,
                'repostPlusCount': posts_mapping[neo4j_post.reference]['repost_plus_count'] if neo4j_post.reference in posts_mapping else 0,
                'saveCount': posts_mapping[neo4j_post.reference]['save_count'] if neo4j_post.reference in posts_mapping else 0,
                'shareCount': posts_mapping[neo4j_post.reference]['share_count'] if neo4j_post.reference in posts_mapping else 0,
                'postImages': neo4j_post.serialized_post_images(),
                'likeStatus': neo4j_post.get_like_status(visitor_reference=visitor_reference),
                'repostStatus': neo4j_post.get_repost_status(visitor_reference=visitor_reference),
                'saveStatus': neo4j_post.get_save_status(visitor_reference=visitor_reference),
                'createdBy': {
                    'reference': creator.reference,
                    'handle': creator.handle,
                    'icon': creator.icon,
                    'entityType': creator.get_entity_type(),
                    'name': creator.name,
                    'phoneNumber': creator.phonenumber,
                    'pincode': creator.pincode,
                    'city': creator.city,
                    'isDeleted': creator.is_deleted,
                    'createdDate': creator.created_date.strftime("%d:%m:%Y %H:%M:%S") if creator.created_date else None,
                    'categoryName': creator.category_name if hasattr(creator, 'category_name') else None,
                    'followersOrSupportersCount': creator.get_follower_count(),
                    'followingOrSupportingCount': creator.get_following_count(),
                    'followStatus': creator.follow_status(visitor_reference=visitor_reference),
                    'subscriptionType': creator.get_subscription_type(),
                } if (creator := neo4j_post.get_post_creator()) else None,
                'contentCategory': neo4j_post.get_content_category(visitor_reference=visitor_reference),
                'contentHeaders': neo4j_post.get_content_headers(visitor_reference=visitor_reference) or [],
                'contentHeaderText': neo4j_post.get_content_header_text(visitor_reference=visitor_reference)
            }, required_posts))

            return Response(
                {
                    "message": "success",
                    "post": post_data
                },
                status=status.HTTP_200_OK,
            )


        # ########################Old search logic#######################
        if entity_type == "product":
            # product related search
            # filter all the products from db to use for further use.
            master_query_product = Product.objects.filter(
                deleted=False,
                store_reference__is_active=True
            ).exclude(store_reference__in=all_test_store_references)

            test_store_products = Product.objects.filter(store_reference__in=user_test_stores_references, deleted=False)
            master_query_product = master_query_product | test_store_products

            # In products, search should consider the following fields - product_name, brand_name, product_description.
            # Here uses fuzzy search. A fuzzy search searches for text that matches a term closely instead of exactly.
            # And here we use fuzzy search on product_name and brand_name.
            # In product_description just using django 'icontains' - The icontains lookup is used to get records that
            # contain a specified value and this is case-insensitive


            ################### product name search #####################
            # get list of all the 'product_name' from the already filtered master_query_product.
            list_of_product_names = master_query_product.values_list(
                "product_name", flat=True
            )

            # This list will store the product_name that are closely similar to the given query.
            product_name_result = []

            for product_value, product_score in process.extract(
                query, list_of_product_names, limit=4
            ):
                if product_score >= 70:
                    product_name_result.append(product_value)
            list_of_product_queryset = [
                list(master_query_product.filter(product_name=name))
                for name in product_name_result
            ]
            fuzzy_product_names = sum(list_of_product_queryset, [])

            ###################### brand name search ######################
            # fuzzy search on product brand name
            list_of_brand_names = master_query_product.values_list(
                "brand_name", flat=True
            )
            brand_name_result = []
            for product_value, product_score in process.extract(
                query, list_of_brand_names, limit=4
            ):
                if product_score >= 70:
                    brand_name_result.append(product_value)
            list_of_queryset = [
                list(master_query_product.filter(brand_name=brand))
                for brand in brand_name_result
            ]
            fuzzy_brand_names = sum(list_of_queryset, [])


            ###################### category name search ######################

            list_of_category_names = master_query_product.values_list(
                "product_category", flat=True
            )

            # This list will store the product_name that are closely similar to the given query.
            product_category_result = []

            for product_value, product_score in process.extract(
                    query, list_of_category_names, limit=4
            ):
                if product_score >= 70:
                    product_category_result.append(product_value)
            list_of_category_queryset = [
                list(master_query_product.filter(product_category=category))
                for category in product_category_result
            ]
            fuzzy_category_names = sum(list_of_category_queryset, [])

            ###################### store name search ######################

            list_of_store_names = master_query_product.values_list(
                "store_reference__store_name", flat=True
            )

            # This list will store the product_name that are closely similar to the given query.
            store_name_result = []

            for product_value, product_score in process.extract(
                    query, list_of_store_names, limit=4
            ):
                if product_score >= 70:
                    store_name_result.append(product_value)
            list_of_store_name_queryset = [
                list(master_query_product.filter(store_reference__store_name=storename))
                for storename in store_name_result
            ]
            fuzzy_store_names = sum(list_of_store_name_queryset, [])

            ###################### store handle search ######################

            list_of_store_handles = master_query_product.values_list(
                "store_reference__storehandle", flat=True
            )

            # This list will store the product_name that are closely similar to the given query.
            store_handle_result = []

            for product_value, product_score in process.extract(
                    query, list_of_store_handles, limit=4
            ):
                if product_score >= 70:
                    store_handle_result.append(product_value)
            list_of_store_handle_queryset = [
                list(master_query_product.filter(store_reference__storehandle=handle))
                for handle in store_handle_result
            ]
            fuzzy_store_handles = sum(list_of_store_handle_queryset, [])

            exact_product_name = master_query_product.filter(product_name__exact=query)
            exact_brand_name = master_query_product.filter(brand_name__exact=query)
            product_description = master_query_product.filter(
                product_description__icontains=query
            )
            exact_category = master_query_product.filter(product_category__exact=query)
            exact_store_name = master_query_product.filter(store_reference__store_name__exact=query)
            exact_store_handle = master_query_product.filter(store_reference__storehandle__exact=query)

            combined_product_list = (
                list(exact_product_name)
                + list(fuzzy_product_names)
                + list(exact_brand_name)
                + list(fuzzy_brand_names)
                + list(product_description)
                + list(exact_category)
                + list(fuzzy_category_names)
                + list(exact_store_name)
                + list(fuzzy_store_names)
                + list(exact_store_handle)
                + list(fuzzy_store_handles)
            )

            all_products = list(dict.fromkeys(combined_product_list))
            if limit or offset:
                all_products = all_products[offset:offset+limit]
            return Response(
                {"messsage": "success",
                    "product": GetProductSerializer(
                        all_products, many=True, context={"user_pincode": user_pincode, "visitor_reference":visitor_reference}
                        ).data,

                },
                status=status.HTTP_200_OK,
            )

        if entity_type == "post":
            test_stores_to_omit = set(all_test_store_references).difference(set(user_test_stores_references))
            stores_to_omit = test_stores_to_omit.union(all_inactive_store_references)
            post_references_to_omit = list(Posts.objects.filter(store_reference__in=stores_to_omit).values_list("post_reference", flat=True))

            required_post_references = dbqueries.get_required_posts_in_search(search_query=query,
                                                                              references_to_omit=post_references_to_omit,
                                                                              limit=limit,
                                                                              offset=offset
                                                                              )

            required_posts = Neo4jPost.nodes.filter(reference__in=required_post_references)

            post_data = list(map(lambda post: {
                'postReference': post.reference,
                'postText': post.post_text,
                'createdDate': post.created_date,
                'isDeleted': post.is_deleted,
                'likeStatus': post.user_like_status(visitor_reference=visitor_reference),
                'createdBy': post.get_post_creator().serialize(),  # Example for accessing related property
                'likeCount': post.get_like_count(),
                'commentCount': post.get_comment_count(),
                'postImages': post.serialized_post_images()  # Example for a method returning a value
                # Add other properties as needed
            }, required_posts))
            return Response(
                {
                    "messsage": "success",
                    "post": post_data
                },
                status=status.HTTP_200_OK,
            )

        else:
            return Response(
                {
                    "messsage": "Invalid entity type"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class SearchHistoryAV(generics.ListCreateAPIView):
    serializer_class = SearchHistorySerializer
    queryset = SearchHistory.objects.all()
    def post(self, request, *args, **kwargs):
        serializer = SearchHistorySerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({"message":"success", "data":serializer.data}, status=status.HTTP_200_OK)
        return Response ({"message": "error", "data": serializer.error}, status=status.HTTP_400_BAD_REQUEST)


class SearchedItemsAV(generics.ListCreateAPIView):
    serializer_class = SearchedItemsSerializer
    queryset = SearchedItems.objects.all()
    def post(self, request, *args, **kwargs):
        serializer = SearchedItemsSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "success", "data": serializer.data}, status=status.HTTP_200_OK)
        return Response({"message": "error", "data": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class RecentSearch(generics.RetrieveAPIView):
    def get(self, request, *args, **kwargs):
        user_reference = kwargs['user_reference']

        # filter out the latest search history texts.

        # when using distinct and order_by together, it will first order by "search_input_text" and only use
        # "created_date" as a secondary ordering. Which means the order by "created_date" will be incorrect.

        # To fix this problem, we take the list of search_history_id and again filter this search_history_id order_by
        # created_date. this will give the required result.

        latest_search_history_ids = SearchHistory.objects.filter(user_reference=user_reference, is_deleted=False).order_by(
            'search_input_text', '-created_date').distinct('search_input_text').values_list('search_history_id', flat=True)
        sorted_recent_searches = SearchHistory.objects.filter(search_history_id__in=latest_search_history_ids).order_by('-created_date')
        recent_searches_serializer = RecentSearchSerializer(sorted_recent_searches, many=True)

        # Just like recent searched texts, we have to take the recent searched items. This follow same logic as above,
        # but here using the table SearchedItems, which store the searched items. it can
        # be a store, product or user. for all three have to get the latest searches. below queries will do it.

        latest_searched_product_item_ids = SearchedItems.objects.filter(user_reference=user_reference,
                                                                         search_item_type=SearchedItems.SearchItemType.PRODUCT,
                                                                         is_deleted=False).order_by('search_item',
                                                                                                    '-created_date',).distinct(
                                                                         'search_item').values_list('searched_item_id', flat=True)
        sorted_recent_searched_products = SearchedItems.objects.filter(searched_item_id__in=latest_searched_product_item_ids).order_by('-created_date')
        searched_products_serializer = SearchedItemsSerializer(sorted_recent_searched_products, many=True)

        latest_searched_store_item_ids = SearchedItems.objects.filter(user_reference=user_reference, is_deleted=False,
                                                       search_item_type=SearchedItems.SearchItemType.STORE).order_by('search_item',
                                                                                                    '-created_date',).distinct(
                                                                         'search_item').values_list('searched_item_id', flat=True)
        sorted_recent_searched_stores = SearchedItems.objects.filter (
            searched_item_id__in=latest_searched_store_item_ids).order_by ('-created_date')
        searched_stores_serializer = SearchedItemsSerializer(sorted_recent_searched_stores, many=True)

        latest_searched_user_item_ids = SearchedItems.objects.filter(user_reference=user_reference, is_deleted=False,
                                                                 search_item_type=SearchedItems.SearchItemType.USER).order_by('search_item',
                                                                                                    '-created_date',).distinct(
                                                                         'search_item').values_list('searched_item_id', flat=True)
        sorted_recent_searched_users = SearchedItems.objects.filter(
            searched_item_id__in=latest_searched_user_item_ids).order_by('-created_date')
        searched_users_serializer = SearchedItemsSerializer(sorted_recent_searched_users, many=True)

        return Response({"message": "success", "recent_searches": recent_searches_serializer.data,
                         "store": searched_stores_serializer.data,
                         "product": searched_products_serializer.data,
                         "user": searched_users_serializer.data}, status=status.HTTP_200_OK)


class ClearSearchHistory(generics.UpdateAPIView):
    def update(self, request, *args, **kwargs):
        user_reference = kwargs['user_reference']
        SearchedItems.objects.filter(user_reference=user_reference, is_deleted=False).update(is_deleted=True)
        SearchHistory.objects.filter(user_reference=user_reference, is_deleted=False).update (is_deleted=True)
        return Response({"message": "success", "data": "history cleared"}, status=status.HTTP_200_OK)


class ClearOneSearchHistory(generics.UpdateAPIView):
    def update(self, request, *args, **kwargs):
        searched_item_id = request.data['searched_item_id']
        search_history_id = request.data['search_history_id']
        if searched_item_id != 0:
            searched_item_instance = SearchedItems.objects.get(searched_item_id=searched_item_id, is_deleted=False)
            search_item = searched_item_instance.search_item
            SearchedItems.objects.filter(search_item=search_item, is_deleted=False).update(is_deleted=True)
        elif search_history_id != 0:
            search_history_instance = SearchHistory.objects.get(search_history_id=search_history_id, is_deleted=False)
            search_input_text = search_history_instance.search_input_text
            SearchHistory.objects.filter(search_input_text__iexact=search_input_text, is_deleted=False).update(is_deleted=True)

        return Response({"message": "success", "data": "history cleared"}, status=status.HTTP_200_OK)


class GetSearchResultsCount(APIView):
    def get(self, request, *args, **kwargs):
        visitor_reference = request.query_params.get("visitor_reference")
        query = request.query_params.get("query", "").strip()

        if not query:
            return Response(
                {"message": "Query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        response_data = {
            "posts": 0,
            "people": 0,
            "products": 0,
            "stores": 0
        }

        # Count products using the same logic as LeanSearchViewNew._search_product
        response_data["products"] = self._count_products(query, visitor_reference)

        # Count stores using the same logic as LeanSearchViewNew._search_store
        response_data["stores"] = self._count_stores(query, visitor_reference)

        # Count users using the same logic as LeanSearchViewNew._search_user
        response_data["people"] = self._count_users(query)

        # Count posts using the same logic as LeanSearchViewNew._search_post
        response_data["posts"] = self._count_posts(query, visitor_reference)

        return Response(
            response_data,
            status=status.HTTP_200_OK,
        )

    def _count_products(self, query, visitor_reference):
        """Count products using the same logic as LeanSearchViewNew._search_product"""
        # Identify user from visitor_reference (can be user ID or store reference)
        owner_user = None
        if visitor_reference:
            store = Store.objects.filter(store_reference=visitor_reference, is_test_store=True).only('created_by').first()
            if store:
                owner_user = store.created_by
            else:
                try:
                    owner_user = User.objects.only('userid').get(user_reference=visitor_reference)
                except User.DoesNotExist:
                    logger.warning(f"Could not resolve visitor_reference '{visitor_reference}")

        # Base store filter: real stores + test stores created by owner
        store_filter = Q(store_reference__is_test_store=False)
        if owner_user:
            store_filter |= Q(store_reference__is_test_store=True, store_reference__created_by=owner_user)

        # Define weighted search vector
        search_vector = (
            SearchVector('product_name', weight='A') +
            SearchVector('brand_name', weight='A') +
            SearchVector('product_description', weight='C') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B')
        )
        search_query = SearchQuery(query)

        # Count products with the same filtering criteria as in LeanSearchViewNew
        product_count = Product.objects.filter(
            deleted=False,
            store_reference__is_active=True,
        ).filter(store_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('product_name', query),
            brand_similarity=TrigramSimilarity('brand_name', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(product_name__icontains=query) |
            Q(brand_name__icontains=query) |
            Q(name_similarity__gt=0.3) |
            Q(brand_similarity__gt=0.3)
        ).count()

        return product_count

    def _count_stores(self, query, visitor_reference):
        """Count stores using the same logic as LeanSearchViewNew._search_store"""
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('store_name', weight='A') +
            SearchVector('storehandle', weight='A') +
            SearchVector('business_description', weight='B') +
            SearchVector('store_details__city', weight='C') +
            SearchVector('store_desc', weight='C')
        )
        search_query = SearchQuery(query)

        # Base store filter: real stores + test stores created by owner (resolved via visitor_reference)
        store_filter = Q(deleted=False, is_active=True) & ~Q(storehandle__startswith="test_")

        # If visitor_reference is provided, resolve user or store reference
        if visitor_reference:
            owner_user = None
            try:
                # Check if visitor is a test store
                store = Store.objects.only('created_by').filter(
                    store_reference=visitor_reference,
                    is_test_store=True
                ).first()
                if store:
                    owner_user = store.created_by
            except Exception as e:
                logger.warning(f"Could not resolve visitor_reference '{visitor_reference}' to owner: {e}")

            # If owner_user exists, include test stores created by this user
            if owner_user:
                store_filter |= Q(is_test_store=True, created_by=owner_user)

        # Count stores with the same filtering criteria as in LeanSearchViewNew
        store_count = Store.objects.filter(store_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            name_similarity=TrigramSimilarity('store_name', query),
            handle_similarity=TrigramSimilarity('storehandle', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(store_name__icontains=query) |
            Q(storehandle__icontains=query) |
            Q(business_description__icontains=query) |
            Q(store_details__city__icontains=query) |
            Q(name_similarity__gt=0.3) |
            Q(handle_similarity__gt=0.3)
        ).count()

        return store_count

    def _count_users(self, query):
        """Count users using the same logic as LeanSearchViewNew._search_user"""
        # Create search vector with appropriate weights
        search_vector = (
            SearchVector('user_name', weight='A') +
            SearchVector('first_name', weight='B') +
            SearchVector('display_name', weight='B') +
            SearchVector('about_user', weight='C')
        )
        search_query = SearchQuery(query)

        # Count users with the same filtering criteria as in LeanSearchViewNew
        user_count = User.objects.filter(deleted=False).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            similarity=TrigramSimilarity('user_name', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(user_name__icontains=query) |
            Q(similarity__gt=0.3)
        ).count()

        return user_count

    def _count_posts(self, query, visitor_reference):
        """Count posts using the same logic as LeanSearchViewNew._search_post"""
        # Identify user from visitor_reference (can be user ID or store reference)
        owner_user = None
        if visitor_reference:
            store = Store.objects.filter(store_reference=visitor_reference, is_test_store=True).only('created_by').first()
            if store:
                owner_user = store.created_by
            else:
                try:
                    owner_user = User.objects.only('userid').get(user_reference=visitor_reference)
                except User.DoesNotExist as e:
                    logger.warning(f"Could not resolve visitor_reference '{visitor_reference}' to owner: {e}")

        # Base store filter: real stores + test stores created by owner
        store_filter = Q(store_reference__is_test_store=False)
        if owner_user:
            store_filter |= Q(store_reference__is_test_store=True, store_reference__created_by=owner_user)

        user_filter = Q(user_reference__isnull=False)

        # Define weighted search vector
        search_vector = (
            SearchVector('post_text', weight='A') +
            SearchVector('store_reference__store_name', weight='B') +
            SearchVector('store_reference__storehandle', weight='B')
        )
        search_query = SearchQuery(query)

        # Count posts with the same filtering criteria as in LeanSearchViewNew
        post_count = Posts.objects.select_related('store_reference').filter(
            is_deleted=False,
        ).filter(store_filter | user_filter).annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            post_text_similarity=TrigramSimilarity('post_text', query)
        ).filter(
            Q(rank__gte=0.1) |
            Q(post_text__icontains=query) |
            Q(post_text_similarity__gt=0.3)
        ).count()

        return post_count


class TypingSuggestionsAV(APIView):
    """
    Optimized content search API that combines ContentSearchAV and EntityContentSearchAV functionality.
    Handles @ syntax searches with improved performance and scalability.

    Supported patterns:
    - @query -> search users and stores
    - @username/ -> return empty (users don't have products)
    - @storehandle/ -> show all products in that store
    """

    def get(self, request, *args, **kwargs):
        query = request.query_params.get("query", "").strip()
        limit = int(request.query_params.get("limit", 10))
        offset = int(request.query_params.get("offset", 0))
        visitor_reference = request.query_params.get("visitor_reference")
        user_pincode = request.query_params.get("user_pincode")

        # Validate query format
        if not query or not query.startswith("@"):
            return Response({"results": []}, status=status.HTTP_200_OK)

        # Remove @ symbol and parse
        query = query[1:]
        parts = query.split("/")

        try:
            if len(parts) == 1:
                # Pattern: @query -> search users and stores
                return self._search_users_and_stores(parts[0], limit, offset)
            elif len(parts) == 2 and parts[0] and not parts[1]:
                # Pattern: @handle/ -> check if user or store, handle accordingly
                return self._handle_entity_products(parts[0], limit, offset, visitor_reference, user_pincode)
            else:
                return Response({"results": []}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in OptimizedContentSearchAV: {str(e)}")
            return Response({
                "message": f"An error occurred: {str(e)}",
                "results": []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _search_users_and_stores(self, query, limit, offset):
        """
        Optimized search for users and stores using Neo4j with improved query structure.
        Returns users and stores that match the query with proper ranking.
        """
        if not query.strip():
            return Response({"results": []}, status=status.HTTP_200_OK)

        # Optimized Cypher query with better performance
        cypher_query = """
            MATCH (u:Neo4jEntity)
            WHERE u.is_deleted = false
            AND (toLower(u.handle) STARTS WITH toLower($query)
                OR toLower(u.name) STARTS WITH toLower($query))
            WITH u AS node,
                CASE
                    WHEN 'Neo4jUser' IN labels(u) THEN 'USER'
                    ELSE 'STORE'
                END AS type,
                CASE
                    WHEN toLower(u.handle) = toLower($query) OR toLower(u.name) = toLower($query) THEN 0
                    WHEN toLower(u.handle) STARTS WITH toLower($query) OR toLower(u.name) STARTS WITH toLower($query) THEN 1
                    ELSE 2
                END AS priority
            ORDER BY priority, type, u.handle
            SKIP $offset LIMIT $limit
            RETURN node, type
        """
        parameters = {'query': query, 'limit': limit, 'offset': offset}

        try:
            results, _ = db.cypher_query(cypher_query, parameters)

            formatted_results = []
            for node, node_type in results:
                formatted_results.append({
                    'type': node_type,
                    'reference': node['reference'],
                    'primary_text': node['handle'],
                    'secondary_text': node['name'],
                    'image_url': node.get('icon')  # Use get() for safer access
                })

            return Response({"results": formatted_results}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in _search_users_and_stores: {str(e)}")
            return Response({"results": []}, status=status.HTTP_200_OK)

    def _handle_entity_products(self, handle, limit, offset, visitor_reference, user_pincode):
        """
        Handle @handle/ pattern - check if it's a user or store and respond accordingly.
        Users: return empty results (users don't have products)
        Stores: return all products in that store
        """
        try:
            # First check if it's a user
            user = User.objects.filter(user_name=handle, deleted=False).first()
            if user:
                # Users don't have products, return empty results
                return Response({
                    "results": [],
                    "message": "Users do not have products",
                    "entity_type": "USER"
                }, status=status.HTTP_200_OK)

            # Check if it's a store
            store = Store.objects.filter(
                storehandle=handle,
                deleted=False,
                is_active=True
            ).first()

            if not store:
                return Response({
                    "results": [],
                    "message": "Entity not found",
                    "entity_type": "UNKNOWN"
                }, status=status.HTTP_200_OK)

            # It's a store, return all products with optimized query
            return self._get_store_products_optimized(store, limit, offset)

        except Exception as e:
            logger.error(f"Error in _handle_entity_products: {str(e)}")
            return Response({
                "results": [],
                "message": f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_store_products_optimized(self, store, limit, offset):
        """
        Get all products for a store with optimized database queries.
        Uses lean serializers and efficient prefetching for better performance.
        """
        try:
            sql_instance = sqlqueries()
            product_data = sql_instance.get_product_suggestions( store_ref=store.store_reference, limit=limit, offset=offset)
            product_data_json =  json.loads(product_data)

            return Response({
                "results": product_data_json,
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Error in _get_store_products_optimized: {str(e)}")
            return Response({
                "results": [],
                "message": f"An error occurred while fetching store products: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EntityContentSearchAV(APIView):
    def post(self, request, *args, **kwargs):
        entity_reference = request.data.get("entity_reference")
        search_query = request.data.get("search_query")
        visitor_reference = request.data.get("visitor_reference")
        search_content_type = request.data.get("search_content_type")
        user_pincode = request.data.get("user_pincode")
        limit = int(request.data.get("limit", 10))
        offset = int(request.data.get("offset", 0))

        if not all([entity_reference, search_query, visitor_reference, search_content_type]):
            return Response({
                "message": "Missing required parameters",
                "status": "error"
            }, status=status.HTTP_400_BAD_REQUEST)

        if search_content_type not in ["POST", "PRODUCT"]:
            return Response({
                "message": "Invalid search_content_type. Must be either POST or PRODUCT",
                "status": "error"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Create search vectors
            post_search_vector = SearchVector('post_text', weight='A')
            product_search_vector = (
                    SearchVector('product_name', weight='A') +
                    SearchVector('brand_name', weight='A') +
                    SearchVector('product_description', weight='C')
            )

            search_query_obj = SearchQuery(search_query)

            # Handle user entity (only posts)
            if entity_reference.startswith('U'):
                if search_content_type == "PRODUCT":
                    return Response({
                        "message": "Users do not have products",
                        "status": "error"
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Prepare the base queryset with annotations
                combined_results = Posts.objects.filter(
                    user_reference__user_reference=entity_reference,
                    is_deleted=False
                ).annotate(
                    search=post_search_vector,
                    rank=SearchRank(post_search_vector, search_query_obj),
                    text_similarity=TrigramSimilarity('post_text', search_query)
                ).filter(
                    Q(rank__gte=0.01) |
                    Q(post_text__icontains=search_query) |
                    Q(text_similarity__gt=0.1)
                ).annotate(
                    combined_score=Case(
                        When(post_text__icontains=search_query, then=Value(0.1)),
                        default=Greatest('rank', 'text_similarity'),
                        output_field=TextField()
                    )
                ).order_by('-combined_score')[offset:offset + limit]

                # Fetch Neo4j data for the results
                post_references = list(combined_results.values_list('post_reference', flat=True))
                content_references_dict = dbqueries.get_content_interaction_details(visitor_reference=visitor_reference, content_references=post_references)
                interaction_and_creator_details = content_references_dict[0]
                post_references = content_references_dict[2]

                sql_instance = sqlqueries()
                post_data = sql_instance.get_posts_full(post_refs=post_references)
                post_data_json = json.loads(post_data)

                # # method 1
                # Build a single lookup for all references
                reference_lookup = {}

                for post in post_data_json:
                    reference_lookup[post["post_reference"]] = post


                # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
                # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

                # Build consolidated output in one pass
                consolidated_output = [
                    {
                        **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                        **item["interaction"],
                        **item["creator"],
                    }
                    for item in interaction_and_creator_details
                ]


                return Response({
                    "message": "success",
                    "data": consolidated_output
                }, status=status.HTTP_200_OK)

            # Handle store entity (both posts and products)
            elif entity_reference.startswith('S'):
                if search_content_type == "POST":
                    # Prepare the base queryset with annotations
                    combined_results = Posts.objects.filter(
                        store_reference__store_reference=entity_reference,
                        is_deleted=False
                    ).annotate(
                        search=post_search_vector,
                        rank=SearchRank(post_search_vector, search_query_obj),
                        text_similarity=TrigramSimilarity('post_text', search_query)
                    ).filter(
                        Q(rank__gte=0.01) |
                        Q(post_text__icontains=search_query) |
                        Q(text_similarity__gt=0.1)
                    ).annotate(
                        combined_score=Case(
                            When(post_text__icontains=search_query, then=Value(0.1)),
                            default=Greatest('rank', 'text_similarity'),
                            output_field=TextField()
                        )
                    ).order_by('-combined_score')[offset:offset + limit]

                    # Fetch Neo4j data for the results
                    post_references = list(combined_results.values_list('post_reference', flat=True))
                    content_references_dict = dbqueries.get_content_interaction_details(visitor_reference=visitor_reference, content_references=post_references)
                    interaction_and_creator_details = content_references_dict[0]
                    post_references = content_references_dict[2]

                    sql_instance = sqlqueries()
                    post_data = sql_instance.get_posts_full(post_refs=post_references)
                    post_data_json = json.loads(post_data)

                    # # method 1
                    # Build a single lookup for all references
                    reference_lookup = {}
                    for post in post_data_json:
                        reference_lookup[post["post_reference"]] = post


                    # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
                    # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

                    # Build consolidated output in one pass
                    consolidated_output = [
                        {
                            **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                            **item["interaction"],
                            **item["creator"],
                        }
                        for item in interaction_and_creator_details
                    ]
                    return Response({
                        "message": "success",
                        "data": consolidated_output
                    }, status=status.HTTP_200_OK)


                else:  # PRODUCT
                    products = Product.objects.filter(
                        store_reference__store_reference=entity_reference,
                        deleted=False,
                        active=True
                    ).annotate(
                        search=product_search_vector,
                        rank=SearchRank(product_search_vector, search_query_obj),
                        name_similarity=TrigramSimilarity('product_name', search_query),
                        brand_similarity=TrigramSimilarity('brand_name', search_query)
                    ).filter(
                        Q(rank__gte=0.1) |
                        Q(product_name__icontains=search_query) |
                        Q(brand_name__icontains=search_query) |
                        Q(name_similarity__gt=0.3) |
                        Q(brand_similarity__gt=0.3)
                    ).annotate(
                        combined_score=Greatest('rank', 'name_similarity', 'brand_similarity')
                    ).order_by('-combined_score')[offset:offset + limit]

                    product_references = list(products.values_list('product_reference', flat=True))

                    content_references_dict = dbqueries.get_content_interaction_details(
                    content_references= product_references, 
                    visitor_reference=visitor_reference
                    )
                    interaction_and_creator_details = content_references_dict[0]
                    product_references = content_references_dict[1]

                    sql_instance = sqlqueries()
                    product_data = sql_instance.get_products_hybrid(product_refs=product_references, store_ref=None, limit=limit, offset=0 )
                    product_data_json =  json.loads(product_data)

                    checker = BuyStatusCheckerBatch(products=products, user_pincode=user_pincode)
                    buy_status_map = checker.get_buy_status_map()

                    # # method 1
        
                    # Build a single lookup for all references
                    reference_lookup = {}

                    for product in product_data_json:
                        reference_lookup[product["product_reference"]] = product

                    # print(f"post_reference_lookup: {reference_lookup[post_references[0]]} ")
                    # print(f"product_reference_lookup: {reference_lookup[product_references[0]] }")

                    # Build consolidated output in one pass
                    consolidated_output = [
                        {
                            **reference_lookup.get(item["reference"], {}),  # Flat merge, no prefix
                            **item["interaction"],
                            **item["creator"],
                            **buy_status_map.get(item["reference"], {})
                        }
                        for item in interaction_and_creator_details
                    ]

                    return Response({
                        "message": "success",
                        "data": consolidated_output
                    }, status=status.HTTP_200_OK)

            else:
                return Response({
                    "message": "Invalid entity_reference format",
                    "status": "error"
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in EntityContentSearchAV: {str(e)}")
            return Response({
                "message": f"An error occurred: {str(e)}",
                "status": "error"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
