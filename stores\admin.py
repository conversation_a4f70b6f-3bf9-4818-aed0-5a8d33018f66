from django.contrib import admin
from .store_api.models import Store, Storelink, Category, Cities, BusinessTypes
from .store_settings_api.models import (
    TrustCenter,
    Documents,
    DeliveryLocations,
    DeliverySettings,
    RefundAndWarranty,
    Address,
)
from .feedback_api.models import FeedBack, FeedBackFiles
from .store_reward_api.models import StoreRewards, StoreRewardsHistory, StoreRewardsMonthlyHistory

# Register your models here.

admin.site.register(Store)
admin.site.register(Category)
admin.site.register(Address)
admin.site.register(Storelink)
admin.site.register(TrustCenter)
admin.site.register(Documents)
admin.site.register(DeliveryLocations)
admin.site.register(DeliverySettings)
admin.site.register(RefundAndWarranty)
admin.site.register(FeedBack)
admin.site.register(FeedBackFiles)
admin.site.register(Cities)
admin.site.register(BusinessTypes)
admin.site.register(StoreRewards)
admin.site.register(StoreRewardsHistory)
admin.site.register(StoreRewardsMonthlyHistory)

