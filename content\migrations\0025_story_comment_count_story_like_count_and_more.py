# Generated by Django 4.2.7 on 2024-10-29 17:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("content", "0024_story"),
    ]

    operations = [
        migrations.AddField(
            model_name="story",
            name="comment_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="story",
            name="like_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="story",
            name="repost_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="story",
            name="repost_plus_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="story",
            name="save_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="story",
            name="share_count",
            field=models.IntegerField(default=0),
        ),
    ]
