from django.urls import path
from .views import (
    CreateAndDeleteOrder,
    GetOrderDetailsByUser,
    GetOrderDetailsByStore,
    DeleteSubOrders,
    OrderPackaging,
    UpdateAndDeleteOrderPackage,
    UpdateDeliveryTrackingDetails,
    UpdateReturnTrackingDetails,
    UpdateOrderStatus,
    GetCustomerDetails,
    OrderFeesDetails,
    AddShippingUpdates,
    GetShippingUpdates,
    EditDeleteShippingUpdates,
    AddOrderRating,
    GetSubOrderHistory,
    CreateDisplayPackageNumber,
    auto_cancel_order_every_day_midnight,
    CheckPossibleRefund,
    CheckPossibleRefundNew,
    OrderedProductDetailsWithSpecificVersion,
    LogisticPartnerAV,
    CreateOrderAndInitiatePayment,
    InvoiceAPIView_new
)

urlpatterns = [
    # ORDER
    path(
        "create_delete_order/<str:ordernumber>/",
        CreateAndDeleteOrder.as_view(),
        name="create-order",
    ),
    path(
        "get_user_order_details/<int:userid>/",
        GetOrderDetailsByUser.as_view(),
        name="get-order-user-details",
    ),
    path(
        "get_user_order_details/<int:userid>/<str:order_number>/",
        GetOrderDetailsByUser.as_view(),
        name="get-order-user-details",
    ),
    path(
        "get_store_order_details/<int:storeid>/",
        GetOrderDetailsByStore.as_view(),
        name="get-order-store-details",
    ),
    path(
        "get_store_order_details/<int:storeid>/<str:order_number>/",
        GetOrderDetailsByStore.as_view(),
        name="get-order-store-details",
    ),
    path(
        "delete_suborders/<str:subordernumber>/",
        DeleteSubOrders.as_view(),
        name="order-partial-delete",
    ),
    path("order_packaging/", OrderPackaging.as_view(), name="order-packaging"),
    path(
        "update_and_delete_order_package/",
        UpdateAndDeleteOrderPackage.as_view(),
        name="update-order-package",
    ),
    path(
        "update_order_status/", UpdateOrderStatus.as_view(), name="update-order-status"
    ),
    path(
        "update_delivery_tracking_details/<str:package_number>/",
        UpdateDeliveryTrackingDetails.as_view(),
        name="update-delivery-tracking-details",
    ),
    path(
        "update_return_tracking_details/<str:return_package_number>/",
        UpdateReturnTrackingDetails.as_view(),
        name="update-return-tracking-details",
    ),
    path(
        "get_customer_details/<str:order_number>/<int:user_id>/",
        GetCustomerDetails.as_view(),
        name="get-customer-details",
    ),
    path(
        "order_payment_details/<str:order_number>/",
        OrderFeesDetails.as_view(),
        name="get-order-details",
    ),
    path(
        "order_payment_details/<str:order_number>/<str:is_seller>/",
        OrderFeesDetails.as_view(),
        name="get-order-details",
    ),
    # shipping
    path(
        "add_shipping_updates/",
        AddShippingUpdates.as_view(),
        name="add-shipping-address",
    ),
    path(
        "get_shipping_updates/<str:shipping_reference>/",
        GetShippingUpdates.as_view(),
        name="get-shipping-address",
    ),
    path(
        "edit_and_delete_shipping_updates/<int:pk>/",
        EditDeleteShippingUpdates.as_view(),
        name="edit-and-delete-shipping-updates",
    ),
    path("add_order_rating/", AddOrderRating.as_view(), name="add-order-rating"),
    path(
        "get_suborder_history/<str:suborder_number>/",
        GetSubOrderHistory.as_view(),
        name="get-suborder-history",
    ),
    path(
        "ordered_product_details/<str:suborder_number>/",
        OrderedProductDetailsWithSpecificVersion.as_view(),
        name="ordered-product-details",
    ),
    path(
        "create_display_package_number/",
        CreateDisplayPackageNumber.as_view(),
        name="create-display-package-number",
    ),
    path(
        "logistic_partner_list/",
        LogisticPartnerAV.as_view(),
        name="logistic-partner-list",
    ),
    path(
        "auto_cancel_order_every_day_midnight/",
        auto_cancel_order_every_day_midnight,
        name="send-otp",
    ),
    path(
        "check_possible_refund/",
        CheckPossibleRefundNew.as_view(),
        name="check-possible-refund",
    ),
    path(
        "check_possible_refund/<str:is_seller>/",
        CheckPossibleRefundNew.as_view(),
        name="check-possible-refund",
    ),
    path("create_order_and_initiate_payment/", CreateOrderAndInitiatePayment.as_view(), name="create-order-and-initiate-payment"),
    path("invoice/", InvoiceAPIView_new.as_view(),
         name="create-order-invoice")

]
