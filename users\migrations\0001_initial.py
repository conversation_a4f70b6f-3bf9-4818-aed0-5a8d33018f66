# Generated by Django 3.2.13 on 2022-07-27 06:07

from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("products", "0005_auto_20220727_1137"),
        ("stores", "0004_auto_20220727_1137"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                ("userid", models.AutoField(primary_key=True, serialize=False)),
                (
                    "phonenumber",
                    phonenumber_field.modelfields.PhoneNumberField(
                        max_length=128, region=None
                    ),
                ),
                ("is_verified", models.BooleanField(default=False)),
                ("counter", models.IntegerField(default=0)),
                ("email", models.EmailField(blank=True, max_length=100, null=True)),
                ("gender", models.CharField(blank=True, max_length=50, null=True)),
                ("age", models.IntegerField(blank=True, null=True)),
                (
                    "icon",
                    models.ImageField(blank=True, null=True, upload_to="profile_image"),
                ),
                ("user_name", models.CharField(blank=True, max_length=50, null=True)),
                ("user_handle", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "user_location",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("pincode", models.CharField(blank=True, max_length=6, null=True)),
                ("deleted", models.BooleanField(default=False)),
            ],
            options={
                "db_table": '"user"."user"',
            },
        ),
        migrations.CreateModel(
            name="UserStore",
            fields=[
                ("userstoreid", models.AutoField(primary_key=True, serialize=False)),
                ("is_following", models.BooleanField(default=False)),
                ("is_visited", models.BooleanField(default=False)),
                ("modified_date", models.DateTimeField(auto_now=True)),
                (
                    "storeid",
                    models.ForeignKey(
                        blank=True,
                        db_column="storeid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="stores.store",
                    ),
                ),
                (
                    "userid",
                    models.ForeignKey(
                        blank=True,
                        db_column="userid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "user stores",
                "db_table": '"user"."user_store"',
            },
        ),
        migrations.CreateModel(
            name="UserProduct",
            fields=[
                ("userproductid", models.AutoField(primary_key=True, serialize=False)),
                ("is_saved", models.BooleanField(null=True)),
                ("is_ordered", models.BooleanField(default=False)),
                ("is_visited", models.BooleanField(default=False)),
                ("modified_date", models.DateTimeField(auto_now=True)),
                (
                    "productid",
                    models.ForeignKey(
                        blank=True,
                        db_column="productid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                    ),
                ),
                (
                    "userid",
                    models.ForeignKey(
                        blank=True,
                        db_column="userid",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="get_users",
                        to="users.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "user products",
                "db_table": '"user"."user_product"',
            },
        ),
        migrations.CreateModel(
            name="UserAddress",
            fields=[
                ("useraddressid", models.AutoField(primary_key=True, serialize=False)),
                ("address", models.CharField(blank=True, max_length=255, null=True)),
                ("city", models.CharField(blank=True, max_length=100, null=True)),
                ("pincode", models.CharField(blank=True, max_length=6, null=True)),
                ("state", models.CharField(blank=True, max_length=100, null=True)),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "address_type",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                (
                    "userid",
                    models.ForeignKey(
                        db_column="userid",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.user",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "user address",
                "db_table": '"user"."user_address"',
            },
        ),
    ]
