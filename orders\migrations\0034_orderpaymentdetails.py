# Generated by Django 3.2.13 on 2022-11-16 07:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0033_auto_20221112_1006"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderPaymentDetails",
            fields=[
                (
                    "order_payment_details_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "product_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "product_quantity",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("product_price", models.PositiveIntegerField(blank=True, null=True)),
                ("delivery_fee", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "store_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("store_level_fee", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "payment_status",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                (
                    "order_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="order_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.order",
                        to_field="order_number",
                    ),
                ),
                (
                    "suborder_number",
                    models.ForeignKey(
                        blank=True,
                        db_column="suborder_number",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.suborder",
                        to_field="suborder_number",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "order payment details",
                "db_table": '"order"."order_payment_details"',
            },
        ),
    ]
