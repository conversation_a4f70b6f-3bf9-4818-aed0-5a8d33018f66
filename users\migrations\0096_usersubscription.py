# Generated by Django 4.2.7 on 2024-11-14 22:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("swadesic_admin", "0001_initial"),
        ("users", "0095_user_subscription_type_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserSubscription",
            fields=[
                (
                    "user_subscription_id",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("subscription_start_date", models.DateTimeField(auto_now_add=True)),
                ("subscription_end_date", models.DateTimeField(blank=True, null=True)),
                (
                    "subscription_status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("INACTIVE", "Inactive"),
                            ("EXPIRED", "Expired"),
                        ],
                        default="INACTIVE",
                        max_length=100,
                    ),
                ),
                (
                    "razorpay_payment_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "razorpay_subscription_id",
                    models.<PERSON>r<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                ("auto_renew", models.BooleanField(default=False)),
                (
                    "subscription_plan_reference",
                    models.ForeignKey(
                        db_column="subscription_plan_reference",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_subscriptions",
                        to="swadesic_admin.subscriptionplan",
                        to_field="plan_reference",
                    ),
                ),
                (
                    "user_reference",
                    models.ForeignKey(
                        db_column="user_reference",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_subscriptions",
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_reference",
                    ),
                ),
            ],
            options={
                "db_table": '"user"."user_subscription"',
            },
        ),
    ]
