import graphene
from graphene_django.types import DjangoObjectType
from graphql import GraphQLError

from .models import Posts, PostImages, PostLikes
from .feed_api.models import FeedPost
from users.user_api.models import User
from stores.store_api.models import Store


class UserType(DjangoObjectType):
    class Meta:
        model = User

    icon = graphene.Field(graphene.String)

    def resolve_icon(self, info):
        return self.icon.url if self.icon else None


class StoreType(DjangoObjectType):
    class Meta:
        model = Store

    icon = graphene.Field(graphene.String)

    def resolve_icon(self, info):
        return self.icon.url if self.icon else None


class PostImagesType(DjangoObjectType):
    class Meta:
        model = PostImages

    post_image = graphene.Field(graphene.String)

    def resolve_post_image(self, info):
        return self.post_image.url if self.post_image else None
        # Assuming your image field has a URL attribute


class PostLikesType(DjangoObjectType):
    class Meta:
        model = PostLikes


class PostsType(DjangoObjectType):
    class Meta:
        model = Posts

    post_id = graphene.ID(source='post_id')
    post_reference = graphene.String(source='post_reference')
    user = UserType
    store = StoreType
    post_text = graphene.String(source='post_text')
    created_date = graphene.DateTime(source='created_date')
    is_deleted = graphene.Boolean(source='is_deleted')
    post_images = graphene.List(PostImagesType)  # Change to a list of PostImagesType

    # New field for like count
    like_count = graphene.Int()
    post_likes = graphene.List(PostLikesType)

    user_like_status = graphene.Boolean(visitor_reference=graphene.String())

    def resolve_user_like_status(self, info, visitor_reference=None):
        if visitor_reference.startswith('U'):
            liked_by_user = User.objects.get(user_reference=visitor_reference, deleted=False)
            liked_by_store = None
        else:
            liked_by_store = Store.objects.get(store_reference=visitor_reference, deleted=False)
            liked_by_user = None

        # Check if the visiting user has liked the post
        return self.post_likes.filter(liked_by_user=liked_by_user, liked_by_store=liked_by_store, is_liked=True).exists()

    def resolve_like_count(self, info):
        return self.post_likes.filter(is_liked=True).count()

    def resolve_post_images(self, info):
        # Return a list of PostImagesType instances
        return self.post_images.filter(post_reference=self.post_reference)