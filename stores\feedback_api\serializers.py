from rest_framework import serializers
from .models import FeedBack, FeedBackFiles
import pytz


class FeedBackFilesSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeedBackFiles
        fields = "__all__"


class GetFeedBackSerializer(serializers.ModelSerializer):
    feedback_files = FeedBackFilesSerializer(many=True)
    date = serializers.SerializerMethodField("get_date")
    responses_count = serializers.SerializerMethodField("get_responses_count")
    responses = serializers.SerializerMethodField("get_responses")
    class Meta:
        model = FeedBack
        fields = [
            "feedback_id",
            "ticket_reference",
            "feedback_type",
            "name",
            "screen_category",
            "brief",
            "details",
            "status",
            "group",
            "priority",
            "date",
            "user_reference",
            "upvote_count",
            "upvote_users",
            "responses_count",
            "responses",
            "feedback_files",
        ]

    def get_responses_count(self, obj):
        responses = obj.responses
        if responses:
            responses_list = responses.split('|')
            response_count = len(responses_list)
        else:
            response_count = 0
        return response_count

    def get_responses(self, obj):
        if self.context.get("response"):
            responses = obj.responses
            if responses:
                responses_list = responses.split('|')
                instances = FeedBack.objects.filter(feedback_id__in=responses_list).prefetch_related('feedback_files').order_by("-created_date")
                serializer = GetFeedBackSerializer(instances, many=True)
                return serializer.data

    def get_date(self, obj):
        date = obj.created_date
        local_time = date.astimezone(pytz.timezone("Asia/Kolkata"))
        return local_time.strftime("%d:%m:%Y %H:%M:%S")


class FeedBackSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeedBack
        fields = [
            "feedback_id",
            "feedback_type",
            "user_reference",
            "name",
            "screen_category",
            "brief",
            "details",
            "status",
            "group",
            "responses",
            "priority",
        ]
