from django.db import models
import datetime
from django.utils.translation import gettext_lazy as _
from ..payout_api.models import OrderPayout
from ..order_api.models import OrderConfiguration, RefundedAmount, SubOrder, Order
from stores.store_api.models import Store
from stores.store_settings_api.models import RefundAndWarranty
from users.user_api.models import User
from django.db.models import Q



import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class SubOrderPaymentDetails(models.Model):
    class Payment_Status(models.TextChoices):
        PAYMENT_INITIATED = "PAYMENT_INITIATED", _("Payment initiated")
        PAYMENT_SUCCESS = "PAYMENT_SUCCESS", _("Payment success")
        REFUND_INITIATED = "REFUND_INITIATED", _("Refund initiated")
        REFUND_INITIATE_FAILED = "REFUND_INITIATE_FAILED", _("Refund initiate failed")
        REFUND_PENDING = "REFUND_PENDING", _("Refund pending")
        REFUND_SUCCESS = "REFUND_SUCCESS", _("Refund success")
        REFUND_FAILED = "REFUND_FAILED", _("Refund failed")

    class Deleted_Conditions(models.TextChoices):
        CANCELLED = "CANCELLED", _("Cancelled")
        RETURNED = "RETURNED", _("Returned")

    order_payment_details_id = models.AutoField(primary_key=True)
    suborder_number = models.ForeignKey(
        "orders.SubOrder",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="suborder_number",
        db_column="suborder_number",
        related_name="suborder_payment_detail_items",
    )
    order_number = models.ForeignKey(
        "orders.Order",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="order_number",
        db_column="order_number",
        related_name="suborder_payment_detail_items",

    )
    order_request_number = models.CharField(max_length=255, blank=True, null=True)
    product_reference = models.CharField(max_length=255, blank=True, null=True)
    product_unit_price = models.PositiveIntegerField(null=True, blank=True, default=0)
    product_quantity = models.PositiveIntegerField(null=True, blank=True, default=0)
    product_amount = models.PositiveIntegerField(null=True, blank=True, default=0)
    product_delivery_fee = models.PositiveIntegerField(blank=True, null=True, default=0)
    store_reference = models.CharField(max_length=20, null=True, blank=True)
    store_delivery_fee = models.PositiveIntegerField(blank=True, null=True, default=0)
    payment_status = models.CharField(
        max_length=50, choices=Payment_Status.choices, null=True, blank=True
    )
    refund_id = models.CharField(max_length=10, null=True, blank=True)
    refund_amount = models.FloatField(null=True, blank=True)
    is_suborder_deleted = models.BooleanField(default=False)
    suborder_deleted_condition = models.CharField(
        max_length=50, choices=Deleted_Conditions.choices, null=True, blank=True
    )
    delivery_fee_scope = models.CharField(max_length=50, null=True, blank=True)

    __payment_status = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__payment_status = self.payment_status

    def save(self, *args, **kwargs):
        """This method is responsible for saving the OrderPaymentDetails model instance and performing additional actions based on the payment status.

        When the payment status changes or the payment is successful, it creates a new OrderPaymentDetailsLifeCycle record to track the payment status history.

        If the payment is successful and there is no existing OrderPayout record for the suborder, it calls the create_order_payout method to create a new OrderPayout record."""
        pg_transctionfee_with_tax_perc = kwargs.pop('pg_transctionfee_with_tax_perc', None)
        super(SubOrderPaymentDetails, self).save(*args, **kwargs)
        if (
            self.payment_status != self.__payment_status
            or self.payment_status == self.Payment_Status.PAYMENT_SUCCESS
        ):
            OrderPaymentDetailsLifeCycle.objects.create(
                suborder_number=self.suborder_number,
                order_number=self.order_number,
                payment_status=self.payment_status,
                refund_id=self.refund_id,
            )
        self.__payment_status = self.payment_status

    def suborder_with_store_level_fee_exists(self):
        """This method checks if any of the suborders of given order has a store level fee."""
        return SubOrderPaymentDetails.objects.filter(order_number=self.order_number, store_delivery_fee__isnull=False).exists()

    def store_df_record_exists(self):
        return OrderPayout.objects.filter(suborder_number__isnull=True, order_number=self.order_number).exists()

    # def create_order_payout(self, pg_transctionfee_with_tax_perc,store_df_record=False):
    #     store_reference = Store.objects.get(store_reference=self.store_reference)
    #     user_reference = User.objects.get(userid=self.order_number.userid)
    #
    #     order_configuration_instance = OrderConfiguration.objects.all().first()
    #
    #     grace_period = order_configuration_instance.grace_period
    #     grace_period_timedelta = datetime.timedelta(minutes=grace_period)
    #
    #     return_period = self.get_return_period()
    #     fulfillment_window_days = order_configuration_instance.fulfillment_window / (24 * 60)  # Convert minutes to days
    #     return_period_timedelta = datetime.timedelta(days=max(return_period, fulfillment_window_days))
    #
    #     total_timedelta = return_period_timedelta + grace_period_timedelta
    #     release_date = datetime.date.today() + total_timedelta
    #     order_date = self.order_number.date
    #
    #     # order_amount = self.calculate_order_amount(store_amount_order_df=store_df_record)
    #     order_amount, expected_payout_amount, transaction_fee_calculated, commission_calculated, transaction_fee_percentage, \
    #         commission_fee, commission_fee_discount, tds_calculated = self.calculate_expected_payout_values(store_df_record=store_df_record, pg_transctionfee_with_tax_perc=pg_transctionfee_with_tax_perc)
    #
    #     if not store_df_record:
    #         OrderPayout.objects.create(
    #             store_reference=store_reference,
    #             suborder_number=self.suborder_number,
    #             order_number=self.order_number,
    #             user_reference=user_reference,
    #             product_total=self.product_amount,
    #             order_amount=order_amount,
    #             transaction_fee_calculated=transaction_fee_calculated,
    #             commission_calculated=commission_calculated,
    #             transaction_fee_percentage=transaction_fee_percentage,
    #             commission_fee=commission_fee,
    #             promotional_value=commission_fee_discount,
    #             tds_calculated=tds_calculated,
    #             payout_amount=expected_payout_amount,
    #             expected_payout_amount=expected_payout_amount,
    #             product_df=self.product_delivery_fee,
    #             # store_order_df=self.get_store_level_fee(),
    #             order_date=order_date,
    #             payout_status=OrderPayout.Payout_Status.WAITING_FOR_CONFIRMATION,
    #             payout_release_date=release_date,
    #         )
    #     else:
    #         OrderPayout.objects.create(
    #             store_reference=store_reference,
    #             # suborder_number=self.order_number.order_number + '-00',
    #             order_number=self.order_number,
    #             user_reference=user_reference,
    #             product_total=0,
    #             order_amount=order_amount,
    #             transaction_fee_calculated=transaction_fee_calculated,
    #             commission_calculated=commission_calculated,
    #             transaction_fee_percentage=transaction_fee_percentage,
    #             commission_fee=commission_fee,
    #             promotional_value=commission_fee_discount,
    #             tds_calculated=tds_calculated,
    #             payout_amount=expected_payout_amount,
    #             expected_payout_amount=expected_payout_amount,
    #             # product_df=self.delivery_fee,
    #             store_order_df=self.get_store_level_fee(),
    #             order_date=order_date,
    #             payout_status=OrderPayout.Payout_Status.WAITING_FOR_CONFIRMATION,
    #             payout_release_date=release_date,
    #         )

    def calculate_expected_payout_values(self, pg_transctionfee_with_tax_perc, store_df_record=False):
        order_configuration = OrderConfiguration.objects.first()
        order_amount = self.calculate_order_amount(store_amount_order_df=store_df_record)

        refunded_amount = self._get_refunded_amount(store_df_record, order_amount)
        tds_calculated = self._calculate_tds(order_amount, refunded_amount)
        transaction_fee_calculated, transaction_fee_percentage = self._calculate_transaction_fee(order_amount, pg_transctionfee_with_tax_perc)
        commission_calculated, commission_fee, commission_fee_discount = self._calculate_commission(order_configuration)

        expected_payout_amount = self._calculate_payout_amount(
            order_amount, refunded_amount, tds_calculated,
            transaction_fee_calculated, commission_calculated
        )

        return (order_amount, expected_payout_amount, transaction_fee_calculated, commission_calculated,
                transaction_fee_percentage, commission_fee, commission_fee_discount, tds_calculated)


    
    def calculate_order_amount(self, store_amount_order_df):
        # find the order_amount of that suborder ie; product_total + df.
        if store_amount_order_df:
            order_amount = self.store_delivery_fee
        else:
            order_amount = self.product_amount + self.product_delivery_fee # product amount = price * quantity

        # # suborder has store order df.
        # else:
        #     suborder_number = self.suborder_number.suborder_number
        #     suffix = "-01"
        #     # for suborders with store order df, df is credited to seller for first suborder in that order
        #     # below condition checks that.
        #     if suborder_number.endswith(suffix):
        #         order_amount = product_total + store_order_delivery_fee
        #     else:
        #         order_amount = product_total

        return order_amount

    def _get_refunded_amount(self, store_df_record, order_amount):
        if store_df_record:
            suborders = SubOrder.objects.filter(order_number=self.order_number)
            if suborders.exists() and all(
                    s.suborder_status == SubOrder.Suborder_Status.ORDER_CANCELLED for s in suborders):
                return order_amount
        else:
            try:
                suborder = SubOrder.objects.get(suborder_number=self.suborder_number)
                if suborder.suborder_status == SubOrder.Suborder_Status.ORDER_CANCELLED:
                    return RefundedAmount.objects.get(suborder_number=self.suborder_number).refunded_amount
            except (SubOrder.DoesNotExist, RefundedAmount.DoesNotExist):
                pass
        return 0

    def _calculate_tds(self, order_amount, refunded_amount):
        return round((order_amount - refunded_amount) * 0.01, 2)

    def _calculate_transaction_fee(self, order_amount, transaction_fee_percentage):
        if transaction_fee_percentage is None:
            raise ValueError("Transaction fee percentage is required")
        transaction_fee = order_amount * (transaction_fee_percentage / 100)
        return round(transaction_fee, 2), transaction_fee_percentage

    def _calculate_commission(self, order_configuration):
        commission_fee = order_configuration.commission_fee
        commission_fee_discount = order_configuration.commission_fee_discount
        if self.suborder_number.suborder_number.endswith("-01"):
            return -abs(commission_fee) + commission_fee_discount, commission_fee, commission_fee_discount
        return 0, commission_fee, commission_fee_discount

    def _calculate_payout_amount(self, order_amount, refunded_amount, tds, transaction_fee, commission):
        return round(order_amount - refunded_amount - tds - transaction_fee + commission, 2)

    def get_store_level_fee(self):
        order_payment_details_instance = SubOrderPaymentDetails.objects.get(suborder_number=self.suborder_number)
        store_level_fee = order_payment_details_instance.store_delivery_fee
        return store_level_fee

    def get_return_period(self):
        return_period = None
        if self.suborder_number is not None:
            refund_warranty_id = self.suborder_number.refund_warranty_id
            refund_warranty = RefundAndWarranty.objects.get(refundandwarrantyid=refund_warranty_id)
            return_period = 0
            if refund_warranty.return_type:
                return_period = refund_warranty.return_period
        return return_period

    class Meta:
        verbose_name_plural = "suborder payment details"
        db_table = '"order"."suborder_payment_details"'


class OrderPaymentDetailsLifeCycle(models.Model):
    order_payment_details_lifecycle_id = models.AutoField(primary_key=True)
    suborder_number = models.ForeignKey(
        "orders.SubOrder",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="suborder_number",
        db_column="suborder_number",
    )
    order_number = models.ForeignKey(
        "orders.Order",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        to_field="order_number",
        db_column="order_number",
    )
    payment_status = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    refund_id = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        verbose_name_plural = "order payment details lifecycle"
        db_table = '"order"."order_payment_details_lifecycle"'
